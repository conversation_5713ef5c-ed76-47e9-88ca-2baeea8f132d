# 连接模式：0 单点模式  1 哨兵模式  2 cluster模式
redis.mode=0
# redis 服务连接密码
redis.password=
# redis 连接超时时间 ms
redis.timeout=10000
# 使用第几个库，不配置默认0（只对单点模式、哨兵模式有效）
redis.dbIndex=11
#-1 表示无限次，0 表示不重试，N 为具体次数
redis.retry=-1
#redis重连间隔时间
redis.retryInterval=5
# 连接池配置（Apache 对象连接池）
redis.pool.testOnBorrow=false
redis.pool.testOnReturn=false
redis.pool.testWhileIdle=true
redis.pool.maxTotal=50
redis.pool.maxIdle=30
# 单节点模式的 ip 端口配置
redis.single.host=127.0.0.1
#redis.single.host=*************
redis.single.port=6379
# 哨兵模式的 ip 端口配置 列表
redis.sentinel.master=master1
redis.sentinel.nodes=*************:26379,*************:26379
# 集群模式配置
# 连接失败重试次数
redis.cluster.maxRetry=5
#启用该模式，必须配置大于3个
redis.cluster.nodes=*************:7000,*************:7001,*************:7002