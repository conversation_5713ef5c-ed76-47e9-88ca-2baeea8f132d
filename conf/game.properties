# 服务器id
serverId=231
# 平台id（自定）
platformId=1
# 游戏服务器端口
gameServerPort=12010
# 配置文件路径
configDataPath=D:\\ztxy_h5\\data\\miracle-data\\
# 开服时间
openTime=2025-02-22 17:20:00
# 合服日期:
#combineTime=2023-09-01 09:00:00
# debug模式
debug=true
# 本地模式
local=1
# 单个协议号每秒限制请求次数
protocol.limit.log.rate=10
#-----------------------------------------#
# 跨服相关
remoteAddress=127.0.0.1:9200
# 跨服组内sid,逗号分割
crossServerGroup=1
#跨服信息获取的方式 1 本地配置，2 api接口
remoteInfoType=1
# 匹配服相关
matchRemoteInfoType=1
#matchRemoteInfoAPI=http://127.0.0.1
matchHost=127.0.0.1:9400
matchKey=match
matchGroup=1
#-----------------------------------------#
# 服务器类型 1：游戏服 2: 跨服 3、战斗服 4、匹配服
serverType=1
#游戏服连接池配置文件
gameDbConfigPath=./conf/db.properties
#中心服API（用于礼包卡，全局排行榜等）
centerAPI=http://127.0.0.1:9524/
# center key
centerKey=xxxdddswersssssss
#本机ip地址
host=127.0.0.1
#日志服务器端口
backServerPort=9001
#http服务器端口
httpServerPort=9002
#api加解密秘钥
apiMd5=asiekbucyyoukane
#后台登录
backLoginSign=sjdalfjlbjd3358scjbbkjljdd
#是否开启防沉迷
fcmCheck=false
#是否开启外挂检测
wgCheck=false
#是否开启聊天信息推送
pushChat=false
#是否开启角色信息变更推送
pushRole=false
#聊天监控(自动禁言)
autoBanChat=false
#中心服API（用于礼包卡，全局排行榜等）
#dataCenterAPI=http://127.0.0.1:9524/
# center key
dataCenterKey=xxxdddswersssssss
redisConfigPath=./conf/redis.properties
