package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 神铸装备成功返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResEquipShenZhuMessage extends AbsProtostuffMessage {
  /**
   * item表的type
   */
  private int type;

  /**
   * 神铸阶数
   */
  private int shenzhuLevel;

  @Override
  public int getId() {
    return 13071;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setShenzhuLevel(int shenzhuLevel) {
    this.shenzhuLevel = shenzhuLevel;
  }

  public int getShenzhuLevel() {
    return this.shenzhuLevel;
  }
}
