package com.sh.game.common.redis;


import com.sh.game.common.redis.commands.RedisCommands;
import com.sh.game.common.redis.commands.RedisZSetsCommands;
import com.sh.game.common.redis.factory.JedisConnectionFactory;
import com.sh.game.common.redis.factory.RedisConnectionFactory;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;
import java.util.function.Function;

/**
 * redis操作助手
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2019/3/20.
 */
@Slf4j
public class RedisHelper {

    public static final RedisHelper INSTANCE = new RedisHelper();

    private RedisConnectionFactory factory;

    public static RedisHelper getInstance() {
        return INSTANCE;
    }


    /**
     * 初始化模块
     */
    public static void init(String proFile) throws Exception {
        try {
            log.info("redis开始进行初始化配置文件路径：{}", proFile);
            InputStream in = new FileInputStream(proFile);
            Properties properties = new Properties();
            properties.load(in);
            RedisConfiguration configuration = RedisConfiguration.build(properties);
            INSTANCE.factory = redisConnectFactory(configuration);
        } catch (Exception e) {
            log.error("redis初始化失败", e);
            throw new Exception("redis初始化失败");
        }
    }

    public RedisCommands getCommands() {
        if (factory == null) {
            throw new RuntimeException("redis 插件初始化失败");
        }

        try {
            return factory.getConnection();
        } catch (Exception e) {
            throw new RuntimeException("Redis 连接失败", e);
        }
    }

    /**
     * 使用jdk8 h函数接口，处理redis調用問題
     *
     * @param invoker
     * @param <R>
     * @return
     */
    public static <R> R exec(Function<RedisCommands, R> invoker) {
        RedisCommands commands = getInstance().getCommands();
        return invoker.apply(commands);
    }


    public static <R> R zRank(Function<RedisZSetsCommands, R> invoker) {
        RedisZSetsCommands commands = getInstance().getCommands();
        return invoker.apply(commands);
    }

    /**
     * 根据配置获取redis工厂
     */
    private static RedisConnectionFactory redisConnectFactory(RedisConfiguration configuration) {
        return new JedisConnectionFactory(configuration);
    }

    public void logRedisPoolInfo() {
        if (factory != null) {
            factory.logRedisPoolInfo();
        }
    }

}
