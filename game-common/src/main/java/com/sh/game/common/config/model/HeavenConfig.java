package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/7 15:50
 */
@Getter
@Setter
@ConfigData(file = "cfg_heaven")
public class HeavenConfig extends AbstractConfigData {

    private int id;

    private int actId;

    private int actType;

    /**
     * 对应充值额度
     */
    private int cost;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rate;

    private int map;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> zonesCost;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] target;

    /**
     * 地图存在时间
     */
    private int mapExist;
}
