package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 境界特殊buff配置
 *
 * <AUTHOR>
 * @date 2022/11/14 13:54
 */
@Setter
@Getter
@ConfigData(file = "cfg_shentonglingbao")
public class RealmSpecialConfig extends AbstractConfigData {

    private int id;

    /**
     * buff列表
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> buffId;

    /**
     * 活动id
     */
    private int activityId;

    /**
     * 直购id
     */
    private int rechargeId;

    /**
     * 噬灵怪物类型
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> monsterType;

    /**
     * 升级需要消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 每日噬灵次数限制
     */
    private int limit;

    /**
     * buff增强
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> buffSpecial;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] att;
}
