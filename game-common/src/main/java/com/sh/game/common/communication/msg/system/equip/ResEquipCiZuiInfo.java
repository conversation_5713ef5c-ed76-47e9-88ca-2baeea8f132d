package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.communication.msg.system.equip.bean.EquipCiZuiBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * 返回装备词缀信息
 * 该文件由工具根据 equip.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResEquipCiZuiInfo extends AbsProtostuffMessage {
  /**
   * 词缀集合
   */
  private List<EquipCiZuiBean> beans = new ArrayList<>();

  /**
   * 装备唯一id
   */
  private long equipId;

  /**
   * 当前洗炼位置
   */
  private int curIndex;

  @Override
  public int getId() {
    return 13106;
  }

  public void setBeans(List<EquipCiZuiBean> beans) {
    this.beans = beans;
  }

  public List<EquipCiZuiBean> getBeans() {
    return this.beans;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setCurIndex(int curIndex) {
    this.curIndex = curIndex;
  }

  public int getCurIndex() {
    return this.curIndex;
  }
}
