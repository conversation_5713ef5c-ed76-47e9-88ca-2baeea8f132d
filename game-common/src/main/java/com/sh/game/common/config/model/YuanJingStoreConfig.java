package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
@ConfigData(file = "cfg_yuanjing_store")
public class YuanJingStoreConfig extends AbstractConfigData {
    /**
     * 配置id
     */
    private int id;

    /**
     * 部位
     */
    private int locationId;

    /**
     * 抽卡消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> cost;

    /**
     * 抽奖宝箱
     */
    private int box;

    /**
     * 保底奖励-暂时配的是itemId
     */
    private int minBox;

    /**
     * 保底次数
     */
    private int count;
}
