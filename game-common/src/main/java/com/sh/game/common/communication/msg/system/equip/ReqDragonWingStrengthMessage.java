package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求神龙之魂强化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqDragonWingStrengthMessage extends AbsProtostuffMessage {
  /**
   * 强化道具id
   */
  private int itemId;

  @Override
  public int getId() {
    return 13058;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }
}
