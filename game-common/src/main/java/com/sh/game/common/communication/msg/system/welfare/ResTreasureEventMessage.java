package com.sh.game.common.communication.msg.system.welfare;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 藏宝图事件通知
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTreasureEventMessage extends AbsProtostuffMessage {
  /**
   * 事件编号
   */
  private int eventId;

  @Override
  public int getId() {
    return 27020;
  }

  public void setEventId(int eventId) {
    this.eventId = eventId;
  }

  public int getEventId() {
    return this.eventId;
  }
}
