package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回开服时间
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResOpenServerTimeMessage extends AbsProtostuffMessage {
  /**
   * 开服时间
   */
  private int openServerTime;

  @Override
  public int getId() {
    return 43151;
  }

  public void setOpenServerTime(int openServerTime) {
    this.openServerTime = openServerTime;
  }

  public int getOpenServerTime() {
    return this.openServerTime;
  }
}
