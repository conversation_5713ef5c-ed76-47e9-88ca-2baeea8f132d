package com.sh.game.common.communication.msg.system.mounts;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 使用坐骑
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMountsUseMessage extends AbsProtostuffMessage {
  /**
   * 坐骑表id
   */
  private int mountCfgId;

  @Override
  public int getId() {
    return 195004;
  }

  public void setMountCfgId(int mountCfgId) {
    this.mountCfgId = mountCfgId;
  }

  public int getMountCfgId() {
    return this.mountCfgId;
  }
}
