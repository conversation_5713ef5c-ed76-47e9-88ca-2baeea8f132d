package com.sh.game.common.communication.msg.system.aptitude;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 请求资质鉴定
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-09-13
 **/
@RPC("toLogic")
public class ReqAptitudeChangeMessage extends AbstractMessage {

    @Override
    public int getId() {
        return 183003;
    }

    /**
     * 资质道具cid
     */
    private long aptitudeItemUid;

    public long getAptitudeItemUid() {
        return aptitudeItemUid;
    }

    public void setAptitudeItemUid(long aptitudeItemUid) {
        this.aptitudeItemUid = aptitudeItemUid;
    }

    @Override
    public boolean read(KryoInput buf) {

        this.aptitudeItemUid = readLong(buf);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {

        this.writeLong(buf, aptitudeItemUid);
        return true;
    }
}
