package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;

/**
 * 请求装备萃取
 * 该文件由工具根据 equip.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEquipCuiQu extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  @Override
  public int getId() {
    return 13117;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }
}
