package com.sh.game.common.communication.msg.system.booksword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求书剑升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqBookSwordUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 书剑类型
   */
  private int sub;

  @Override
  public int getId() {
    return 360003;
  }

  public void setSub(int sub) {
    this.sub = sub;
  }

  public int getSub() {
    return this.sub;
  }
}
