package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

public class RoleDanYaoPointConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 2) {
            return false;
        }
        int point = params[1];
        int roleDanYaoPoint = avatar.findRoleDanYaoPoint();
        return roleDanYaoPoint >= point;
    }
}
