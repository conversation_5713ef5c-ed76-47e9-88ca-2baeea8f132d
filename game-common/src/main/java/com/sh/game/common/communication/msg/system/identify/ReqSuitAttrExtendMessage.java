package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求专属套装效果继承
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSuitAttrExtendMessage extends AbsProtostuffMessage {
  /**
   * 套装属性装备uid
   */
  private long sourceItemUid;

  /**
   * 专属装备uid
   */
  private long targetItemUid;

  @Override
  public int getId() {
    return 183005;
  }

  public void setSourceItemUid(long sourceItemUid) {
    this.sourceItemUid = sourceItemUid;
  }

  public long getSourceItemUid() {
    return this.sourceItemUid;
  }

  public void setTargetItemUid(long targetItemUid) {
    this.targetItemUid = targetItemUid;
  }

  public long getTargetItemUid() {
    return this.targetItemUid;
  }
}
