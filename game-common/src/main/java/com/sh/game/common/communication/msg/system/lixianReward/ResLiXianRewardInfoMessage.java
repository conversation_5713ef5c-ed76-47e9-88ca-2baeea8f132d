package com.sh.game.common.communication.msg.system.lixianReward;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回离线奖励信息
 * 该文件由工具根据 lixianReward.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResLiXianRewardInfoMessage extends AbsProtostuffMessage {
  /**
   * 离线时间
   */
  private int offlineTime;

  /**
   * 可领取配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 410003;
  }

  public void setOfflineTime(int offlineTime) {
    this.offlineTime = offlineTime;
  }

  public int getOfflineTime() {
    return this.offlineTime;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
