package com.sh.game.common.communication.msg.system.clientdata;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求删除客户端存储数据
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqClientDataRemoveMessage extends AbsProtostuffMessage {
  private List<Integer> key = new ArrayList<>();

  @Override
  public int getId() {
    return 391003;
  }

  public void setKey(List<Integer> key) {
    this.key = key;
  }

  public List<Integer> getKey() {
    return this.key;
  }
}
