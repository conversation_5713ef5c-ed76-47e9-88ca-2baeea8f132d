package com.sh.game.common.communication.msg.system.lottery;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求抽奖
 * 该文件由工具根据 lottery.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqLotteryMessage extends AbsProtostuffMessage {
  /**
   *  抽奖配置id
   */
  private int configId;

  /**
   *  抽奖次数
   */
  private int count;

  @Override
  public int getId() {
    return 409001;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
