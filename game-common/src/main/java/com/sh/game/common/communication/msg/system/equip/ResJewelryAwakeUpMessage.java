package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.communication.msg.system.equip.bean.AwakeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回饰配觉醒升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ResJewelryAwakeUpMessage extends AbsProtostuffMessage {
  private AwakeBean awakeBean = new AwakeBean();

  @Override
  public int getId() {
    return 13062;
  }

  public void setAwakeBean(AwakeBean awakeBean) {
    this.awakeBean = awakeBean;
  }

  public AwakeBean getAwakeBean() {
    return this.awakeBean;
  }
}
