package com.sh.game.common.communication.msg.system.advertise;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回礼包信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResAdvertisePackInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动类型
   */
  private int actType;

  /**
   * 礼包购买信息 key:配置id value：领取次数
   */
  private List<CommonKeyValueBean> beanList = new ArrayList<>();

  @Override
  public int getId() {
    return 407001;
  }

  public void setActType(int actType) {
    this.actType = actType;
  }

  public int getActType() {
    return this.actType;
  }

  public void setBeanList(List<CommonKeyValueBean> beanList) {
    this.beanList = beanList;
  }

  public List<CommonKeyValueBean> getBeanList() {
    return this.beanList;
  }
}
