package com.sh.game.common.communication.notice.scene;

import com.sh.game.common.entity.map.PlayerDTO;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 地图玩家离开模块
 */
@Notice
@Getter
@Setter
public class MapPlayerLeftToPlayerNotice extends ProcessNotice {

    private long roleId;

    private int mapCfgId;

    private long mapId;

    private int x;

    private int y;

    /**
     * 玩家假人id，离线需要存储
     */
    private long fakeId;

    private Map<Long, PlayerDTO> playerDTO;


}
