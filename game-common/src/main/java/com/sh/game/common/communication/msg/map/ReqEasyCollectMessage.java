package com.sh.game.common.communication.msg.map;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueLongIntBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求批量采集
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqEasyCollectMessage extends AbsProtostuffMessage {
  /**
   * 采集的怪物列表，k:采集怪的唯一id，v:采集的次数
   */
  private List<CommonKeyValueLongIntBean> hurtList = new ArrayList<>();

  @Override
  public int getId() {
    return 67090;
  }

  public void setHurtList(List<CommonKeyValueLongIntBean> hurtList) {
    this.hurtList = hurtList;
  }

  public List<CommonKeyValueLongIntBean> getHurtList() {
    return this.hurtList;
  }
}
