package com.sh.game.common.communication.msg.system.team;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 离队惩罚截止时间通知
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResArenaPunishMessage extends AbsProtostuffMessage {
  /**
   * 截止时间戳
   */
  private int arenaPunish;

  @Override
  public int getId() {
    return 101019;
  }

  public void setArenaPunish(int arenaPunish) {
    this.arenaPunish = arenaPunish;
  }

  public int getArenaPunish() {
    return this.arenaPunish;
  }
}
