package com.sh.game.common.communication.msg.system.guajisystem;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求邻取离线挂机奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqLiXianGuaJiGetRewardMessage extends AbsProtostuffMessage {
  /**
   * 是否需要双倍领取:  0: 正常领取  1: 需要双倍领取
   */
  private int isNeedDouble;

  @Override
  public int getId() {
    return 379003;
  }

  public void setIsNeedDouble(int isNeedDouble) {
    this.isNeedDouble = isNeedDouble;
  }

  public int getIsNeedDouble() {
    return this.isNeedDouble;
  }
}
