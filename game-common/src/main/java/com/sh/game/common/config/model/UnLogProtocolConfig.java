package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/7/29 17:26
 */
@Getter
@Setter
@ConfigData(file = "cfg_unlogprotocol")
public class UnLogProtocolConfig extends AbstractConfigData {

    /**
     * 协议id
     */
    private int id;
//
//    /**
//     * 协议名
//     */
//    private String messageName;
}
