package com.sh.game.common.communication.msg.system.arena;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回竞技场战斗信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResArenaBattleMessage extends AbsProtostuffMessage {
  private int score;

  private int scoreChange;

  private int rivalScore;

  private int rivalScoreChange;

  @Override
  public int getId() {
    return 394004;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setScoreChange(int scoreChange) {
    this.scoreChange = scoreChange;
  }

  public int getScoreChange() {
    return this.scoreChange;
  }

  public void setRivalScore(int rivalScore) {
    this.rivalScore = rivalScore;
  }

  public int getRivalScore() {
    return this.rivalScore;
  }

  public void setRivalScoreChange(int rivalScoreChange) {
    this.rivalScoreChange = rivalScoreChange;
  }

  public int getRivalScoreChange() {
    return this.rivalScoreChange;
  }
}
