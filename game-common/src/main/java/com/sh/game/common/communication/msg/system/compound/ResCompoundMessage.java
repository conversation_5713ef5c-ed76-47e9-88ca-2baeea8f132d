package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 道具合成返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCompoundMessage extends AbsProtostuffMessage {
  /**
   * cfg_compose配置id
   */
  private int configId;

  /**
   * 合成道具id列表
   */
  private List<Integer> itemIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 31002;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setItemIdList(List<Integer> itemIdList) {
    this.itemIdList = itemIdList;
  }

  public List<Integer> getItemIdList() {
    return this.itemIdList;
  }
}
