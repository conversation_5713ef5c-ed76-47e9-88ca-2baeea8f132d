package com.sh.game.common.communication.msg.system.imperialedictchallenge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回挑战信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResEdictInfoMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int configId;

  /**
   * 怪物配置id
   */
  private int monsterConfigId;

  /**
   * 状态(1:待接取、2:接取中、3:已完成、4:已领奖、5:任务失败)
   */
  private int state;

  /**
   * 任务时间(除了接取中，其他时候没用都为0)
   */
  private int time;

  /**
   * 完成类型(1:击杀 2:参与)
   */
  private int type;

  /**
   * 剩余挑战次数
   */
  private int killCount;

  /**
   * 参与剩余次数
   */
  private int aidCount;

  /**
   * 剩余刷新次数
   */
  private int refreshCount;

  @Override
  public int getId() {
    return 335004;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setMonsterConfigId(int monsterConfigId) {
    this.monsterConfigId = monsterConfigId;
  }

  public int getMonsterConfigId() {
    return this.monsterConfigId;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setTime(int time) {
    this.time = time;
  }

  public int getTime() {
    return this.time;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setKillCount(int killCount) {
    this.killCount = killCount;
  }

  public int getKillCount() {
    return this.killCount;
  }

  public void setAidCount(int aidCount) {
    this.aidCount = aidCount;
  }

  public int getAidCount() {
    return this.aidCount;
  }

  public void setRefreshCount(int refreshCount) {
    this.refreshCount = refreshCount;
  }

  public int getRefreshCount() {
    return this.refreshCount;
  }
}
