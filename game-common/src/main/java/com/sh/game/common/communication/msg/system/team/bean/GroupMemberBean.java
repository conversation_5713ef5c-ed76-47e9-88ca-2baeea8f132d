package com.sh.game.common.communication.msg.system.team.bean;

import java.lang.String;

public class GroupMemberBean {
  /**
   * 玩家编号
   */
  private long rid;

  /**
   * 玩家名称
   */
  private String playerName = new String();

  /**
   * 玩家等级
   */
  private int level;

  /**
   * 职业
   */
  private int career;

  /**
   * 性别
   */
  private int sex;

  /**
   * 家族编号
   */
  private long unionId;

  /**
   * 家族名称
   */
  private String unionName = new String();

  /**
   * 所在地图配置编号
   */
  private int mapId;

  /**
   * 是否离线，0:在线，1:离线
   */
  private int offline;

  /**
   * 当前hp
   */
  private long hp;

  /**
   * 最大hp
   */
  private long maxHp;

  /**
   * 当前mp
   */
  private long mp;

  /**
   * 最大mp
   */
  private long maxMp;

  /**
   * 当前x
   */
  private int x;

  /**
   * 当前y
   */
  private int y;

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setPlayerName(String playerName) {
    this.playerName = playerName;
  }

  public String getPlayerName() {
    return this.playerName;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setMapId(int mapId) {
    this.mapId = mapId;
  }

  public int getMapId() {
    return this.mapId;
  }

  public void setOffline(int offline) {
    this.offline = offline;
  }

  public int getOffline() {
    return this.offline;
  }

  public void setHp(long hp) {
    this.hp = hp;
  }

  public long getHp() {
    return this.hp;
  }

  public void setMaxHp(long maxHp) {
    this.maxHp = maxHp;
  }

  public long getMaxHp() {
    return this.maxHp;
  }

  public void setMp(long mp) {
    this.mp = mp;
  }

  public long getMp() {
    return this.mp;
  }

  public void setMaxMp(long maxMp) {
    this.maxMp = maxMp;
  }

  public long getMaxMp() {
    return this.maxMp;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }
}
