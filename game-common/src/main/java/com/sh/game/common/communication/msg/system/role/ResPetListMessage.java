package com.sh.game.common.communication.msg.system.role;

import com.sh.game.common.communication.msg.system.role.bean.PetBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送宠物列表
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPetListMessage extends AbsProtostuffMessage {
  /**
   * 宠物列表
   */
  private List<PetBean> petBean = new ArrayList<>();

  @Override
  public int getId() {
    return 8012;
  }

  public void setPetBean(List<PetBean> petBean) {
    this.petBean = petBean;
  }

  public List<PetBean> getPetBean() {
    return this.petBean;
  }
}
