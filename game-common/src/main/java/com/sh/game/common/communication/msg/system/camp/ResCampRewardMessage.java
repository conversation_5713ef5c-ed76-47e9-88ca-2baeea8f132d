package com.sh.game.common.communication.msg.system.camp;

import com.sh.game.common.communication.msg.system.camp.bean.CampRewardBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 训练营奖励信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCampRewardMessage extends AbsProtostuffMessage {
  /**
   * 奖励信息
   */
  private List<CampRewardBean> beanList = new ArrayList<>();

  /**
   * 成长等级
   */
  private int level;

  /**
   * 精英礼包是否开启 T:已开启 F:未开启
   */
  private boolean elite;

  @Override
  public int getId() {
    return 197004;
  }

  public void setBeanList(List<CampRewardBean> beanList) {
    this.beanList = beanList;
  }

  public List<CampRewardBean> getBeanList() {
    return this.beanList;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setElite(boolean elite) {
    this.elite = elite;
  }

  public boolean getElite() {
    return this.elite;
  }
}
