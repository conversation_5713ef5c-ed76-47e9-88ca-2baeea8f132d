package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 怪物首杀活动
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-12-01
 **/
@Getter
@Setter
@ConfigData(file = "cfg_equip_shouling")
public class ActivityFirstKillMonsterConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * 怪物表id
     */
    private int bossid;

    /**
     * 领取数量上限
     */
    private int limit;

    /**
     * 奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 区域类型
     */
    private int areaType;

}
