package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.notice.TimeProcessNotice;
import com.sh.server.Session;
import lombok.Getter;
import lombok.Setter;

@Notice
@Getter
@Setter
public class RoleRechargeNotice extends TimeProcessNotice {

    /**
     * 请求顺序
     */
    private short sequence;
    /**
     * 请求session
     */
    private Session session;
    /**
     * 平台
     */
    private int platform;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 玩家登录账号
     */
    private String uid;
    /**
     * pid（log）
     */
    private int pid;
    /**
     * sid
     */
    private int sid;
    /**
     * 角色id
     */
    private long rid;
    /**
     * 人民币（元）
     */
    private int rmb;
    /**
     * 平台订单号
     */
    private String orderSn;
    /**
     * 自有订单号
     */
    private long orderId;
    /**
     * 商品id
     */
    private int goodsId;
    /**
     * 充值方式，0默认，1微信公众号
     */
    private int way;

    @Override
    public boolean first() {
        return true;
    }
}
