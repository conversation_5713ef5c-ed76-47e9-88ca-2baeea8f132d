package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求大侠币充值
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqDaXiaCoinRechargeMessage extends AbsProtostuffMessage {
  /**
   * 充值表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 39035;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
