package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 领取祝福礼包
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZhuFuRewardMessage extends AbsProtostuffMessage {
  /**
   * 祝福礼包id
   */
  private int rewardId;

  @Override
  public int getId() {
    return 4263;
  }

  public void setRewardId(int rewardId) {
    this.rewardId = rewardId;
  }

  public int getRewardId() {
    return this.rewardId;
  }
}
