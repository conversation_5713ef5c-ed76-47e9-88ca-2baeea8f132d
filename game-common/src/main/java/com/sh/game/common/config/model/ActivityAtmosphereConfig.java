package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/01/03 11:43
 */
@Getter
@Setter
@ConfigData(file = "cfg_cj_zhuanpan_count")
public class ActivityAtmosphereConfig extends AbstractConfigData {

    private int id;

    /**
     * 活动id
     */
    private int activityId;

    /**
     * 目标次数
     */
    private int count;

    /**
     * 活动内充值额度
     */
    private int recharge;

    /**
     * 奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 公告id
     */
    private int announce;
}
