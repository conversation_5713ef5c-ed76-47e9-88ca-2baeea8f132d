package com.sh.game.common.communication.msg.system.function;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求神兵打造奖励领取
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqFunctionMessage extends AbsProtostuffMessage {
  /**
   * 类型
   */
  private int type;

  /**
   * 打造次数
   */
  private int count;

  @Override
  public int getId() {
    return 202003;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
