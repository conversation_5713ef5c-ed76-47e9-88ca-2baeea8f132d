package com.sh.game.common.constant;

/**
 * <AUTHOR> <<EMAIL>>
 * @version 创建时间：2017年6月13日 下午9:07:06
 * global配置表的id字段
 */
public interface GameConst {

    interface GlobalId {
        /**
         * 即时战斗攻击药水 150%
         */
        int FIGHT_ATTACK_DRAG_BUY1 = 171;
        /**
         * 即时战斗攻击药水 200%
         */
        int FIGHT_ATTACK_DRAG_BUY2 = 172;
        /**
         * 跨服异常退出目的点
         */
        int CROSS_ERROR_BIRTH_POINT = 9002;
        /**
         * 跨服遗迹小怪
         */
        int CROSS_RUINS_NORMAL_MONSTER = 15604;
        /**
         * 神魔遗迹探险值最大上限值
         */
        int DEVIL_RUINS_MAX_EXPLORE = 28301;
        /**
         * 神魔遗迹 购买探险值消耗
         */
        int BUY_DEVIL_RUINS_EXPLORE_CONSUME = 28305;
        /**
         * 神魔遗迹 购买上限
         */
        int BUY_DEVIL_RUINS_EXPLORE_COUNT_LIMIT = 28306;

        /**
         * 世界花苑探险值最大上限值
         */
        int WORLD_GARDEN_MAX_EXPLORE = 28301;
        /**
         * 世界花园 购买探险值消耗
         */
        int BUY_WORLD_GARDEN_EXPLORE_CONSUME = 29304;
        /**
         * 世界花园 购买上限
         */
        int BUY_WORLD_GARDEN_EXPLORE_COUNT_LIMIT = 29305;
        /**
         * 邀请码不开放平台
         */
        int CREATE_ROLE_CAT_NOT_OPEN_PLATFORM = 30201;

        /**
         * 攻方地图ID1#X#Y#复活CD（城门破前）|攻方地图ID2#X#Y#复活CD（城门破后）|守方地图ID1#X#Y#复活CD（城门破前）|守方地图ID2#X#Y#复活CD（城门破后）
         */
        int SBK_REBORN = 1000;

        /**
         * 跨服沙巴克雕像
         */
        int SHABAKE_STATUE = 580021;

        /**
         * 跨服沙巴攻方地图
         */
        int CROSS_SBK_REBORN = 580022;

        /**
         * 沙巴克泡点
         */
        int SBK_PAODIAN = 1005;

        /**
         * 跨服沙巴克泡点
         */
        int CROSS_SBK_PAODIAN = 580023;

        /**
         * 沙巴克击杀积分
         */
        int SBK_KILL_SCORE = 1006;

        /**
         * 跨服沙巴克击杀积分
         */
        int CROSS_SBK_KILL_SCORE = 580024;

        /**
         * 召唤符持续时间内，召唤物死亡复活时间，单位（秒）
         */
        int ITEM_CALL_RELIVE_TIME = 1004;
        /**
         * 怪物地图数量限制-禁地封魔
         */
        int TREASURE_BOSS_LIMIT = 1008;

        /**
         * 社交列表上限
         */
        int SOCIAL_MAX = 100291;

        /**
         * 回城石回城点
         */
        int BACK_CITY_POINT = 100002;
        /**
         * 死亡万分比掉落全身耐久（万分比）
         */
        int RELIVE_COST_DURABLE = 100005;
        /**
         * 维修系数
         */
        int ITEM_REPAIR_FIX = 100009;
        /**
         * 精力倍数
         */
        int ENERGY_RATE = 100011;
        /**
         * 精力累计上限
         */
        int ENERGY_RECOVERY_MAX = 100012;
        /**
         * 精力值每日恢复量
         */
        int ENERGY_RECOVERY = 100013;
        /**
         * 累计经验上限（达到扣1点精力）
         */
        int ENERGY_EXP_MAX = 100014;

        /**
         * 新号精力初始值
         */
        int ENERGY_FIRST = 100015;

        /**
         * 组队人数限制
         */
        int TEAM_LIMIT_SIZE = 100023;

        /**
         * 行会申请保存时间
         */
        int UNION_APPLY_TIME = 100030;

        /**
         * 创建行会消耗
         */
        int UNION_CREATE_COST = 100031;

        /**
         * 行会创建成功公告
         */
        int UNION_CREATE_ANNOUNCE = 100032;

        /**
         * 退出行会后时间冷却
         */
        int UNION_OUT = 100033;

        /**
         * 行会大事件信息条目存储数量
         */
        int UNION_ENENT_NUM = 100038;

        /**
         * 行会会长转让邮件
         */
        int UNION_CHARMAN_CHANGE_MAIL = 100044;

        /**
         * 预设入会等级
         */
        int UNION_ENTER_LEVEL = 100047;

        /**
         * 移动参数
         */
        int MOVE_PARAM = 100056;

        int UNION_DEFAULT_ANNOUNCE = 100260;

        /**
         * 公共CD毫秒 战#法#道
         */
        int COMMON_CD = 100057;
        /**
         * 死亡保护等级上限
         */
        int DIE_NOT_DROP_CONDITION = 100059;
        /**
         * PK值每多少秒清掉一点
         */
        int PK_DROP_TIME = 100060;
        /**
         * 炼狱地图离开
         */
        int HELL_EXIT = 100069;
        /**
         * 每次受击消耗耐久值
         */
        int BE_ATTACK_COST_DURABLE = 100113;
        /**
         * 抢公主活动杀玩家积分#公主1积分#公主2积分#公主3积分#公主4积分#上交灭魔令积分
         */
        int PRINCESS = 100074;

        /**
         * todo 占位
         * 抢公主
         */
        int PRINESE = 0;

        /**
         * 行会可捐献时间段
         */
        int UNION_DONATE_TIME_RANGE = 100116;
        /**
         * 行会红包信息
         */
        int UNION_RED_PACK_INFO = 100122;
        /**
         * 神兽切换模型
         */
        int BEAST_CHANGE_MODE = 100126;

        /**
         * 沙巴克红包最小值
         */
        int SHABAKE_REDPACK_MIN = 100127;
        /**
         * 神龙之魂特殊属性（PK时对神龙之魂等级低于自己的玩家造成额外减伤）
         */
        int DRAGON_WING_ATTR = 100210;

        int MIND_GHOST_ASSIGNS = 100203;
        /**
         * 行会旗帜
         */
        int UNION_SNATCH = 100066;

        /**
         * 拍卖分红
         */
        int AUCTION_SHARE_REPLACE = 100256;
        int AUCTION_SHARE_RATE = 100252;
        int AUCTION_SHARE_MIN = 28606;

        /**
         * 结盟
         */
        int UNION_ENEMY_LAST = 100039;
        int UNION_ALIGN_LAST = 100040;

        /**
         * fly boot
         */
        int FLY_BOOT_NO_COST = 100259;

        int FUSION_BUY_DISCOUNT = 100053;
        /**
         * 宣战
         */
        int UNION_ENEMY = 100261;

        /**
         *
         */
        int SUNSET_RACE_BENEFIT = 1061;

        /**
         * 经验宝箱兑换  宝箱ID#所需数量#每日兑换限制次数
         */
        int EXP_BOX_DAILY_TIME = 100271;

        /**
         * 英雄最大忠诚度
         */
        int MAX_LOYALTY = 100272;

        /**
         * 死亡扣除忠诚度
         */
        int DEAD_COST_LOYALTY = 100273;

        /**
         * 击杀怪物增加忠诚度
         */
        int KILL_MONSTER_ADD_LOYALTY = 100274;

        /**
         * 忠诚度伤害比率
         */
        int LOYALTY_HURT_RATE = 100275;

        /**
         * 仓库不能放的类型
         */
        int WAREHOUSE_TYPE = 100276;

        /**
         * 拍卖行上架上限
         */
        int AUCTION_PUT_ON_LIMIT = 100279;

        /**
         * 拍卖行手续费
         */
        int AUCTION_HAND_FEE = 100254;

        /**
         * 符文兑换
         */
        int FUWEN_DUIHUAN = 100281;


        /**
         * 每日藏宝图最大次数
         */
        int MAX_TREASURE_MAP_COUNT = 100282;

        /**
         * 火龙之心初始id
         */
        int HUO_LONG_FIRST_ID = 100284;

        /**
         * 矿石产出权值
         */
        int OREHOLE_RANDOM = 100285;

        /**
         * 挖矿一次时长 秒
         */
        int OREHOLE_ONE_TIME = 100286;

        /**
         * 挖矿消耗
         */
        int OREHOLE_DIG_COST = 100287;


        /**
         * 挖矿持续时间
         */
        int OREHOLE_CD = 100288;

        /**
         * 挖矿地图
         */
        int OREHOLE_MAP = 100289;

        /**
         * 祝福默认暴击增加值
         */
        int ZHUFU_BAOJI = 100290;

        /**
         * 自动删除队伍时间，秒
         */
        int AUTO_REMOVE_TEAM_MINUTE = 100292;

        /**
         * 队伍邀请时间间隔，秒
         */
        int TEAM_INVITE_SPANCE_SECOND = 100293;

        /**
         * 火龙之心能量购买参数
         */
        int BUY_HUO_LONG_VALUE_PARAM = 100294;

        /**
         * 合体技能能量槽最大值
         */
        int COMB_SKILL_VALUE_MAX = 100295;

        /**
         * 治疗使用间隔，秒
         */
        int CURE_TIME_INTERVAL = 100296;

        /**
         * 清除罪恶值消耗
         */
        int CLEAN_PKVALUE_COST = 100297;

        /**
         * 职业排名称号
         */
        int CAREER_RANK_FASHION = 100298;
        /**
         * 合体技单次消耗的能量
         */
        int COMB_SKILL_COST_VALUE = 100299;

        /**
         * 被申请好友所需条件
         */
        int FRIEND_APPLY_CONDITION = 100300;

        /**
         * 行会人数少于直接解散
         */
        int UNION_DISSOLVE_NUM = 100301;

        /**
         * 行会多于指定人数解散倒计时
         */
        int UNION_DISSOLVE_TIME = 100302;

        /**
         * 解散倒计时邮件
         */
        int UNION_DISSOLVE_START_MAIL = 100303;

        /**
         * 资金少于天数自动解散
         */
        int UNION_AUTO_DISSLOVE_DAY = 100305;

        /**
         * 自动解散邮件
         */
        int UNION_AUTO_DISSLOVE_MAIL = 100306;

        /**
         * 行会踢人邮件
         */
        int UNION_TICK_MAIL = 100307;

        /**
         * 行会升级公告
         */
        int UNION_LEVEL_ANNOUNCE = 100308;

        /**
         * 行会正常解散邮件
         */
        int UNION_DISSOLVE_MAIL = 100309;

        /**
         * 降职邮件
         */
        int UNION_BE_MEMBER = 100310;

        /**
         * 矿洞自动出售特权
         */
        int OREHOLE_PREVILEGE = 100313;

        /**
         * 火龙之心免费充能次数
         */
        int HUO_LONG_FREE_COUNT = 100320;

        /**
         * 沙巴克修复数量
         */
        int SHABAKE_FIX_COST = 100403;

        /**
         * 沙巴克召唤数量
         */
        int SHABAKE_CALL_NUM = 100404;

        /**
         * 沙巴克城墙buff
         */
        int SHBAK_WALL_BUFF = 100405;

        /**
         * 沙巴克怪物
         */
        int SHABAKE_MONSTER = 100406;

        /**
         * 弓箭手id#带刀护卫id
         */
        int SHABAKE_PROTECT_ID = 100408;

        /**
         * 弓箭手坐标x#弓箭手坐标y#面向
         */
        int SHABAKE_GJ_POINT = 100409;

        /**
         * 带刀护卫坐标x#带刀护卫坐标y#面向
         */
        int SHABAKE_PROTECT_POINT = 100410;

        /**
         * 可以占领时间
         */
        int SHABAKE_CHANGE_TIME = 100411;

        /**
         * npc坐标
         */
        int SHABAKE_NPC_POINT = 100412;

        /**
         * 沙巴克称号
         */
        int SHABAKE_WIN_TITLE = 100416;

        /**
         * 沙巴克会长时装
         */
        int SHABAKE_WIN_FASHION = 100417;

        /**
         * 沙巴克分配时间
         */
        int SHABAKE_ALLOC_TIME = 100418;

        /**
         * 沙巴克分配展示时间
         */
        int SHABAKE_ALLOC_SHOW_TIME = 100419;


        /**
         * 跨服沙巴克修复数量
         */
        int CROSS_SHABAKE_FIX_COST = 580004;

        /**
         * 跨服沙巴克召唤数量
         */
        int CROSS_SHABAKE_CALL_NUM = 580005;

        /**
         * 跨服沙巴克城墙buff
         */
        int CROSS_SHBAK_WALL_BUFF = 580006;

        /**
         * 跨服沙巴克怪物
         */
        int CROSS_SHABAKE_MONSTER = 580007;

        /**
         * 跨服弓箭手id#带刀护卫id
         */
        int CROSS_SHABAKE_PROTECT_ID = 580009;

        /**
         * 跨服弓箭手坐标x#弓箭手坐标y#面向
         */
        int CROSS_SHABAKE_GJ_POINT = 580010;

        /**
         * 跨服带刀护卫坐标x#带刀护卫坐标y#面向
         */
        int CROSS_SHABAKE_PROTECT_POINT = 580011;

        /**
         * 跨服可以占领时间
         */
        int CROSS_SHABAKE_CHANGE_TIME = 580012;

        /**
         * 跨服npc坐标
         */
        int CROSS_SHABAKE_NPC_POINT = 580013;

        /**
         * 跨服沙巴克称号
         */
        int CROSS_SHABAKE_WIN_TITLE = 580017;

        /**
         * 跨服沙巴克会长时装
         */
        int CROSS_SHABAKE_WIN_FASHION = 580018;

        /**
         * 跨服沙巴克分配时间
         */
        int CROSS_SHABAKE_ALLOC_TIME = 580019;

        /**
         * 跨服沙巴克称号邮件
         */
        int CROSS_SHABAKE_WIN_MAIL = 580031;

        /**
         * 寻宝主题
         */
        int TREASURE_HUNT_THEMES = 100501;

        /**
         * 寻宝公告道具组
         */
        int TREASURE_HUNT_ANNO = 100502;

        /**
         * 寻宝记录的最大保存条数
         */
        int TREASURE_HUNT_MAX_RECORD = 100503;

        /**
         * 主线挖矿
         */
        int OREHOLE_TASK = 100504;

        /**
         * 世界BOSS排名奖励
         */
        int WORLD_RANK_AWARD = 100506;
        /**
         * 世界BOSS参与奖励
         */
        int WORLD_PARTICIPATE_AWARD = 100507;
        /**
         * 世界BOSS竞拍奖励
         */
        int WORLD_AUCTION_AWARD = 100508;
        /**
         * 创建行会条件
         */
        int CREATE_UNION_CONDITION = 100512;

        /**
         * 回城复活血量百分数
         */
        int HOME_RELIVE_HP_PERCENT = 100513;

        /**
         * 创角默认称号
         */
        int CREATE_DEFAULT_FASHION = 100514;

        /**
         * 神龙遗迹每日免费次数
         */
        int ENTER_MAP_COST_BY_CLS = 100515;

        /**
         * 特戒组
         */
        int SPECIAL_RING_GROUP = 100517;

        /**
         * 魔器组
         */
        int DEMON_EQUIP_GROUP = 100578;

        /**
         * 特戒合成组
         */
        int SPECIAL_RING_COMPOUND_GROUP = 100529;

        /**
         * 每日给道具
         */
        int DAILY_ITEM = 100519;

        /**
         * 开服天数#邀请码
         */
        int INVITATION_CODE = 100520;

        /**
         * 地下宫殿活动id
         */
        int DIGONG_ACTIVITY_ID = 100522;

        /**
         * 地下宫殿地图id
         */
        int DIGONG_MAP_ID = 100523;

        /**
         * 首次藏宝图，常规
         */
        int FIRST_TREASURE_MAP_NORMAL = 100525;

        /**
         * 首次藏宝图，高级
         */
        int FIRST_TREASURE_MAP_SENIOR = 100526;

        /**
         * 幻影魔王buff
         */
        int SHADOW_DEVIL_BUFF_ID = 100532;

        /**
         * 幻影魔王刷怪等待时间
         */
        int SHADOW_DEVIL_WAIT_TIME = 100533;

        /**
         * 幻影魔王幸运奖
         * 对应box表
         */
        int SHADOW_DEVIL_LUCK_AWARD = 100534;

        /**
         * 幻影魔王参与奖
         * 对应box表
         */
        int SHADOW_DEVIL_PARTICIPATE_AWARD = 100535;

        /**
         * 幻影魔王最大波数
         */
        int SHADOW_DEVIL_MAX_WAVE = 100537;

        /**
         * 幻影魔王出了4级技能书后，掉落该box的id  掉落
         */
        int SHADOW_DEVIL_LUCK_AWARD_B = 100538;

        /**
         * 幻影魔王出了4级技能书的id
         */
        int SHADOW_DEVIL_FOUR_LEVEL_SKILL = 100539;

        /**
         * 最大掉落日志数量
         */
        int MAX_DROP_RECORD_COUNT = 100542;

        /**
         * 神塔进入奖励
         */
        int SHEN_TA_ENTER_REWARD = 100540;

        /**
         * 神塔进去消耗 这个消耗只需要一次所以没走地图
         */
        int SHEN_TA_ENTER_COST = 100543;

        /**
         * 神塔第一个地图id
         */
        int SHEN_TA_FIRST_MAPID = 100544;

        /**
         * 生肖装备合成特殊部位
         */
        int ZODIAC_SPECIAL_TYPE = 100545;

        /**
         * 生肖装备合成box组
         */
        int ZODIAC_COMPOUND_BOX = 100546;

        /**
         * 狂暴之力不移除的地图id #号分割
         */
        int VIOLENT_POWER_NOT_VANISH = 100548;

        /**
         * 丢弃道具到地图开关
         */
        int DISCARD_ITEM_COMMAND = 100549;

        /**
         * 最大每日邮件数量
         */
        int MAX_DAILY_MAIL_COUNT = 100550;

        /**
         * 掉落置顶
         */
        int DROP_TOP_RANK = 100552;
        /**
         * 重新分配阵营时间#重新分配阵营时间- 皇宫乱斗
         */
        int BRAWLING_RESTART_RANK = 100554;
        /**
         * 出生点1|出生点2- 皇宫乱斗
         */
        int BRAWLING_BORN = 100555;
        /**
         * 重新分配阵营时间- 皇宫乱斗
         */
        int BRAWLING_RESTART = 100556;
        /**
         * 泡点积分#泡点间隔- 皇宫乱斗
         */
        int BRAWLING_TIME_SCORE = 100557;
        /**
         * 击杀玩家获得积分#击杀怪获得积分#助攻获得积分#被击杀获得积分- 皇宫乱斗
         */
        int BRAWLING_SCORE = 100558;
        /**
         * buff刷新坐标- 皇宫乱斗
         */
        int BRAWLING_BUFF_POINT = 100559;
        /**
         * 复活的buff- 皇宫乱斗
         */
        int BRAWLING_RELIVE_BUFF = 100560;
        /**
         * 连续被击杀次数限制- 皇宫乱斗
         */
        int BRAWLING_RELIVE_BUFF_DEAD_COUNT = 100561;
        /**
         * buff刷新规则- 皇宫乱斗
         */
        int BRAWLING_BUFF_RULE = 100563;
        /**
         * 阵营人数差规则- 皇宫乱斗
         */
        int BRAWLING_GROUP_RULE = 100564;
        /**
         * 击杀bossbuff - 皇宫乱斗
         */
        int BRAWLING_BOSS_BUFF = 100565;
        /**
         * 场景buff刷新时间 - 皇宫乱斗
         */
        int BRAWLING_BUFF_REFRESH_TIME = 100566;
        /**
         * 离开场景所需删除的buff - 皇宫乱斗
         */
        int BRAWLING_REMOVE_BUFF_LIST = 100568;

        /**
         * 交易系统开启验证
         */
        int TRADE_CONDITION = 100125;

        /**
         * 竞技场离队惩罚
         */
        int ARENA_LEAVE_TEAM = 100580;

        /**
         * 竞技场击杀buff转移百分数
         */
        int ARENA_BUFF_COUNT = 100581;

        /**
         * 竞技场积分百分比
         */
        int ARENA_SCORE_PENCENT = 100582;

        /**
         * 竞技场奖励配置
         */
        int ARENA_REWARD_INFO = 100584;

        /**
         * 祭坛：Boss协助奖励的box的id
         */
        int JI_TAN_BOSS_HELP_REWARD = 100592;

        /**
         * 祭坛：可获得圣火的boss的id
         */
        int JI_TAN_BOSS = 100593;

        /**
         * 圣祭坛的bossid
         */
        int JI_TAN_SHENG = 100594;

        /**
         * 祭坛首领奖励次数#首领协助次数#宝箱采集次数
         */
        int JI_TAN_COUNT = 100595;

        /**
         * 祭坛Boss奖励次数可购买上限#消耗货币#单次价格
         */
        int JI_TAN_BUY = 100596;

        /**
         * 祭坛：圣祭坛激活所需圣火
         */
        int JI_TAN_NEED_VALUE = 100598;

        /**
         * 祭坛：地图id，按顺序东南西北圣
         */
        int JI_TAN_MAP_ID = 100599;

        /**
         * 祭坛：圣地图关闭时间
         */
        int JI_TAN_CLOSE_MAP_CD = 100600;

        /**
         * 天下第一buff
         */
        int WORLD_FIRST_BUFF_ITEM = 100610;

        /**
         * 天下第一军饷奖励CD
         */
        int WORLD_FIRST_REWARD_CD = 100611;

        /**
         * 天下第一buff刷新CD
         */
        int WORLD_FIRST_BUFF_CD = 100612;

        /**
         * 天下第一最后一层锁死倒计时
         */
        int WORLD_FIRST_LOCK_TIME = 100613;

        /**
         * 天下第一冠军奖励
         */
        int WORLD_FIRST_KEMP_REWARD = 100614;

        /**
         * 天下第一冠军时装
         */
        int WORLD_FIRST_KEMP_FASHION = 100630;

        /**
         * 实名认证状态道具
         */
        int AUTHENTICATION_ITEM = 100706;

        /**
         * 关注微信公众号道具
         */
        int WECHAT_ITEM = 100705;

        /**
         * 隐藏房间每日可进入次数
         */
        int HIDDEN_ROOM_COUNT = 100321;

        /**
         * 道具掉落公告
         */
        int ITEM_DROP_ANNOUNCE = 100569;

        /**
         * 英雄转职需要删除的装备部位
         */
        int HERO_CAREER_CHANGE_UPDATE_EQUIP_POS = 100640;

        /**
         * 英雄转职需要卸下的装备部位
         */
        int HERO_CAREER_CHANGE_CHECK_EQUIP_POS = 100641;

        /**
         * 刷新任务地图编号
         */
        int REFRESH_TASK_MAPS = 100801;

        /**
         * 竞技场积分buff
         */
        int ARENA_SCORE_BUFF = 100586;

        /**
         * 火龙之心快速充能参数
         */
        int FAST_BUY_HUO_LONG_VALUE_PARAM = 100821;
        /**
         * 火龙之心快速充能次数
         */
        int FAST_BUY_HUO_LONG_VALUE_COUNT = 100822;

        /**
         * 神秘商店自动刷新间隔
         */
        int MYSTERY_SHOP_REFRESH_CD = 100901;
        /**
         * 神秘商店免费刷新次数
         */
        int MYSTERY_SHOP_REFRESH_COUNT = 100902;
        /**
         * 神秘商店手动刷新消耗
         */
        int MYSTERY_SHOP_REFRESH_COST = 100903;

        /**
         * 周期连充循环周期
         */
        int CYCLE_DAYS_RECHARGE_STAGE = 100831;
        /**
         * 周期累充循环周期
         */
        int CYCLE_TOTAL_RECHARGE_STAGE = 100832;
        /**
         * 周期连充最大周期数
         */
        int DAYS_RECHARGE_MAX_STAGE = 100833;
        /**
         * 周期累充最大周期数
         */
        int TOTAL_RECHARGE_MAX_STAGE = 100834;
        /**
         * 周期连充一周期天数
         */
        int DAYS_RECHARGE_STAGE_TIME = 100835;
        /**
         * 周期累充一周期天数
         */
        int TOTAL_RECHARGE_STAGE_TIME = 100836;
        /**
         * 怪物额外掉落参数
         */
        int MONSTER_DROPUP_PARAM = 101001;

        /**
         * 神兵升级是否补充神兵经验
         */
        int SHEN_BING_EXP_FILL = 101004;

        /**
         * 补签条件
         */
        int SIGNED_CONDITION = 101007;

        /**
         * 免费补签次数
         */
        int FREE_SIGNED_COUNT = 101009;

        /**
         * 付费补签消耗
         */
        int SIGNED_COST = 101010;

        /**
         * 怪物掉落公告(系统频道)
         */
        int MONSTER_DROP_ANNOUNCE_SYSTEM = 101014;

        /**
         * 怪物掉落公告(普通公告)
         */
        int MONSTER_DROP_ANNOUNCE_NORMAL = 101016;

        /**
         * 刷新日常挑战每次消耗刷新符
         */
        int DAILY_CHALLENGE_REFRESH_COST = 103001;
        /**
         * 日常挑战快速完成每次消耗扫荡符
         */
        int DAILY_CHALLENGE_SWEEP_COST = 103002;
        /**
         * 日常挑战每日可完成最大次数
         */
        int DAILY_CHALLENGE_COUNT_LIMIT = 103003;
        /**
         * 攻速比例配置
         * 影响技能公共CD
         */
        int SKILL_CD_REDUCE_RATIO = 103004;

        /**
         * 拾取范围提升
         */
        int PICKUP_RANGE_UP = 104001;

        /**
         * 附魂消耗
         */
        int FUHUN_COST = 105001;
        /**
         * 拆分消耗
         */
        int CHAIFEN_COST = 105002;

        /**
         * 天池宫boss
         */
        int TIANCHI_BOSS = 106001;

        /**
         * 神兵日常奖励
         */
        int SHENBING_DAILY_REWARD = 104002;

        /**
         * 血量护盾时间
         */
        int HP_SHIELD_TIME = 109200;

        /**
         * 连杀判定时间
         */
        int COMBO_TIME = 109101;

        /**
         * 合成记录显示条数
         */
        int SHOW_RECORD_COUNT = 109301;

        /**
         * 掉落公告稀有度(系统频道)
         */
        int ANNOUNCE_DROP_RARE_SYSTEM = 109601;

        /**
         * 掉落公告稀有度(普通公告)
         */
        int ANNOUNCE_DROP_RARE_NORMAL = 109602;

        /**
         * 全服唯一专属称号
         */
        int UNIQUE_TITLE = 109801;
        /**
         * 最低攻击伤害
         */
        int MIN_ATTACK = 109901;
        /**
         * 重置天赋消耗
         */
        int TALENT_CLEAR = 113001;

        /**
         * 免费骰子次数
         */
        int CLIMB_TOWER_FREE_TIMES = 115001;
        /**
         * 随机骰子次数消耗
         */
        int CLIMB_TOWER_COST1 = 115002;
        /**
         * 至尊骰子次数消耗
         */
        int CLIMB_TOWER_COST2 = 115003;
        /**
         * 通天塔转盘消耗
         */
        int CLIMB_TOWER_TURN_COST = 115004;
        /**
         * 整点公告
         */
        int HOUR_ANNOUNCE = 101002;


        /**
         * 神秘商店刷新时间
         */
        int MYSTERY_STORE_FLUSH_TIME = 117001;
        /**
         * 神秘商店刷新消耗
         */
        int MYSTERY_STORE_FLUSH_COST = 117002;

        /**
         * 共铸神剑进度上限
         */
        int ZHU_JIAN_PROGRESS_LIMIT = 130001;

        /**
         * 共铸神剑完成上限
         */
        int ZHU_JIAN_LIMIT = 130003;

        /**
         * 每次凝炼增加的万分比
         */
        int NING_LIAN_ADD_RATE = 118001;

        /**
         * 每次凝炼消耗的材料
         */
        int NING_LIAN_COST = 118002;

        /**
         * 每次传承的消耗
         */
        int CHUAN_CHENG_COST = 118003;

        /**
         * 假行会创建数量
         */
        int FAKE_UNION_COUNT_LIMIT = 1300101;

        /**
         * 专属面板通知稀有度限制
         */
        int ZHUANSHU_PANEL_RARE_LIMIT = 1300200;

        /**
         * 治疗消耗物
         */
        int CURE_COST = 102002;

        /**
         * 合成弹窗类型
         */
        int COMPOUNT_SHOW = 1300300;

        /**
         * 泡点禁区
         */
        int PENALTY_AREA = 140100;

        /**
         * 可获取泡点的最大等级
         */
        int PENALTY_MAX_LEVEL = 140101;

        /**
         * 神兵经验获得间隔
         */
        int PENALTY_INTERVAL = 140102;

        /**
         * 泡点经验值
         */
        int PENALTY_REWARD = 140103;

        /**
         * 277属性影响的装备位
         */
        int EQUIP_POS = 150001;

        /**
         * 277属性影响的属性id
         */
        int ATTR_ID = 150002;

        /**
         * 幸运掉落杀怪数量（小）
         */
        int LUCK_DROP_SMALL_KILL = 170001;

        /**
         * 幸运掉落奖励（小）
         */
        int LUCK_DROP_SMALL_REWARD = 170002;

        /**
         * 幸运掉落杀怪数量（大）
         */
        int LUCK_DROP_BIG_KILL = 170003;

        /**
         * 幸运掉落奖励（大）
         */
        int LUCK_DROP_BIG_REWARD = 170004;

        /**
         * 幸运掉落怪物等级限制（小）
         */
        int LUCK_DROP_SMALL_LEVEL = 170005;

        /**
         * 幸运掉落怪物等级限制（大）
         */
        int LUCK_DROP_BIG_LEVEL = 170006;

        /**
         * 一键购买所有特权消耗
         */
        int BUY_ALL_PRIVILEGE_COST = 109150;

        /**
         * 一键续费所有特权消耗
         */
        int RENEW_ALL_PRIVILEGE_COST = 109151;

        /**
         * 积分商店刷新消耗
         */
        int JI_FEN_STORE_REFRESH_COST = 180001;

        /**
         * 积分商店免费刷新间隔
         */
        int JI_FEN_STORE_AUTO_REFRESH_GAP = 180002;

        /**
         * 积分商店每日免费刷新次数
         */
        int JI_FEN_STORE_FREE_REFRESH_COUNT = 180003;

        /**
         * 每次刷新商品数量
         */
        int JI_FEN_STORE_REFRESH_GOOD_COUNT = 180004;

        /**
         * 武力盾消耗
         */
        int WU_LI_DUN_COST = 200001;

        /**
         * 武力盾奖励
         */
        int WU_LI_DUN_REWARD = 200002;

        /**
         * 宝藏秘境挑战兑灵符比例
         */
        int XUN_BAO_LIN_FU_RATE = 200101;


        /**
         * 发送红包限制
         */
        int RED_PACK_MAX_REMAIN_SIZE = 200301;
        /**
         * 红包记录
         */
        int RED_PACK_RECORD_MAX_SIZE = 200302;
        /**
         * 红包发言长度
         */
        int BLESSING_MAX_SIZE = 200303;

        /**
         * 投资金额
         */
        int TOU_ZI_JIN_E = 200201;

        /**
         * 投资会员要求
         */
        int TOU_ZI_VIP_LIMIT = 200202;
        /**
         * 购买投资理财的时间条件
         */
        int TOU_ZI_VIP_TIME_CONDITION = 200203;
        /**
         * 退出副本获取装备
         */
        int DUPLICATE_EXIT_EQUIP = 101011;

        /**
         * 购买狂暴之力消耗
         */
        @Deprecated
        int BUY_SUPER_MAN_COST = 210001;

        /**
         * 狂暴之力额外掉落
         */
        @Deprecated
        int SUPER_MAN_DROP_BOX = 210002;

        /**
         * 狂暴之力增加buff
         */
        @Deprecated
        int SUPER_MAN_BUFF = 210003;

        /**
         * 怪物选择侠侣或者玩家的权重,第一位为侠侣权重，第二位为玩家权重
         */
        int MONSTER_CHOOSE_PLAYER_OR_XIALV_PROB = 101012;

        /**
         * 怪物清除仇恨时间
         */
        int MONSTER_CLEAR_THREAD_TIME = 101013;

        /**
         * 邀请码开启天数
         */
        int INVITATION_CODE_EFFECTIVE_DAY = 160101;

        /**
         * 服务器可注册人数上限
         */
        int INVITATION_CODE_SERVER_REGISTER_MAX = 160102;

        /**
         * 老玩家可生成邀请码的条件
         */
        int INVITATION_CODE_GENERATE_CONDITION = 160103;

        /**
         * 每条邀请码可使用上限
         */
        int INVITATION_CODE_USE_LIMIT_MAX = 160104;

        /**
         * 挖宝装备出处名称
         */
        int DIT_TREASURE_MAP = 160005;

        /**
         * 成长基金充值目标
         */
        int CZJJ_RECHARGE_SET = 240000;

        /**
         * 炼妖壶鞭尸提示屏蔽地图
         */
        int SHIELDING_EXTRA_DROP_TIPS = 250002;

        /**
         * 人民币兑换配置    rmb点id#数量&x元充值记录&获得灵符id#数量
         */
        int RMB_EXCHANGE_SET = 260000;

        /**
         * 每日充值达标送轮回挑战
         */
        int RMB_LUNHUITA_SET = 270000;

        /**
         * 鞭尸属性额外掉落box
         */
        int ATTRIBUTE_EXTRA_DROP = 280000;

        /**
         * 沙城捐献第一次捐献数量限制
         */
        int SHACHENG_JUANXIAN = 310000;

        /**
         * 创角称号自动消失条件等级
         */
        int CREATE_DEFAULT_FASHION_VANISH = 330000;


        /**
         * cd补偿时间（毫秒）
         */
        int CD_COMPENSATE = 103005;

        /**
         * 神威BOSS每日击杀boss掉落奖励次数
         */
        int SHEN_WEI_BOSS_DROP_COUNT_LIMIT = 340000;

        /**
         * 押镖距离
         */
        int ESCORT_DISTANCE = 350010;

        /**
         * equip_suit表神兵套装类型
         */
        int SHENBING_TYPE = 171000;

        /**
         * 神兵套装检测部位
         */
        int SHENBING_POS = 171001;

        /**
         * 怪物受伤害比例计算参数
         */
        int MONSTER_HURT_RATE_PARAM = 360010;

        /**
         * 皇榜挑战击杀次数限制
         */
        int EDICT_CHALLENGE_KILL_COUNT_LIMIT = 370001;

        /**
         * 皇榜挑战参与次数限制
         *
         * @deprecated 策划不用了
         */
        @Deprecated
        int EDICT_CHALLENGE_AID_COUNT_LIMIT = 370002;

        /**
         * 皇榜挑战-皇榜令牌单次使用 刷新次数
         */
        int EDICT_CHALLENGE_REFRESH_COUNT_LIMIT = 370003;

        int MOVE_NETWORK_PARAM = 400001;

        /**
         * 元宝兑换参数
         */
        int YUAN_BAO_DUI_HUAN = 260001;

        /**
         * 论剑基金充值目标
         */
        int LJJJ_RECHARGE_SET = 240001;

        /**
         * 特解属性加成部位
         */
        int TE_JIE_ATK_ADD_POS = 410002;

        /**
         * 特解属性加成的属性
         */
        int TE_JIE_ATK_ADD_ATTR = 410003;

        /**
         * 丹药每日使用上限
         */
        int DANYAO_COUNT = 390002;

        /**
         * 重置丹消耗
         */
        int DANYAO_CLEAR_COST = 390003;

        /**
         * 通天之路排行榜第一公告
         */
        int ANNOUNCE_BABEL_ROAD_FIRST = 410001;

        /**
         * 通天之路榜一奖励
         */
        int BABEL_ROAD_FIRST_REWARD = 410004;

        /**
         * 暗黑神殿每日0点后累加时间
         * itemConfigId#count
         */
        int DAILY_DARK_TEMPLE = 430002;

        /**
         * 暗黑神殿累加时间condition
         */
        int CONDITION_DARK_TEMPLE = 430003;

        /**
         * 每日门派日常挑战次数
         */
        int DAILY_SECT_TASK_LIMIT = 440000;

        /**
         * 门派日常挑战最后一个任务的奖励
         */
        int SECT_TASK_LAST_REWARD = 440001;

        /**
         * 门派日常挑战最后一个任务的多倍消耗
         * itemConfigId#count#mul
         */
        int SECT_TASK_LAST_COST = 440002;

        /**
         * 门派日常挑战刷新消耗
         */
        int SECT_TASK_REFRESH_COST = 440004;

        /**
         * 相同法宝列表
         */
        int MAGIC_WEAPON_SAME = 440005;

        /**
         * 藏宝图召唤npc偏移格子
         */
        int CANGBANGTU_PIANYI = 440010;

        /**
         * 强化等级condition
         */
        int STRENGTHEN_CONDITION = 101030;

        /**
         * 新号送一次暗黑神殿时间
         * 开服时间#itemConfigId#count
         */
        int FIRST_DARK_TEMPLE_REWARD = 460001;

        /**
         * 道士召唤物不会对配置type的怪物造成伤害
         */
        int YUE_LING_INVALID_MONSTER_TYPE = 480001;

        /**
         * 兽决内丹领悟时的概率设置 领悟兽决概率（百分比，格子对应位置&概率，多个格子用|分隔）1&100|2&80|3&60|4&40|5&20|6&10|7&8|8&5|9&2
         */
        int SHOUJUEEQUIP_GAILVSET = 470001;
        /**
         * 替换兽决概率（百分比，内丹等级#概率，多个内丹用|分隔） 1#10|2#10|3#20
         */
        int SHOUJUEEQUIP_LEVELSET = 470002;
        /**
         * 锁定内丹价格，灵符（锁定数量#消耗，多个用|分隔，锁定多个数量价格叠加） 1#2000|2#5000|3#10000|4#20000
         */
        int SHOUJUEEQUIP_LOCKLSET = 470003;

        /**
         * 兽决锁可解锁道具 设定( 默认已开放个数#最大可锁定个数)
         */
        int SHOUJUEEQUIP_UNLOCKCNTSET = 470005;


        /**
         * 宝藏之地飞行坐标
         */
        int SPECIAL_WA_BAO_TELEPORT_POS = 190012;

        /**
         * 幸运属性算法参数
         */
        int LUCK_ATTR_PARAM = 510001;

        /**
         * 宝藏之地公告存储长度
         */
        int SPECIAL_WA_BAO_ANNOUNCE_SIZE_LiMIT = 190013;

        /**
         * 全民夺宝开奖时间
         */
        int QUAN_MING_DUO_BAO_REWARD_TIME = 492001;

        /**
         * 全民夺宝数字区间
         */
        int QUAN_MING_DUO_BAO_NUMS = 492002;

        /**
         * 进阶之路任务完成奖励
         */
        int JINGJIEZHILUREWARD = 500001;

        /**
         * 盟主令每日奖励离线天数
         */
        int MONTHCARD_MAIL_DAY = 520100;

        /**
         * 如意转盘充值金额可获得次数
         */
        int RU_YI_ZHUAN_PAN_RECHARGE = 491001;

        /**
         * 如意转盘充值金额可获得次数单日次数上限
         */
        int RU_YI_ZHUAN_PAN_COUNT = 491002;

        /**
         * 每日免费领取体力值
         */
        int PHYSICAL_POWER_RECEIVE = 540103;

        /**
         * 解锁特戒灵石格子消耗
         */
        int RINGGEM_UNLOCK_POS_COST = 490001;

        /**
         * 特戒所有部位
         */
        int SPECIALRING_POS = 490002;


        /**
         * 沙巴克前几次合服额外红包
         */
        int SBK_RED_PACK = 541001;

        /**
         * 宝箱积分
         */
        int FIGHTFORCA_BOX_SCORE = 550007;
        /**
         * 玩家击杀与被杀积分
         */
        int FIGHTFORCA_KILL_SCORE = 550008;

        /**
         * 丹药不计入个人数量得itemid #分割
         */
        int DANYAO_NORECORD_QUANTITY = 390004;

        /**
         * 长安秘境免费次数
         */
        int CHANG_AN_MI_JING_FREE_TIME = 550001;

        /**
         * 长安秘境传送上限
         */
        int CHANG_AN_MI_JING_TELEPORT_LIMIT = 550002;

        /**
         * 长安秘境传送道具
         */
        int CHANG_AN_MI_JING_TELEPORT_ITEM = 550003;

        /**
         * 长安秘境购买限制
         */
        int CHANG_AN_MI_JING_BUY_LIMIT = 550004;

        /**
         * 长安秘境每次购买传送道具给几个
         */
        int CHANG_AN_MI_JING_BUY_PER_COUNT = 550005;

        /**
         * 长安秘境初始坐标
         */
        int CHANG_AN_MI_JING_BORN_POS = 550006;

        /**
         * 据点每几秒给一次积分
         */
        int CHANG_AN_WAR_TIME = 550009;

        /**
         * 长安秘境奖池重置
         */
        int CHANG_AN_MI_JING_RESET_REWARD = 550010;

        /**
         * 长安秘境传送次数
         */
        int CHANG_AN_MI_JING_TELEPORT_COUNT = 550011;

        /**
         * 长安秘境保底进度重置
         */
        int CHANG_AN_MI_JING_RESET_PROGRESS = 550012;

        /**
         * 地煞来袭刷新时间
         */
        int DI_SHA_REFRESH_TIME = 560001;

        /**
         * 地煞刷新condition
         */
        int DI_SHA_CONDITION = 572001;

        /**
         * 持续移动判定下限
         */
        int PERSIST_MOVE_LIMIT_LOWER = 570001;

        /**
         * 持续移动判定上限
         */
        int PERSIST_MOVE_LIMIT_UPPER = 570002;

        /**
         * 足迹充能完成公告
         */
        int FOOTPRINT_ANNOUNCE = 570003;

        /**
         * 阵法升级暴击有效类型
         */
        int MAGIC_CIRCLE_UP_CRIT_VALID_TYPE = 571001;

        /**
         * 灵根升华-功能条件
         */
        int SUBLIMATION_CONDITION = 551001;

        /**
         * 灵根升华-每日消耗灵根天命数量上限
         */
        int SUBLIMATION_COST_LIMIT = 551002;

        /**
         * 灵根升华-天命灵根根据type消耗增加的升华经验
         */
        int SUBLIMATION_EXP = 551003;

        /**
         * 怪物图鉴活动-每日怪物掉落次数限制
         */
        int MONSTER_CARD_DROP_LIMIT = 600001;

        /**
         * 会长未上线自动弹劾时间(天)
         */
        int IMPEACH_DAY = 601001;

        /**
         * 会长自动弹劾全行会邮件id
         */
        int IMPEACH_MAIL = 601002;

        /**
         * 神魔锻体-功能条件
         */
        int BODY_FORGE_CONDITION = 602001;

        /**
         * 行会掉落助攻邮件
         */
        int UNION_ASSIST_DROP_MAIL = 580025;

        /**
         * 296属性影响的该装备的属性
         */
        int SP_TITAN_ADD_ATTR = 150003;

        /**
         * 护盾值回复cd
         */
        int SHIELD_REPLY_CD = 606005;

        /**
         * 杀同一玩家多次后不在加积分
         */
        int SHA_BA_KE_KILL_NUM = 580026;

        /**
         * 天命-每日运势免费抽签限制次数
         */
        int FATE_DAILY_LIMIT_FREE = 614001;

        /**
         * 天命-每日运势购买抽签限制次数
         */
        int FATE_DAILY_LIMIT_BUY = 614002;

        /**
         * 天命-每日运势购买抽签消耗
         */
        int FATE_DAILY_LIMIT_BUY_COST = 614003;

        /**
         * 防骗奖励
         */
        int FANGPIAN_REWARD = 611001;

        /**
         * 天魔来袭要同步的怪物id
         */
        int TIAN_MO_SYN_MONSTER = 608002;

        /**
         * 九天之巅死亡掉落层数
         */
        int JIU_TIAN_DIAO_LUO_CENG_SHU = 610001;

        /**
         * 九天之巅击杀玩家积分
         */
        int JIU_TIAN_KILL_PLAYER_JI_FEN = 610002;

        /**
         * 九天之巅击杀怪物积分
         */
        int JIU_TIAN_KILL_MONSTER_JI_FEN = 610003;

        /**
         * 九天之巅需要给前端发送怪物刷新倒计时的地图
         */
        int JIU_TIAN_NEED_RECORD_MAP = 610005;

        /**
         * 龙魂宝石最大融合数
         */
        int LONG_HUN_LIMIT = 615001;

        /**
         * 龙装部位列表
         */
        int LONG_HUN_POS = 615002;

        /**
         * 特殊极品法宝itemConfigId列表
         */
        int SPECIAL_MAGIC_WEAPON = 616001;

        /**
         * 缴械装备部位列表
         */
        int DISARM_POS = 619001;

        /**
         * 缴械属性
         */
        int DISARM_ATTRIBUTE = 619002;

        /**
         * 秘籍部位对应itemId
         */
        int MIJI_POS_ITEMID = 618001;

        /**
         * 秘籍默认解锁孔位
         */
        int MIJI_INIT_KONGWEI = 618002;

        /**
         * 秘籍部位
         */
        int MIJI_EQUIP_POS = 618003;

        /**
         * 秘籍系统开启condition
         */
        int MIJI_CONDITION = 618004;

        /**
         * 免费匹配次数
         */
        int FREE_MATCH_COUNT = 620001;

        /**
         * 每日购买次数
         */
        int PAY_MATCH_COUNT = 620002;

        /**
         * 购买花费
         */
        int PAY_MATCH_COST = 620003;

        /**
         * 旗子存在玩家身上时间（秒）
         */
        int QI_ZI_EXIST_TIME = 620004;

        /**
         * 赢的旗子数量
         */
        int WIN_QI_ZI_COUNT = 620005;

        /**
         * 3v3副本出生点
         */
        int XIU_LUO_BIRTH_POINT = 620006;

        /**
         * 3v3副本复活次数
         */
        int XIU_LUO_RELIVE_COUNT = 620007;

        /**
         * 战报上限
         */
        int XIU_LUO_ZHAN_BAO_LIMIT = 620008;

        /**
         * 旗子特效
         */
        int QI_ZI_TE_XIAO = 620009;

        /**
         * 武帝城禁地地图id
         */
        int WU_DI_CHENG_JIN_DI_MAPS = 580029;

        /**
         * 宠物装备部位
         */
        int CHONGWU_EQUIP_POS = 622002;

        /**
         * 宠物装备强化系统开启条件condition
         */
        int CHONGWU_EQUIP_CONDITION = 622003;

        /**
         * 宠物装备鉴定获得技能万分比
         */
        int CHONGWU_EQUIP_JIANDING_SKILL = 622001;

        /**
         * 宠物装备鉴定随机品质权重
         */
        int CHONGWU_EQUIP_RATE_PROB = 622004;

        /**
         * 宠物装备点化消耗
         */
        int CHONGWU_EQUIP_DIANHUA_COST = 622005;

        /**
         * 商店购买商品上限
         */
        int STORE_BUYGOODS_MAXCOUNT = 640001;

        /**
         * 需要掉线重连的副本
         */
        int DUPLICATE_RECONNECT = 620010;

        /**
         * 3v3上榜条件
         */
        int XIU_LUO_RANK_LIMIT = 620011;

        /**
         * 3v3排行榜条数
         */
        int XIU_LUO_RANK_NUM = 620012;
        /**
         * 两个特殊装备的装备位
         */
        int SP_EQUIP_INDEX = 650001;

        /**
         * 特殊装备无限回城装备itemId
         */
        int SP_EQUIP_HUICHENG = 670001;

        /**
         * 特殊装备无限随机装备itemId
         */
        int SP_EQUIP_SUIJI = 670002;

        /**
         * 特殊装备无限大陆传送石装备itemId
         */
        int SP_EQUIP_DALU_CHUANSONG = 670004;

        /**
         * 特殊装备每日免费20次行会召集令装备itemId
         */
        int SP_EQUIP_DALU_UNION_ZHAOJI = 670003;

        /**
         * 刀刀元宝获取的道具id
         */
        int GOLD_ITEM_CONFIG_ID = 680001;

        /**
         * 刀刀元宝特效飘字id
         */
        int GOLD_DAMAGE_ID = 680002;

        /**
         * 混沌附魂消耗
         */
        int CHAOTIC_INLAY_COST = 105001;

        /**
         * 混沌拆分消耗
         */
        int CHAOTIC_SEPARATE_COST = 105002;

        /**
         * 中秋boss超链
         */
        int ZHONGQIU_BOSS_DELIVE = 573102;

        /**
         * 3v3点击匹配传送回安全区
         */
        int XIU_LUO_MATCH_SAFE_AREA = 620013;

        /**
         * 3v3积分胜利数
         */
        int XIU_LUO_WIN_JI_FEN_COUNT = 620014;

        /**
         * 每次旗子结算的积分
         */
        int XIU_LUO_QI_ZI_JI_FEN = 620015;
        /**
         * 每次杀人结算的积分
         */
        int XIU_LUO_KILL_JI_FEN = 620016;

        /**
         * 3v3旗子怪物
         */
        int XIU_LUO_QI_ZI_MONSTER = 620017;


        /**
         * 人皇祭祀保底进度增长时间(h)
         */
        int SACRIFICE_PROGRESS_TIME = 660000;

        /**
         * 人皇祭祀保底进度列表(%)
         */
        int SACRIFICE_PROGRESS = 660001;

        /**
         * 人皇祭祀召唤进度条件
         */
        int SACRIFICE_CALL_PROGRESS = 660002;

        /**
         * 人皇祭祀召唤怪物位置 mapConfigId#x#y
         */
        int SACRIFICE_MAP = 660003;

        /**
         * 人皇祭祀召唤怪物配置id
         */
        int SACRIFICE_MONSTER = 660004;

        /**
         * 神皇之力系统开启条件
         */
        int UNION_SHENHUANG_CONDITION = 700001;

        /**
         * 神皇之力次数限制
         */
        int UNION_SHENHUANG_COUNT = 700002;

        /**
         * 武器特殊真伤属性影响的属性id
         */
        int WUQI_SPZHENSHANG_ATTRID = 654001;

        /**
         * 修罗匹配条件
         */
        int XIU_LUO_MATCH_CONDITION = 620018;

        /**
         * 刷新任务地图不刷新的任务id
         */
        int REFRESH_TASK_MAPS_NOCHECK_TASK = 655001;

        /**
         * 星官怪物id
         */
        int XINGGUAN_MONSTERS_LIST = 721001;

        /**
         * 背包神器id#道具id|
         */
        int BAG_SHENQI_DUIYING = 670005;

        /**
         * 背包神器部位
         */
        int BAG_SHENQI_POS = 670006;

        /**
         * 地狱之路-泡点获取积分间隔(s)
         */
        int HELL_ROAD_AUTO_SCORE_INTERVAL = 720001;

        /**
         * 地狱之路-泡点获取积分
         */
        int HELL_ROAD_AUTO_SCORE = 720002;

        /**
         * 地狱之路-击杀玩家基础积分
         */
        int HELL_ROAD_KILL_PLAYER_BASE_SCORE = 720003;

        /**
         * 地狱之路-击杀玩家积分获取百分比
         */
        int HELL_ROAD_KILL_PLAYER_SCORE_RATE = 720004;

        /**
         * 地狱之路-采集上限
         */
        int HELL_ROAD_COLLECT_LIMIT = 720005;

        /**
         * 地狱之路-活动地图id#形象id#武器id
         */
        int HELL_ROAD_PARAMS = 720006;

        /**
         * 地狱之路-出生随机点（x#y#范围）
         */
        int HELL_ROAD_INIT_POINT = 720007;

        /**
         * 地狱之路-击杀怪物积分
         */
        int HELL_ROAD_MONSTER_SCORE_MAP = 720008;

        /**
         * 地狱之路-活动地图
         */
        int HELL_ROAD_MAPS = 720009;

        /**
         * 地狱之路-离开地图积分降低比例
         */
        int HELL_ROAD_LEAVE_MAP_SCORE_RATE = 720010;

        /**
         * 地狱之路-排行榜人数限制
         */
        int HELL_ROAD_RANK_LIMIT = 720012;

        /**
         * 地狱之路-排行榜刷新间隔(ms)
         */
        int HELL_ROAD_RANK_REFRESH_INTERVAL = 720013;

        /**
         * 坐骑赞助点加点
         */
        int ZUOQI_ZANZHU_SCORE = 722002;

        /**
         * 坐骑赞助点击杀怪物列表
         */
        int ZUOQI_ZANZHU_MONSTER_LIST = 722003;

        /**
         * 搬砖任务和刺探任务
         */
        int BANZHUAN_CITAN_TASKLIST = 710014;

        /**
         * 搬砖和刺探任务道具
         */
        int BANZHUAN_CITAN_TASKITEMLIST = 710017;

        /**
         * 行会宝地开启周限制
         */
        int HANG_HUI_BAO_DI_WEEK_COUNT = 725003;

        /**
         * 行会宝地开启日限制
         */
        int HANG_HUI_BAO_DI_DAY_COUNT = 725004;

        /**
         * 行会宝地出生点
         */
        int HANG_HUI_BAO_DI_BIRTH_POINT = 725006;

        /**
         * 行会宝地转盘奖励数量
         */
        int HANG_HUI_BAO_DI_ZHUAN_PAN_REWARD_COUNT = 725001;

        /**
         * 行会宝地转盘消耗
         */
        int HANG_HUI_BAO_DI_ZHUAN_PAN_COST = 725005;

        /**
         * 行会宝地转盘权重
         */
        int HANG_HUI_BAO_DI_ZHUAN_PAN_WEIGHT = 725002;

        /**
         * 铸魂-等级规则
         */
        int ZHU_HUN_LEVEL = 724001;

        /**
         * 铸魂-加锁后消耗
         */
        int ZHU_HUN_LOCK_COST = 724002;

        /**
         * 铸魂-各部位条件
         */
        int ZHU_HUN_CONDITION = 724003;

        /**
         * 铸魂-基础消耗
         */
        int ZHU_HUN_BASE_COST = 724004;

        /**
         * 随从超过距离传送
         */
        int SERVANT_TELEPROT_DIS = 728001;

        /**
         * 国庆boss超链
         */
        int GUOQING_BOSS_DELIVE = 573202;

        /**
         * 航海保底玩家每周参与次数
         */
        int HANG_HUI_BAO_DI_ROLE_LIMIT = 725010;

        /*巅峰联赛global参数*/
        /**
         * 沙巴克排名前几名自动获得参赛资格
         */
        int DFLS_SBK_RANK_LIMIT = 750001;

        /**
         * 巅峰联赛购买资格券花费
         */
        int DFLS_BUY_COST = 750002;

        /**
         * 单个行会巅峰联赛报名人员上限
         */
        int DFLS_MEMBER_MAX_LIMIT = 750003;

        /**
         * 单个行会巅峰联赛报名人员下限
         */
        int DFLS_MEMBER_MIN_LIMIT = 750004;

        /**
         * 更改参赛人数次数限制
         */
        int DFLS_CHANGE_PARTICIPANTS_LIMIT = 750005;

        /**
         * 巅峰联赛通过沙巴克获取参赛资格时间
         */
        int DFLS_SBK_QUALIFICATIONS_DAY = 750006;

        /**
         * 巅峰联赛主动报名时间限制
         */
        int DFLS_SIGN_UP_TIME = 750007;

        /**
         * 巅峰联赛重置时间
         */
        int DFLS_REST_TIME = 750008;

        /**
         * 巅峰联赛报名消耗
         */
        int DFLS_SIGN_UP_COST = 750009;

        /**
         * 巅峰联赛海选赛开始天数
         */
        int DFLS_HAI_XUAN_SAI_OPEN_DAY = 750010;

        /**
         * 海选赛当天开始时间戳
         */
        int DFLS_HAI_XUAN_SAI_START_TIME = 750011;

        /**
         * 巅峰联赛捐献转换奖金率
         */
        int DFLS_JUAN_XIAN_JIANG_JIN_RATE = 750013;

        /**
         * 巅峰联赛捐献下限
         */
        int DFLS_JUAN_XIAN_COUNT_LIMIT = 750041;

        /**
         * 竞猜第一邮件
         */
        int DFLS_JING_CAI_FIRST = 750015;

        /**
         * 可更换参赛人员的天数
         */
        int DFLS_CHANGE_PARTICIPANTS_DAY = 750016;

        /**
         * 巅峰联赛累计获胜战力皇宫时间
         */
        int DFLS_HUANG_GONG_WIN_TIME = 750017;

        /**
         * 巅峰联赛淘汰赛双发初始出生点
         */
        int DFLS_TAO_TAI_BIRTH_POINT = 750018;

        /**
         * 巅峰联赛攻方出生点
         */
        int DFLS_TAO_TAI_GONG_BIRTH_POINT = 750019;

        /**
         * 巅峰联赛守方出生点
         */
        int DFLS_TAO_TAI_SHOU_BIRTH_POINT = 750020;

        /**
         * 巅峰联赛淘汰赛旗子怪物id
         */
        int DFLS_TAO_TAI_QI_ZI_MONSTER = 750021;

        /**
         * 巅峰联赛淘汰赛守卫栅栏关联
         */
        int DFLS_TAO_TAI_SHOU_WEI = 750022;

        /**
         * 巅峰联赛淘汰赛皇宫传送阵id
         */
        int DFLS_TAO_TAI_HUANG_GONG_TRAMIST_EVENT = 750023;

        /**
         * 传送皇宫初始坐标
         */
        int DFLS_TAO_TAI_HUANG_GONG_BIRTG_POINT = 750024;

        /**
         * 巅峰联赛冠军行会灵符分配比率
         */
        int DFLS_LING_FU_FEN_PEI_RATE = 750026;

        /**
         * 巅峰联赛战令销售额注入奖金比例(万分比)
         */
        int DFLS_ZHAN_LING_JIANG_JIN_RATE = 750027;


        /**
         * 巅峰联赛战令-购买经验参数
         * 消耗道具配置id#数量#获得经验值#次数上限
         */
        int DFLS_ZHAN_LING_BUY_EXP = 750030;

        /**
         * 竞猜成功邮件
         */
        int DFLS_JING_CAI_SUCCESS = 750031;

        /**
         * 竞猜消耗
         */
        int DFLS_JING_CAI_COST = 750032;

        /**
         * 竞猜开始时间
         */
        int DFLS_JING_CAI_START_TIME = 750033;

        /**
         * 竞猜数据清除时间
         */
        int DFLS_JING_RANK_REST_TIME = 750034;

        /**
         * 可竞猜时间
         */
        int DFLS_JING_CAI_TIME = 750035;

        /**
         * 巅峰联赛灵符奖池基础灵符
         */
        int DFLS_LING_FU_FEN_PEI_BAO_DI = 750036;

        /**
         * 捐献开始时间
         */
        int DFLS_JUAN_XIAN_START_DAY = 750037;

        /**
         * 捐献结束时间
         */
        int DFLS_JUAN_XIAN_END_DAY = 750038;

        /**
         * 捐献结束当天的时间戳
         */
        int DFLS_JUAN_XIAN_END_DAY_TIME = 750039;

        /**
         * 捐献结算日
         */
        int DFLS_JUAN_XIAN_JIE_SUAN_TIME = 750040;

        /**
         * 巅峰联赛皇宫传送阵坐标
         */
        int DFLS_HUANG_GONG_TRANSMIT_POINT = 750043;

        /**
         * 巅峰联赛战令注入奖金额-时间
         * startDay|endDay
         * 每月第几天开始|每月第几天结束
         */
        int DFLS_ZHAN_LING_AVAILABLE_DAY = 750044;

        /**
         * 巅峰联赛资格获取condition
         */
        int DFLS_ZI_GE_CONDITION = 750046;

        /**
         * 巅峰联赛竞猜condition
         */
        int DFLS_JING_CAI_CONDITION = 750045;

        /**
         * 巅峰联赛竞猜券道具id
         */
        int DFLS_JING_CAI_QUAN_ITEM_ID = 750047;

        /**
         * 巅峰联赛不可竞猜时间
         */
        int DFLS_JING_NO_CAI_TIME = 750048;

        /**
         * 巅峰联赛赛区名
         */
        int DFLS_SAI_QU_NAME = 750049;

        /**
         * 巅峰联赛最高捐献
         */
        int DFLS_JUAN_XIAN_MAX_COUNT = 750050;

        /**
         * 巅峰联赛战令购买上限生效时间
         */
        int DFLS_ZHAN_LING_BUY_EXP_LIMIT_DAY = 750051;

        /**
         * 装备套装取总等级的类型
         */
        int SUIT_LEVEL_TYPE = 760001;

        /**
         * 世界杯商店刷新消耗
         */
        int WORLD_CUP_STORE_FLUSH_COST = 117012;

        /**
         * 世界杯商店每日最大限购次数
         */
        int WORLD_CUP_STORE_DAILYCOUNT = 117013;

        /**
         * 全服排行榜开启天数组
         */
        int RECHARED_RANK_OPENTIME = 117014;


        /**
         * 侠魂宝石最大融合数
         */
        int XIA_HUN_LIMIT = 770001;

        /**
         * 侠装部位列表
         */
        int XIA_HUN_POS = 770002;

        /**
         * 异火塔配置
         */
        int FIRE_TOWER_CONFIG = 790001;

        /**
         * 异火塔免费次数
         */
        int FIRE_TOWER_FREE_COUNT = 790002;

        /**
         * 需要修复的沙巴克限时时装
         */
        int FIX_CROSS_SBK_FASHIONID = 580032;

        /**
         * 世界杯投资天数限制
         */
        int WORLDCUP_TOUZI_ACTIVITY_LIMIT = 117015;

        /**
         * 领取红包cd(秒
         */
        int GET_RED_PACK_CD = 743001;

        /**
         * 灵宝怪物类型
         */
        int REALM_MONSTER_TYPE = 761002;

        /**
         * 灵宝怪物每日掉落次数
         */
        int REALM_DROP_LIMIT = 761003;

        /**
         * 灵宝怪物每日可购买掉落次数
         */
        int REALM_BUY_LIMIT = 761004;

        /**
         * 灵宝怪物每日掉落次数购买消耗
         */
        int REALM_BUY_COST = 761005;

        /**
         * 真言塔每日归属次数
         */
        int ZHENYAN_TOWER_DAILY_COUNT = 754001;

        /**
         * 真言塔特殊消耗
         */
        int ZHENYAN_TOWER_SP_COST = 754002;

        /**
         * 真言塔地图id
         */
        int ZHENYAN_TOWER_MAPID = 754003;

        int MILLION_RETURN_CONFIG = 727002;

        int MILLION_RESET_TYPE = 727003;

        int HUI_HUN_HP_JUDGE = 820001;

        /**
         * 命格-猎命-满进度条奖池id
         */
        int BIRTH_CHART_EVENT_PROGRESS_JACKPOT = 821001;

        /**
         * 命格-猎命-进度条总量
         */
        int BIRTH_CHART_EVENT_PROGRESS_AMOUNTS = 821002;

        /**
         * 命格-猎命-临时仓库容量
         */
        int BIRTH_CHART_EVENT_REWARD_LIMIT = 821004;

        /**
         * 命格-猎命-大奖
         */
        int BIRTH_CHART_EVENT_BIG_PRIZE = 821005;

        /**
         * 直购id:  激活离线挂机
         */
        int LIXIANGUAJI_RECHARGEID = 842005;

        /**
         *  离线多少分钟后才算
         *
         */
        int LIXIANGUAJI_TIMESET = 842001;

        /**
         *  离线挂机最长时间( 小时)
         *
         */
        int LIXIANGUAJI_MAXTIME = 842002;

        /**
         * 离线挂机需要加倍的道具
         */
        int LIXIANGUAJI_MUTIL_ITEMID = 842007;

        /**
         *  离线挂机每杀多少怪升级一次
         */
        int LIXIANGUAJI_UPLV_PERKILL = 842008;

        /**
         * 圣诞礼物活动抽取box
         */
        int CHRISTMAS_GIFT_BOX = 861000;

        /**
         * 圣诞礼物每日免费次数上限
         */
        int CHRISTMAS_GIFT_DAY_FREE_COUNT_LIMIT = 861003;

        /**
         * 圣诞礼物多少充值获得一次抽奖机会
         */
        int CHRISTMAS_GIFT_PER_COUNT_RECHARGE = 861004;

        /**
         * 圣诞礼物每日充值次数上限
         */
        int CHRISTMAS_GIFT_RECHARGE_COUNT_DAY_LIMIT = 861005;

        /**
         * 圣诞商店每日购买次数上限
         */
        int CHRISTMAS_STORE_BUY_LIMIT = 117100;

        /**
         * 圣诞商店刷新商品消耗
         */
        int CHRISTMAS_STORE_RANDOM_COST = 117101;

        /**
         * 圣诞商店刷新折扣消耗
         */
        int CHRISTMAS_STORE_RANDOM_DISCOUNT_COST = 117102;

        /**
         * 圣诞商店抽取折扣权重
         */
        int CHRISTMAS_STORE_RANDOM_DISCOUNT_PROB = 117103;

        /**
         * 圣诞商店每日随机折扣次数上限
         */
        int CHRISTMAS_STORE_RANDOM_DISCOUNT_LIMIT = 117104;

        /**
         * 公共cd保底
         */
        int COMMON_CD_BAO_DI = 875001;

        /**
         * 新春锦鲤抽奖时间
         */
        int XIN_CHUN_JIN_LI_REWARD_TIME = 653320;

        /**
         * 新春锦鲤第一名
         */
        int XIN_CHUN_JIN_LI_FIRST = 653321;

        /**
         * 新春锦鲤第二名
         */
        int XIN_CHUN_JIN_LI_SECOND = 653322;

        /**
         * 新春锦鲤第三名
         */
        int XIN_CHUN_JIN_LI_THIRD = 653323;

        /**
         * 金鼓迎财保底灵符
         */
        int JIN_GU_YING_CAI_BAO_DI_LING_FU = 881001;

        /**
         * 金鼓迎财每次增加灵符
         */
        int JIN_GU_YING_CAI_ADD_LING_FU = 881002;

        /**
         * 新春boss超链
         */
        int XINCHUN_BOSS_DELIVERID = 652321;

        /**
         * 受诸神之力影响的怪物类型
         */
        int ZHU_SHEN_MONSTER_TYPE = 891001;

        /**
         * 拆家消耗
         */
        int CHAI_JIA_COST = 900001;
        /**
         * 拆家返还
         */
        int CHAI_JIA_BACK = 900002;
        /**
         * 拆家cd
         */
        int CHAI_JIA_CD_TIME = 900003;

        /**
         * 免费缩短拆家升级时间cd
         */
        int CHAI_JIA_FREE_CD = 900005;

        /**
         * 免费缩短拆家升级时间
         */
        int CHAI_JIA_FREE_TIME = 900006;

        /**
         * 创角给100次拆家
         */
        int CHAI_JIA_INIT = 900007;

        /**
         * 伙伴刷新数量
         */
        int HUO_BAN_REFRESH_COUNT = 910001;

        /**
         * 伙伴背包初始容量
         */
        int HUO_BAN_BAG_INIT_SIZE = 910002;

        /**
         * 伙伴每日免费刷新次数
         */
        int HUO_BAN_FREE_COUNT = 910003;

        /**
         * 购买伙伴栏消耗
         */
        int HUO_BAN_BAG_COST = 910004;

        /**
         * 伙伴背包最大容量
         */
        int HUO_BAN_BAG_MAX_SIZE = 910005;

        /**
         * 刷新伙伴消耗
         */
        int HUO_BAN_REFRESH_COST = 910006;

        /**
         * 伙伴初始刷新
         */
        int HUO_BAN_FIRST_REFRESH = 910008;

        /**
         * 神魔上阵最大数量
         */
        int SHEN_MO_GO_BATTLE_MAX_NUM = 920001;

        int CHAI_JIA_YUE_KA_ACTIVITYID = 930001;
        int CHAI_JIA_YUE_KA_TYPE = 930002;
        int CHAI_JIA_YUE_KA_RATE_LIMIT = 930003;
        int CHAI_JIA_YUE_KA_LEVEL_LIMIT = 930004;

        int ARENA_BATTLE_COST = 940001;
        int ARENA_REFRESH_COST = 940002;
        int ARENA_DAY_SETTLE_TIME = 940003;
        int ARENA_WEEK_SETTLE_TIME = 940004;
        int ARENA_UNLOCK_CONDITION = 940005;
        int ARENA_BATTLE_REWARD = 940006;
        int ARENA_DAY_OPEN_TIME = 940008;

        /**
         * 竞技场结算时间#结算最小间隔
         */
        int ARENA_RESET_TIME = 940009;
        /**
         * 拆家额外道具限制
         */
        int CHAI_JIA_LIMIT_ITEM = 940007;

        /**
         * 万仙榜挑战消耗
         */
        int WAN_XIAN_BANG_CHALLENGE_COST = 950001;
        /**
         * 万仙榜挑战列表刷新消耗
         */
        int WAN_XIAN_BANG_CHALLENGE_LIST_REFRESH_COST = 950002;
        /**
         * 万仙榜每日关闭时间
         */
        int WAN_XIAN_BANG_DAY_CLOSE_TIME = 950003;
        /**
         * 万仙榜开启条件
         */
        int WAN_XIAN_BANG_OPEN_CONDITION = 950005;
        /**
         * 万仙榜挑战成功奖励
         */
        int WAN_XIAN_BANG_CHALLENGE_REWARD = 950006;

        /**
         * 万仙榜结算时间#结算最小间隔
         */
        int WAN_XIAN_BANG_RESET_TIME = 950007;

        /**
         * 万仙榜周一开启时间
         */
        int WAN_XIAN_BANG_MONDAY_OPEN_TIME = 950008;
        /**
         * 万仙榜挑战列表自动刷新时间
         */
        int WAN_XIAN_BANG_CHALLENGE_LIST_AUTO_REFRESH_GAP = 950009;
        /**
         * 万仙榜数据上传间隔时间
         */
        int WAN_XIAN_BANG_ATTR_UPDATE_GAP = 950010;

        int WAN_XIAN_BANG_TICKET_RELIVE_GAP = 950012;

        int WAN_XIAN_BANG_TICKET_LIMIT = 950013;
        int WAN_XIAN_BANG_TICKET_BUY_LIMIT = 950014;
        int WAN_XIAN_BANG_TICKET_BUY_COST = 950015;
        /**
         * 万仙榜每日开启时间
         */
        int WAN_XIAN_BANG_DAY_OPEN_TIME = 950011;

        int YAO_XIE_OPEN_TIME = 960001;

        int YAO_XIE_END_TIME = 960002;

        int UNION_LEADER_VX = 960003;
        /**
         * 行会默认宣言
         */
        int UNION_DEFAULT_XUAN_YAN = 960004;

        int YAO_XIE_ATTR = 960005;

        int YAO_XIE_OP_DAY = 960006;

        int YAO_XIE_BOX = 960007;

        int YAO_XIE_ATTR_DURATION = 960008;

        int ZHEN_BAO_GE_OPEN_TIME = 960009;

        int ZHEN_BAO_GE_END_TIME = 960010;

        int XIE_ZHU_TIME = 960012;

        int XIE_ZHU_LIMIT = 960013;

        /**
         * 退出行会消耗
         */
        int QUIT_UNION_REMOVE_ITEM = 960014;
        /**
         * 修改行会名消耗
         */
        int CHANGE_UNION_NAME_COST = 960016;

        /**
         * 运势抽奖道具
         */
        int YUN_SHI_CHOU_JIANG_ITEM = 1000001;

        int HOARD_COST = 1001001;

        int HOARD_EXP = 1001002;

        int SHENMO_LILIAN_REFRESH = 920002;

        /**
         * 后院邻居数量
         */
        int HOU_YUAN_LING_JU_COUNT = 1002001;
        /**
         * 后院敌人数量
         */
        int HOU_YUAN_DI_REN_COUNT = 1002002;

        int HOU_YUAN_REFRESH_HOUR = 1002003;

        int HOU_YUAN_NEIGHBOR_REFRESH_CD = 1002004;

        int HOU_YUAN_ITEM_REFRESH_COST = 1002005;

        int HOU_YUAN_NEIGHBOR_REFRESH_DAY_COUNT = 1002006;

        int HOU_YUAN_SUPER_ITEM_TIME = 1002007;

        /**
         * 后院老鼠初始疲劳值
         */
        int HOU_YUAN_RAT_PLZ = 1002008;

        int HOU_YUAN_LOG_COUNT = 1002009;
        /**
         * 福地道具数量
         */
        int HOU_YUAN_ITEM_COUNT = 1002010;
        /**
         * 一只鼠鼠偷道具成功，疲劳值-1
         */
        int HOU_YUAN_RAT_PLZ_COST = 1002011;

        int HOU_YUAN_RAT_AUTO_FREE_TIME = 1002012;

        int HOU_YUAN_RAT_AUTO_PARAM = 1002013;

        int HOU_YUAN_SUPER_ITEM_COUNT = 1002014;

        /**
         * 后院超级刷新消耗
         */
        int HOU_YUAN_SUPER_ITEM_COST = 1002016;

        /**
         * 后院超级刷新限制条件
         */
        int HOU_YUAN_SUPER_ITEM_CONDITION = 1002017;

        int ZHEN_YAO_TA_RANK_NUM = 1003001;

        int ARENA_EXTRA_REWARD = 1004002;

        int BOSS_KING_EXTRA_REWARD = 1004001;
        /**
         * 战令额外奖励 格式：活动id#购买次数#奖励boxId
         */
        int ZHAN_LING_EXTRA_REWARD = 2000002;

        /**
         * 周刊数迴单次活动奖励轮次上限
         */
        int ZHOU_KAN_WHEEL = 2000003;

        /**
         * 周刊数迴未领取奖励补发邮件id
         */
        int ZHOU_KAN_TASK_MAIL = 2000004;

        /**
         * 神魔历练次数回复时间
         */
        int SHENMO_LILIAN_COUNT_TIME_GAP = 920003;

        /**
         * 战令礼包额外奖励补发邮件id
         */
        int ZHAN_LING_PACK_EXTRA_MAIL = 2000006;

        /**
         * 装备投影解锁   索引#解锁消耗
         */
        int LINE_UP_UNLOCK_COST = 2000009;

        int ENERGY_DRINK_RECHARGE = 1005001;

        /**
         *  伙伴商店刷新保底-特权月卡专用
         *  刷新次数#必出品质
         */
        int HUO_BAN_BI_CHU_QUALITY = 2000013;

        /**
         *  伙伴商店刷新保底-特权月卡专用
         *  品质#刷新次数#必出宝箱
         */
        int HUO_BAN_BI_CHU_ITEM = 2000018;

        /**
         *  每日改名次数
         *  次数
         */
        int GAI_MING = 2000028;

        /**
         *  改名消耗道具
         *  道具ID#数量
         */
        int GAI_MING_ITEM = 2000029;

        int NAME_LENGTH = 2000030;

        /**
         * 五行山挑战次数上限
         */
        int WU_XING_SHAN_LIMIT = 2000031;

        /**
         * 五行山挑战时间上限
         */
        int WU_XING_SHAN_END_TIME = 2000033;

        /**
         * 获取奖励需要拆分的活动type
         */
        int CHAI_FEN_JIANG_LI_TYPE = 2000032;

        /**
         * 仙盟宝箱普通宝箱列表上限
         */
        int UNION_CHEXT_NORMAL_LIMIT = 960020;

        /**
         * 仙盟宝箱稀有宝箱列表上限
         */
        int UNION_CHEXT_RARE_LIMIT = 960021;

        /**
         * 珍宝阁最低价后强制砍价区间
         */
        int ZHEN_BAO_GE_YI_JIA_AFTER_MIN_PRICE = 960022;


        /**
         * 珍宝阁砍价比例参数
         */
        int ZHEN_BAO_GE_KAN_JIA_PARAM = 960025;

        /**
         * 珍宝阁砍价人数比例
         */
        int ZHEN_BAO_GE_KAN_JIA_MEMBER_COUNT_RATE = 960026;

        /**
         * 暴击增伤倍率
         */
        int BAO_JI_ZENG_SHANG_BEI_LV = 2000016;

        int MAIL_AUTO_DELETE_DAY = 100007;

        /**
         *  单场战斗最大轮次
         */
        int BATTLE_MAX_ROUND = 2000019;

        /**
         * 伙伴重置消耗道具id
         */
        int HUO_BAN_REST_COST_ITEM_ID = 910007;

        /**
         * 建筑升级买断-每秒需要消耗的道具
         */
        int WORK_LEVEL_UP_BUYOUT = 1006501;

        /**
         * 建筑生产买断-每秒需要消耗的道具
         */
        int WORK_PRODUCE_BUYOUT = 1006502;

        /**
         * 建筑协助-单次减少百分比时间
         */
        int WORK_XIE_ZHU_PERCENT_REDUCE = 1006503;

        /**
         * 建筑协助-单次最小缩短时间
         */
        int WORK_XIE_ZHU_MIN_REDUCE = 1006504;

        /**
         * 建筑行会协助-建筑类型
         * 根据该类型建筑等级获得协助上限
         */
        int WORK_XIE_ZHU_BUILD = 1006505;

        /**
         * 玩家首次改造，单个产出所需时间
         */
        int WORK_FIRST_TRANS_ONCE_NEED_TIME = 1006507;

        /**
         * 同一时间可升级的最大建筑数量
         */
        int WORK_LEVEL_UP_MAX = 1006508;

        /**
         * 创号可解锁建筑id集合
         */
        int WORK_INIT = 1006509;

        /**
         * 建筑改造买断-每秒需要消耗的道具
         */
        int WORK_TRANS_BUYOUT = 1006511;

        /**
         * 默认聊天气泡
         * fashionId
         */
        int CREATE_DEFAULT_CHAT_BUBBLE = 1080002;

        /**
         * 系统后院头像
         * 名称#头像id
         */
        int SYSTEM_HOU_YUAN = 1002015;

        /**
         * 联盟宝箱钥匙道具ID#经验道具ID
         */
        int YAO_SHI_JING_YAN_ITEM = 961003;

        /**
         * 超度背包最大容量
         */
        int CHAODU_BAG_MAX_SIZE = 1006601;

        /**
         * 自然资源-存储上限(主角)
         */
        int NATURAL_RESOURCES_BAG_MAX = 1100001;
        /**
         * 自然资源-采集收获速度(主角)
         */
        int NATURAL_RESOURCES_HARVEST_SPEED = 1100002;
        /**
         * 自然资源-单次采集伤害(主角)
         */
        int NATURAL_RESOURCES_HARVEST_HURT = 1100003;
        /**
         * 自然资源-刷新limit(主角)
         */
        int NATURAL_RESOURCES_REFRESH_LIMIT = 1100006;
        /**
         * 自然资源采集道具id 原木#小麦
         */
        int NATURAL_RESOURCES_ITEM_ID = 1100007;

        /**
         * 自然资源 -- 仙草圃（麦子）资源池上限
         */
        int NATURAL_RESOURCES_POOL_WHEAT_REBORN_MAX = 1100008;

        /**
         * 自然资源 -- 伐木林资源池上限
         */
        int NATURAL_RESOURCES_POOL_WOOD_REBORN_MAX = 1100009;

        /**
         * 自然资源 -- 仙草圃（麦子）恢复速度 恢复量#恢复所需时间
         */
        int NATURAL_RESOURCES_POOL_WHEAT_REBORN = 1100010;

        /**
         * 自然资源 -- 伐木林恢复速度 恢复量#恢复所需时间
         */
        int NATURAL_RESOURCES_POOL_WOOD_REBORN = 1100011;

        /**
         * 委任生产道具id 木板#包子
         */
        int WEIREN_PRODUCTION_ITEM_ID = 1200002;

        /**
         * 公共 cd
         */
        int ZT_COMMON_CD = 10001;

        /**
         * 离线奖励离线时长上限, 单位分钟
         */
        int LI_XIAN_TIME_LIMIT = 11201;

        /**
         * 自选抽卡达到x次后增加自选概率
         */
        int OPTIONAL_DRAW_NEED_COUNT = 11500;

        /**
         * 自选每次抽卡增加概率
         */
        int OPTIONAL_DRAW_INCREASE_PROBABILITY = 11501;

        /**
         * 自选抽卡消耗
         */
        int OPTIONAL_DRAW_COST = 11502;

        /**
         * 自选抽卡全服记录最大值
         */
        int OPTIONAL_DRAW_SERVICE_MAX_COUNT = 11503;

        int STONE_PUT_ON_COST = 11600;

        /**
         * 对应的装备品质，能够开启锻造
         */
        int DUAN_ZAO_QUALITY = 11800;

        /**
         * 巅峰等级 普通等级满级#巅峰等级提升几级给巅峰点#每次获得的巅峰点#巅峰点上限
         */
        int DAN_YAO_PEAK_LEVEL = 11900;

        /**
         * 服务内可创建的最大角色数量（小于等于），超出后不可创建
         */
        int MAX_CREATE_ROLE_LIMIT = 2000042;
    }

    interface SpecialGlobalId {
        /**
         * 名字正则
         */
        int NAME_REGX = 1001;
    }
}
