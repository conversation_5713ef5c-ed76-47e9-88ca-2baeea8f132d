package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_zhenyingtask_gh")
public class ZhenYingTaskGHConfig extends AbstractConfigData {

    private int id;

    /**
     * 道具id
     */
    private int itemid;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 该道具对应的任务
     */
    private int itemtaskid;
}
