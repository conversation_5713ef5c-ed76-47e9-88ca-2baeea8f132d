package com.sh.game.common.communication.msg.system.tianchi;

import com.sh.game.common.communication.msg.system.tianchi.bean.TianChiInfoBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 天池信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTianChiInfoMessage extends AbsProtostuffMessage {
  /**
   * 每个大陆的信息
   */
  private List<TianChiInfoBean> infos = new ArrayList<>();

  @Override
  public int getId() {
    return 305002;
  }

  public void setInfos(List<TianChiInfoBean> infos) {
    this.infos = infos;
  }

  public List<TianChiInfoBean> getInfos() {
    return this.infos;
  }
}
