package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 视野广播玩家性别发生改变消息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleSexChangeViewMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long rId;

  /**
   * 玩家当前性别
   */
  private int sex;

  @Override
  public int getId() {
    return 67058;
  }

  public void setRId(long rId) {
    this.rId = rId;
  }

  public long getRId() {
    return this.rId;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }
}
