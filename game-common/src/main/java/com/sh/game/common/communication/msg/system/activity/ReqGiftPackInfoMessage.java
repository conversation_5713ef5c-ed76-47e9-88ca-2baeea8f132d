package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求礼包信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqGiftPackInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动类型
   */
  private int actType;

  @Override
  public int getId() {
    return 4192;
  }

  public void setActType(int actType) {
    this.actType = actType;
  }

  public int getActType() {
    return this.actType;
  }
}
