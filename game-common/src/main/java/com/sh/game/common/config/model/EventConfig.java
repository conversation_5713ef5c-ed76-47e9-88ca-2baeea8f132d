package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * 地图中的事件配置
 */
@Getter
@Setter
@ConfigData(file = "cfg_events")
public class EventConfig extends AbstractConfigData {

    private int id;

    private String name;

    private int type;

    private String param;

    private int triggertype;

    private String triggerparam;

    private int priority;

    private int range;

    private int model;

    private String extraParam;

    /**
     * 可触发次数，不填无限次
     */
    private int triggerTimes;
}
