package com.sh.game.common.communication.msg.system.brawling.bean;

import java.lang.String;

public class BrawlingBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * 玩家名字
   */
  private String name = new String();

  /**
   * 玩家得分
   */
  private int score;

  /**
   * 玩家击杀数
   */
  private int killNum;

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setKillNum(int killNum) {
    this.killNum = killNum;
  }

  public int getKillNum() {
    return this.killNum;
  }
}
