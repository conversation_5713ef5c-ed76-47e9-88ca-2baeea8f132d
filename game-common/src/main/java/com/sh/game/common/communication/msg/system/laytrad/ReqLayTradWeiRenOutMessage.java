package com.sh.game.common.communication.msg.system.laytrad;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求经营委任移除
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqLayTradWeiRenOutMessage extends AbsProtostuffMessage {
  private int weirenLbId;

  private int weirenHeroId;

  @Override
  public int getId() {
    return 500004;
  }

  public void setWeirenLbId(int weirenLbId) {
    this.weirenLbId = weirenLbId;
  }

  public int getWeirenLbId() {
    return this.weirenLbId;
  }

  public void setWeirenHeroId(int weirenHeroId) {
    this.weirenHeroId = weirenHeroId;
  }

  public int getWeirenHeroId() {
    return this.weirenHeroId;
  }
}
