package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求鉴定
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqIdentifyEquipMessage extends AbsProtostuffMessage {
  /**
   * 装备uid
   */
  private long uid;

  /**
   * 锁定的属性位置
   */
  private List<Integer> lockAttrPos = new ArrayList<>();

  /**
   * 锁定的buff位置
   */
  private List<Integer> lockBuffPos = new ArrayList<>();

  /**
   * 对应表里的type
   */
  private int type;

  @Override
  public int getId() {
    return 183001;
  }

  public void setUid(long uid) {
    this.uid = uid;
  }

  public long getUid() {
    return this.uid;
  }

  public void setLockAttrPos(List<Integer> lockAttrPos) {
    this.lockAttrPos = lockAttrPos;
  }

  public List<Integer> getLockAttrPos() {
    return this.lockAttrPos;
  }

  public void setLockBuffPos(List<Integer> lockBuffPos) {
    this.lockBuffPos = lockBuffPos;
  }

  public List<Integer> getLockBuffPos() {
    return this.lockBuffPos;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
