package com.sh.game.common.entity.backpack.storage;

import com.sh.game.common.config.model.StorageConfig;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.item.ItemCuiQuInfo;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.condition.ConditionUtil;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 背包小类
 */
@Getter
@Setter
public class Storage {

    @Tag(1)
    private int unlocked = 0;

    /**
     * 道具信息 <key:道具index,value:道具>
     */
    @Tag(2)
    private Map<Integer, Item> data = new HashMap<>();

    /**
     * 装备位锻造 <key:装备位置,value:锻造id>
     */
    @Tag(3)
    private Map<Integer, Integer> equipDuanZao = new HashMap<>();

    /**
     * 装备萃取信息
     */
    @Tag(4)
    private ItemCuiQuInfo cuiQuInfo = new ItemCuiQuInfo();


    @Exclude
    private BackpackConst.Place place;

    /**
     * 当前背包格子类 不清楚为啥用map 这种背包小类应该只有一个啊
     */
    @Exclude
    private static final Map<Integer, StorageGrid> gridMap = new HashMap<>();

    /**
     * 获取道具
     *
     * @param index
     * @return
     */
    public Item fetch(int index) {
        Item item = data.get(index);
        if (item != null) {
            item.setIndex(index);
        }

        return item;
    }

    /**
     * 替换道具 同时返回变化
     *
     * @param index
     * @param item
     * @param logAction
     * @return
     */
    public ItemChange update(int index, Item item, int logAction) {
        Item origin = data.get(index);
        data.put(index, item);

        ItemChange change = new ItemChange();
        change.setPlace(place);
        change.setIndex(index);
        change.setOItem(origin);
        change.setNItem(item);
        change.setLogAction(logAction);

        return change;
    }

    /**
     * 获取当前背包格子数
     *
     * @param avatar
     * @return
     */
    public int capacity(IAvatar avatar) {
        StorageGrid grid = this.grid();
        int n = 0;
        for (StorageConfig config : grid.getInit()) {
            if (ConditionUtil.validate(avatar, config.getCondition())) {
                n += config.getCount();
            }
        }
        return n + findUnlockCount();
    }

    /**
     * 获取格子类
     *
     * @return
     */
    public StorageGrid grid() {
        StorageGrid grid = gridMap.get(place.getWhere());
        if (grid == null) {
            grid = new StorageGrid(place.getWhere());
            gridMap.put(place.getWhere(), grid);
        }

        return grid;
    }

    /**
     * 常见的那个背包不可解锁
     *
     * @return 解锁的背包格子数量
     */
    public int findUnlockCount() {
        return place == BackpackConst.Place.BACKPACK ? 0 : unlocked;
    }
}
