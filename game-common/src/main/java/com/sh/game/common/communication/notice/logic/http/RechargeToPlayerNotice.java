package com.sh.game.common.communication.notice.logic.http;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 *  充值转发到玩家
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/1/25.
 */
@Notice
@Setter
@Getter
public class RechargeToPlayerNotice extends ProcessNotice {

    /**
     * 玩家id
     */
    private long rid;
    /**
     * 充值配置id
     */
    private int rechargeConfigId;
    /**
     * 充值来源
     */
    private int way;
    /**
     * 订单id
     */
    private long orderId;
}
