package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 根据平台设置同一ip最多同时在线多少玩家
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqSetSameIpUserLoginCountMessage extends AbsProtostuffMessage {
  /**
   * 同时在线限制数量
   */
  private int maxCount;

  @Override
  public int getId() {
    return 43055;
  }

  public void setMaxCount(int maxCount) {
    this.maxCount = maxCount;
  }

  public int getMaxCount() {
    return this.maxCount;
  }
}
