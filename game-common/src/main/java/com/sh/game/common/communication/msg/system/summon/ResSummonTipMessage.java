package com.sh.game.common.communication.msg.system.summon;

import com.sh.game.common.communication.msg.abc.bean.RoleBriefBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 发送召集信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSummonTipMessage extends AbsProtostuffMessage {
  /**
   * 召唤者信息
   */
  private RoleBriefBean summoner = new RoleBriefBean();

  /**
   * 唯一id 用来响应传送
   */
  private long summonId;

  /**
   * 召唤类型 1队伍 2行会
   */
  private int type;

  /**
   * 地图id
   */
  private int map;

  /**
   * 过期时间 时间戳
   */
  private int expire;

  @Override
  public int getId() {
    return 194011;
  }

  public void setSummoner(RoleBriefBean summoner) {
    this.summoner = summoner;
  }

  public RoleBriefBean getSummoner() {
    return this.summoner;
  }

  public void setSummonId(long summonId) {
    this.summonId = summonId;
  }

  public long getSummonId() {
    return this.summonId;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setMap(int map) {
    this.map = map;
  }

  public int getMap() {
    return this.map;
  }

  public void setExpire(int expire) {
    this.expire = expire;
  }

  public int getExpire() {
    return this.expire;
  }
}
