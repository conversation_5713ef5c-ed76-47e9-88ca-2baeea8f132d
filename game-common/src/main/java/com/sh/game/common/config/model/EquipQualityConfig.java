package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @data 2020/8/17 20:28
 */
@Getter
@Setter
@ConfigData(file = "cfg_equip_quality", keys = "rank")
public class EquipQualityConfig extends AbstractConfigData {
    /**
     * 品质等级
     */
    private int rank;

    /**
     * 升星上限
     */
    private int star_num;

    /**
     * 淬炼等级上限
     */
    private int level;

    /**
     * 每日获取上限
     */
    private int daylimit;

    /**
     * 觉醒上限
     */
    private int awake_max;

    /**
     * 祝福上限
     */
    private int blessing;

}
