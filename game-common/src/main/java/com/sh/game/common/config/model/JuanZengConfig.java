package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/15 13:34
 */
@Getter
@Setter
@ConfigData(file = "cfg_juanzeng")
public class JuanZengConfig extends AbstractConfigData {

    private int id;

    private int rank;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 上榜所需捐赠灵符数
     */
    private int num;

    private int mail;
}
