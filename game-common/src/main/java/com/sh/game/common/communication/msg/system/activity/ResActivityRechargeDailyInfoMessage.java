package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.communication.msg.system.activity.bean.ActivityStatusBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResActivityRechargeDailyInfoMessage extends AbsProtostuffMessage {
  private int recharged;

  private List<ActivityStatusBean> status = new ArrayList<>();

  @Override
  public int getId() {
    return 4123;
  }

  public void setRecharged(int recharged) {
    this.recharged = recharged;
  }

  public int getRecharged() {
    return this.recharged;
  }

  public void setStatus(List<ActivityStatusBean> status) {
    this.status = status;
  }

  public List<ActivityStatusBean> getStatus() {
    return this.status;
  }
}
