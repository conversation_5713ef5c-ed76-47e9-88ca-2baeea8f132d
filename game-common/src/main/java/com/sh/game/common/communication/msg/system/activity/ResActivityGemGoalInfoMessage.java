package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.communication.msg.system.task.bean.TaskDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResActivityGemGoalInfoMessage extends AbsProtostuffMessage {
  private List<TaskDataBean> goals = new ArrayList<>();

  @Override
  public int getId() {
    return 4113;
  }

  public void setGoals(List<TaskDataBean> goals) {
    this.goals = goals;
  }

  public List<TaskDataBean> getGoals() {
    return this.goals;
  }
}
