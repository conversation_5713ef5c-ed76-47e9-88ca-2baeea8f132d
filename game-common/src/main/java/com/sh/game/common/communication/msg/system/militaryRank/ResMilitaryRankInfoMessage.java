package com.sh.game.common.communication.msg.system.militaryRank;

import com.sh.game.common.communication.msg.system.task.bean.TaskDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回军衔信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMilitaryRankInfoMessage extends AbsProtostuffMessage {
  /**
   * 军衔等级
   */
  private int level;

  /**
   * 正在军衔任务信息
   */
  private List<TaskDataBean> taskBean = new ArrayList<>();

  /**
   * 是否领取过每日俸禄
   */
  private boolean isRequired;

  /**
   * 是否升级
   */
  private boolean isLevelUp;

  @Override
  public int getId() {
    return 366002;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setTaskBean(List<TaskDataBean> taskBean) {
    this.taskBean = taskBean;
  }

  public List<TaskDataBean> getTaskBean() {
    return this.taskBean;
  }

  public void setIsRequired(boolean isRequired) {
    this.isRequired = isRequired;
  }

  public boolean getIsRequired() {
    return this.isRequired;
  }

  public void setIsLevelUp(boolean isLevelUp) {
    this.isLevelUp = isLevelUp;
  }

  public boolean getIsLevelUp() {
    return this.isLevelUp;
  }
}
