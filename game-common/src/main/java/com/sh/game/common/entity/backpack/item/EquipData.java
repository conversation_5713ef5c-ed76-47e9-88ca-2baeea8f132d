package com.sh.game.common.entity.backpack.item;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 装备属性
 */
@Getter
@Setter
public class EquipData {

    /**
     * 耐久
     */
    @Tag(1)
    @Deprecated
    private int durable;

    /**
     * 星级
     */
    @Tag(2)
    @Deprecated
    private int starLevel;

    /**
     * 固星
     */
    @Tag(3)
    @Deprecated
    private int starLocked;

    /**
     * 星级经验
     */
    @Tag(4)
    @Deprecated
    private int starExp;

    /**
     * 超属性
     */
    @Tag(8)
    @Deprecated
    private EquipAttribute superAttribute;

//    /**
//     * 旧的鉴定属性
//     */
//    @Tag(9)
//    private EquipAttribute oldIdentifyAttribute;

    /**
     * 鉴定属性
     */
    @Tag(10)
    @Deprecated
    private EquipAttribute xilianAttr;

    /**
     * 项链祝福
     */
    @Tag(11)
    @Deprecated
    private int zhufu;

    /**
     * 祝福属性
     */
    @Tag(12)
    @Deprecated
    private EquipAttribute zhufuAttribute;

    /**
     * 融合的特戒id
     */
    @Tag(13)
    @Deprecated
    private int tjId;

    /**
     * 魔血石容量已使用
     */
    @Tag(14)
    @Deprecated
    private int magicBloodStoneValue;

    /**
     * 融合的魔器id
     */
    @Tag(15)
    @Deprecated
    private int demonEquipId;


    /**
     * 装备来源类型
     */
    @Tag(16)
    private int originInfoType;

    /*
     * 装备来源信息 0:角色名、1:地图名、2:怪物名
     */
    @Tag(17)
    private List<String> originInfo = new ArrayList<>();

//    /**
//     * 觉醒等级
//     */
//    @Tag(18)
//    private int juexingLevel;

//    /**
//     * 神铸等级
//     */
//    @Tag(19)
//    private int shenzhuLevel;

    /**
     * 宝石ID
     */
    @Tag(20)
    @Deprecated
    private int gemId;

    /**
     * 资质属性
     */
    @Deprecated
    @Tag(21)
    private EquipAttribute aptitudeAttribute;

    /**
     * 资质值
     */
    @Tag(22)
    @Deprecated
    private int aptitudeValue;

    /**
     * 对应的资质表id
     */
    @Tag(23)
    @Deprecated
    private int aptitudeCfgId;

    /**
     * 投保
     */
    @Tag(24)
    @Deprecated
    private int toubao;


    /**
     * 继承的套装id
     */
    @Tag(25)
    @Deprecated
    private List<Integer> extendSuitIdList = new ArrayList<>();

    /**
     * 天命缔造属性赋予比例,10000封顶
     */
    @Tag(26)
    @Deprecated
    private int tmdzAddRate;

    /**
     * 缔造玩法的属性
     */
    @Tag(27)
    @Deprecated
    private EquipAttribute diZaoAttribute;

    /**
     * 天命缔造醒命属性id{@link com.sh.game.common.config.model.DestinynSpecialConfig}
     */
    @Tag(28)
    @Deprecated
    private int xingMingId;

    /**
     * {@link com.sh.game.common.config.model.DestinynConfig} 的id
     */
    @Tag(29)
    @Deprecated
    private int tmdzCfgId;

    /**
     * 新的鉴定属性
     */
    @Tag(30)
    @Deprecated
    private IdentifyAttribute identifyAttribute = new IdentifyAttribute();

    /**
     * 是否可封印
     */
    @Tag(31)
    @Deprecated
    private boolean fengyin = false;

    /**
     * 是否生效(目前就兽决内丹使用）
     * @return
     */
    @Tag(32)
    @Deprecated
    private boolean bufferEnabled = true;

    /**
     * 法宝额外属性万分比
     *
     */
    @Tag(33)
    @Deprecated
    private int magicWeaponAttributeRate;

    /**
     * 融合的法宝
     * key: cfg_equip_fabaokong配置id
     * value: 法宝
     */
    @Tag(34)
    @Deprecated
    private Map<Integer, MagicWeapon> magicWeaponMap = new HashMap<>();

    /**
     * 法宝携带的buffId
     */
    @Tag(35)
    @Deprecated
    private int magicWeaponBuffId;

    /**
     * 兽决强化等级
     */
    @Tag(36)
    @Deprecated
    private int shouJueLevel;

    /**
     * 泰坦附魂
     */
    @Tag(37)
    @Deprecated
    private int tiTanGemId;

    /**
     * 龙魂融合
     * key:     龙魂类型
     * value:   龙魂宝石id
     */
    @Tag(38)
    @Deprecated
    private Map<Integer, Integer> longhunGem = new HashMap<>();

    /**
     * 淬炼等级
     */
    @Tag(39)
    @Deprecated
    private int refinementLevel;

    /**
     * 缴械时间戳(ms)
     */
    @Tag(40)
    @Deprecated
    private long disarmTime;

    /**
     * 淬炼额外成功率
     */
    @Tag(41)
    @Deprecated
    private int refinementRate;

    /**
     * 宠物装备品质万分比
     */
    @Tag(42)
    @Deprecated
    private int petEquipRate;

    /**
     * 宠物装备获得buff
     */
    @Tag(43)
    @Deprecated
    private int petBuff;

    /**
     * 混沌宝石
     */
    @Tag(44)
    @Deprecated
    private int chaoticGemId;

    /**
     * 龙魂融合
     * key:     龙魂类型
     * value:   龙魂宝石id
     */
    @Tag(45)
    @Deprecated
    private Map<Integer, Integer> xiahunGem = new HashMap<>();

    /**
     * 混沌赋灵等级
     */
    @Tag(46)
    @Deprecated
    private int chaoticPowerLevel;

    /**
     * 神兵铸魂等级
     */
    @Tag(47)
    @Deprecated
    private int shenBingZhuHunLevel = 1;

    /**
     * 命格等级
     */
    @Tag(48)
    @Deprecated
    private int birthChartLevel;

    /**
     * 装备等级
     */
    @Tag(49)
    private int level;

    /**
     * 拆家属性
     * 替代资质属性
     */
    @Tag(50)
    @Deprecated
    private EquipAttribute chaiJiaAttribute = new EquipAttribute();

    @Tag(51)
    private int attrcreatId;

    /**
     * 超度属性
     */
    @Tag(52)
    @Deprecated
    private EquipAttribute chaoDuAttribute = new EquipAttribute();


    @Tag(53)
    private EquipAttribute attribute = new EquipAttribute();

    @Tag(54)
    private EquipAttribute ciZuiAttribute = new EquipAttribute();

    public boolean hasXilian() {
        return identifyAttribute != null && (identifyAttribute.getAttributes().size() > 0 || identifyAttribute.getBuffs().size() > 0);
    }
}
