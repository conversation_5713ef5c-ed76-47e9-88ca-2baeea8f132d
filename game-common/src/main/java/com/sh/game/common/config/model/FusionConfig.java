package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@ConfigData(file = "cfg_ronglian_set")
public class FusionConfig extends AbstractConfigData {

    /**
     * 道具id
     */
    private int id;

    /**
     * 道具名字
     */
    private String name;

    /**
     * 熔炼消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rongliancost;

    /**
     * 熔炼奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> ronglianreward;

    /**
     * 兑换消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> honour;

}
