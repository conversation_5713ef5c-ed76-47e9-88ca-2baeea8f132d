package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/9 17:55
 */
@Setter
@Getter
@ConfigData(file = "cfg_destinyn_special",keys = "attrId")
public class DestinynSpecialConfig extends AbstractConfigData {

    private int attrId;

    private int group;

    private int level;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    private int buffId;

    /**
     * 1 表示不可升级
     */
    private int maxLevel;

    /**
     * 消耗道具
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemId;

    /**
     * 升级成功概率
     */
    private int probability;

    private int announce;
}
