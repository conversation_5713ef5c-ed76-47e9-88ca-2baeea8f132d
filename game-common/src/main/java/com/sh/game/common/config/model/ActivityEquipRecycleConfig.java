package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@ConfigData(file="cfg_equip_recycle")
public class ActivityEquipRecycleConfig extends AbstractConfigData {

    private int id;

    private int activityID;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    private int item;

    private int limit;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

}
