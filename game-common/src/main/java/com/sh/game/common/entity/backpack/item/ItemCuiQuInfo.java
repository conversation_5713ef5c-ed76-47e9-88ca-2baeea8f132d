package com.sh.game.common.entity.backpack.item;

import io.protostuff.Tag;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * description:
 * create: 2025/6/19
 * author: chen bin
 */
@Data
public class ItemCuiQuInfo {

    /**
     * 已经萃取过的 cuiQuId 列表
     */
    @Tag(1)
    private Set<Integer> cuiQuList = new HashSet<>();

    /**
     * 已穿戴萃取的 cuiQuId
     * key: 部位, value: cuiQuId
     */
    @Tag(2)
    private Map<Integer, Integer> wearCuiQu = new HashMap<>();
}
