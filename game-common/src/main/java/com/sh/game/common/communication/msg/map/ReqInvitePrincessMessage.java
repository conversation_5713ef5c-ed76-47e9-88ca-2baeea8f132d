package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求邀请公主
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqInvitePrincessMessage extends AbsProtostuffMessage {
  /**
   * 公主唯一怪物id
   */
  private long mid;

  @Override
  public int getId() {
    return 193001;
  }

  public void setMid(long mid) {
    this.mid = mid;
  }

  public long getMid() {
    return this.mid;
  }
}
