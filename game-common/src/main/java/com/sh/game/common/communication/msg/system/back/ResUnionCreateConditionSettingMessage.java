package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 返回行会创建限制设置信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionCreateConditionSettingMessage extends AbsProtostuffMessage {
  /**
   * condition格式内容
   */
  private String value = new String();

  @Override
  public int getId() {
    return 43182;
  }

  public void setValue(String value) {
    this.value = value;
  }

  public String getValue() {
    return this.value;
  }
}
