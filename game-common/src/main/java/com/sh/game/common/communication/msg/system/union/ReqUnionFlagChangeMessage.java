package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求更换行会旗帜
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqUnionFlagChangeMessage extends AbsProtostuffMessage {
  /**
   * union_flag表id
   */
  private int cid;

  @Override
  public int getId() {
    return 23631;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
