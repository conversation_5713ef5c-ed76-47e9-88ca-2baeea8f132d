package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

import java.util.Map;

/**
 * @Description: 超度次数
 * <AUTHOR>
 * @Date 2024/8/14 19:57
 */
public class ChaoDuCountConditionValidator extends IConditionValidatorDefault {

    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        int len = params.length;
        if (len < 2) {
            return false;
        }

        return avatar.findChaoDuCount() >= params[1];
    }
}
