package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 开服礼包（限时礼包）
 */

@Getter
@Setter
@ConfigData(file="cfg_salegift")
public class ActivitySaleBagConfig extends AbstractConfigData {

    /**
     * 礼包编号
     */
    private int id;

    /**
     * 活动编号
     */
    private int activityID;

    /**
     * 购买条件
     */
    @ConfigField(converter= JinHaoAndYuHaoListConverter.class)
    private List<int[]> condition;

    /**
     * 购买花费
     */
    @ConfigField(converter= JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 礼包编号
     */
    private int item;


    /**
     * 购买的消耗
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> discount;

}
