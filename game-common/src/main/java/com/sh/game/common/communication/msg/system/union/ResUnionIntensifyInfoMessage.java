package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 行会属性强化信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionIntensifyInfoMessage extends AbsProtostuffMessage {
  /**
   * 强化过期时间（时间戳）
   */
  private int intensifyExpire;

  /**
   * 下次可强化时间(时间戳)
   */
  private int intensifyAvailable;

  @Override
  public int getId() {
    return 23203;
  }

  public void setIntensifyExpire(int intensifyExpire) {
    this.intensifyExpire = intensifyExpire;
  }

  public int getIntensifyExpire() {
    return this.intensifyExpire;
  }

  public void setIntensifyAvailable(int intensifyAvailable) {
    this.intensifyAvailable = intensifyAvailable;
  }

  public int getIntensifyAvailable() {
    return this.intensifyAvailable;
  }
}
