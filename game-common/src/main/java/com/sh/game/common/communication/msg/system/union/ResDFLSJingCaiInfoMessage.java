package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.communication.msg.system.union.bean.JingCaiInfoBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回玩家竞猜信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDFLSJingCaiInfoMessage extends AbsProtostuffMessage {
  /**
   * 竞猜信息
   */
  private List<JingCaiInfoBean> infos = new ArrayList<>();

  /**
   * 竞猜总数
   */
  private int totalCount;

  /**
   * 竞猜成功数
   */
  private int successCount;

  @Override
  public int getId() {
    return 23615;
  }

  public void setInfos(List<JingCaiInfoBean> infos) {
    this.infos = infos;
  }

  public List<JingCaiInfoBean> getInfos() {
    return this.infos;
  }

  public void setTotalCount(int totalCount) {
    this.totalCount = totalCount;
  }

  public int getTotalCount() {
    return this.totalCount;
  }

  public void setSuccessCount(int successCount) {
    this.successCount = successCount;
  }

  public int getSuccessCount() {
    return this.successCount;
  }
}
