package com.sh.game.common.constant;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https//:github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 17/6/23 13:35
 * desc  : 属性常量
 * Copyright(©) 2017 by xiaomo.
 * <AUTHOR>
 */
public interface AttributeConst {

    interface AttributeType {
        enum Role implements AttributeType {
            /**
             * 玩家基础属性（等级）
             */
            BASIC,
            /**
             * GM 调整的属性
             */
            GM,
            /**
             * 装备
             */
            EQUIP,
            /**
             * 装备套装
             */
            SUIT_EQUIP,
            /**
             * 伙伴
             */
            HUO_BAN,
            /**
             * 转生
             */
            ZHUANSHENG,

            /**
             * 建筑
             */
            ARCHITECTURE,

            /**
             * 坐骑
             */
            MOUNT,
            /**
             * 宝石
             */
            GEM,
            /**
             * 丹药属性
             */
            DANYAO_ATTR,
            /**
             * 宠物
             */
            ZT_PET,
            ;
        }

        enum Cate implements AttributeType {
            BASE,
            BUFF,
            BUFF_RATIO,
            INSPIRE,
            SERVANT_GROW,
            PK
        }
    }
}
