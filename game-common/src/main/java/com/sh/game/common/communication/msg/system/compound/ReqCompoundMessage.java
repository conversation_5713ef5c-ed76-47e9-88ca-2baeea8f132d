package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求合成道具
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqCompoundMessage extends AbsProtostuffMessage {
  /**
   * cfg_compose配置id
   */
  private int configId;

  /**
   * 合成数量
   */
  private int mergeCount;

  @Override
  public int getId() {
    return 31001;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setMergeCount(int mergeCount) {
    this.mergeCount = mergeCount;
  }

  public int getMergeCount() {
    return this.mergeCount;
  }
}
