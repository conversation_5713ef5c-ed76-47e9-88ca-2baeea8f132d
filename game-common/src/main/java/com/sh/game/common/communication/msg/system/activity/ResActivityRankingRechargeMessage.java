package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.communication.msg.abc.bean.CommonRankingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回累充排行活动信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResActivityRankingRechargeMessage extends AbsProtostuffMessage {
  /**
   * 排名信息
   */
  private List<CommonRankingBean> rankings = new ArrayList<>();

  /**
   * 自己的积分
   */
  private int selfScore;

  /**
   * 自己的排名 0未上榜
   */
  private int selfRanking;

  @Override
  public int getId() {
    return 4190;
  }

  public void setRankings(List<CommonRankingBean> rankings) {
    this.rankings = rankings;
  }

  public List<CommonRankingBean> getRankings() {
    return this.rankings;
  }

  public void setSelfScore(int selfScore) {
    this.selfScore = selfScore;
  }

  public int getSelfScore() {
    return this.selfScore;
  }

  public void setSelfRanking(int selfRanking) {
    this.selfRanking = selfRanking;
  }

  public int getSelfRanking() {
    return this.selfRanking;
  }
}
