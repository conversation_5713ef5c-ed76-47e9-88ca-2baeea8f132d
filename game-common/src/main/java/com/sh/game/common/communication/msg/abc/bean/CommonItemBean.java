package com.sh.game.common.communication.msg.abc.bean;

import java.lang.Integer;
import java.util.ArrayList;
import java.util.List;

public class CommonItemBean {
  /**
   * 唯一id
   */
  private long uid;

  /**
   * 道具配置id
   */
  private int itemId;

  /**
   * 数量
   */
  private int count;

  /**
   * 过期时间
   */
  private int expire;

  /**
   * 已经使用的次数
   */
  private int uTimes;

  /**
   * 道具获得时间
   */
  private int time;

  /**
   * 道具参数
   */
  private List<Integer> params = new ArrayList<>();

  /**
   * 装备属性（null表示没有）
   */
  private EquipDataBean equipData = new EquipDataBean();

  /**
   * 固化数据
   */
  private ItemImmobilization immobilization = new ItemImmobilization();

  /**
   * 来源
   */
  private ItemFromBean from = new ItemFromBean();

  public void setUid(long uid) {
    this.uid = uid;
  }

  public long getUid() {
    return this.uid;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setExpire(int expire) {
    this.expire = expire;
  }

  public int getExpire() {
    return this.expire;
  }

  public void setUTimes(int uTimes) {
    this.uTimes = uTimes;
  }

  public int getUTimes() {
    return this.uTimes;
  }

  public void setTime(int time) {
    this.time = time;
  }

  public int getTime() {
    return this.time;
  }

  public void setParams(List<Integer> params) {
    this.params = params;
  }

  public List<Integer> getParams() {
    return this.params;
  }

  public void setEquipData(EquipDataBean equipData) {
    this.equipData = equipData;
  }

  public EquipDataBean getEquipData() {
    return this.equipData;
  }

  public void setImmobilization(ItemImmobilization immobilization) {
    this.immobilization = immobilization;
  }

  public ItemImmobilization getImmobilization() {
    return this.immobilization;
  }

  public void setFrom(ItemFromBean from) {
    this.from = from;
  }

  public ItemFromBean getFrom() {
    return this.from;
  }
}
