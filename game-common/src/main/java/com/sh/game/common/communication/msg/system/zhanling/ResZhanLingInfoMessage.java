package com.sh.game.common.communication.msg.system.zhanling;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回战令信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZhanLingInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动id
   */
  private int activityId;

  /**
   * 战令数量
   */
  private int integral;

  /**
   * 是否解锁高级
   */
  private boolean unlocked;

  /**
   * 战令普通奖励已领取集合
   */
  private List<Integer> receiveCidList = new ArrayList<>();

  /**
   * 战令高级奖励已领取集合
   */
  private List<Integer> receiveBetterCidList = new ArrayList<>();

  @Override
  public int getId() {
    return 402004;
  }

  public void setActivityId(int activityId) {
    this.activityId = activityId;
  }

  public int getActivityId() {
    return this.activityId;
  }

  public void setIntegral(int integral) {
    this.integral = integral;
  }

  public int getIntegral() {
    return this.integral;
  }

  public void setUnlocked(boolean unlocked) {
    this.unlocked = unlocked;
  }

  public boolean getUnlocked() {
    return this.unlocked;
  }

  public void setReceiveCidList(List<Integer> receiveCidList) {
    this.receiveCidList = receiveCidList;
  }

  public List<Integer> getReceiveCidList() {
    return this.receiveCidList;
  }

  public void setReceiveBetterCidList(List<Integer> receiveBetterCidList) {
    this.receiveBetterCidList = receiveBetterCidList;
  }

  public List<Integer> getReceiveBetterCidList() {
    return this.receiveBetterCidList;
  }
}
