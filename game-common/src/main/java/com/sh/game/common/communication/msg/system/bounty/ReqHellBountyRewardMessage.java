package com.sh.game.common.communication.msg.system.bounty;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求炼狱悬赏信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHellBountyRewardMessage extends AbsProtostuffMessage {
  /**
   * 领取奖励组id
   */
  private int groupId;

  @Override
  public int getId() {
    return 185003;
  }

  public void setGroupId(int groupId) {
    this.groupId = groupId;
  }

  public int getGroupId() {
    return this.groupId;
  }
}
