package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;

/**
 * 请求装备萃取穿戴
 * 该文件由工具根据 equip.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEquipCuiQuWear extends AbsProtostuffMessage {
  /**
   * 萃取id
   */
  private int cuiQuId;

  @Override
  public int getId() {
    return 13118;
  }

  public void setCuiQuId(int cuiQuId) {
    this.cuiQuId = cuiQuId;
  }

  public int getCuiQuId() {
    return this.cuiQuId;
  }
}
