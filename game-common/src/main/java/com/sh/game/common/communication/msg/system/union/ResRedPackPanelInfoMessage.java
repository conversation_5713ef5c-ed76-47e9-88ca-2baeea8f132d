package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.communication.msg.system.union.bean.RedPackBean;
import com.sh.game.common.communication.msg.system.union.bean.UnionEventBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回红包面板
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRedPackPanelInfoMessage extends AbsProtostuffMessage {
  /**
   * 红包列表
   */
  private List<RedPackBean> redPackBeans = new ArrayList<>();

  /**
   * 红包记录列表
   */
  private List<UnionEventBean> redPackRecordBeans = new ArrayList<>();

  /**
   * 今日领取红包钱
   */
  private long myCount;

  /**
   * 今日发送红包钱
   */
  private long mySendCount;

  @Override
  public int getId() {
    return 23302;
  }

  public void setRedPackBeans(List<RedPackBean> redPackBeans) {
    this.redPackBeans = redPackBeans;
  }

  public List<RedPackBean> getRedPackBeans() {
    return this.redPackBeans;
  }

  public void setRedPackRecordBeans(List<UnionEventBean> redPackRecordBeans) {
    this.redPackRecordBeans = redPackRecordBeans;
  }

  public List<UnionEventBean> getRedPackRecordBeans() {
    return this.redPackRecordBeans;
  }

  public void setMyCount(long myCount) {
    this.myCount = myCount;
  }

  public long getMyCount() {
    return this.myCount;
  }

  public void setMySendCount(long mySendCount) {
    this.mySendCount = mySendCount;
  }

  public long getMySendCount() {
    return this.mySendCount;
  }
}
