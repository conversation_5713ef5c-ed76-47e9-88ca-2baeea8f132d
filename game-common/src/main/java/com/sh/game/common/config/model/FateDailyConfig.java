package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 天命运势配置
 *
 * <AUTHOR>
 * @date 2022/08/02 19:49
 */
@Getter
@Setter
@ConfigData(file = "cfg_fate_day")
public class FateDailyConfig extends AbstractConfigData {

    private int id;

    private int type;

    /**
     * 奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> award;

    /**
     * 转运消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 运势排序
     */
    private int max;

    /**
     * 权重
     */
    private int pr;
}
