package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.communication.msg.system.equip.bean.EquipCuiQuBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;

/**
 * 返回装备萃取信息
 * 该文件由工具根据 equip.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResEquipCuiQuInfo extends AbsProtostuffMessage {
  /**
   * 装备萃取信息
   */
  private EquipCuiQuBean bean = new EquipCuiQuBean();

  @Override
  public int getId() {
    return 13116;
  }

  public void setBean(EquipCuiQuBean bean) {
    this.bean = bean;
  }

  public EquipCuiQuBean getBean() {
    return this.bean;
  }
}
