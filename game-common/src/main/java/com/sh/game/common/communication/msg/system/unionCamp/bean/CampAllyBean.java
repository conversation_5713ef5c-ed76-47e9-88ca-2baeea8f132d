package com.sh.game.common.communication.msg.system.unionCamp.bean;

import java.lang.String;

public class CampAllyBean {
  /**
   * 区服
   */
  private int sid;

  /**
   * 行会id
   */
  private long unionId;

  /**
   * 行会名
   */
  private String unionName = new String();

  /**
   * 会长昵称
   */
  private String chairmanName = new String();

  /**
   * 会员数量
   */
  private int count;

  /**
   * 势力值
   */
  private int powerValue;

  public void setSid(int sid) {
    this.sid = sid;
  }

  public int getSid() {
    return this.sid;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setChairmanName(String chairmanName) {
    this.chairmanName = chairmanName;
  }

  public String getChairmanName() {
    return this.chairmanName;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setPowerValue(int powerValue) {
    this.powerValue = powerValue;
  }

  public int getPowerValue() {
    return this.powerValue;
  }
}
