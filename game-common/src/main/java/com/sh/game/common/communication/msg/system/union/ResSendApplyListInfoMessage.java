package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.communication.msg.system.union.bean.ApplyListInfoBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送申请入会列表信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSendApplyListInfoMessage extends AbsProtostuffMessage {
  /**
   * 申请列表信息
   */
  private List<ApplyListInfoBean> applyInfo = new ArrayList<>();

  @Override
  public int getId() {
    return 23012;
  }

  public void setApplyInfo(List<ApplyListInfoBean> applyInfo) {
    this.applyInfo = applyInfo;
  }

  public List<ApplyListInfoBean> getApplyInfo() {
    return this.applyInfo;
  }
}
