package com.sh.game.common.communication.msg.system.bag;

import com.sh.game.common.communication.msg.system.bag.bean.BagItemBean;
import com.sh.game.common.communication.msg.system.bag.bean.CoinBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 背包发生变化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBagChangeMessage extends AbsProtostuffMessage {
  /**
   * 变化原因
   */
  private int action;

  /**
   * 道具列表
   */
  private List<BagItemBean> itemList = new ArrayList<>();

  /**
   * 货币列表
   */
  private List<CoinBean> coinList = new ArrayList<>();

  /**
   * 道具移除列表
   */
  private List<BagItemBean> removedItemList = new ArrayList<>();

  @Override
  public int getId() {
    return 10003;
  }

  public void setAction(int action) {
    this.action = action;
  }

  public int getAction() {
    return this.action;
  }

  public void setItemList(List<BagItemBean> itemList) {
    this.itemList = itemList;
  }

  public List<BagItemBean> getItemList() {
    return this.itemList;
  }

  public void setCoinList(List<CoinBean> coinList) {
    this.coinList = coinList;
  }

  public List<CoinBean> getCoinList() {
    return this.coinList;
  }

  public void setRemovedItemList(List<BagItemBean> removedItemList) {
    this.removedItemList = removedItemList;
  }

  public List<BagItemBean> getRemovedItemList() {
    return this.removedItemList;
  }
}
