package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回生肖装备合成成功率
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZodiacSuccessRateMessage extends AbsProtostuffMessage {
  /**
   * 成功率
   */
  private int successRate;

  @Override
  public int getId() {
    return 31011;
  }

  public void setSuccessRate(int successRate) {
    this.successRate = successRate;
  }

  public int getSuccessRate() {
    return this.successRate;
  }
}
