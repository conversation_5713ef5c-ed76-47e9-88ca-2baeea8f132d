package com.sh.game.common.constant;

import java.util.Arrays;
import java.util.List;

public interface ServantConst {

    /**
     * 攻击范围
     * 可攻击目标的范围
     */
    int ATTACK_AREA = 7;

    interface From {
        int ARREST = 1;
        int SKILL_CALL = 2;
        int ITEM_CALL = 3;
        int EVENT_CALL = 4;
    }

    interface FightState {
        int ATTACK = 0;
        int LOCK = 1;
        int REST = 2;
        int FOLLOW = 3;//单纯跟随

        List<Integer> ALL_STATE = Arrays.asList(ATTACK, LOCK, REST,FOLLOW);
    }

    List<Integer> BEAST_ID_LIST = Arrays.asList(5021, 5022, 5023, 5024, 5025, 5026, 5027, 5028);

    List<Integer> YUE_LING_ID_LIST = Arrays.asList(5031, 5032, 5033, 5034, 5035, 5036, 5037, 5038);
}
