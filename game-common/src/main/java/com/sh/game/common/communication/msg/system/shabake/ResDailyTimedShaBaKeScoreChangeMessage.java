package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回沙巴克个人积分改变
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDailyTimedShaBaKeScoreChangeMessage extends AbsProtostuffMessage {
  /**
   * 个人积分
   */
  private int roleScore;

  @Override
  public int getId() {
    return 275006;
  }

  public void setRoleScore(int roleScore) {
    this.roleScore = roleScore;
  }

  public int getRoleScore() {
    return this.roleScore;
  }
}
