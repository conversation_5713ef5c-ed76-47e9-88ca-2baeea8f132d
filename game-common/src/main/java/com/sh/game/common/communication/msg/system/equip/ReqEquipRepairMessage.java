package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求装备修理
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEquipRepairMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long lid;

  @Override
  public int getId() {
    return 13035;
  }

  public void setLid(long lid) {
    this.lid = lid;
  }

  public long getLid() {
    return this.lid;
  }
}
