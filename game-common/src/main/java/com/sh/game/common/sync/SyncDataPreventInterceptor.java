package com.sh.game.common.sync;

import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;

public class SyncDataPreventInterceptor implements MethodInterceptor {

    @Override
    public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
        throw new RuntimeException("trying to set value for field which rights out hand: " + method.getName());
    }
}
