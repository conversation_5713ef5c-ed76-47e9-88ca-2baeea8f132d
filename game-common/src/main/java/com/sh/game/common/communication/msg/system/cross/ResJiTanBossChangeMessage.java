package com.sh.game.common.communication.msg.system.cross;

import com.sh.game.common.communication.msg.system.cross.bean.JiTaBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 怪物变化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResJiTanBossChangeMessage extends AbsProtostuffMessage {
  /**
   * 怪物复活时间
   */
  private JiTaBean boss = new JiTaBean();

  @Override
  public int getId() {
    return 84010;
  }

  public void setBoss(JiTaBean boss) {
    this.boss = boss;
  }

  public JiTaBean getBoss() {
    return this.boss;
  }
}
