package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取每日活跃进度奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqDailyProgressRewardMessage extends AbsProtostuffMessage {
  /**
   * cfg_dailytask_reward表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 4258;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
