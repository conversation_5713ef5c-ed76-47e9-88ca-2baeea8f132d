package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求发红包
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqRedPackSendMessage extends AbsProtostuffMessage {
  /**
   * 总元宝
   */
  private int money;

  /**
   * 红包数
   */
  private int count;

  /**
   * 祝福语
   */
  private String blessing = new String();

  @Override
  public int getId() {
    return 23303;
  }

  public void setMoney(int money) {
    this.money = money;
  }

  public int getMoney() {
    return this.money;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setBlessing(String blessing) {
    this.blessing = blessing;
  }

  public String getBlessing() {
    return this.blessing;
  }
}
