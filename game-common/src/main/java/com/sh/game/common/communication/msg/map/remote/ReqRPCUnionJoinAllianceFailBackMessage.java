package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 帮会加入联盟失败
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRPCUnionJoinAllianceFailBackMessage extends AbsProtostuffMessage {
  /**
   * 帮会id
   */
  private long unionId;

  @Override
  public int getId() {
    return 82044;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }
}
