package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求获得跨服遗迹地图怪物信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqCrossRuinsMonsterInfoMessage extends AbsProtostuffMessage {
  /**
   * 地图id
   */
  private int mapId;

  @Override
  public int getId() {
    return 67053;
  }

  public void setMapId(int mapId) {
    this.mapId = mapId;
  }

  public int getMapId() {
    return this.mapId;
  }
}
