package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description: 【长安争夺战（神器仙岛争夺战）】
 * @Date 2022-07-5 18:15
 **/
@Setter
@Getter
@ConfigData(file="cfg_changan")
public class FightForChangAnConfig extends AbstractConfigData {
    private int id;
    /**
     * 据点名
     */
    private String name;

    /**
     * X坐标
     */
    private int posX;

    /**
     * Y坐标
     */
    private int posY;

    /**
     * 范围
     */
    private int round;

    /**
     * 积分
     */
    private int scorce;

    /**
     * 占领时需要的时间
     */
    private int time;

    /**
     * 占领公告
     */
    private int annouce = 0;

    /**
     * 巅峰联赛专用公告
     */
    private int dfAnnounce;
}
