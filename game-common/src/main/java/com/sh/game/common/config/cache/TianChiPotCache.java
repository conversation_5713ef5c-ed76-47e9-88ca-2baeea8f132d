package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.TianChiPotConfig;
import com.sh.game.common.config.model.TianChiPotControlConfig;
import lombok.Getter;

import java.util.*;

/**
 * 天池宫奖励
 */
@Getter
@ConfigCache
public class TianChiPotCache implements IConfigCache {

    private Map<Integer, List<TianChiPotControlConfig>> ctrls = new HashMap<>();
    private Map<String, TianChiPotControlConfig> ctrlSureDrops = new HashMap<>();
    private Map<String, TianChiPotControlConfig> ctrlRates = new HashMap<>();
    private Map<String, List<TianChiPotConfig>> pots = new HashMap<>();
    private Map<String, List<Integer>> potRates = new HashMap<>();
    private Map<String, TianChiPotConfig> potSureDrops = new HashMap<>();

    @Override
    public void build() {
        ctrls.clear();
        ctrlSureDrops.clear();
        ctrlRates.clear();
        pots.clear();
        potRates.clear();
        potSureDrops.clear();
        for (TianChiPotControlConfig config : ConfigDataManager.getInstance().getList(TianChiPotControlConfig.class)) {
            if (config.getNeed() > 0) {
                String key = config.getGoal_type() + "#" + (config.getNeed() - 1);
                ctrlSureDrops.put(key, config);
            } else {
                if (!ctrls.containsKey(config.getGoal_type())) {
                    ctrls.put(config.getGoal_type(), new ArrayList<>());
                }
                ctrls.get(config.getGoal_type()).add(config);

                String key = config.getGoal_type() + "#" + config.getGroup();
                ctrlRates.put(key, config);
            }
        }

        for (TianChiPotConfig config : ConfigDataManager.getInstance().getList(TianChiPotConfig.class)) {
            if (config.getGroup() == 0) {
                String key = config.getGoal_type() + "#" + (config.getBase_award() - 1) + "#" + config.getGroup();
                potSureDrops.put(key, config);
            } else {
                String key = config.getGoal_type() + "#" + config.getGroup();
                if (!pots.containsKey(key)) {
                    pots.put(key, new ArrayList<>());
                }
                pots.get(key).add(config);

                if (!potRates.containsKey(key)) {
                    potRates.put(key, new ArrayList<>());
                }
                potRates.get(key).add(config.getBox_weight());
            }
        }
    }

    public List<TianChiPotControlConfig> getControls(int goalType) {
        return ctrls.getOrDefault(goalType, Collections.emptyList());
    }
}