package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回至尊礼包信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZhiZunLiBaoInfoMessage extends AbsProtostuffMessage {
  /**
   * 至尊礼包购买次数
   */
  private int count;

  /**
   * 已领取的至尊礼包奖励
   */
  private List<Integer> reward = new ArrayList<>();

  @Override
  public int getId() {
    return 4276;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setReward(List<Integer> reward) {
    this.reward = reward;
  }

  public List<Integer> getReward() {
    return this.reward;
  }
}
