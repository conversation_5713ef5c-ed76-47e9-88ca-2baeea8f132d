package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 结盟与宣战消耗
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-10-14
 **/

@Getter
@Setter
@ConfigData(file = "cfg_union_declare", keys = {"id#type"})
public class UnionDeclareConfig extends AbstractConfigData {
    /**
     * 主键
     */
    private int id;

    /**
     * 类型
     * 1：结盟  2：宣战
     */
    private int type;

    /**
     * 持续时间(秒)
     */
    private int time;

    /**
     * 消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

}
