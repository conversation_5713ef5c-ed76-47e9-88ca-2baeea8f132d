package com.sh.game.common.util;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.JinghaoDoubleArrayConverter;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.*;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.config.util.ConverterUtil;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.condition.ConditionUtil;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.sh.game.common.constant.GameConst.GlobalId.JI_TAN_SHENG;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/11 19:09<br>;
 * 版本：1.0<br>;
 * 描述：策划配置的全局数值获取工具
 */
public class  GlobalUtil{

    /**
     * 获取全局值
     *
     * @param globalID 全局值编号
     * @return
     * @see com.sh.game.common.constant.GameConst.GlobalId
     */
    public static final String getGlobalValue(int globalID) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalID);
        if (config == null) {
            return null;
        }

        return config.getValue();
    }


//    public static final <T extends Serializable> T getGlobalValueSerializable(int globalID) {
//        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalID);
//        if (config == null) {
//            return null;
//        }
//        return (T) config.getValue();
//    }

    /**
     * 获取全局编号对应的整数值
     *
     * @param globalID
     * @return
     * @see com.sh.game.common.constant.GameConst.GlobalId
     */
    public static final int getGlobalInt(int globalID) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalID);
        if (config == null || config.getValue().length() == 0) {
            return 0;
        }
        return Integer.parseInt(config.getValue());
    }

    public static long getGlobalLong(int globalID) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalID);
        if (config == null || config.getValue() == null || config.getValue().isEmpty()) {
            return 0;
        }
        return Long.parseLong(config.getValue());
    }

    /**
     * 获取祭坛所有相关地图
     */
    public static final ArrayList<Integer> getJiTanMapId() {
        ArrayList<Integer> jiTanMap = new ArrayList<>();
        ArrayList<Integer> convert = new CommonArrayConvert().convert(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_MAP_ID));
        jiTanMap.addAll(convert);
        return jiTanMap;
    }

    /**
     * 获取祭坛所有boss
     */
    public static final List<Integer> getAllBossId() {
        List<Integer> bossList = new CommonArrayConvert().convert(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_BOSS));
        List<Integer> allList = new ArrayList<>();
        allList.add(GlobalUtil.getGlobalInt(JI_TAN_SHENG));
        allList.addAll(bossList);
        return allList;
    }

    /**
     * 获取祭坛四方祭坛id
     */
    public static final ArrayList<Integer> getFourJiTanMapId() {
        ArrayList<Integer> jiTanMap = new ArrayList<>();
        String jiTanValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_MAP_ID);
        String[] split = jiTanValue.split(Symbol.JINHAO);
        for (int i = 1; i < split.length; i++) {
            jiTanMap.add(Integer.parseInt(split[i - 1]));
        }
        return jiTanMap;
    }


    /**
     * 获取不满足专属掉落展示面板条件的装备品质
     *
     * @param avatar 角色
     * @return 装备品质列表
     */
    public static List<Integer> findHidePanelEquipRank(IAvatar avatar) {
        List<Integer> rankList = new ArrayList<>();

        String globalValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.ZHUANSHU_PANEL_RARE_LIMIT);
        if (globalValue == null) {
            return rankList;
        }

        String[] arr = globalValue.split(Symbol.SHUXIAN);
        for (String s : arr) {
            String[] split = s.split(Symbol.AND);
            if (split.length < 2) {
                return rankList;
            }
            List<int[]> condition = new ConditionConvert().convert(split[0]);
            if (ConditionUtil.validate(avatar, condition)) {
                rankList.add(Integer.parseInt(split[1]));
            }
        }

        return rankList;
    }


    /**
     * 获取消耗物列表
     *
     * @param globalId global配置表id
     * @return 消耗物列表
     */
    public static List<int[]> findItemCost(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return new ArrayList<>(0);
        }

        return new JinHaoAndYuHaoListConverter().convert(GlobalUtil.getGlobalValue(globalId));
    }

    /**
     * 解析Global中的布尔值
     *
     * @param globalId Global表id
     * @return 参数布尔值
     */
    public static boolean findSwitches(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return false;
        }

        return Integer.parseInt(config.getValue()) != 0;
    }

    /**
     * 解析Global中的condition
     *
     * @param globalId Global表id
     * @return condition
     */
    public static List<int[]> findConditions(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return Collections.emptyList();
        }
        return ConverterUtil.conditionConvert(config.getValue());
    }

    /**
     * 解析Global中的井号分割list
     *
     * @param globalId Global表id
     * @return 列表
     */
    public static List<Integer> findJingHaoList(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return Collections.emptyList();
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new JinhaoIntegerListConverter().convert(globalValue);
    }
    /**
     * 解析Global中的竖线分割list
     *
     * @param globalId Global表id
     * @return 列表
     */
    public static List<Integer> findShuXianList(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return Collections.emptyList();
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new ShuXianIntegerListConverter().convert(globalValue);
    }


    /**
     * 解析Global中的井号分割list
     *
     * @param globalId Global表id
     * @return 列表
     */
    public static List<int[]> findJingHaoAndShuXianList(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return Collections.emptyList();
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new JinHaoAndShuXianListConverter().convert(globalValue);
    }

    public static List<List<int[]>> findJinHaoYuHaoAndShuXianList(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return Collections.emptyList();
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new JinHaoYuHaoAndShuXianListConverter().convert(globalValue);
    }

    public static <T> T find(int globalId, Class<? extends IConverter> clazz) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return null;
        }
        String globalValue = GlobalUtil.getGlobalValue(globalId);
        try {
            Method convert = clazz.getMethod("convert", Object.class);
            IConverter instance = clazz.getConstructor().newInstance();
            return (T)convert.invoke(instance, globalValue);
        } catch (Exception ignored) {
        }
        return null;
    }

    /**
     * 将道具 map 转成 道具id#数量&道具id#数量
     */
    public static final String tranItemIntArrToString(int[] items){
        if (items == null || items.length < 2){
            return "";
        }

        return items[0] + Symbol.JINHAO + items[1];
    }

    public static int[] findJinghaoIntArray(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return new int[0];
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new JinghaoIntArrayConverter().convert(globalValue);
    }

    public static double[] findJinghaoDoubleArray(int globalId) {
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, globalId);
        if (config == null) {
            return new double[0];
        }

        String globalValue = GlobalUtil.getGlobalValue(globalId);
        return new JinghaoDoubleArrayConverter().convert(globalValue);
    }

    public static <T> T find(int globalId, IConverter converter, Class<T> clazz) {
        String globalValue = getGlobalValue(globalId);
        Object result = converter.convert(globalValue);
        if (clazz.isInstance(result)) {
            return clazz.cast(result);
        }
        return null;
    }

}
