package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.LongHunSuitConfig;
import com.sh.game.common.config.model.SkillAwakeConfig;
import com.sh.game.common.config.model.SkillAwakeSuitConfig;
import lombok.Getter;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 技能觉醒配置
 *
 * <AUTHOR>
 * @date 2022/08/15 11:10
 */
@ConfigCache
@Getter
public class SkillAwakeCache implements IConfigCache {

    /**
     * rowKey:      skillConfigId 技能配置id
     * columnKey:   level 觉醒等级
     * value:       skillAwakeConfig 技能觉醒配置
     */
    HashBasedTable<Integer, Integer, SkillAwakeConfig> awakeConfigTable = HashBasedTable.create();

    @Override
    public void build() {
        awakeConfigTable.clear();
        List<SkillAwakeConfig> awakeConfigList = ConfigDataManager.getInstance().getList(SkillAwakeConfig.class);
        awakeConfigList.forEach(config -> {
            awakeConfigTable.put(config.getSkillId(), config.getLv(), config);
        });
    }

    /**
     * 根据技能配置id、觉醒等级获取技能觉醒配置
     *
     * @param skillConfigId     配置id
     * @param level             觉醒等级
     * @return SkillAwakeConfig 技能觉醒配置
     */
    public SkillAwakeConfig getConfig(int skillConfigId, int level) {
        return awakeConfigTable.get(skillConfigId, level);
    }

    /**
     * 获取全部技能id
     *
     * @return Set<Integer> 技能id列表
     */
    public Set<Integer> getSkillIdSet() {
        return awakeConfigTable.rowKeySet();
    }

    /**
     * 根据技能觉醒总等级获取技能觉醒套装组合配置id
     *
     * @param level 技能觉醒总等级
     * @return int 技能觉醒套装组合配置id
     */
    public int getSuitId(int level) {
        SkillAwakeSuitConfig suitConfig = ConfigDataManager.getInstance().getList(SkillAwakeSuitConfig.class).stream()
                .filter(config -> level >= config.getLv())
                .max(Comparator.comparing(SkillAwakeSuitConfig::getLv)).orElse(null);
        if (Objects.nonNull(suitConfig)) {
            return suitConfig.getId();
        }
        return 0;
    }
}
