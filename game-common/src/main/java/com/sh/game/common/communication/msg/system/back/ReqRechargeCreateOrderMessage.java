package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求创建订单
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRechargeCreateOrderMessage extends AbsProtostuffMessage {
  /**
   * 角色id
   */
  private long roleId;

  /**
   * 配置表id
   */
  private int productId;

  @Override
  public int getId() {
    return 43170;
  }

  public void setRoleId(long roleId) {
    this.roleId = roleId;
  }

  public long getRoleId() {
    return this.roleId;
  }

  public void setProductId(int productId) {
    this.productId = productId;
  }

  public int getProductId() {
    return this.productId;
  }
}
