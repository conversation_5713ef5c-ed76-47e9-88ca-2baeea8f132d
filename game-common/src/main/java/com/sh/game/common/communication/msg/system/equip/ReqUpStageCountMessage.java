package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求升阶次数
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUpStageCountMessage extends AbsProtostuffMessage {
  /**
   * 升阶类型，读表id
   */
  private int type;

  @Override
  public int getId() {
    return 13052;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
