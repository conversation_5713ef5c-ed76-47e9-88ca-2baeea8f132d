package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求解散帮会
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqDeleteUnionMessage extends AbsProtostuffMessage {
  /**
   * 帮会id
   */
  private long unionid;

  @Override
  public int getId() {
    return 43031;
  }

  public void setUnionid(long unionid) {
    this.unionid = unionid;
  }

  public long getUnionid() {
    return this.unionid;
  }
}
