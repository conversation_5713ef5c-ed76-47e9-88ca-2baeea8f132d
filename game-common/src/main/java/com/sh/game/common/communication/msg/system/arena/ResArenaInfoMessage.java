package com.sh.game.common.communication.msg.system.arena;

import com.sh.game.common.communication.msg.system.arena.bean.ArenaRoleBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回竞技场信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResArenaInfoMessage extends AbsProtostuffMessage {
  private List<ArenaRoleBean> ArenaRoleBean = new ArrayList<>();

  private int score;

  private int rank;

  private int successCount;

  @Override
  public int getId() {
    return 394002;
  }

  public void setArenaRoleBean(List<ArenaRoleBean> ArenaRoleBean) {
    this.ArenaRoleBean = ArenaRoleBean;
  }

  public List<ArenaRoleBean> getArenaRoleBean() {
    return this.ArenaRoleBean;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setRank(int rank) {
    this.rank = rank;
  }

  public int getRank() {
    return this.rank;
  }

  public void setSuccessCount(int successCount) {
    this.successCount = successCount;
  }

  public int getSuccessCount() {
    return this.successCount;
  }
}
