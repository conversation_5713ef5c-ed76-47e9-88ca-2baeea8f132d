package com.sh.game.common.communication.msg.system.climbTower;

import com.sh.game.common.communication.msg.system.climbTower.bean.ClimbTowerItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回转盘次数
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResClimbTowerTurnMessage extends AbsProtostuffMessage {
  /**
   * 奖池
   */
  private List<ClimbTowerItemBean> item = new ArrayList<>();

  /**
   * 今日已经转的次数
   */
  private int times;

  /**
   * 类型
   */
  private int type;

  @Override
  public int getId() {
    return 309009;
  }

  public void setItem(List<ClimbTowerItemBean> item) {
    this.item = item;
  }

  public List<ClimbTowerItemBean> getItem() {
    return this.item;
  }

  public void setTimes(int times) {
    this.times = times;
  }

  public int getTimes() {
    return this.times;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
