package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 玩家封号变化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPlayerTitleChangeMessage extends AbsProtostuffMessage {
  /**
   * id
   */
  private long lid;

  /**
   * 封号id
   */
  private int title;

  @Override
  public int getId() {
    return 67039;
  }

  public void setLid(long lid) {
    this.lid = lid;
  }

  public long getLid() {
    return this.lid;
  }

  public void setTitle(int title) {
    this.title = title;
  }

  public int getTitle() {
    return this.title;
  }
}
