package com.sh.game.common.communication.msg.system.union.bean;

import com.sh.game.common.communication.msg.abc.bean.CommonSlotBean;
import java.lang.String;
import java.util.ArrayList;
import java.util.List;

public class XieZhuInfoBean {
  /**
   *  玩家id
   */
  private long rid;

  /**
   *  玩家名字
   */
  private String name = new String();

  /**
   *  已被协助次数
   */
  private int count;

  /**
   *  当前玩家是否协助，1 以协助
   */
  private int xieZhu;

  /**
   *  协助者的拆家id
   */
  private int chaiJiaId;

  /**
   * 时装
   */
  private List<CommonSlotBean> fashions = new ArrayList<>();

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setXieZhu(int xieZhu) {
    this.xieZhu = xieZhu;
  }

  public int getXieZhu() {
    return this.xieZhu;
  }

  public void setChaiJiaId(int chaiJiaId) {
    this.chaiJiaId = chaiJiaId;
  }

  public int getChaiJiaId() {
    return this.chaiJiaId;
  }

  public void setFashions(List<CommonSlotBean> fashions) {
    this.fashions = fashions;
  }

  public List<CommonSlotBean> getFashions() {
    return this.fashions;
  }
}
