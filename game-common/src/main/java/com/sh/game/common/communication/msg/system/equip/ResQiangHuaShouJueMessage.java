package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回强化兽决结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResQiangHuaShouJueMessage extends AbsProtostuffMessage {
  /**
   * 强化的兽决装备是否成功
   */
  private boolean success;

  @Override
  public int getId() {
    return 340008;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }
}
