package com.sh.game.common.entity.map;

import com.sh.common.jdbc.ProtostuffSerializable;
import com.sh.game.common.entity.skill.Skill;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2018/8/30 17:21
 */
@Getter
@Setter
public class SkillDTO implements ProtostuffSerializable {
    @Tag(1)
    private int skillId;

    @Tag(2)
    private int level;

    @Tag(3)
    private int practice;

    @Tag(4)
    private boolean autoUse = true;

    public Skill toSkill(){
        Skill skill = new Skill();
        skill.setSkillId(getSkillId());
        skill.setLevel(getLevel());
        skill.setPractice(getPractice());
        return skill;
    }
}
