package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.EquipHandbookConfig;
import com.sh.game.common.config.model.HandBookSublimationConfig;

import java.util.*;

/**
 * 灵根天命升华配置缓存
 *
 * <AUTHOR>
 * @date 2022/07/12 20:49
 */
@ConfigCache
public class HandBookSublimationCache implements IConfigCache {

    /**
     * rowKey:      type 类型
     * columnKey:   level 等级
     * value:       configId 配置id
     */
    HashBasedTable<Integer, Integer, Integer> cache = HashBasedTable.create();

    /**
     * 可消耗的道具id列表
     * key:     itemConfigId、灵根天命id、灵根天命mapping
     * value:   灵根天命组type
     */
    Map<Integer, Integer> costMap = new HashMap<>();

    /**
     * 灵根天命类型
     */
    Set<Integer> typeSet = new HashSet<>();

    /**
     * 灵根天命组
     * key:     type图鉴组
     * value:   configIdList灵根天命id列表
     */
    Map<Integer, List<Integer>> goalMap = new HashMap<>();

    @Override
    public void build() {
        cache.clear();
        costMap.clear();
        typeSet.clear();
        goalMap.clear();

        List<HandBookSublimationConfig> sublimationConfigList = ConfigDataManager.getInstance().getList(HandBookSublimationConfig.class);
        sublimationConfigList.forEach(v -> {
                cache.put(v.getType(), v.getLevel(), v.getId());
                typeSet.add(v.getType());
        });

        List<EquipHandbookConfig> handbookConfigList = ConfigDataManager.getInstance().getList(EquipHandbookConfig.class);
        handbookConfigList.forEach(v -> {
            costMap.put(v.getId(), v.getType());
            costMap.put(v.getMappingId(), v.getType());
            goalMap.computeIfAbsent(v.getType(), k -> new ArrayList<>());
            goalMap.get(v.getType()).add(v.getId());
        });
    }

    /**
     * 根据类型和等级获取configId
     *
     * @param type 组别
     * @param level 等级
     * @return int configId
     */
    public int getConfigIdByTypeAndLevel(int type, int level) {
        Integer configId = cache.get(type, level);
        return configId == null ? 0 : configId;
    }

    /**
     * 获取可消耗的道具id列表
     *
     * @return 可消耗的道具id列表
     */
    public Map<Integer, Integer> getCostIdMap() {
        return costMap;
    }

    /**
     * 灵根天命类型
     *
     * @return Set<Integer>
     */
    public Set<Integer> getTypeSet() {
        return typeSet;
    }

    /**
     * 获取灵根天命组所对应的全部图鉴
     *
     * @return Map<Integer,List<Integer>> 灵根天命组所对应的全部灵根天命id
     */
    public Map<Integer, List<Integer>> getGoalMap() {
        return goalMap;
    }
}
