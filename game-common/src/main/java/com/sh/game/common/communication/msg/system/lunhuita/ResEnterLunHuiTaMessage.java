package com.sh.game.common.communication.msg.system.lunhuita;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 进入是否成功应答
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResEnterLunHuiTaMessage extends AbsProtostuffMessage {
  /**
   * 0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
   */
  private int state;

  @Override
  public int getId() {
    return 325004;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }
}
