package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求踢出玩家
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqKickOutMemberMessage extends AbsProtostuffMessage {
  /**
   * 选择的玩家ID
   */
  private long memberId;

  @Override
  public int getId() {
    return 23007;
  }

  public void setMemberId(long memberId) {
    this.memberId = memberId;
  }

  public long getMemberId() {
    return this.memberId;
  }
}
