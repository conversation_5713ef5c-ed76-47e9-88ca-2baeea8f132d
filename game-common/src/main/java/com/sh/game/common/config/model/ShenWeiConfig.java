package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @Date 2022-05-06 14:05
 **/

@Getter
@Setter
@ConfigData(file = "cfg_shenwei")
public class ShenWeiConfig extends AbstractConfigData {

    private int id;

    /**
     * 神威玩法类型
     */
    private int type;

    /**
     * 等级
     */
    private int level;

    /**
     * 下一级cfgId
     */
    private int nextId;

    /**
     * 神威突破消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 神威属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * buffId
     */
    private int[] buff;

    /**
     * 条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> conditions;


}
