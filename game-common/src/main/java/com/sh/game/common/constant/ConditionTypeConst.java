package com.sh.game.common.constant;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/1 13:33<br>;
 * 版本：1.0<br>;
 * 描述：条件
 */
public interface ConditionTypeConst {

    /**
     * 判断玩家等级区间
     */
    int ROLE_LEVEL = 1;

    /**
     * 判断玩家转生
     */
    int ROLE_ZHUANSHENG = 2;

    /**
     * 判断开服天数区间
     */
    int OPEN_SERVER_DAY = 3;

    /**
     * 判断职业
     */
    int ROLE_CAREER = 4;

    /**
     * 判断性别
     */
    int ROLE_SEX = 5;

    /**
     * 判断道具是否足够(只检测背包)
     */
    int ITEM_ENOUGH_IN_BAG = 6;

    /**
     * 判断道具是否足够(只检测穿戴)
     */
    int ITEM_ENOUGH_IN_BODY = 7;

    /**
     * 判断道具是否足够(检测背包与穿戴)
     */
    int ITEM_ENOUGH = 8;

    /**
     * 判断主线任务提交
     */
    int MAIN_TASK_COMPLETE = 9;

    /**
     * 判断每日充值的额度
     */
    int RECHARGE_DAILY = 10;

    /**
     * 判断充值总额度
     */
    int RECHARGE_TOTAL = 11;

    /**
     * 判断当前时间戳秒
     */
    int TIME_STAMP = 12;

    /**
     * 判断单个技能等级
     */
    int SKILL_LEVEL = 13;

    /**
     * 判断当天已过去的秒
     */
    int DAY_NIGHT = 14;

    /**
     * 判断当前所在地图
     */
    int SPACE = 15;

    /**
     * 判断当前每日活动是否开启
     */
    int DAILY_TIMED_OPEN = 16;

    /**
     * 判断合服天数
     */
    int SERVER_MERGE_DAYS = 17;

    /**
     * 判断装备部位是否穿戴
     */
    int EQUIP_WARE = 19;

    /**
     * 判断月卡特权
     */
    int MONTH_CARD = 21;

    /**
     * 判断行会等级
     */
    int UNION_LEVEL = 23;

    /**
     * 判断玩家称号等级
     */
    int ROLE_TITLE_LEVEL = 24;

    /**
     * 判断玩家攻击
     */
    int ATK = 25;

    /**
     * 判断宝石最大等级（检测穿戴和背包）
     */
    int BORE_LEVEL = 26;

    /**
     * 判断行会boss排名
     */
    int UNION_BOSS_RANKING = 27;

    /**
     * 判断道具获得天数
     */
    int ITEM_OBTAIN_TIME = 28;

    /**
     * 调用自己的条件验证
     * com.sh.game.RoleConditionValidator
     */
    int ROLE_CONDITION_VALIDATOR = 29;

    /**
     * 判断是否进入行会
     */
    int UNION = 30;

    /**
     * 判断限购商品已购买次数
     */
    int STORE_BUY = 31;

    /**
     * 判断天地宝石最大等级(检测穿戴和背包)
     */
    int STONE_LEVEL = 32;

    /**
     * 判断穿戴的马匹ID
     */
    int ROLE_MOUNT_OWN_BY_ID = 36;

    /**
     * 判断穿戴的马匹的TYPE
     */
    int ROLE_MOUNT_OWN_BY_TYPE = 37;

    /**
     * 判断活动是否开启
     */
    int ACTIVITY_OPEN = 38;

    /**
     * 英雄转生等级
     */
    int HERO_ZHUANSHENG = 39;

    /**
     * 创建英雄
     */
    int CREATE_HERO = 40;

    /**
     * 判断狂暴之力buff
     */
    int VIOLENT_POWER = 41;

    /**
     * 判断当前时间是周几
     */
    int WEEK_DAY = 42;

    /**
     * 判断主角与英雄职业
     */
    int ROLE_AND_HERO_CAREER = 43;
    /**
     * 判断主角，英雄限制
     */
    int ROLE_OR_HERO = 44;

    /**
     * 官职
     */
    int OFFICIAL = 45;

    /**
     * 忠诚度
     */
    int LOYALTY = 46;

    /**
     * 合击技能是否学习
     */
    int COMBINE_SKILL_LEARN = 47;

    /**
     * 跨服天数处理
     */
    int CROSS_SERVER_DAY = 50;

    /**
     * 指定时间的本周
     */
    int APPOINT_TIME_WEEK = 51;

    /**
     * 副本通关条件
     */
    int PASS_DUPLICATE = 54;

    /**
     * 普通寻宝宫殿免费入场条件
     */
    int FREE_NORMAL_DI_GONG = 55;

    /**
     * 地下宫殿免费入场条件
     */
    int FREE_LIMIT_DI_GONG = 56;

    /**
     * 成就等级
     */
    int ACHIEVEMENT_LEVEL = 57;

    /**
     * 战意重数
     */
    int ZHANYI_LEVEL = 58;

    /**
     * 侠侣等级
     */
    int XIALV_LEVEL = 59;

    /**
     * 神兵会员等级
     */
    int SHENBING_LEVEL = 60;

    /**
     * 穿戴的装备等级大于等于
     */
    int EQUIP_ON_LEVEL = 62;

    /**
     * 穿戴的装备的品质大于等于几
     */
    int EQUIP_ON_QUALITY = 64;

    /**
     * 判断玩家是否拥有指定时装或称号
     */
    int FASHION_TITLE = 65;

    /**
     * 判断玩家攻击上限
     */
    int ATK_MAX = 66;

    /**
     * 侠侣光环等级
     */
    int XIALV_GUANGHUAN_LEVEL = 67;

    /**
     * 玩家剩余血量万分比
     */
    int RESIDUE_HP_POINTS = 69;

    /**
     * 最终神兵开启任务类型状态
     */
    int LAST_SHENBING_TASK_STATUS = 71;

    /**
     * 判断道具是否足够(只检测装备)
     */
    int ITEM_ENOUGH_IN_EQUIP = 72;

    /**
     * 检测当日时间区间
     */
    int DAYTIME_IN_RANGE = 73;

    /**
     * 检测威望等级
     */
    int WEI_WANG_LEVEL = 74;

    /**
     * 强化等级校验
     */
    int BODY_EQUIP_LEVEL = 75;

    /**
     * 武道增益等级
     */
    int WUDAO_LEVEL = 76;

    /**
     * 今日是否进入过通天塔的层数
     */
    int CLIMB_TOWER_FLOOR = 77;

    /**
     * 玩家封号id
     */
    int FENG_HAO_LEVEL = 78;

    /**
     * 是否拥有某个称号
     */
    int HAVE_TITLE = 79;

    /**
     * 元婴总等级
     */
    int YUAN_YING_TOTAL_LEVEL = 80;

    /**
     * 元婴部位等级
     */
    int YUAN_YING_TYPE_LEVEL = 81;

    /**
     * 天书等级
     */
    int TIAN_SHU_LEVEL = 82;

    /**
     * 等级判断,会判断248属性
     */
    int LEVEL_LIMIT = 83;

    /**
     * 战意判断,会判断251属性
     */
    int ZHANYI_LIMIT = 84;

    /**
     * 某排行榜上达到某条件人数
     */
    int RANK_CONDITION = 85;

    /**
     * 是否拥有指定侠侣
     */
    int HAS_XIA_LV = 86;

    /**
     * 合服次数
     */
    int MERGE_COUNT = 87;

    /**
     * 套装
     */
    int EQUIP_SUIT = 88;

    /**
     * 觉醒等级校验
     */
    int JUE_XING_LEVEL = 89;

    /**
     * 侠侣守护等级
     */
    int XIALV_GUARDIAN_LEVEL = 90;

    /**
     * 麒麟臂等级
     */
    int QI_LING_BI_LEVEL = 91;

    /**
     * 四大传承等级
     */
    int LINEAGE_LEVEL = 92;

    /**
     * 特殊充值金额
     */
    int RECHARGE_SPECIAL_COUNT = 93;

    /**
     * 操作系统判断
     */
    int OS_CONDITION = 94;

    /**
     * 神威等级
     */
    int SHENWEI_LEVEL = 96;

    /**
     * 法器会员等级
     */
    int FAQIHUIYUAN_LEVEL = 99;

    /**
     * 判断支线任务状态
     */
    int BRANCH_TASK_STATUS = 101;

    /**
     * 法阵等级
     */
    int MAGIC_CIRCLE_LEVEL = 102;

    /**
     * 灵根天命总等级
     */
    int HAND_BOOK = 103;

    /**
     * 宠物等级
     */
    int PEY_LEVEL = 105;

    /**
     * 特戒等级
     */
    int RING_LEVEL = 104;

    /**
     * 法阵等级(根据type)
     */
    int MAGIC_CIRCLE = 106;

    /**
     * 足迹充能
     */
    int FOOTPRINT_RECHARGE = 107;

    /**
     * 神魔锻体
     */
    int BODY_FORGE = 110;

    /**
     * 判断是否为跨服沙巴克获胜行会会长
     */
    int UNION_SHABAKE_WIN_CHAIRMAN = 111;

    /**
     * 角色渠道
     */
    int ROLE_CHANNEL = 112;

    /**
     * 兽决强化等级
     */
    int SHOUJUEEQUIP_QIANGHUA_LEVEL = 113;

    int ATTR_CHECK = 114;

    /**
     * 角色创建天数
     */
    int CREATE_ROLE_DAYS = 115;

    /**
     * 天命等级
     */
    int FATE_LEVEL = 118;

    /**
     * 玉佩等级
     */
    int YU_PEI_LEVEL = 120;

    /**
     * 天书神剑-天书总等级
     */
    int BOOK_LEVEL = 121;

    /**
     * 天书神剑-神剑总等级
     */
    int SWORD_LEVEL = 122;

    /**
     * 匹配服是否连接
     */
    int MATCH_SERVER_AVAILABLE = 123;

    /**
     * 行会阵营
     */
    int CAMP_TYPE = 125;

    /**
     * 判断玩家是否解锁该时装
     */
    int FASHION_UNLOCK = 126;

    /**
     * 军衔等级
     */
    int MILITARYRANK_LEVEL = 127;

    /**
     * 是否拥有阵营
     */
    int UNION_CAMP = 128;

    /**
     * 玄兵等级
     */
    int XUAN_BING = 129;

    /**
     * 判断玩家是否拥有该类型的时装
     */
    int UNLOCK_TYPE_FASHION = 130;

    /**
     * 玩家异火总等级
     */
    int FIRE_ALL_LEVEL = 131;

    /**
     * 丹药系统玩家已分配和剩余点数
     */
    int ROLE_DANYAO_POINT = 132;

    /**
     * 境界
     */
    int REALM = 135;

    /**
     * 圣器总等级
     */
    int SHENG_QI = 137;

    /**
     * 神魔等级
     */
    int SHEN_MO_LEVEL = 201;

    /**
     * 道友等级
     */
    int FRIEND_LEVEL = 221;

    /**
     * 拥有后院老鼠数量
     */
    int HOU_YUAN_RAT_COUNT = 222;

    /**
     * 镇妖塔层数
     */
    int ZHEN_YAO_TA_LAYER = 223;

    /**
     * 斗技场获胜次数
     */
    int ARENA_SUCCESS_COUNT = 224;

    /**
     * 击败指定妖王
     */
    int BOSS_KING = 225;

    /**
     * 建筑等级
     */
    int HOU_YUAN_WORK_LEVEL = 226;

    /**
     * 伙伴等级
     */
    int HUO_BAN_LEVEL = 203;

    /**
     * 后院等级
     */
    int CHAI_JIA_LEVEL = 211;

    /**
     * 西行通关层数
     */
    int XI_XING_LAYER = 200;

    /**
     * 神魔召唤次数
     */
    int SHEN_MO_XI_YING = 227;

    /**
     * 伙伴召唤次数
     */
    int HUO_BAN_BUY = 228;

    /**
     * 获得神魔数量
     */
    int SHEN_MO_COUNT = 229;

    /**
     * 获得伙伴数量
     */
    int HUO_BAN_COUNT = 230;

    /**
     * 是否拥有至尊卡
     */
    int OWN_SPECIAL_CARD = 252;

    /**
     * 自定义
     */
    int CUSTOM = 999;

    /**
     * 拥有伙伴
     */
    int OWN_HUO_BAN = 401;

    /**
     * 拥有神魔
     */
    int OWN_SHEN_MO = 402;

    /**
     * 副本通关
     */
    int COMP_DUPLICATE = 701;
    /**
     * 超度次数
     */
    int CHAO_DU_COUNT = 702;
    /**
     * 经验大于多少
     */
    int EXP = 710;
    /**
     * 最小通关时间
     */
    int MIN_COMPLETE_TIME = 711;
}
