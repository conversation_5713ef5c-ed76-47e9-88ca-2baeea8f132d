package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
@ConfigData(file = "cfg_horse_qh")
public class MountQiangHuaConfig extends AbstractConfigData {

    private int id;

    private int stage;

    private int level;

    private int exp;

    private int cost;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] att;

    private int highAtt;

    private int jinJie;
}
