package com.sh.game.common.entity.backpack.committype;

import com.sh.game.common.communication.msg.system.backpack.ResBackpackChangedMessage;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackCommitController;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.intf.IAvatar;

/**
 * 道具处理提交类
 */
public abstract class AbstractCommitHandler {

    public Boolean check(IAvatar avatar, LogAction action, boolean tips, int flag, BackpackStash backpackStash, ResBackpackChangedMessage backPackChange, BackpackCommitController controller) {
        return true;
    }

    public void commit(IAvatar avatar, LogAction action, boolean tips, int flag, BackpackStash backpackStash, ResBackpackChangedMessage backPackChange, BackpackCommitController controller) {
    }

}
