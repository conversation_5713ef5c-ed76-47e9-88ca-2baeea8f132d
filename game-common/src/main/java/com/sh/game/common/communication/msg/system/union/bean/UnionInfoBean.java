package com.sh.game.common.communication.msg.system.union.bean;

import java.lang.String;

public class UnionInfoBean {
  /**
   * 行会id
   */
  private long unionId;

  /**
   * 行会名
   */
  private String unionName = new String();

  /**
   * 行会会长名
   */
  private String leaderName = new String();

  /**
   * 行会公告
   */
  private String announcement = new String();

  /**
   * 行会等级
   */
  private int unionLevel;

  /**
   * 行会人数
   */
  private int unionNum;

  /**
   * 行会申请状态 1可申请 2已申请
   */
  private int applyState;

  /**
   * 行会创建时间
   */
  private long createTime;

  /**
   * 行会战力
   */
  private int nbValue;

  /**
   * 行会资金
   */
  private int unionFund;

  /**
   * 申请限制
   */
  private String joinCondition = new String();

  /**
   * 是否自动加入
   */
  private int autoJoin;

  /**
   * 会长是否在线 1在线
   */
  private int leaderOnline;

  /**
   * 行会boss排名
   */
  private int bossRanking;

  /**
   * 解散时间
   */
  private int time;

  /**
   * 召集令使用职位限制
   */
  private int callPosition;

  /**
   * 阵营类型
   */
  private int campType;

  /**
   * 行会扩建等级
   */
  private int expendLevel;

  /**
   * 行会宣言
   */
  private String xuanYan = new String();

  /**
   *  行会旗帜
   */
  private int flag;

  /**
   *  行会总战力
   */
  private long unionFightPower;

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setLeaderName(String leaderName) {
    this.leaderName = leaderName;
  }

  public String getLeaderName() {
    return this.leaderName;
  }

  public void setAnnouncement(String announcement) {
    this.announcement = announcement;
  }

  public String getAnnouncement() {
    return this.announcement;
  }

  public void setUnionLevel(int unionLevel) {
    this.unionLevel = unionLevel;
  }

  public int getUnionLevel() {
    return this.unionLevel;
  }

  public void setUnionNum(int unionNum) {
    this.unionNum = unionNum;
  }

  public int getUnionNum() {
    return this.unionNum;
  }

  public void setApplyState(int applyState) {
    this.applyState = applyState;
  }

  public int getApplyState() {
    return this.applyState;
  }

  public void setCreateTime(long createTime) {
    this.createTime = createTime;
  }

  public long getCreateTime() {
    return this.createTime;
  }

  public void setNbValue(int nbValue) {
    this.nbValue = nbValue;
  }

  public int getNbValue() {
    return this.nbValue;
  }

  public void setUnionFund(int unionFund) {
    this.unionFund = unionFund;
  }

  public int getUnionFund() {
    return this.unionFund;
  }

  public void setJoinCondition(String joinCondition) {
    this.joinCondition = joinCondition;
  }

  public String getJoinCondition() {
    return this.joinCondition;
  }

  public void setAutoJoin(int autoJoin) {
    this.autoJoin = autoJoin;
  }

  public int getAutoJoin() {
    return this.autoJoin;
  }

  public void setLeaderOnline(int leaderOnline) {
    this.leaderOnline = leaderOnline;
  }

  public int getLeaderOnline() {
    return this.leaderOnline;
  }

  public void setBossRanking(int bossRanking) {
    this.bossRanking = bossRanking;
  }

  public int getBossRanking() {
    return this.bossRanking;
  }

  public void setTime(int time) {
    this.time = time;
  }

  public int getTime() {
    return this.time;
  }

  public void setCallPosition(int callPosition) {
    this.callPosition = callPosition;
  }

  public int getCallPosition() {
    return this.callPosition;
  }

  public void setCampType(int campType) {
    this.campType = campType;
  }

  public int getCampType() {
    return this.campType;
  }

  public void setExpendLevel(int expendLevel) {
    this.expendLevel = expendLevel;
  }

  public int getExpendLevel() {
    return this.expendLevel;
  }

  public void setXuanYan(String xuanYan) {
    this.xuanYan = xuanYan;
  }

  public String getXuanYan() {
    return this.xuanYan;
  }

  public void setFlag(int flag) {
    this.flag = flag;
  }

  public int getFlag() {
    return this.flag;
  }

  public void setUnionFightPower(long unionFightPower) {
    this.unionFightPower = unionFightPower;
  }

  public long getUnionFightPower() {
    return this.unionFightPower;
  }
}
