package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.communication.msg.system.union.bean.UnionMemberInfoBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回行会增加成员
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionMemberAddMessage extends AbsProtostuffMessage {
  /**
   * 行会成员信息
   */
  private UnionMemberInfoBean memberInfo = new UnionMemberInfoBean();

  @Override
  public int getId() {
    return 23307;
  }

  public void setMemberInfo(UnionMemberInfoBean memberInfo) {
    this.memberInfo = memberInfo;
  }

  public UnionMemberInfoBean getMemberInfo() {
    return this.memberInfo;
  }
}
