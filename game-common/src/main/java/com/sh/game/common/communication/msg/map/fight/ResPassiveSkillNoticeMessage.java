package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 被动技能通告
 * 该文件由工具根据 fight.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResPassiveSkillNoticeMessage extends AbsProtostuffMessage {
  /**
   * 技能id
   */
  private int skillId;

  @Override
  public int getId() {
    return 69027;
  }

  public void setSkillId(int skillId) {
    this.skillId = skillId;
  }

  public int getSkillId() {
    return this.skillId;
  }
}
