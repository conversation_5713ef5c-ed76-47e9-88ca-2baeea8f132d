package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020/10/12 20:27
 */
@Getter
@Setter
@ConfigData(file = "cfg_gem")
public class StoneConfig extends AbstractConfigData {

    private int id;
    private int level;
    private int type;
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;
    private int skill_type;
    private int isbind;
    private int result1;
    private int result2;
    private int random_result1;
    private int random_result2;
}
