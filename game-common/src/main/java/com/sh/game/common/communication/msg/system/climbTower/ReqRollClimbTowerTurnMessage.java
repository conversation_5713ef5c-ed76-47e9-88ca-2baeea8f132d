package com.sh.game.common.communication.msg.system.climbTower;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求转
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRollClimbTowerTurnMessage extends AbsProtostuffMessage {
  /**
   * 类型
   */
  private int type;

  @Override
  public int getId() {
    return 309010;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
