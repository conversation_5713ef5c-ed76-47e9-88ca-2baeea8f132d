package com.sh.game.common.communication.msg.system.zhenYan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求进入真言塔
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqIntoZhenYanTowerMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 378006;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
