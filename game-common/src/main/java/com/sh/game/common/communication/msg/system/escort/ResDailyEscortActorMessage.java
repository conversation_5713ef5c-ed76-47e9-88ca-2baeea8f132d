package com.sh.game.common.communication.msg.system.escort;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 同步镖车信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDailyEscortActorMessage extends AbsProtostuffMessage {
  /**
   * 正在押送的镖车配置id
   */
  private int escortingCid;

  /**
   * 正在押送的镖车唯一id
   */
  private long escortingUid;

  /**
   * 状态 1成功/2失败
   */
  private int status;

  /**
   * 结束时间
   */
  private int endTime;

  /**
   * 血量
   */
  private long hp;

  /**
   * 目标地图
   */
  private int dest;

  /**
   * 地图
   */
  private int map;

  /**
   * 坐标x
   */
  private int x;

  /**
   * 坐标y
   */
  private int y;

  @Override
  public int getId() {
    return 105012;
  }

  public void setEscortingCid(int escortingCid) {
    this.escortingCid = escortingCid;
  }

  public int getEscortingCid() {
    return this.escortingCid;
  }

  public void setEscortingUid(long escortingUid) {
    this.escortingUid = escortingUid;
  }

  public long getEscortingUid() {
    return this.escortingUid;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public int getStatus() {
    return this.status;
  }

  public void setEndTime(int endTime) {
    this.endTime = endTime;
  }

  public int getEndTime() {
    return this.endTime;
  }

  public void setHp(long hp) {
    this.hp = hp;
  }

  public long getHp() {
    return this.hp;
  }

  public void setDest(int dest) {
    this.dest = dest;
  }

  public int getDest() {
    return this.dest;
  }

  public void setMap(int map) {
    this.map = map;
  }

  public int getMap() {
    return this.map;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }
}
