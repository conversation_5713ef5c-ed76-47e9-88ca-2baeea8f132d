package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/1/11
 * @Desc : to do anything
 */
@Getter
@Setter
@ConfigData(file="cfg_attribute_score")
public class AttributeScoreConfig extends AbstractConfigData {
    /**
     * 属性编号
     */
    private int id;
    /**
     * 战力分
     */
    private double[] score;
    /**
     * 0不发1发
     */
    private int transmit;


    /**
     * 需要广播的属性
     */
    private int broadcast;

    /**
     * 需要添加的属性
     */
    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] addpercent;

    /**
     * 触发类型
     */
    private int triggerType;

    /**
     * 触发效果
     */
    private int effectId;

    /**
     * 加成属性id
     */
    private int[] addiAttrId;

    /**
     * 抵抗属性id
     */
    private int[] antiAttrId;

    /**
     * 是否是进攻者触发 1是 0不是
     */
    private int isAttackerTrigger;

    private int levelParam;

    /**
     * 对应的抵抗属性
     */
    private int reductionAttrId;

    /**
     * 技能加成类型
     * 1=叠加（保持原有逻辑）
     * 2=放大（使用新公式）
     */
    private int type1;
}
