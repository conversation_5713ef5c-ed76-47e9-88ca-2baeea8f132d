package com.sh.game.common.communication.msg.system.houyuan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求召回老鼠
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHouYuanRecallRatMessage extends AbsProtostuffMessage {
  /**
   * 后院主人id
   */
  private long rid;

  /**
   *  后院道具在后院中的位置
   */
  private int index;

  @Override
  public int getId() {
    return 401004;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }
}
