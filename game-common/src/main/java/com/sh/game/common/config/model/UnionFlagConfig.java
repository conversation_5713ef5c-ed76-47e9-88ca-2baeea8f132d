package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23 15:08
 */
@Getter
@Setter
@ConfigData(file = "cfg_union_flag")
public class UnionFlagConfig extends AbstractConfigData {

    private int id;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> conditions;

}
