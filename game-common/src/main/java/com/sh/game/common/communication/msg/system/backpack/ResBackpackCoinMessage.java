package com.sh.game.common.communication.msg.system.backpack;

import com.sh.game.common.communication.msg.system.backpack.bean.BackpackCoinBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBackpackCoinMessage extends AbsProtostuffMessage {
  private List<BackpackCoinBean> coins = new ArrayList<>();

  @Override
  public int getId() {
    return 2103;
  }

  public void setCoins(List<BackpackCoinBean> coins) {
    this.coins = coins;
  }

  public List<BackpackCoinBean> getCoins() {
    return this.coins;
  }
}
