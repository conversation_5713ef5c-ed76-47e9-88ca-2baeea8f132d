package com.sh.game.common.communication.msg.system.trade;

import com.sh.game.common.communication.msg.system.trade.bean.TradeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTradeTradingMessage extends AbsProtostuffMessage {
  private List<TradeBean> trade = new ArrayList<>();

  @Override
  public int getId() {
    return 104012;
  }

  public void setTrade(List<TradeBean> trade) {
    this.trade = trade;
  }

  public List<TradeBean> getTrade() {
    return this.trade;
  }
}
