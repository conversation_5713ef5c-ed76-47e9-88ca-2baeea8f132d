package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.HellRoadConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地狱之路配置缓存
 *
 * <AUTHOR>
 * @date 2022/09/20 2:05
 */
@ConfigCache
public class HellRoadCache implements IConfigCache {

    private Map<Integer, List<HellRoadConfig>> configMap = new HashMap<>();

    @Override
    public void build() {
        configMap.clear();
        List<HellRoadConfig> configList = ConfigDataManager.getInstance().getList(HellRoadConfig.class);
        for (HellRoadConfig config : configList) {
            List<HellRoadConfig> configGroupList = configMap.getOrDefault(config.getType(), new ArrayList<>());
            configGroupList.add(config);
            configMap.put(config.getType(), configGroupList);
        }
    }

    /**
     * 根据奖励类型获取奖励配置列表
     *
     * @param type 奖励类型
     * @return List<HellRoadConfig> 奖励配置列表
     */
    public List<HellRoadConfig> findConfigList(int type) {
        return configMap.getOrDefault(type, new ArrayList<>());
    }
}
