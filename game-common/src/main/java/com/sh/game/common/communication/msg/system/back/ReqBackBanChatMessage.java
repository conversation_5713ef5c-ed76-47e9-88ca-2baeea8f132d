package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求玩家禁言
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBackBanChatMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * 0slience 2soliloquize
   */
  private int type;

  private int time;

  private String reason = new String();

  @Override
  public int getId() {
    return 43104;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setTime(int time) {
    this.time = time;
  }

  public int getTime() {
    return this.time;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public String getReason() {
    return this.reason;
  }
}
