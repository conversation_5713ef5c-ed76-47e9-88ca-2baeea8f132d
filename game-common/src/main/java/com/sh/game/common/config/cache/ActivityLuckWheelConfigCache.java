package com.sh.game.common.config.cache;


import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.ActivityLuckWheelConfig;

import java.util.List;

@ConfigCache
public class ActivityLuckWheelConfigCache implements IConfigCache {


    /**
     * key1: 活动类型
     * key2: 充值金额
     * value: 幸运转盘配置
     */
    private Table<Integer, Integer, ActivityLuckWheelConfig> cache = HashBasedTable.create();


    @Override
    public void build() {
        cache.clear();
        List<ActivityLuckWheelConfig> configList = ConfigDataManager.getInstance().getList(ActivityLuckWheelConfig.class);
        for (ActivityLuckWheelConfig config : configList) {
            cache.put(config.getType(), config.getRecharge(), config);
        }
    }

    /**
     * 查找转盘配置
     *
     * @param actType  活动类型
     * @param recharge 充值金额
     * @return 转盘配置
     */
    public ActivityLuckWheelConfig findActivityLuckWheelConfigByRecharge(int actType, int recharge) {
        return cache.get(actType, recharge);
    }

}
