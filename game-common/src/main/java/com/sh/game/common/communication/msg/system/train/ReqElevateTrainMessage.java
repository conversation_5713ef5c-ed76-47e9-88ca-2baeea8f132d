package com.sh.game.common.communication.msg.system.train;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求接受任务
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqElevateTrainMessage extends AbsProtostuffMessage {
  /**
   * 培养目标类型，0：主角，1：英雄
   */
  private int targetType;

  /**
   * 培养类型
   */
  private int trainType;

  @Override
  public int getId() {
    return 11001;
  }

  public void setTargetType(int targetType) {
    this.targetType = targetType;
  }

  public int getTargetType() {
    return this.targetType;
  }

  public void setTrainType(int trainType) {
    this.trainType = trainType;
  }

  public int getTrainType() {
    return this.trainType;
  }
}
