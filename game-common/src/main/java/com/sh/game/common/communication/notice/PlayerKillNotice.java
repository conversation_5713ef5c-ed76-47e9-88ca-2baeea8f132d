package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Notice
public class PlayerKillNotice extends ProcessNotice {

    private long rid;

    private long killer;

    private int mapId;
    /**
     * 是否能添加仇人列表
     */
    private boolean canAddEnemy;

    public PlayerKillNotice() {

    }

    public PlayerKillNotice(long rid, long killer, int mapId) {
        this.rid = rid;
        this.killer = killer;
        this.mapId = mapId;
    }
}
