package com.sh.game.common.communication.msg.system.houyuan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求获取道具
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHouYuanTakeItemMessage extends AbsProtostuffMessage {
  /**
   * 后院主人id
   */
  private long rid;

  /**
   *  后院道具在后院中的位置
   */
  private int index;

  /**
   *  老鼠数量，包括玩家可以先前就在这个道具上的老鼠数量
   */
  private int count;

  @Override
  public int getId() {
    return 401003;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
