package com.sh.game.common.communication.msg.system.levelreward;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求获取等级奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqGainLevelRewardMessage extends AbsProtostuffMessage {
  /**
   * cfg_grade_reward表id
   */
  private int cid;

  @Override
  public int getId() {
    return 323003;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
