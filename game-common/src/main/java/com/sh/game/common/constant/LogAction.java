package com.sh.game.common.constant;

/**
 * 日志
 *
 * <AUTHOR>
 */
public enum LogAction {
    UNSURE(0, "未知"),
    GM(1, "GM"),
    GM_REMOVE(2, "GM命令移除"),
    ITEM_USE(3, "使用道具"),
    COIN_ITEM_USE(4, "使用货币道具"),
    UP_LEVEL_DAN_USE(5, "使用升级丹"),
    ITEM_COMPOUND(6, "合成道具"),
    ADD_MAX_COUNT(7, "背包扩充"),
    USE_JINGYAN_DAN(8, "使用经验丹"),
    USE_WING_DAN(9, "使用光翼直升丹"),
    USE_XIUWEI_DAN(10, "使用修为丹"),
    USE_UNION_CONTRIBUTION_ITEM(11, "使用帮贡道具"),
    BACK_SEND(12, "后台发送"),
    BACK_REMOVE(13, "后台移除"),
    BACK_RECHARGE(14, "后台充值"),
    PLATFORM_RECHARGE(15, "平台接口充值"),
    USE_SKILL_BOOK_GET(16, "使用技能书获得"),
    NAME_AUTH_REWARD(17, "实名认证奖励"),
    HUNT_CREDITS(18, "寻宝积分"),
    USE_ROLE_NAME_CHANGE_CARD(19, "使用玩家改名卡"),
    USE_UNION_NAME_CHANGE_CARD(20, "使用帮会改名卡"),
    DELETE_SPECIAL_ITEM(21, "摧毁特殊道具"),
    INNER_POWER_UPLEVEL_COST(22, "内功升级消耗"),
    THROUGH_RECHARGE_GET(23, "限时直购获得"),
    BAG_COMPOUND(24, "背包合成"),
    BAG_SELL_ITEM(25, "出售道具"),
    BAG_SELL_ITEM_ADD(26, "售卖道具获得"),
    STORAGE_TAKE_OUT(27, "仓库取出"),
    STORAGE_EXTEND(28, "仓库扩容"),
    STORAGE_PUT_IN(29, "放进仓库"),
    TRADE_REMOVE(30, "交易移除"),
    TRADE_GAIN(31, "交易获得"),
    TRADE_ROLLBACK(32, "交易回滚"),
    UP_LEVEL_DAN_USE_BACK(33, "使用升级丹返还"),
    JEWELRY_INLAY(34, "宝石镶嵌"),
    JEWELRY_TAKE_OUT(35, "宝石取出"),
    UNLOCK_JEWELRY_GROOVE(36, "解锁宝石槽消耗"),
    JEWELRY_UPGRADE_COST(37, "宝石合成升级消耗"),
    JEWELRY_UPGRADE_PRODUCT(38, "宝石合成成功产出"),
    UPGRADE_QUCKLY_BUY_GET(39, "合成升级快速购买获得"),
    RECHARGE_GIVE_ITEM(40, "充值赠送道具"),
    DRAW_FIRST_RECHARGE_ITEM_USE(41, "首充礼包道具领取"),
    SKILL_ACTIVE(42, "激活技能"),
    SKILL_UP(43, "升级技能"),
    OPEN_BOX(44, "开宝箱"),
    ACTIVITY(45, "活动"),
    ACTIVITY_LOGIN_GIFT_REWARD(46, "每日登录奖励"),
    ACTIVITY_SIGN_DAY_REWARD(47, "每日签到奖励"),
    BIQI_SCORE_REWARD(48, "比奇积分领奖"),
    ACTIVITY_LIMIT_TIME_REWARD(49, "限时活动领奖"),
    ACTIVITY_LIAN_ZHI(50, "经验炼制"),
    GIFT_CARD(51, "礼包卡"),
    MOBAI(52, "膜拜"),
    ACTIVITY_REWARD_MULTI_GET_COST(53, "活动奖励多倍领取消耗"),
    ALL_PEOPLE_BOSS(54, "全民BOSS"),
    COMB_RECHARGE_RETURN(55, "连充返利"),
    COMBINE_DAY_RECHARGE(56, "合服每日充值"),
    COMBINE_TOTAL_LOGIN(57, "合服累计登录"),
    FIGHT_POWER_RANK(58, "战力排行"),
    FIX_TIME_LOGIN_NEW(59, "新登录领奖"),
    FIX_TIME_LOGIN(60, "登录领奖"),
    GOD_SWORD_PK(61, "神剑竞技"),
    HUNT_GIFT(62, "寻宝送礼"),
    LUCK_TREASURE(63, "财富幸运儿"),
    WEEK_TOTAL_RECHARGE(64, "每周累计充值"),
    MAGIC_WEAPON_RANK(65, "法宝冲榜"),
    MAGIC_WEAPON_PK(66, "法宝竞技"),
    SPEND_RANK(67, "消费排行"),
    WING_PK(68, "光翼竞技"),
    GOD_GONG_RANK(69, "神弓冲榜"),
    GOD_GONG_PK(70, "神弓竞技"),
    FIGHT_POWER_PK(71, "战力竞技"),
    BOSS_FIRST_KILL_GET(72, "boss首杀领奖"),
    HEART_METHOD_PK(73, "心法竞技"),
    EQUIPMENT_FIGHTPOWER_PK(74, "装备战力竞技"),
    JUNXIAN_PK(75, "军衔竞技"),
    LUCKY_WHEEL_COST(76, "幸运转轮消耗"),
    LUCKY_WHEEL_GET(77, "幸运转轮获得"),
    DRAW_FOREVER_SIGN_REWARD(78, "领取永久签到奖励"),
    ILLUSTRATION_PK(79, "图鉴竞技"),
    HAPPY_SEVEN_DAY_ACTIVITY_REWARD(80, "七日狂欢活动奖励"),
    RAFFLE(81, "抽奖活动"),
    DAY_RECHARGE(82, "每日充值指定金额"),
    CRAZY_HAPPY_GOAL_REWARD(83, "狂欢活动目标奖励"),
    CRAZY_HAPPY_TOTAL_REWARD(84, "狂欢活动总奖励"),
    INVEST_PLAN_REWARD(85, "投资计划奖励"),
    DRAGON_BALL_REWARD(86, "龙珠寻宝额外奖励"),
    BACK_RECHARGE_REWARD(87, "充值返还"),
    CRAZY_TURNTABLE(88, "狂欢转盘"),
    CHOSE_TURNTABLE(89, "自选转盘"),
    DIAMOND_EQUIP_RECYCLE_CONSUME(90, "钻石装备回收消耗"),
    BUY_INVESTMENT_SEVEN_DAY(91, "购买七日投资"),
    REWARD_INVESTMENT_SEVEN_DAY(92, "七日投资奖励"),
    CUT_PK(93, "切割竞技"),
    REFINE_PK(94, "精炼竞技"),
    TREASURE_PK(95, "宝物竞技"),
    LEVEL_PK(96, "等级竞技"),
    TREASURE_HUNT_BONFIRE(97, "全民寻宝，篝火泡点奖励"),
    TIAN_GONG_CI_FU(98, "天宫赐福奖励"),
    MONTH_CARD_DAY_REWARD(99, "领取月卡每日奖励"),
    MONTH_CARD_IMMEDIATE_REWARD(100, "月卡立即返利"),
    BUY_MONTH_CARD(101, "购买月卡"),
    FIRST_RECHARGE_GET(102, "首充领奖"),
    DAY_RECHARGE_GET(103, "每日充值领奖"),
    FIRST_BUY_RECHARGE_GET(104, "首充"),
    VIP_REWARD(105, "vip奖励"),
    BUY_RECHARGE_GET(106, "充值"),
    BUY_SPEC_MONTH_CARD(107, "购买特殊月卡"),
    TREASURE_ACTIVATE(108, "宝物激活"),
    TREASURE_UP(109, "宝物升级"),
    TREASURE_HUNT_COST_ITEM(110, "消耗道具寻宝"),
    TREASURE_TAKEOUT(111, "寻宝装备取出"),
    TREASURE_COST(112, "寻宝消耗"),
    DRAW_GOD_EQUIP_HUNT_PROCESS_REWARD(113, "领取神装寻宝进度奖励"),
    DRAW_EQUIP_HUNT_PROCESS_REWARD(114, "领取装备寻宝进度奖励"),
    EQUIP_TREASURE_COST(115, "装备寻宝消耗"),
    HEART_METHOD_TREASURE_COST(116, "心法寻宝消耗"),
    GOD_EQUIP_TREASURE_COST(117, "神装寻宝消耗"),
    GROW_TRAIL_GOAL_REWARD(118, "成长试炼目标奖励"),
    GROW_TRAIL_FINAL_REWARD(119, "成长试炼终极奖励"),
    HANG_UP(120, "挂机掉落"),
    BUY_ATTACK_DRAG(121, "攻击药水购买"),
    FIRE_PIG_INSTANCE_GET(122, "烧猪副本获得"),
    TIAN_TI_CHALLENGE_COUNT_BUY(123, "购买天梯挑战次数"),
    FENGMO_GET_BACK(124, "封魔副本找回奖励"),
    BOSS_COUNT_BUY(125, "购买boss次数"),
    TIAN_TI_CHALLENGE_REWARD(126, "天梯挑战结算奖励"),
    SHENLONGMIJING_BOSS_COUNT_BUY(127, "购买神龙秘境次数"),
    HUANGLING_BOSS_COUNT_BUY(128, "购买皇陵次数"),
    CROSS_ELEMENT_BOSS_COUNT_BUY(129, "购买元素BOSS次数"),
    ELEMENT_SWEEP(130, "元素副本扫荡"),
    ELEMENT_PK(131, "元素副本竞技"),
    ELEMENT_PASS(132, "元素副本通关"),
    ELEMENT_CHALLENGE_COUNT_BUY(133, "元素副本次数购买"),
    TREASRE_HUNT_ALL_PEOPLE_DIGGING(134, "全民寻宝副本挖到物品直接进背包"),
    NIMOGUZHEN_DUPLICATE_REWARD(135, "逆魔古镇副本奖励"),
    UNION_CREATE(136, "创建帮会"),
    ROB_UNION_RED_PACK(137, "抢帮会红包获得"),
    SEND_UNION_RED_PACK(138, "发帮会红包"),
    IMPEACH_LEADER(139, "弹劾会长"),
    EQUIP_RECYCLE(140, "回收装备"),
    EQUIP_PUT_ON(141, "穿装备"),
    EQUIP_PUT_OFF(142, "脱装备"),
    UP_GOD_SWORD_ARMOR(143, "神剑神甲升级"),
    COMBINE_GOD_SWORD_ARMOR(144, "神剑神甲合成"),
    CULTIVATE_SOUL_EQUIP_FU_LING(145, "炼魂装备附灵"),
    CULTIVATE_SOUL_EQUIP_ZHU_HUN(146, "炼魂装备铸魂"),
    UP_LEVEL_LEI_SHEN(147, "雷神装备升级"),
    UP_LEVEL_LIANLEI(148, "练雷升级"),
    ROLE_UP_REIN(149, "角色升级转生"),
    ROLE_EXCHANGE_REIN(150, "角色降级兑换修为"),
    ROLE_ACTIVATE_WING(151, "激活角色光翼"),
    ROLE_UP_WING(152, "升级角色光翼"),
    ROLE_STRENGTHEN(153, "角色强化"),
    ROLE_PUT_ON_WING_EQUIP(154, "角色穿戴光翼装备"),
    ROLE_SHENZHU(155, "角色铸造"),
    ROLE_REFINE(156, "角色精炼"),
    ROLE_OFFICIAL_UP(157, "角色官职晋级"),
    ROLE_JUNXIAN_UP(158, "角色军衔升级"),
    ROLE_FENGHAO_UP(158, "角色封号升级"),
    ROLE_DRAW_JUNXIAN_SALARY(159, "领取军衔俸禄"),
    ROLE_GODAX_UP(160, "角色神斧升级"),
    ROLE_ACTIVE_LINGQI(161, "角色激活灵器"),
    ROLE_PUT_OFF_WING_EQUIP(162, "角色脱下光翼装备"),
    EXP_BUFF(163, "经验buff"),
    DELIVER_COST(164, "传送消耗"),
    REIN_PK(165, "转生竞技"),
    TITLE_TIMEOUT(166, "称号过期"),
    WEIWANG_DAY_RECYCLE(167, "每日回收威望"),
    RANK_UPDATE_REMOVE_TITLE(168, "排行榜更新移除称号"),
    WEIWANG_GLOBE_DRAW(169, "领取威望球"),
    LEVEL_UP_TITLE(170, "升级时装"),
    STORE_BUY(171, "商城购买"),
    MANUAL_FRESH_MYSTERY_SHOP(172, "手动刷新神秘商店"),
    ITEM_SHOP_BUY(173, "道具商店购买"),
    MYSTERY_SHOP_BUY(174, "神秘商店购买"),
    VIP_SHOP_BUY(175, "VIP礼包购买"),
    LINGHUN_QUICK_BUY(176, "灵魂之力快捷购买"),
    ZHENQIDAN_QUICK_BUY(177, "真气丹快捷购买"),
    XIUWEIDAN_QUICK_BUY(178, "修为丹快捷购买"),
    HERO_XIUWEIDAN_QUICK_BUY(179, "英雄修为丹快捷购买"),
    ACTIVITY_LIMIT_TIME_BUY(180, "活动限购礼包购买"),
    SHENBIN_GIFT_BUY(181, "神兵礼包购买"),
    WING_GIFT_BUY(182, "羽翼礼包购买"),
    COMBINE_VIP_SHOP(183, "合服VIP礼包购买"),
    OPEN_SERVER_DAY_LIMIT_BUY(184, "开服每日限购"),
    LIANHUN_LIMIT_BUY(185, "炼魂限购"),
    JIEJIE_LIMIT_BUY(186, "结界限购"),
    OPEN_SERVER_GIFT_BUY(187, "开服礼包"),
    REIN_QUICK_BUY(188, "转生快捷购买"),
    LIANHUN_QUICK_BUY(189, "炼魂快捷购买"),
    TREASURE_QUICK_BUY(190, "宝物快捷购买"),
    QICAI_STONE_QUICK_BUY(191, "七彩灵石快捷购买"),
    SHOPPING_CART_BUY(192, "购物车商品购买"),
    LOGIN_IN(193, "登录游戏"),
    LOGIN_OUT(194, "退出游戏"),
    CREATE_CHARACTER(195, "创建角色"),
    FORENOTICE_REWARD(196, "功能预告奖励"),
    FCM_VALIDATE(197, "防沉迷认证奖励"),
    EMAIL_GET(198, "邮件获得"),
    SHOBAK_SCORE_ATTACH_REWARD(199, "龙城争霸积分达标奖励"),
    REMOVE_LAST_SHOBAK_UNION_FASHION(200, "去除旧的霸主帮会时装奖励"),
    SHOBAK_UNION_CHANGE_LEADER_REMOVE_FASHION(201, "霸主帮会会长更换移除时装"),
    QUIT_SHOBAK_UNION_REMOVE_FASHION(202, "退出霸主帮会移除时装"),
    DRAW_DAILY_ACTIVE_REWARD(203, "领取日活奖励"),
    DAILY_TASK_COMPLETE(204, "每日任务完成"),
    OFFICIAL_UP_REWARD(205, "升级官职奖励"),
    TOWER_DAILY_REWARD(206, "通天塔每日领奖"),
    TURNTABLE_OPEN_REWARD(207, "通天塔轮盘摇奖"),
    HEART_METHOD_UP_COST(208, "升级心法消耗"),
    HEART_METHOD_LEARN_COST(209, "学习心法消耗"),
    HEART_METHOD_COMPOUND_COST(210, "心法合成消耗"),
    HEART_METHOD_COMPOUND_GET(211, "心法合成获得"),
    RECYCLE_XINFA_GET(212, "心法回收"),
    REPLACE_XINFA(213, "心法替换"),
    DRAW_REDENVELOP(214, "领取红包"),
    TREASURE_MAP(215, "藏宝图"),
    USE_TREASURE_MAP(216, "挖藏宝图"),
    TREASURE_MAP_COLLECT(217, "藏宝图采集"),
    DUPLICATE_REWARD(218, "副本领奖"),
    ENTER_WORLD_BOSS(219, "进入副本"),
    RELIVE(220, "原地复活"),
    ENTER_WORD_BOSS_COST(221, "进副本消耗"),
    PICK_UP(222, "地图拾取"),
    LEVEL_UP_ILLUSTRATION(223, "升级图鉴"),
    TOTEM_LEVEL_UP(224, "升级图腾"),
    WUSHU_REPLACE(225, "武术置换"),
    WUSHU_LOCK(226, "武术锁定"),
    JIEJIE_ACTIVE(227, "激活结界"),
    JIEJIE_UP_TIANFU(228, "结界升级天赋"),
    JIEJIE_UP_XIANDUAN(229, "结界升级仙锻"),
    SHENBING_FRAGMENT_EQUIP(230, "神兵碎片穿上"),
    SHENBING_FRAGMENT_UNEQUIP(231, "神兵碎片脱下"),
    RENHUANG_BUY_COUNT(232, "人皇购买次数"),
    RENHUANG_FIGHT_REWARD(233, "人皇战斗奖励"),
    RENHUANG_SWEEP_REWARD(234, "人皇扫荡奖励"),
    UP_NIE_PAN(235, "涅槃升级"),
    AWAKE_EQUIP(236, "装备觉醒元素"),
    AWAKE_UNEQUIP(237, "卸下觉醒元素"),
    AWAKE_STRENGTHEN(238, "强化装备位-觉醒元素"),
    TASK_WEEKLY_REWARD(239, "单任务奖励"),
    TASK_SUBMIT(240, "任务完成"),
    RANDOM_BOSS_COMMON_REFRESH(241, "随机boss普通刷新"),
    RANDOM_BOSS_BEST_REFRESH(242, "随机boss满星刷新"),
    RANDOM_BOSS_COMPLETE_TASK(243, "立即完成随机boss任务"),
    SHENWEI_COMPLETE_TASK(244, "神威任务一键完成"),
    DEMON_REMOVAL_MUTIREWARD_COST(245, "除魔任务多倍领取消耗"),
    RECYCLE_REFRESH_STAR(246, "回收任务刷新星级"),
    RECYCLE_DOUBLE_TASK(247, "回收任务双倍领取"),
    UP_SERVANT_COST(248, "宠物升阶消耗"),
    TOWER_BAILIAN(249, "百炼塔"),
    PLAYER_DIE_DROP(250, "玩家死亡掉落"),
    DISCARD(251, "玩家丢弃"),
    ESCORT_INSURE(252, "镖车投保"),
    ADDITEM_UNION(253, "帮派仓库兑换"),
    REMOVE_UNION(254, "帮派仓库捐献"),
    CUT_UP(255, "分割升级"),
    XUAN_SHANG_BOSS(256, "悬赏boss奖励"),
    REWARD_FIND_BACK(257, "资源找回"),
    ONLINE_REWARD(258, "在线奖励"),
    HATCH_EGGS_COST(259, "孵蛋消耗"),
    HATCH_EGGS_UNLOCK_SLOT(260, "孵蛋解锁槽位"),
    HATCH_EGGS_COMPLETE_IMMEDIATELY(261, "孵蛋立即完成消耗"),
    DRAW_HATCH_EGGS_REWARD(262, "孵蛋完成领奖"),
    FUNCTION__OPEN_REWARD(263, "功能开放奖励"),
    BEGIN_STALL__COST(264, "开始摆摊消耗"),
    ITEM_ADD_TO_STALL(265, "道具上架"),
    ITEM_REMOVE_FROM_STALL(266, "道具下架"),
    BUY_STALL_GOODS(267, "摊位购买"),
    OVER_STALL(268, "收摊"),
    BUY_STALL_ROLL_BACK(269, "购买商品回滚"),
    BEGIN_STALL_COST_BACK(270, "开始摆摊消耗回滚"),
    LEASE_COVENANT_BUY(271, "租赁契约购买"),
    LONG_SHEN_MI_BAO_SCORE_REWARD(272, "龙神秘宝积分奖励"),
    YAN_MO_SHEN_YU_COST(273, "炎魔神狱消耗"),
    RESOLVE(274, "背包分解"),
    REFINEMENT_FOR_REFINING(275, "修为炼制"),
    HUNT_RECYCLE(276, "寻宝回收"),
    PHANTOM_STAIRS(277, "幻兽升到7阶"),
    CUT_STAIRS(278, "切割升到7阶"),
    WING_STAIRS(279, "光翼升到7阶"),
    TREASURE_STAIRS(280, "宝物升到7阶"),
    MICRO_TERMINAL_LOGIN_REWARD(281, "微端登录领奖"),
    WING_UPS(282, "使用光翼7阶特殊道具"),
    TREASURE_UPS(283, "使用宝物7阶特殊道具"),
    INSURE_EQUIP(284, "投保装备"),
    WARRING_SPIRIT_PK(285, "战灵竞技"),
    UP_SHOU_HU(286, "升级守护"),
    UP_SHOU_HU_TALENT(287, "升级守护天赋"),
    GUARD_RANK(288, "守护冲榜"),
    GUARD_LIANZHI(289, "守护炼制"),
    SHUXINGDAN(290, "属性丹"),
    OPENSERVICESPRINT(291, "开服冲刺"),
    STAREQUIPCOMPOUND(292, "合成星装"),
    STAREQUIPRESOLVE(293, "星装分解"),
    UP_LEGEND(294, "传说升级"),
    TREASUREWAREHOUSE(295, "寻包仓库回收"),
    STARMATERIALCOMPOUND(296, "星装材料合成"),
    UNION_DONATE(297, "帮会捐献"),
    UP_UNION_SKILL(298, "帮会技能升级"),
    FAKEVIP(299, "假vip奖励"),
    YMSYMULTIPLEREWARD(300, "炎魔神域多倍领取奖励"),
    COMBINE_VIP_SHOP1(301, "合服VIP礼包购买"),
    COMBINE_VIP_SHOP2(302, "合服VIP礼包购买"),
    MOBILE_LOGIN_REWARD(303, "H5登录领奖"),
    FAQI(304, "法器"),
    OFFLINE_REWARD(305, "离线奖励"),
    GENIUS_RESET(306, "天赋重置"),
    LUCKY_DROP(307, "幸运掉落"),
    ZHU_SHEN_REMOVE_FASHION(308, "诛神榜去除称号"),
    UP_PET(309, "升级灵宠"),
    UP_FAZHEN(310, "升级法阵"),
    UP_FAZHENTIANFU(311, "升级法阵天赋"),
    STAR(312, "星辰之力"),
    LIMIT_SURPRISE(313, "限时惊喜奖励"),
    ONLY_GOAL_FINAL_REWARD(314, "纯goal类型活动终极大奖"),
    PASSION_DROP_COST(315, "激情大爆消耗"),
    CONSUMPTION_RETURNS(316, "消费返还"),
    LIFE_SOUL(320, "命魂系统"),
    SHENGXIAO_REFINE(321, "生肖精炼"),
    DECOMPOSE(322, "分解"),
    RUINS_BOSS_REWARD(323, "遗迹boss奖励"),
    UP_LEVEL_DRAGON_KING(324, "升级龍皇装备"),
    EXPLORE(325, "仙灵宝藏探险"),
    DOUBLE_RECHARGE(326, "双倍充值奖励"),
    DRAGON_PATTERN_INLAY(327, "龙纹系统镶嵌消耗"),
    DRAGON_PATTERN_TAKEOFF(328, "龙纹系统取下增加"),
    DRAGON_PATTERN_RECOVERY(329, "龙纹系统回收变化"),
    DRAGON_PATTERN_STRENGTHEN(330, "龙纹系统增强消耗"),
    WELFARE_TURNTABLE(331, "福利转盘活动奖励"),
    PVP_CHALLENGE_COUNT_BUY(332, "pvp次数购买"),
    PLATFORM_GIFT(333, "平台礼包"),
    LUCKY_DIAL(334, "幸运转盘"),
    ACTIVATION_CODE_REWARD(335, "激活码领取"),
    UP_LEVEL_FULING(336, "龍皇附灵升级"),
    UPGRADE_PRODUCT(337, "合成成功产出"),
    SHENBING_COMPOUND(338, "神兵合成"),
    GOSSIP_COMPOUND(339, "八卦装备置换"),
    LINGSHOU_HEART(340, "灵兽之心升级消耗"),
    MAGIC_DEVICE(342, "魔器升级"),
    MAGIC_STAR(343, "魔器星图升级"),
    PVP_KILL_MONSTER_REWARD(344, "pvp击杀怪物奖励"),
    PVP_END_REWARD(345, "pvp胜负奖励"),
    PVP_KILL_PLAYER_REWARD(346, "pvp击杀玩家奖励"),
    UP_LEVEL_SHEN_BING_PU(347, "升级神兵谱"),
    PVP_TIANT_REWARD(348, "pvp天梯 对局奖励"),
    INFINITREBATE(349, "无限返利活动奖励"),
    SOUGOU_INPUT_METHOD_LOGIN_REWARD(350, "搜狗输入法登录奖励"),
    SEVEN_STAR_LEVEL_UP_COST(351, "七星系统升级消耗"),
    BREAKTHROUGH_SKILL_COST(352, "突破技能消耗"),
    PASS_DUPLICATE_REWORD(353, "轮回魔域奖励"),
    UP_LEVEL_TREASURE_CARD(354, "升级藏品卡"),
    TREASURE_CARD_RECOVERY(355, "藏品卡片回收"),
    UP_LEVLE_LEI_SHEN_ZENG_FU(356, "雷神增幅升级"),
    UP_LEVLE_DRAGON_KING_ZENG_FU(357, "巃皇增幅升级"),
    CANYON_HEGEMONY_SCORE_REWARD(358, "峡谷争霸积分奖励"),
    CANYON_HEGEMONY_END_REWARD(359, "峡谷争霸结算奖励"),
    INVESTMENT_KILL_DEMON(360, "灭魔计划购买"),
    INVESTMENT_SANCTUARY(361, "圣域计划购买"),
    INVESTMENT_PET_GROW_UP(362, "幻兽成长购买"),
    INVESTMENT_PET_EXAM(363, "幻兽试炼购买"),
    INVESTMENT_BATTLE_EXAM(364, "战场试炼购买"),
    INVESTMENT_MESS_EXAM(365, "混沌试炼购买"),
    INVESTMENT_REIN(366, "转生投资购买"),
    INVESTMENT_SANCTUARY_EXAM(367, "圣域试炼购买"),
    INVESTMENT_HELP_GROW_UP(368, "成长助力购买"),
    INVESTMENT_DESTROY_WORLD(369, "灭世计划购买"),
    INVESTMENT_THUNDER_GOD(370, "雷神投资购买"),
    INVESTMENT_DRAGON_GOD(371, "龙皇投资购买"),
    INVESTMENT_GOD_EQUIPMENT_EXAM(372, "神装试炼购买"),
    INVESTMENT_THE_VOID_EXAM(373, "虚空试炼购买"),
    INVESTMENT_SECRET_KILL_MONSTER(374, "降妖密令购买"),
    INVESTMENT_HONOR_WAY(375, "荣誉之征购买"),
    INVESTMENT_HERO_EXAM(376, "英雄试炼购买"),
    INVESTMENT_BRAVE_EXAM(377, "勇者试炼购买"),
    INVESTMENT_KING_EXAM(378, "勇者试炼购买"),
    FAST_COMPLETE_ESCORT(379, "一键押镖"),
    CANYON_HEGEMONY_KILL_REWARD(380, "峡谷争霸击杀奖励"),
    SPOKE_MAN_DUPLICATE_COUNT_BUY(381, "购买代言人副本次数"),
    CREATE_ALLIANCE_COST(382, "创建同盟消耗"),
    WECHAT_COLLECT_REWARD(383, "微信小游戏收藏奖励"),
    WECHAT_FLOAT_REWARD(384, "微信小游戏浮窗奖励"),
    BUY_CANYON_HEGEMONY_COUNT(385, "峡谷争霸购买消耗"),
    UP_SMELT(386, "熔炼"),
    CROSS_STAY_POINT_REWARD(387, "跨服泡点奖励"),
    BUY_CROSS_STAY_POINT_COUNT(388, "购买跨服泡点次数消耗"),
    PASSION_DROP_SWEEP_REWARD(389, "修罗副本奖励奖励"),
    CROSS_SHABAKE_INTEHERL_REWARD(390, "群雄逐鹿积分奖励"),
    PASSION_DROP_SWEEP_COST(391, "修罗副本扫荡消耗"),
    PASS_DUPLICATE_SWEEP_REWARD(392, "轮回魔域扫荡奖励"),
    ASSIST_REWARD(393, "协助奖励"),
    PVP_SBK_PERSON_POINT_SCORE_REWARD(394, "群雄逐鹿个人积分目标奖励"),
    PVP_SBK_PERSON_RANK_REWARD(395, "群雄逐鹿玩家积分排名奖励"),
    PVP_SBK_UNION_RANK_REWARD(396, "群雄逐鹿帮会积分排名奖励"),
    RELAX_PLAY_REWARD(397, "春节轻松玩法奖励"),
    PVP_SBK_TRANSMIT_COST(398, "pvp沙巴克npc传送消耗"),
    ZXACCEQUIP_UPGRADE(399, "诛仙部件装备升阶消耗"),
    CROSS_STAY_POINT_KILL_REWARD(400, "跨服泡点击杀奖励"),
    ZODIAC_LEVEL_UP_COST(401, "十二星座系统升级消耗"),
    PET_ILLUSION_LEVEL_UP(402, "灵宠幻化升级消耗"),
    WELFARE_RECHARGE_GET(403, "福利充值获得"),
    UP_SPI(404, "升级本命灵宝"),
    UP_SPI_TALENT(405, "升级本命灵宝天赋"),
    HERO_EXAM_DUPLICATE_REWARD(406, "英雄试炼副本奖励"),
    HERO_EXAM_TIME_REWARD(407, "英雄试炼固定时间奖励"),
    LEVEL_UP_GODDESS_TEAR(408, "升级女神之泪消耗"),
    HERO_EXAM_MULTI_GET_COST(409, "英雄试炼多倍领取消耗"),
    MEDAL_FREE_REWARD(410, "免费勋章领奖"),
    MEDAL_PAY_REWARD(411, "付费勋章领奖"),
    ACTIVE_PAY_MEDAL(412, "激活付费勋章消耗"),
    STRENGTH_TITLE_COST(413, "强化称号消耗"),
    UP_LEVEL_WU_XING(414, "升级五行装备"),
    UP_LEVEL_WX_STREN(415, "升级五行淬炼"),
    UP_LEVLE_YUSHOUPU(416, "激活升级御兽谱"),
    ACHIEVEMENT_REWARD(417, "成就目标奖励"),
    ACHIEVEMENT_SUMMARY_REWARD(418, "成就点累计奖励"),
    UP_LEVEL_ENCHANT(419, "装备附魔"),
    DEVIL_RUINS_ASSIST_REWARD(420, "神魔遗迹协助奖励"),
    DEVIL_RUINS_OWNER_REWARD(421, "神魔遗迹归属者奖励"),
    PET_EQUIP_RECYCLE(422, "灵宠回收装备"),
    HUNT_WARE_HOUSE_RECYCLE(423, "寻宝仓库回收"),
    DIE_DROP_FROM_ROLE_EQUIP(424, "死亡身上掉落"),
    BOURSE_PUT_ON(425, "拍卖上架"),
    BOURSE_PUT_ON_ROLL_BACK(426, "拍卖上架回退"),
    BOURSE_PUT_DOWN(427, "拍卖下架"),
    BOURSE_BAN(428, "拍卖行一口价"),
    BOURSE_BAN_COST_ROLL_BACK(429, "拍卖行一口价购买回退"),
    BOURSE_BIDDING(430, "拍卖行竞拍"),
    BOURSE_BIDDING_ROLL_BACK(431, "拍卖行竞拍回滚"),
    RECYCLE_TASK_COST(432, "捐献任务一键完成消耗"),
    RESET_HOLY_LAND_COST(433, "重置修炼圣地副本状态消耗"),
    GET_PRACTICE_SCORE(434, "获取修炼积分"),
    GOD_VIP_REWARD(435, "皇权vip礼包奖励"),
    BOURSE_BIDDING_COST_BACK(436, "拍卖行竞拍返还"),
    BOURSE_BIDDING_COST_BACK_BY_BAN(437, "拍卖行被一口价竞拍返还"),
    UP_SHRENGTH_ZENGFU(438, "神铸增幅升级"),
    OWNER_BOSS_REWARD(439, "个人boss奖励"),
    WECHAT_DAY_SHARE_REWARD(440, "小游戏分享奖励"),
    WECHAT_SHARE_REWARD(441, "小游戏邀请奖励"),
    GOD_XUAN_EQUIP_UP(442, "神玄装备升阶"),
    INDUSTRY_QUICK_UP(443, "矿镐一键升级"),
    RESET_OFF_STORE(444, "重置返利商城"),
    BUY_OFF_STORE(445, "购买返利商城道具（消耗传奇币）"),
    OFF_STORE_RETURNS(446, "返利商城返还福利传奇币"),
    BUY_OFF_STORE_BACK(447, "购买返利商城道具（发放道具）"),
    OPTIONAL_RECHARGE_GET(448, "自选充值获得"),
    CROSS_CHAT(449, "跨服聊天"),
    INDUSTRY_REWARD(450, "领取挖矿奖励"),
    BOG_STAKE(451, "天下第一下注消耗"),
    INDUSTRY_ASSIST(452, "挖矿协助奖励"),
    SPECIAL_RECYCLE_EXTRA_MONEY(453, "回收特效额外元宝"),
    INDUSTRY_REFRESH(454, "挖矿刷新矿镐消耗"),
    UNION_PET_REWARD(455, "帮会神宠捐献奖励"),
    S39WECHAT_REWARD(456, "疯狂平台关注微信公众号奖励!"),
    INDUSTRY_ROB_REWARD(457, "挖矿掠夺奖励"),
    USE_CARM_ITEM(458, "赠送魅力道具"),
    CHARM_REMOVE_FASHION(459, "魅力榜去除称号"),
    UP_LEVLE_LEI_SHEN_JIE_FANG(460, "雷神解放升级"),
    UP_LEVLE_DRAGON_kING_JIE_FANG(461, "巃皇解放升级"),
    ONE_DISCOUNT_COST(462, "购买一折礼包"),
    NEW_ONE_DISCOUNT_REWARD(463, "购买新一折礼包"),
    UP_LEVLE_WX_JIE_FANG(464, "五行装备解放升级"),
    NEW_SEVEN_REVELRY_REWARD(465, "新七日狂欢goal奖励"),
    NEW_SEVEN_REVELRY_ACTIVE_REWARD(466, "新七日狂欢活跃奖励"),
    NEW_SEVEN_REVELRY_GIFT_REWARD(467, "新七日狂欢购买礼包奖励"),
    UP_LEVEL_EIGHT_PULSE(468, "升级奇经八脉"),
    CHEAT_MAP_DELIVER(469, "脚本地图传送"),
    BUY_WONDERLAND_COUNT_COST(470, "购买仙境副本次数消耗"),
    NEW_SEVEN_REVELRY_GIFT_COST(471, "新七日狂欢购买礼包奖励"),
    UP_LEVLE_SX_JIE_FANG(472, "神选装备解放升级"),
    NEW_SEVEN_REVELRY_REMOVE_TESTVALUE(473, "活动结束移除试炼值"),
    UP_LEVEL_EIGHT_PULSE_STREN(474, "强化奇经八脉"),
    SPECIAL_FOUR_EQUIP_UP(475, "神玄装备升阶"),
    UNION_FOLLOW_ANSWER_REWARD(476, "帮会答题活动跟风答题奖励"),
    UNION_FIRST_ANSWER_REWARD(477, "帮会答题活动每题首位答对奖励"),
    SPECIAL_HUNT_ITEM_COST(478, "特殊寻宝（如意转盘）道具消耗"),
    SPECIAL_HUNT_MONEY_COST(479, "特殊寻宝（如意转盘）传奇币消耗"),
    SPECIAL_HUNT_REWARD(480, "特殊寻宝（如意转盘）奖励"),
    SPECIAL_HUNT_GIVE_ITEM(481, "特殊寻宝（如意转盘）赠送道具"),
    LIMIT_TIME_CHALLENGE_BUY_COUNT(482, "限时挑战购买次数消耗"),
    BATCH_REMOVE_FAIL_ROLL_BACK(483, "批量移除道具失败回滚"),
    KING_FIGHT_REWARD(484, "新王者争霸对局奖励"),
    PERSON_HUNT_RESET(485, "个人寻宝重置消耗"),
    EQUIP_STAR_LEVEL_UP(486, "装备升星"),
    ENTER_MAP(487, "进入地图"),
    COLOR_TASK_REFRESH(488, "五色任务刷新"),
    ACTIVITY_PRESENT_DAILY(489, "直购礼包每日奖励"),
    ACTIVITY_PRESENT_DAYS(490, "直购礼包天数奖励"),
    ACTIVITY_PRESENT_RECHARGE(491, "直购礼包累计充值奖励"),
    EXP_ON_MONSTER(493, "杀怪经验"),
    EXP_ON_EXCHANGE(494, "精力获得经验"),
    ENERGY_RECOVERY(495, "精力每日恢复"),
    MATERIAL_REWARD(497, "材料副本领奖"),
    EQUIP_INHERIT(498, "装备传承"),
    EXCHANGE(499, "NPC兑换"),
    MATERIAL(500, "材料副本"),
    CYCLE_TOTAL_RECHARGE(501, "周期累充奖励"),
    CYCLE_DAYS_RECHARGE(502, "周期连充奖励"),
    ENTER_TIANCHI(503, "进入天池十二宫"),

    EQUIP_IDENTIFY_SAVE(507, "装备鉴定保存"),
    UNION_INTENSIFY(508, "行会属性强化"),
    MONSTER_DROP(509, "怪物掉落"),
    RECYCLE(510, "回收"),
    MIX_MERGE(511, "融合"),
    MIX_BREAK(512, "融合分解"),
    BUY_PRIVILEGE(513, "购买特权"),
    ACTIVITY_FIRST_DROP_REWARD(514, "限时首爆奖励"),
    HELL_BOUNTY(515, "炼狱悬赏"),
    TOWER_REWARD(516, "开天塔奖励"),
    SEAL_DEVIL(517, "封魔图合成"),
    MAP_STAY_CONSUME(518, "地图泡点消耗"),
    HINDER_REWARD(519, "障碍赛奖励"),

    MOUNT_UNLOCK(520, "解锁坐骑"),
    MOUNT_QIANG_HUA(521, "强化坐骑"),
    MOUNT_UPGRADE(522, "升级坐骑"),
    PRINCESS(523, "抢公主兑换"),
    BACKPACK_MERGE(524, "背包移动"),

    ACTIVITY_WEEKEND_GOAL(525, "周末目标"),
    ACTIVITY_RECHARGE_REBATE(526, "累计充值"),
    ACTIVITY_RECHARGE_DRAW(527, "充值抽奖"),

    STONE_REMOVE(528, "宝石拆卸"),
    STONE_GEM(529, "宝石镶嵌"),
    STONE_LOCK(530, "宝石打孔"),

    DAILY_ESCORT_START(531, "开始押镖"),

    BACKPACK_FETCH_TREASURE(532, "提取寻宝仓库"),
    LEVEL_UP(533, "升级"),
    EQUIP_QUENCHING(534, "装备淬炼"),
    TITLE_LEVEL_UP(535, "封号升级"),
    QUENCHING(537, "淬炼"),
    CAMP_REWARD(538, "神武训练营领奖"),
    ACTIVITY_GEM_GOAL(539, "宝石开放活动"),
    ACTIVITY_DAILY_RECHARGE(540, "周末 每日充值"),
    ACTIVITY_DISCOUNT_GIFT(541, "周末特惠"),
    FUNCTION(542, "神兵活动"),
    SUNSET_RACE_BENEFIG(543, "落霞夺宝泡点"),

    ERA_ROLE(544, "纪元个人奖励"),
    ERA_SERVER(545, "纪元全服奖励"),
    ERA_GIVE(546, "纪元捐献"),
    ACTIVITY_LUCK_DRAW(547, "翻牌抽奖"),

    BOSS_OFFER(548, "极品悬赏"),
    EXCHANGE_WITH_ALTER(549, "兑换"),
    ATTACK_REWARD(550, "攻击目标奖励"),
    INSPIRE(551, "鼓舞"),
    FAKEDROP_REWARD(552, "获取副本双倍奖励"),
    COINT_DONATE(553, "货币捐献"),

    ESCORT(600, "押镖"),
    FUSION_SELL(601, "熔炼出售"),
    FUSION_BUY(602, "熔炼购买"),
    EQUIP_IDENTIFY(606, "装备鉴定"),
    RECHARGE(607, "充值"),
    ACTIVITY_EQUIP_RECYCLE(608, "装备回收活动"),
    AUCTION(609, "拍卖行"),
    ACTIVITY_SALE_BAG(610, "礼包活动"),
    ACTIVITY_COMPETE_LEVEL(611, "等级竞技"),
    COLLECT(612, "采集"),
    EXP_BOX(613, "NPC经验宝箱兑换经验"),
    EQUIP_ZHUFU(614, "装备祝福"),
    BUY_HUO_LONG_VALUE(615, "火龙之心充能"),
    EQUIP_FENG_YIN_JIE(616,"装备封印解除"),

    ACTIVITY_REWARD(700, "活动奖励"),
    ACTIVITY_COST(701, "活动花费"),
    AUCTION_TIME_OUT(702, "拍卖行过期返还"),

    DAILY_CHALLENGE_REWARD(703, "日常挑战领奖励"),
    DAILY_CHALLENGE_SWEEP(704, "日常挑战扫荡"),
    DAILY_CHALLENGE_REFRESH(705, "日常挑战刷新"),

    //---------------------------------------------------
    FORGET_SKILL_COST(1000, "遗忘技能消耗"),
    SKILL_EXTRA_COST(1001, "使用技能额外消耗"),
    REPAIR_EQUIP_COST(1002, "修理装备消耗"),
    SKILL_AUTO_LEVEL_UP_COST(1003, "技能自动升级消耗"),
    MOVE_BAG(1004, "背包移动"),
    UNLOCK_BAG_GRID_COST(1005, "解锁背包格子消耗"),
    STRENGTH_POS(1006, "强化装备部位"),
    CURE_COST(1008, "治疗消耗"),
    ACTIVITY_TREASURE_MONSTER_REWARD(1009, "禁地封魔领奖"),
    ACTIVITY_TREASURE_MONSTER_CALL(1010, "禁地封魔召唤消耗"),
    ACTIVITY_TREASURE_REWARD(1011, "禁地封魔仓库奖励"),
    RED_PACK_COST(1014, "发红包消耗"),
    RED_PACK_GET(1015, "领取红包"),
    CLEAN_PKVALUE(1018, "清红名"),
    MAGIC_BLOOD_STONE_UPDATE(1019, "魔血石更新"),

    ROLE_INIT(2001, "角色创建初始化"),
    DURABLE_COST(2002, "装备耐久消耗"),
    ITEM_EXPIRE(2003, "道具过期"),
    ITEM_UPDATE(2004, "道具更新"),
    JULING_DAN(2005, "聚灵丹"),
    ITEM_MULTI(2006, "道具拆分"),
    HERO_INIT(2007, "英雄创建初始化"),

    ZHUAN_SHENG(3000, "转生"),
    AUCTION_BUY(3001, "拍卖行购买"),
    AUCTION_PUT_OFF_ROLL_BACK(3002, "拍卖下架回退"),
    WORSHIP_REFRESH_RATION_COST(3003, "刷新膜拜奖励倍率"),
    WORSHIP(3004, "膜拜"),
    WORSHIP_ONLINE(3005, "膜拜"),
    RING_FUSION(3006, "特戒融合"),
    RING_SPLIT(3007, "特戒拆分"),
    TREASURE_HUNT(3008, "寻宝"),
    RING_BROKEN(3009, "特戒破碎"),
    RING_FIX(3010, "特戒修复"),
    ZODIAC_COMPOUND(3100, "生肖合成"),
    ZODIAC_COMPOUND_COST(3101, "生肖合成消耗"),
    DEMON_EQUIP_BROKEN(3110, "魔器破碎"),
    ZHANYI_LEVEL_UP(3120, "战意升级"),
    ZHANYI_MOXUE_EXCHANGE(3121, "经验兑换磨血"),
    TALENT_CLEAR(3130, "天赋清除加点"),
    XIALV_LEVEL_UP(3140, "侠侣升级"),
    XIALV_GUANGHUAN_UP(3141, "侠侣光环升级"),
    XIALV_GUARDIAN_UP(3142, "侠侣守护等级升级"),
    WUDAO_LEVEL_UP(3150, "武道升级"),
    WUDAO_REWARD(3151, "武道奖励"),
    TIANCHI(3160, "天池宫"),
    JUEXING(3170, "觉醒"),
    SHENZHU(3180, "神铸"),
    FUHUN(3190, "附魂"),
    ZHUANPAN(3200, "转盘"),
    WABAO(3210, "挖宝"),
    CENTRAL_PLAINS_COUNT_REWARD(3211, "中原寻宝次数奖励"),
    TOUBAO(3220, "投保"),
    QIBAO(3221, "弃保"),

    FUWEN(4000, "符文"),

    ORE_HOLE(5000, "矿洞"),
    ORE_HOLE_1(5001, "铁矿"),
    ORE_HOLE_2(5002, "铜矿"),
    ORE_HOLE_3(5003, "银矿"),
    ORE_HOLE_4(5004, "金矿"),
    ORE_HOLE_5(5005, "铂金"),

    SHABAKE(6000, "沙巴克"),
    SHABAKE_PERSONAL_REWARD(6001, "沙巴克个人积分奖励"),
    SHABAKE_PERSONAL_RANK(6002, "沙巴克个人排行奖励"),
    SHABAKE_EXTRA_REDPACK(6003, "沙巴克额外红包"),

    BACK(8000, "后台"),
    BACK_OPERATE(8001, "后台操作"),


    BAG_COMPOUND_INHERIT(9000, "背包合成继承"),

    SHEN_TA(10000, "魔域神塔"),

    TRADE(9100, "培养"),

    JITAN(9200, "祭坛"),

    PERSONAL_BOSS(9210, "个人boss"),
    EQUIP_QINLINBI_LV(9211, "麒麟臂升级"),
    DAILY_ESCORT_ACQUIRE            (9212, "押镖领取"),


    SHENBING_DAILY_REWARD(9213,"神兵会员每日福利"),
    SHENBING_REWARD(9214,"神兵奖励领取"),
    SHENBING_EXTRA_REWARD(9215,"购买神兵额外奖励领取"),
    SHENBING_GIFT_REWARD(9216, "神兵vip限购礼包奖励"),
    LUCK_DROP_SMALL(9217, "幸运掉落小"),
    LUCK_DROP_BIG(9218, "幸运掉落大"),

    EXP_ON_MONSTER_BASE(9300, "基础经验 + 特定怪物额外经验加成"),
    EXP_ON_MONSTER_ROLE_ATTRIBUTE(9301, "角色经验属性加成"),
    EXP_ON_MONSTER_SHENBING(9302, "神兵特权额外经验"),
    Teleport_DELIVER(9303, "传送-deliver"),
    FengHao_REMOVE_TITLE(9304, "封号remove原有称号"),


    ZHUANSHU_COllECT_REWAED(9401, "专属装备收集奖励"),
    ZHUANSHU_COllECT_BOX_REWAED(9402, "专属装备累计收集宝箱奖励"),
    MAP_KEEP_TIME_TIME(9403, "地图持续时间道具"),


    APTITUDE_IDENTIFY(9501, "资质鉴定"),
    APTITUDE_COMPOUBD(9502, "资质融合"),
    APTITUDE_CONCISE(9503, "资质洗练"),

    UNION_ALLY_CONSUME(9601, "行会结盟消耗"),
    UNION_ENEMY(9602, "行会宣战"),
    UNION_ALLY_ROLL(9603, "行会结盟返还"),

    GIFT_PACK(9701,"礼包购买"),
    ITEM_LEVEL_CARD(9702,"等级直升卡"),

    SUIT_EXTEND_COST(9702, "套装属性继承消耗"),
    SUIT_EXTEND_UP_COST(9703, "套装继承属性升级消耗"),
    APPEARANCE_ULOCK(9704, "时装解锁"),

    EXTRACT_COST(9705, "萃取消耗"),
    TIANSHU_EXP_UP_COST(9706, "天书加经验消耗"),
    GOUWUCHE_BUY(9707, "购物车"),

    TIAN_XIE_ZHAN_JIAN(9707,"天邪战鉴"),

    EXTRACT_REWARD(9708, "萃取获得"),
    UNLOAD_SUIT_EXTEND_COST(9709, "装备卸灵消耗"),
    UNLOAD_SUIT_EXTEND_REWARD(9710, "装备卸灵获得"),

    CLIMB_TOWER_ROLL_TOWER1(9801, "通天塔投普通骰子"),
    CLIMB_TOWER_ROLL_TOWER2(9802, "通天塔投金骰子"),
    CLIMB_TOWER_ROLL_TURNTABLE(9802, "通天塔转转盘"),
    CLIMB_TOWER_TURNTABLE(9803, "通天塔转盘获得"),
    CLIMB_TOWER(9804, "通天塔获得"),
    Item_FengHao(9805, "封号等级卡"),

    SUMMON_CROSS_BOSS(9806,"召唤跨服boss消耗"),


    MYSTERY_STORE_FLUSH_COST(9901, "神秘商店刷新消耗"),
    MYSTERY_STORE_BUY_COST(9902, "神秘商店购买消耗"),
    MYSTERY_STORE_BUY_REWARD(9903, "神秘商店购买获得"),
    YUAN_YING_STRENGTH(9904, "元婴强化消耗"),
    YUAN_YING_SHOW_UPDATE(9905,"元婴外显变化"),


    FIRST_KILL_MONSTER_REWARD(9910, "怪物首杀奖励"),
    FIRST_KILL_MONSTER_BOX_REWARD(9911, "怪物首杀进度宝箱奖励"),

    FENG_MO_ZHAN_YI_BUY(9912,"封魔战意活动购买"),
    FENG_MO_ZHAN_GEARS_REWARD(9913,"封魔战意档位奖励"),
    FENG_MO_ZHAN_RANK_REWARD(9914,"封魔战意排行榜奖励"),

    JIANZHONG_REWARD(10001, "剑宗奖励"),
    JIANZHONG_DUPLICATE(10002, "剑宗进副本消耗"),
    JIANZHONG_RANK_REWARD(10003, "剑宗排行榜奖励"),


    ZHUJIAN_COST(10011, "共铸神剑消耗"),
    ZHUJIAN_REWARD(10012, "共铸神剑奖励"),


    TIAN_MING_DI_ZAO(10004,"天命缔造玩法"),
    HE_FU_RECHARGE(10005,"合服充值活动奖励"),
    
    MING_WANG(10006,"江湖名望玩法"),

    HE_FU_RECHARGE_PROGRESS(10007,"合服充值活动进度奖励"),




    HANDBOOK_COST(10021, "专属图鉴消耗"),
    HANDBOOK_REWARD(10022, "专属图鉴奖励"),

    MONSTER_CARD_COST(10031, "怪物图鉴消耗"),
    MONSTER_CARD_REWARD(10032, "怪物图鉴奖励"),

    ZHANLING_PROGRESS_REWARD(10040, "战令进度奖励"),
    ZHANLING_UNLOCK_COST(10041, "战令解锁消耗"),
    ZHANLING_PROGRESS_EX_REWARD(10042, "战令进度额外奖励"),

    XUN_BAO_RWARD(10051,"藏宝图寻宝奖励"),
    XUN_BAO_COST(10052,"藏宝图寻宝消耗"),

    DAILY_PROGRESS_REWARD(10061,"每日活跃奖励"),

    SIGNIN_REWARD(10071, "签到奖励"),
    SIGNED_COST(10072, "补签消耗"),
    SIGNIN_REWARD_DAILY(10073, "每日签到奖励"),


    APPEARANCE_BUY_ULOCK(10080, "装扮购买"),
    RMB_RECYCLE(10081, "人民币回收"),
    DUPLICATE_RECHARGE(10082, "每日充值副本进入"),

    JI_FEN_STORE_REFRESH_COST(10101,"积分商店刷新消耗"),
    JI_FEN_STORE_BUY(10102,"积分商店购买"),

    TOU_ZI_LI_CAI(10201, "投资理财"),
    SUPER_MAN_COST(10301, "购买狂暴之力消耗"),
    XIA_LV_LEVEL_ACT_REWARD(10401, "侠侣冲级活动奖励"),

    EQUIP_COLLECT_REWARD(10421, "装备收集奖励"),

    LINEAGE_LEVEL_UP_COST(10430, "四大传承升级消耗"),

    SELFAPPEARANCE_UNLOCK(10431,"个人称号升级解锁"),
    UPGRAEDE_TIANFULEVEL(10432,"个人天赋升级"),
    UPGRAEDE_TIANFULEVEL_RETURN(10436,"个人天赋升级失败返还奖励"),
    ZHANYI_REWARD(3122, "战意(转生)奖励"),

    LEVEL_REWARD(10440, "等级奖励"),
    CZJJ_RECEIVE_REWARD(10433, "成长基金领取"),
    RMB_EXCHANGE_DECITEMS(10434, "人民币兑换扣除"),
    LUNHUITA_REWARD(10435, "轮回塔奖励领取"),

    SYSTEM_FIRST_DROP_REWARD(10450, "个人装备首爆系统奖励"),

    INJECT_SOUL_COST(10451, "注灵升级消耗"),

    EQUIP_UPGRADE_COST(10460, "四部件升级"),
    EQUIP_UPGRADE_REWARD(10461, "不可脱下装备升级获得"),
    APPEARANCE_UPGRADE_COST(10462, "时装盾牌升级"),

    DAILY_ITEM(10471,"每日赠送道具"),
    SHACHENG_JUANXIAN(10444,"沙城捐献消耗"),
    SHENWEI_UP(10481,"神威升级消耗"),
    DA_LU_LI_BAO(10491,"大陆礼包"),
    SHENBING_UP(10492,"神兵升级"),
    CHONGWU_UP(10493,"宠物升级消耗"),
    CHONGWU_RESELECT(10494,"重新选择宠物消耗"),
    MAGIC_CIRCLE_UP_COST(10500, "法阵特效升级"),

    LUNHUITAHUIKUI_REWARD(10510, "累充回馈活动"),
    HOLIDAY_LOGIN_REWARD(10511, "节日登录奖励"),
    DAXIAHUODONG_REWARD(10512, "大侠活动"),
    FAQIHUIYUANUPGRADE(10513, "法器会员升级"),
    FAQIHUIYUANDIRUPGRADE(10514, "法器会员直升"),
    FAQIHUIYUANGETLUNHUITA(10515, "法器会员扫荡轮回塔"),
    FAQIHUIYUANGETDAILYREARD(10516, "法器会员每日工资"),
    YUAN_BAO_DUI_HUAN(10520,"元宝兑换"),

    LUCK_WHEEL_REWARD(10530, "幸运转盘奖励"),

    DANYAO_CLEAR(10540, "丹药重置丹消耗"),

    BABEL_ROAD_DAILY_REWARD(10550, "通天之路每日奖励"),
    BABEL_ROAD_REWARD(10551, "通天之路通关奖励"),
    BABEL_ROAD_FIRST_REWARD(10552, "通天之路榜一奖励发放"),
    BABEL_ROAD_FIRST_COST(10553, "通天之路榜一奖励收回"),

    EDICT_CHALLENGE_REWARD(10600, "皇榜挑战奖励"),
    EDICT_CHALLENGE_EXTRA_COST(10601, "皇榜挑战额外奖励消耗"),

    DAILY_DARK_TEMPLE(10610, "暗黑神殿每日赠送时间"),

    SECT_SKILL_COST(10620, "门派技能升级消耗"),
    SECT_TASK_MULTI_COST(10621, "门派日常挑战任务多倍领取消耗"),
    SECT_TASK_REFRESH_COST(10622, "门派日常挑战任务单个刷新消耗"),

    SPECIAL_WA_BAO(10631,"宝藏之地挖宝"),
    SHOUJUEQIANRU_COST(10641, "兽决嵌入"),
    JINGJIEZHILU_REWARD(10642, "进阶之路奖励"),

    LIMITED_TIME_RECHARGE_REWARD(10650, "限时累充奖励"),

    FAQI_SHENGYAN(10651,"法器盛宴领取奖励"),

    TIAN_XUAN_ZHI_REN_REWARD(10661,"天选之人大奖"),
    TIAN_XUAN_ZHI_REN_JOIN_REWARD(10662,"天选之人参与奖"),

    MONTH_CARD_DAYMAIL_REWARD(10652, "盟主令每日奖励邮件"),

    MAGIC_WEAPON_COMPOUND_COST(10670, "法宝打造消耗"),
    MAGIC_WEAPON_COMPOUND_REWARD(10671, "法宝打造获取"),
    MAGIC_WEAPON_SLOT_COST(10672, "法宝开孔消耗"),
    MAGIC_WEAPON_SLOT_IN_COST(10673, "法宝融合消耗"),
    MAGIC_WEAPON_SLOT_IN_EQUIP(10674, "法宝融合装备改变"),
    MAGIC_WEAPON_SLOT_OUT_COST(10675, "法宝分离消耗"),
    MAGIC_WEAPON_SLOT_OUT_REWARD(10676, "法宝分离获得"),
    MAGIC_WEAPON_SLOT_OUT_EQUIP(10677, "法宝分离装备改变"),

    RU_YI_ZHAN_PAN_REWARD(10680, "如意转盘奖励"),

    BOSS_FEAST_REWARD(10690, "Boss盛宴奖励"),
    RINGGEM_POS_UNLOCK_COST(10710, "特戒灵石解锁部位消耗"),
    RINGGEM_INLAY(10711, "特戒灵石镶嵌"),
    RINGGEM_DISBOARD(10712, "卸下特戒灵石"),
    RINGGEM_UP_COST(10713, "合成灵石消耗"),

    DAILY_PHYSICAL_POWER_RECEIVE(10700, "每日领取体力值"),
    MONSTER_DROP_COST(10701, "怪物掉落消耗"),

    SPECIAL_LOGIN_REWARD(10710, "特殊登录豪礼奖励"),

    SHENG_BING_KUANG_HUAN_REWARD(10720,"神兵狂欢奖励"),

    JIU_TIAN_KILL_COUNT_REWARD(10721,"九天之巅杀人数奖励"),
    JIU_TIAN_JI_FEN_REWARD(10722,"九天之巅积分奖励"),
    JIU_TIAN_TIME_REWARD(10723,"九天之巅女神眷顾奖励"),
    JIU_TIAN_RANK_REWARD(10724,"九天之巅排行奖励"),
    JIU_TIAN_JI_FEN(10723,"九天之巅积分"),

    BOSS_JIFEN_REWARD(10730, "boss积分奖励"),
    FIGHTFORCHANGAN_REWARD(10760, "长安争夺战积分奖励"),

    CHANG_AN_MI_JING_RANDOM_TELEPORT_COST(17040,"长安秘境随机传送消耗"),
    CHANG_AN_MI_JING_BUY_COST(17041,"长安秘境购买传送道具消耗"),
    CHANG_AN_MI_JING_REWARD(17042, "长安秘境奖励"),

    HAND_BOOK_SUBLIMATION_COST(17050, "灵根天命升华消耗"),
    QIANGHUA_SHOUJUE_COST(17060, "强化兽决消耗"),
    QIANGHUA_SHOUJUE(17061, "强化兽决"),
    
    BODY_FORGE_COST(17070, "神魔锻体升级消耗"),

    TITAN_FUHUN_COST(17080, "泰坦附魂消耗"),
    TITAN_FUHUN(17081, "泰坦附魂"),
    TITAN_FUHUN_SPLIT_COST(17082, "泰坦附魂拆分消耗"),
    TITAN_FUHUN_SPLIT(17083, "泰坦附魂拆分"),

    WHEEL_REWARD(17090, "积分转盘获取"),

    SBK_PALACE_TELEPORT_COST(17100, "跨服沙巴克皇宫密道传送"),
    ACTIVITY_LINGFU_HAOLI_REWARD(17110, "灵符豪礼活动领取奖励"),

    FATE_DAILY_COST(17120, "天命运势转运消耗"),
    FATE_DAILY_REWARD(17121, "天命运势转运领取"),
    FATE_UPGRADE_COST(17122, "天命命格升级消耗"),
    FATE_DAILY_BUY_COST(17123, "天命运势抽签购买消耗"),

    SUMMON_CROSS_MONSTER(17130, "召唤特殊怪物获得奖励"),
    FANGPIAN_REWARD(17131, "防骗奖励"),
    SPECIAL_UP_COST(17132, "盾牌时装升阶消耗"),
    SPECIAL_DUNPAI_UP(17133, "盾牌升阶"),
    TOUZI_HAOLI_REWARD(17140, "投资豪礼奖励"),


    LONG_HUN_INLAY_COST(17150, "龙魂融合消耗"),
    LONG_HUN_INLAY(17151, "龙魂融合"),
    LONG_HUN_SEPARATE_COST(17152, "龙魂分离消耗"),
    LONG_HUN_SEPARATE(17153, "龙魂分离"),
    LONG_HUN_SEPARATE_REWARD(17154, "龙魂分离获得"),

    YU_PEI_LEVEL_UP(17160,"玉佩升级"),

    SKILL_AWAKE_COST(17170, "技能觉醒升级消耗"),

    TIAN_MO_TASK_REWARD(17180,"天魔来袭任务奖励"),
    LINGFU_ZHUANPAN_COST(17190, "灵符转盘消耗"),
    LINGFU_ZHUANPAN_REWARD(17191, "灵符转盘奖励"),

    DISARM(17200, "缴械"),
    DISARM_SORT(17201, "缴械整理"),

    MIJIGEM_INLAY(17210, "特戒灵石镶嵌"),
    MIJIGEM_DISBOARD(17211, "卸下特戒灵石"),
    MIJIGEM_UP_COST(17212, "合成灵石消耗"),
    COMPLETE_TASKS_QUICKLY_COST(17220, "快速完成日常任务消耗"),

    FEEDBACK_GIFT_REWARD(17230, "礼包回馈奖励"),

    PURCHASE_DAILY_REWARD(17240, "每日累充奖励"),
    ROLE_PET_UP_COST(17250, "宠物装备强化消耗"),
    ROLE_PET_JIANDING_COST(17251, "宠物装备鉴定消耗"),
    ROLE_PET_JIANDING(17252, "宠物装备鉴定"),
    ROLE_PET_XILIAN_COST(17253, "宠物装备洗练消耗"),
    ROLE_PET_XILIAN(17254, "宠物装备洗练消耗"),

    MEDAL_UPGRADE_COST(17260, "修罗勋章升级消耗"),

    SHOPPING_CART_COST(17270, "购物车购买消耗"),
    SHOPPING_CART_REWARD(17271, "购物车购买获得"),

    BOOK_SWORD_COST(17280, "天书神剑升级消耗"),

    KNIFE_KNIFE_GOLD(17290, "刀刀元宝获取"),

    XIU_LUO_JIE_SUAN(17500,"3v3副本结算"),
    XIU_LUO_REWARD(17501,"3v3奖励"),
    XIU_LUO_DUAN_WEI(17502,"3v3段位奖励"),
    XIU_LUO_BUY_COST(17503,"3v3购买匹配次数"),
    XIU_LUO_RANK_JIE_SUAN(17504,"3v3排行结算奖励"),

    FREE_EQUIP_REWARD(17510,"免费神装直购奖励"),
    FREE_EQUIP_SKILL_REWARD(17511,"免费神装直购奖励"),
    FREE_EQUIP_DAY_REWARD(17512,"免费神装累计天数奖励"),
    TOKEN_RECHARGE_COST(17520,"大侠币充值消耗"),
    SHENHUANG_UP_COST(17530,"神皇之力升级消耗"),
    SHENHUANG_UP_REWARD(17531,"神皇之力升级奖励"),
    FAQIHUIYUANGETCHANGJINGGE(17541, "法器会员扫荡藏经阁"),

    CHAOTIC_INLAY(17550, "混沌装备镶嵌"),
    CHAOTIC_SEPARATE(17551, "混沌装备拆分"),

    REFINEMENT(17560, "特戒淬炼"),

    MYSTERY_STORE_FLUSH_COST2(17570, "神秘商店刷新消耗"),
    MYSTERY_STORE_BUY_COST2(17571, "神秘商店购买消耗"),
    MYSTERY_STORE_BUY_REWARD2(17572, "神秘商店购买获得"),

    SACRIFICE_SUBMIT(17580, "人皇祭祀提交材料"),

    HEAVEN_REWARD(17590,"元宝累充奖励"),
    HEAVEN_COST(17591,"元宝累充消耗"),
    BAG_SHENQI_CHANGE(17600, "背包神器道具变更"),

    XIU_LUO_CHENG_JIU_REWARD(175601,"修罗成就奖励"),
    XIU_LUO_CHENG_JIU_LEVEL_REWARD(175602,"修罗成就等级奖励"),

    CLEAR_CAMP_CD_COST(17610, "清除阵营cd消耗"),
    JOIN_POWER_UNION_COST(17611,"个人加入强势阵营行会消耗"),
    JOIN_POWER_UNION_ROLL_REWARD(17612,"个人加入强势阵营行会消耗返还"),

    MILITARY_RANK_UP_COST(17620, "军衔升级消耗"),
    MILITARY_RANK_TASK_REWARD(17621, "军衔任务奖励"),
    MILITARY_RANK_DAILY_REWARD(17622, "军衔每日俸禄奖励"),
    MILITARY_RANK_DAILY_MUL_REWARD(17623, "军衔每日俸禄奖励多倍领取消耗"),

    UNION_EXPAND_COST(175631,"行会扩建消耗"),

    MONEY_TREE_LEVEL_REWARD(175640, "摇钱树等级奖励"),
    MONEY_TREE_SHAKE_COST(175641, "摇钱树摇动消耗"),
    MONEY_TREE_SHAKE_REWARD(175642, "摇钱树摇动奖励"),
    ZAJINDAN_REWARD(175650, "砸金蛋奖励"),

    ZHEN_YING_TASK_REWARD(175661,"阵营任务奖励"),
    DAILY_BANZHUANTASK_REWARD(175670,"每日搬砖获得"),
    DAILY_BANZHUANTASK_COST(175671,"每日搬砖替换扣除"),
    DAILY_BANZHUANTASK_DIE_COST(175672,"每日搬砖玩家死亡扣除"),
    ZUOQI_ZANZHUDIAN_REWARD(175680,"坐骑系统击杀boss获得赞助点"),

    HELL_ROAD_SCORE(175690, "地狱之路积分变更"),
    HELL_ROAD_KILL_REWARD(175691, "地狱之路击杀达标奖励"),

    VALUE_CARD_OPEN_REWARD(175701,"超值双卡开通奖励"),
    VALUE_CARD_DAY_REWARD(175702,"超值双卡每日奖励"),

    VALUE_CARD_HUO_BAN_REFRESH_REWARD(175703,"超值双卡伙伴刷新奖励"),

    SHEN_BING_REMOULD_COST(175710, "神兵改造消耗"),

    MILLION_REWARD(175721,"百万灵符奖励"),

    HANG_HUI_BAO_DI_ZHUAN_PAN_REWARD(175731,"行会宝地转盘奖励"),
    HANG_HUI_BAO_DI_ZHUAN_PAN_COST(175732,"行会宝地转盘消耗"),

    ZHU_HUN_COST(175740, "铸魂消耗"),

    XUAN_BING_UPGRADE_COST(175750, "玄兵升级消耗"),

    ZIXUANLIBAO_REWARD(175760, "自选礼包奖励"),
    TOKEN_BINDRECHARGE_COST(175770,"绑定大侠币充值消耗"),

    KING_UPGRADE_COST(175780, "帝王系统升级消耗"),

    QIAN_DAO_LI_BAO(175790,"签到礼包"),
    QIAN_DAO_LI_BAO_COST(175791,"签到礼包消耗"),

    STRANGE_FIRE_UP_COST(175792, "异火升级消耗"),
    STRANGE_FIRE_JINJIE_COST(175793, "异火进阶消耗"),
    WORLD_CUP_DIAN_QIU_LING_FU(175801,"点球大作战灵符"),
    WORLD_CUP_DIAN_QIU_KUANG_HUAN(175802,"点球狂欢"),

    DOOR_COST(175810, "玄界之门升级消耗"),
    WORLD_CUP_STORE_FLUSH_COST(175820,"世界杯商店刷新消耗"),
    WORLD_CUP_STORE_BUY_COST(175821,"世界杯商店购买消耗"),
    WORLD_CUP_STORE_BUY_REWARD(175822,"世界杯商店购买获得"),

    XIA_HUN_INLAY_COST(175830, "侠魂融合消耗"),
    XIA_HUN_INLAY(175831, "侠魂融合"),
    XIA_HUN_SEPARATE_COST(175832, "侠魂分离消耗"),
    XIA_HUN_SEPARATE(175833, "侠魂分离"),
    XIA_HUN_SEPARATE_REWARD(175834, "侠魂分离获得"),
    FIRE_TOWER_BUYCOUNT_COST(175840, "异火塔购买次数消耗"),

    CHAOTIC_POWER(175850, "混沌神源赋灵"),
    FIX_SHABAKE_FASHION(175860, "修复沙巴克时装"),

    REALM_UPGRADE_COST(175870, "境界升级消耗"),
    REALM_INTENSIFY_COST(175871, "境界强化消耗"),

    STRANGE_FIRE_COMPOUND_COST(175880, "异火合成消耗"),
    STRANGE_FIRE_COMPOUND_REWARD(175881, "异火合成获得"),

    DFLS_SIGN_UP_COST(176001,"巅峰联赛报名消耗"),
    DFLS_BUY_TICKET_COST(176002,"巅峰联赛购买资格券消耗"),
    DFLS_TICKET_ADD(176003,"巅峰联赛资格券获得"),
    DFLS_TAO_TAI_REWARD(176004,"巅峰联赛淘汰赛奖励"),
    DFLS_JING_CAI_COST(176005,"巅峰联赛竞猜消耗"),
    DFLS_JUAN_XIAN_REWARD(176006,"巅峰联赛捐献排行奖励"),
    DFLS_JUAN_XIAN_COST(176007,"巅峰联赛捐献消耗"),
    DFLS_LING_FU_REWARD(176008,"巅峰联赛冠军灵符奖励"),
    DFLS_JING_CAI_QUAN_REMOVE(176009,"巅峰联赛赛季重置移除竞猜券"),
    DFLS_ZHAN_LING_REWARD(176010, "巅峰联赛战令奖励"),
    DFLS_ZHAN_LING_BUY_EXP_COST(176011, "巅峰联赛战令购买经验消耗"),
    DFLS_ZHAN_LING_OPEN_BOX_REWARD(176012, "巅峰联赛战令宝箱开启获得"),
    DFLS_HAI_XUAN_TAO_TAI_REWARD(176013,"巅峰联赛海选淘汰奖励"),

    BUY_ZHENYAN_GIFT_REWARD(176020, "真言礼包购买奖励"),
    ZHENYAN_COMPOUND_CREATE_COST(176021, "真言合成组装消耗"),
    ZHENYAN_COMPOUND_SECCUSS_COST(176022, "真言合成成功消耗"),
    ZHENYAN_COMPOUND_REWARD(176023, "真言合成获得"),
    ZHENYAN_COMPOUND_REWARD_CREATE(176024, "真言合成创建获得产出道具"),
    ZHENYAN_COMPOUND_FAIL_COST(176025, "真言合成失败消耗"),
    ZHENYAN_TOWER_COST(176026, "真言塔进入普通消耗"),
    ZHENYAN_TOWER_SP_COST(176027, "真言塔进入灵符消耗"),
    SHENBING_ZHUHUN_UP_COST(176030, "神兵铸魂升级消耗"),
    SHENBING_ZHUHUN_UP(176031, "神兵铸魂升级"),


    LIXIANGUAJI_GETTASKREWARD(176040, "领取离线挂机任务奖励"),
    LIXIANGUAJI_GETREWARD(176041, "领取离线挂机奖励"),

    CHRISTMAS_GIFT_REWARD(176042,"圣诞礼物奖励"),

    BIRTH_CHART_EVENT_COST(176050, "命格猎命消耗"),
    BIRTH_CHART_SUMMON_COST(176051, "命格召唤消耗"),
    BIRTH_CHART_EVENT_REWARD(176052, "命格猎命获得(放入仓库)"),
    BIRTH_CHART_MERGE_MERGE(176053, "命格穿脱"),
    BIRTH_CHART_UPGRADE_COST(176054, "命格升级消耗"),
    BIRTH_CHART_UPGRADE(176055, "命格升级"),
    BIRTH_CHART_COMPOUND_COST(176056, "命格合成消耗"),
    BIRTH_CHART_COMPOUND_REWARD(176057, "命格合成获得"),
    CHRISTMAS_STORE_RANDOMDISCOUNT_COST(176060,"圣诞商店刷新折扣消耗"),
    CHRISTMAS_STORE_RANDOMD_COST(176061,"圣诞商店刷新商品消耗"),
    CHRISTMAS_STORE_BUY_COST(176062,"圣诞商店购买消耗"),
    CHRISTMAS_STORE_BUY_REWARD(176063,"圣诞商店购买奖励"),

    XIN_CHUN_JIN_LI_REWARD(176064,"新春锦鲤"),

    XING_HUN_COST(176065,"星魂升级消耗"),
    SHENG_QI_COST(176066,"圣器升级消耗"),
    ATMOSPHERE_REWARD(176070, "气氛值活动奖励"),
    CROSS_RANK_REWARD(176080,"跨服排行榜发奖"),

    CHAI_JIA_COST(177001,"拆家消耗"),
    CHAI_JIA_BACK(177002,"拆家返还"),
    CHAI_JIA_UP_COST(177003,"拆家升级消耗"),

    WU_XING_SHAN_REWARD(177004,"每日boss伤害奖励"),
    WU_XING_SHAN_RANK_REWARD(177004,"每日boss排行奖励"),

    BARRIER_REWARD(177005,"主线推图奖励"),

    HUO_BAN_REFRESH_COST(177006, "伙伴刷新消耗"),
    HUO_BAN_ZHAO_HUAN_COST(177007, "伙伴召唤消耗"),
    HUO_BAN_ZHAO_HUAN_ADD(177008, "伙伴召唤获得"),
    HUO_BAN_LEVEL_UP_COST(177009, "伙伴升级消耗"),
    HUO_BAN_BAG_COST(177010, "购买伙伴栏位消耗"),
    HUO_BAN_QIANG_HUA_COST(177011, "伙伴强化消耗"),

    SHEN_MO_LEVEL_UP_COST(177012,"神魔升级消耗"),

    CHAI_JIA_INIT(177013, "拆家初始奖励"),

    BOSS_KING_REWAED(177014,"扫荡副本奖励"),
    BOSS_KING_COST(177015,"扫荡副本消耗"),

    FRIEDN_LEVEL_COST(177016,"道友升级消耗"),
    FRIEDN_UNLOCK_COST(177017,"道友解锁消耗"),
    FRIEDN_BOSS_REWARD(177018,"道友副本奖励"),

    ARENA_BATTLE_COST(177020,"竞技场战斗消耗"),
    ARENA_BATTLE_REWARD(177021,"竞技场战斗奖励"),
    ARENA_REFRESH_COST(177022,"竞技场刷新奖励"),
    ARENA_RANK_DAY_REWARD(177023,"竞技场日排行奖励"),
    ARENA_RANK_WEEK_REWARD(177024,"竞技场周排行奖励"),

    OPEN_LIST_REWARD(177025,"妖途奖励"),

    WAN_XIAN_BANG_CHALLENGE_LIST_REFRESH_COST(178001,"万仙榜挑战列表刷新消耗"),
    WAN_XIAN_BANG_CHALLENGE_COST(178002,"万仙榜挑战消耗"),
    WAN_XIAN_BANG_CHALLENGE_REWARD(178003,"万仙榜挑战奖励"),
    WAN_XIAN_BANG_DAY_REWARD(178004,"万仙榜日结算奖励"),
    WAN_XIAN_BANG_WEEK_REWARD(178005,"万仙榜周结算奖励"),
    WAN_XIAN_BANG_RANK_REWARD(178006,"万仙榜排行成就奖励"),
    WAN_XIAN_BANG_TICKET_RELIVE(178007,"万仙榜排行挑战券恢复"),
    WAN_XIAN_BANG_TICKET_BUY(178008,"万仙榜排行挑战券购买"),

    YAO_XIE_REWARD(178009, "妖邪挑战奖励"),

    ZHEN_BAO_GE(178010, "珍宝阁"),

    UNION_TASK_REWARD(178011,"行会任务奖励"),
    UNION_PROGRESS_REWARD(178012,"行会进度奖励"),

    UNION_QUIT(178013,"退出行会扣除"),

    YUN_SHI_END_CLEAR_ITEM(178013,"运势活动结束清除道具"),

    HOARD_COST(178014, "血脉消耗"),

    SHENMO_LILIAN_REFRESH_COST(178015, "神魔历练刷新消耗"),

    SHENMO_LILIAN_DISPEL_COST(178016, "神魔历练驱散消耗"),
    SHENMO_LILIAN_DISPEL_REWARD(178017, "神魔历练驱散奖励"),

    ZHEN_YAO_TA_BATTLE_REWARD(178018, "镇妖塔战斗奖励"),
    ZHEN_YAO_TA_SWEEP_REWARD(178019, "镇妖塔扫荡奖励"),

    UNION_NAME_CHANGE(178021, "行会名称修改消耗"),

    HOU_YUAN_RAT_BUY(178030,"购买后院老鼠"),
    HOU_YUAN_ITEM_GAIN(178031,"后院道具获得"),
    HOU_YUN_ITEM_REFRESH_COST(178032,"后院道具刷新消耗"),
    HOU_YUAN_JU_BAO_PEN(178033,"后院聚宝盆功能"),

    ZHAN_LING_PACK_EXTRA(178040,"战令礼包额外奖励"),

    ENERGY_DRINK_LEVEL_UP(178050,"功能饮料升级消耗"),

    FOUNDATION_RECEIVE_REWARD(178060, "基金领取"),

    LINE_UP_UNLOCK_COST(178070,"解锁投影消耗"),

    UNION_CHEST_REWARD(178080,"行会宝箱奖励"),
    GONG_DE_DAY_REWARD(178081,"功德每日奖励"),

    HUO_BAN_REST_COST(178082, "伙伴重置消耗"),
    HUO_BAN_REST_ADD(178083, "伙伴重置获得"),

    WORK_UP_COST(178090,"建筑升级消耗"),

    WORK_UP_SHORTEN_COST(178091,"建筑升级或生产道具缩短时间消耗"),

    WORK_BUY_OUT_COST(178092,"建筑升级或生产买断消耗"),

    WORK_TRANS_COST(178093,"建筑改造消耗"),

    WORK_TRANS_REWARD(178094,"建筑改造产出"),

    WORK_TRANS_CANCEL_REWARD(178095,"建筑改造取消时获得的产出"),

    WORK_TRANS_CANCEL_BACK(178096,"建筑改造取消返还"),

    WORK_PRODUCE_REWARD(178097,"建筑生产产出"),

    APPEARANCE_UPGRADE(178098,"时装升级"),

    ADVERTISE_PACK_REWARD(178099,"礼包赠送"),

    BAG_MERGE_ITEM(178100,"背包合成获得道具"),

    SHEN_MO_BOX_REWARD(178101,"宝箱获得神魔碎片"),

    HOU_YUAN_SUPER_REFRESH_COST(178102, "后院超级刷新消耗"),

    WORK_USE_ITEM(178103, "背包使用获得工作间道具"),

    ZUI_QIANG_XIU_XING_STAGE_RANK_GAIN(178104, "最强修行阶段排行奖励"),
    CHAO_NENG_LEVEL_COST(178105, "超能升级消耗"),

    CHAO_NENG_RESET_LEVEL_COST(178106, "超能重置等级消耗"),

    CHAO_NENG_RESET_LEVEL_RETURN(178107, "超能重置等级返还"),

    CHAO_NENG_RANK_COST(178108, "超能突破消耗"),

    CHAO_NENG_MING_WEN_PUT_ON(178109, "超能铭文镶嵌"),

    CHAO_NENG_MING_WEN_TAKE_OFF(178110, "卸下超能铭文"),

    CHAO_NENG_MING_WEN_COMPOSE_COST(178111, "超能铭文合成消耗"),

    CHAO_NENG_MING_WEN_COMPOSE(178112, "超能铭文合成"),

    CHAO_NENG_PUT_ON(178113, "超能上阵"),

    CHAO_NENG_TAKE_OFF(178114, "卸下超能"),

    CHAO_NENG_ROBOT(178115, "机器人超能"),

    NEW_BARRIER_REWARD(178116,"新主线推图奖励"),

    BLACK_HOLE_REWARD(178117,"黑洞玩法奖励"),

    MAIN_BATT_REWARD(178118,"主界面打怪奖励"),

    BLACK_HOLE_SWEEP(178119,"主界面扫荡"),

    HOARD_WU_XING_COST(178120, "神识悟性消耗"),

    COUNT_DOWN_FAILED_REWARD(178121, "倒计时剧情失效奖励"),

    CHAI_JIA_BACK_USE_ITEM(178122,"使用道具拆家返还"),

    LAYTRAD_WEIREN_REWARD(178200, "放置经营-委任奖励"),
    LAYTRAD_WEIREN_COST(178201, "放置经营-委任消耗"),
    CHAO_DU_COST(178202,"超度消耗"),
    CHAO_DU_BACK(178203,"超度返还"),
    CHAO_DU_UP_COST(178204,"超度升级消耗"),
    CHAO_DU_INIT(178205, "超度初始奖励"),
    NATURAL_RESOURCES_WAREHOUSING(178206, "自然资源存入仓库"),
    BUILD_EXPLOIT_BUILDING(178207, "建筑开拓"),
    BUILD_BUILDING(178208, "建筑建造"),
    BUILD_ASSIST_PRODUCTION_COST(178209, "建筑协助加速生产消耗"),
    BUILD_ASSIST_PRODUCTION(178209, "建筑协助加速生产"),
    BUILD_TEMP_BAG_WAREHOUSING(178210, "建筑临时背包仓储"),
    NATURAL_RESOURCES_HARVEST(178211, "自然资源采集"),

    STORY_MAIN_GAME_COST(179001, "剧情主线游戏消耗"),
    CFG_REWARD(179010, "cfg_reward表奖励"),
    FATE_TASK_REWARD(179011, "天命历程任务奖励"),

    BARRIER_LI_XIAN_REWARD(180001, "关卡离线奖励"),
    BARRIER_RANK_REWARD(180002, "关卡排行榜奖励"),

    DRAW_CARD_COST(180100,"抽卡消耗"),
    DRAW_CARD(180101,"抽卡获得"),

    SEASON_PRE_REWARD(180200, "赛季手动领取奖励"),
    SEASON_OPEN_MAIL_REWARD(180201, "赛季开启邮件奖励"),
    SEASON_RANK_MAIL_REWARD(180202, "赛季排行邮件奖励"),
    SEASON_EXTRA_REWARD(180203, "赛季副本通关奖励"),

    STONE_LEVEL_UP_COST(180300, "宝石升级消耗"),
    STONE_PUT_ON_COST(180301, "宝石替换消耗"),
    EQUIP_DUAN_ZAO_COST(180302, "装备锻造消化"),

    ZT_PET_LEVEL_UP_COST(180400, "宠物升级消耗"),
    ZT_PET_RANK_UP_COST(180401, "宠物升阶消耗"),

    ZT_PET_BUY_COST(180402, "购买宠物消耗"),
    ;


    final private int code;

    final private String comment;

    private String specialParam;

    LogAction(int code, String comment) {
        this.code = code;
        this.comment = comment;
    }

    public static LogAction valueOf(int code) {
        for (LogAction logAction : LogAction.values()) {
            if (logAction.getCode() == code) {
                return logAction;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getComment() {
        return comment;
    }

    public String getSpecialParam() {
        return specialParam;
    }

    public void setSpecialParam(String specialParam) {
        this.specialParam = specialParam;
    }

    public static void main(String[] args) {
        int index = 1;
        for (LogAction logAction : LogAction.values()) {
            System.out.println(logAction.name() + "(" + index++ + ",\"" + logAction.comment + "\"),");
        }
    }

}
