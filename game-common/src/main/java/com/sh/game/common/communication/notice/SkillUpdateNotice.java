package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.map.SkillDTO;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:12
 */
@Getter
@Setter
@Notice
public class SkillUpdateNotice extends ProcessNotice {

    private long rid;

    private long actorId;

    private List<SkillDTO> dtoList;


    public SkillUpdateNotice() {

    }

    public SkillUpdateNotice(long rid, long actorId, List<SkillDTO> dtoList) {
        this.rid = rid;
        this.actorId = actorId;
        this.dtoList = dtoList;
    }

}
