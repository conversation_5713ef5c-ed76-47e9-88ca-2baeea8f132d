package com.sh.game.common.communication.msg.system.wudao;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 武道信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWuDaoInfoMessage extends AbsProtostuffMessage {
  /**
   * 龙魂
   */
  private int longhun;

  /**
   * 涅槃
   */
  private int niepan;

  /**
   * 领取过的奖励，表的ID
   */
  private int maxRewardId;

  @Override
  public int getId() {
    return 304002;
  }

  public void setLonghun(int longhun) {
    this.longhun = longhun;
  }

  public int getLonghun() {
    return this.longhun;
  }

  public void setNiepan(int niepan) {
    this.niepan = niepan;
  }

  public int getNiepan() {
    return this.niepan;
  }

  public void setMaxRewardId(int maxRewardId) {
    this.maxRewardId = maxRewardId;
  }

  public int getMaxRewardId() {
    return this.maxRewardId;
  }
}
