package com.sh.game.common.communication.msg.system.chat;

import com.sh.game.common.communication.msg.system.chat.bean.ChatUnitBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回聊天记录
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResChatHistoryMessage extends AbsProtostuffMessage {
  private List<ChatUnitBean> history = new ArrayList<>();

  @Override
  public int getId() {
    return 6004;
  }

  public void setHistory(List<ChatUnitBean> history) {
    this.history = history;
  }

  public List<ChatUnitBean> getHistory() {
    return this.history;
  }
}
