package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求切换攻击模式
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqSwitchFightModelMessage extends AbsProtostuffMessage {
  /**
   * 攻击模式 0 全体，1 帮会
   */
  private int fightModel;

  @Override
  public int getId() {
    return 67029;
  }

  public void setFightModel(int fightModel) {
    this.fightModel = fightModel;
  }

  public int getFightModel() {
    return this.fightModel;
  }
}
