package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.communication.msg.system.activity.bean.ActivityStatusBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回禁地封魔信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResActivityTreasureMonsterInfoMessage extends AbsProtostuffMessage {
  /**
   * 积分领取状态列表
   */
  private List<ActivityStatusBean> acquired = new ArrayList<>();

  /**
   * 积分
   */
  private int score;

  @Override
  public int getId() {
    return 4066;
  }

  public void setAcquired(List<ActivityStatusBean> acquired) {
    this.acquired = acquired;
  }

  public List<ActivityStatusBean> getAcquired() {
    return this.acquired;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }
}
