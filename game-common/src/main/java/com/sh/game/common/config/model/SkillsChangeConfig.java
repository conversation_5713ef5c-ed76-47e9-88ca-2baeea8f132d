package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 转职换技能
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-08-24
 **/
@Getter
@Setter
@ConfigData(file = "cfg_skills_change", keys = "beforeSkill#groupId")
public class SkillsChangeConfig extends AbstractConfigData {

    /**
     * 唯一 Id
     */
    private int id;

    /**
     * 目标职业 Id
     */
    private int groupId;

    /**
     * 转职前技能
     */
    private int beforeSkill;

    /**
     * 转职后技能
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> afterSkill;

}