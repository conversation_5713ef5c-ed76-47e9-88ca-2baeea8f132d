package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class EscortCreateToSceneNotice extends ProcessNotice {

    private long roleId;

    private String roleName;

    private int escort;

    private int route;

    private int expire;

    private int x;
    private int y;


}
