package com.sh.game.common.communication.msg.system.work;

import com.sh.game.common.communication.msg.system.work.bean.UnionMemberHelpBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回建筑行会协助信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWorkUnionHelpInfoMessage extends AbsProtostuffMessage {
  /**
   * 行会成员被协助详细信息
   */
  private List<UnionMemberHelpBean> info = new ArrayList<>();

  @Override
  public int getId() {
    return 408016;
  }

  public void setInfo(List<UnionMemberHelpBean> info) {
    this.info = info;
  }

  public List<UnionMemberHelpBean> getInfo() {
    return this.info;
  }
}
