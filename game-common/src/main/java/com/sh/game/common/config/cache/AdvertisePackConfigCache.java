package com.sh.game.common.config.cache;

import com.google.common.collect.ImmutableSet;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.ActivityScheduleConfig;
import com.sh.game.common.config.model.AdvertisePackConfig;

import java.util.HashSet;
import java.util.Set;

@ConfigCache
public class AdvertisePackConfigCache implements IConfigCache {
    private Set<Integer> activityFastSet;

    @Override
    public void build() {
        Set<Integer> tempFastSet = new HashSet<>();
        for (AdvertisePackConfig config : ConfigDataManager.getInstance().getList(AdvertisePackConfig.class)) {
            if (config.getActivityId() > 0) {
                ActivityScheduleConfig scheduleData = ConfigDataManager.getInstance().getById(ActivityScheduleConfig.class, config.getActivityId());
                if (scheduleData != null && scheduleData.getFastActivity() > 0) {
                    tempFastSet.add(scheduleData.getId());
                }
            }

        }
        activityFastSet = ImmutableSet.copyOf(tempFastSet);
    }

    public Set<Integer> getFastActIdSet() {
        return activityFastSet;
    }
}
