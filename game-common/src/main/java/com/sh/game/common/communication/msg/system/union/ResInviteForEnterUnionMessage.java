package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 发送内推信息至被邀请者
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResInviteForEnterUnionMessage extends AbsProtostuffMessage {
  /**
   * 行会id
   */
  private long unionId;

  /**
   * 行会名
   */
  private String unionName = new String();

  /**
   * 邀请者name
   */
  private String inviterName = new String();

  @Override
  public int getId() {
    return 23057;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setInviterName(String inviterName) {
    this.inviterName = inviterName;
  }

  public String getInviterName() {
    return this.inviterName;
  }
}
