package com.sh.game.common.communication.msg.map.play;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 发送boss死亡信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMapBossDeathMessage extends AbsProtostuffMessage {
  /**
   * boss地图id
   */
  private int mapId;

  @Override
  public int getId() {
    return 87019;
  }

  public void setMapId(int mapId) {
    this.mapId = mapId;
  }

  public int getMapId() {
    return this.mapId;
  }
}
