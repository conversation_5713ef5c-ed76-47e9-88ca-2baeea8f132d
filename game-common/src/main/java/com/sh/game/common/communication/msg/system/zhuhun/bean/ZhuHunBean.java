package com.sh.game.common.communication.msg.system.zhuhun.bean;

import java.lang.Boolean;
import java.lang.Integer;
import java.util.ArrayList;
import java.util.List;

public class ZhuHunBean {
  /**
   * 部位
   */
  private int pos;

  /**
   * 当前铸魂id列表
   */
  private List<Integer> idList = new ArrayList<>();

  /**
   * 当前锁列表
   */
  private List<Boolean> lockList = new ArrayList<>();

  /**
   * 替换铸魂id列表, 为null则未铸魂无法替换
   */
  private List<Integer> replaceIdList = new ArrayList<>();

  public void setPos(int pos) {
    this.pos = pos;
  }

  public int getPos() {
    return this.pos;
  }

  public void setIdList(List<Integer> idList) {
    this.idList = idList;
  }

  public List<Integer> getIdList() {
    return this.idList;
  }

  public void setLockList(List<Boolean> lockList) {
    this.lockList = lockList;
  }

  public List<Boolean> getLockList() {
    return this.lockList;
  }

  public void setReplaceIdList(List<Integer> replaceIdList) {
    this.replaceIdList = replaceIdList;
  }

  public List<Integer> getReplaceIdList() {
    return this.replaceIdList;
  }
}
