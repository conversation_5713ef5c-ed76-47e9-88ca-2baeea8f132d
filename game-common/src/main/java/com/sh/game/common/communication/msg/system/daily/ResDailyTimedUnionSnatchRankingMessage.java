package com.sh.game.common.communication.msg.system.daily;

import com.sh.game.common.communication.msg.abc.bean.CommonRankingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回排名信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDailyTimedUnionSnatchRankingMessage extends AbsProtostuffMessage {
  /**
   * 行会排名
   */
  private List<CommonRankingBean> unionRankings = new ArrayList<>();

  /**
   * 个人排名
   */
  private List<CommonRankingBean> roleRankings = new ArrayList<>();

  /**
   * 行会积分
   */
  private int unionScore;

  /**
   * 个人积分
   */
  private int roleScore;

  @Override
  public int getId() {
    return 175082;
  }

  public void setUnionRankings(List<CommonRankingBean> unionRankings) {
    this.unionRankings = unionRankings;
  }

  public List<CommonRankingBean> getUnionRankings() {
    return this.unionRankings;
  }

  public void setRoleRankings(List<CommonRankingBean> roleRankings) {
    this.roleRankings = roleRankings;
  }

  public List<CommonRankingBean> getRoleRankings() {
    return this.roleRankings;
  }

  public void setUnionScore(int unionScore) {
    this.unionScore = unionScore;
  }

  public int getUnionScore() {
    return this.unionScore;
  }

  public void setRoleScore(int roleScore) {
    this.roleScore = roleScore;
  }

  public int getRoleScore() {
    return this.roleScore;
  }
}
