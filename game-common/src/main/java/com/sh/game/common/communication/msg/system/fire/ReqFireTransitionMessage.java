package com.sh.game.common.communication.msg.system.fire;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求异火转换
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqFireTransitionMessage extends AbsProtostuffMessage {
  /**
   * 异火合成表配置id
   */
  private int configId;

  /**
   * 消耗的异火配置id
   */
  private int costId;

  /**
   * 获得的异火配置id
   */
  private int rewardId;

  @Override
  public int getId() {
    return 383005;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setCostId(int costId) {
    this.costId = costId;
  }

  public int getCostId() {
    return this.costId;
  }

  public void setRewardId(int rewardId) {
    this.rewardId = rewardId;
  }

  public int getRewardId() {
    return this.rewardId;
  }
}
