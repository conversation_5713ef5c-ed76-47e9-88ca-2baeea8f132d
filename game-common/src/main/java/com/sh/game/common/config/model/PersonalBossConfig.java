package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.OptionalConditionConvert;
import com.sh.game.common.config.converter.map.SemicolonIndexConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 个人BOSS
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-12-13
 **/
@Getter
@Setter
@ConfigData(file = "cfg_personal_boss", keys = "duplicateId")
public class PersonalBossConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 怪物id
     */
    private int monsterId;

    /**
     * 怪物出现条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> mCondition;

    /**
     * 玩家进入条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> enterCondition;

    /**
     * 副本id
     */
    private int duplicateId;

    /**
     * 次数条件
     */
    @ConfigField(converter = OptionalConditionConvert.class)
    private List<List<int[]>> timesCondition;

    /**
     * 次数
     */
    @ConfigField(converter = SemicolonIndexConverter.class)
    private Map<Integer, Integer> times;

}
