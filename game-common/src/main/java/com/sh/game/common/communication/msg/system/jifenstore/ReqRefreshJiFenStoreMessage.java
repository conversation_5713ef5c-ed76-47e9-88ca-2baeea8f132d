package com.sh.game.common.communication.msg.system.jifenstore;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>刷新积分商店</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqRefreshJiFenStoreMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 16013;
	}
	
	/**
	 * 1 玩家免费次数刷新 2 玩家付费刷新
	 */
	private int freshType;

	public int getFreshType() {
		return freshType;
	}

	public void setFreshType(int freshType) {
		this.freshType = freshType;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.freshType = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, freshType, false);
		return true;
	}
}
