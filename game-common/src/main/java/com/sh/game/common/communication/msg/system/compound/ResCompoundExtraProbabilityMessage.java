package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回合成额外成功率信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCompoundExtraProbabilityMessage extends AbsProtostuffMessage {
  /**
   * 额外成功率列表 key: 合成表配置id, value: 额外成功率
   */
  private List<CommonKeyValueBean> extraProbability = new ArrayList<>();

  @Override
  public int getId() {
    return 31019;
  }

  public void setExtraProbability(List<CommonKeyValueBean> extraProbability) {
    this.extraProbability = extraProbability;
  }

  public List<CommonKeyValueBean> getExtraProbability() {
    return this.extraProbability;
  }
}
