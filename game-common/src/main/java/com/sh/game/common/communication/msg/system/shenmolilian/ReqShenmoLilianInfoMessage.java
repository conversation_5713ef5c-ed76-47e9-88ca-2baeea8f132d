package com.sh.game.common.communication.msg.system.shenmolilian;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求神魔历练信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqShenmoLilianInfoMessage extends AbsProtostuffMessage {
  private int activityId;

  @Override
  public int getId() {
    return 399001;
  }

  public void setActivityId(int activityId) {
    this.activityId = activityId;
  }

  public int getActivityId() {
    return this.activityId;
  }
}
