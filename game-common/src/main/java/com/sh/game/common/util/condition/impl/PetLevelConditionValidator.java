package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * 灵根天命总数量
 *
 * <AUTHOR>
 * @date 2022/6/17 9:38
 */
public class PetLevelConditionValidator extends IConditionValidatorDefault {

    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params == null || params.length < 2) {
            return false;
        }

        int level = avatar.findPetLevel();

        //condition#min
        if (params.length == 2) {
            return level >= params[1];
        }

        //condition#min#max
        if (params.length == 3) {
            return level >= params[1] && level <= params[2];
        }

        return false;
    }
}
