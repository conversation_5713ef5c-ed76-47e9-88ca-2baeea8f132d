package com.sh.game.common.communication.msg.system.zodiac;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回合成结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCompoundMessage extends AbsProtostuffMessage {
  /**
   * 合成结果 1成功 2失败
   */
  private int result;

  /**
   * 合成道具id
   */
  private int itemId;

  @Override
  public int getId() {
    return 281004;
  }

  public void setResult(int result) {
    this.result = result;
  }

  public int getResult() {
    return this.result;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }
}
