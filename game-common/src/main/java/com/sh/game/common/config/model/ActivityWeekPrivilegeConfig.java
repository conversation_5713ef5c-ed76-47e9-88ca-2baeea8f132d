package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/14 19:19<br>;
 * 版本：1.0<br>;
 * 描述：周末助力
 */
@Getter
@Setter
@ConfigData(file = "cfg_week_privilege", keys = "activityID#rechargeId")
public class ActivityWeekPrivilegeConfig extends AbstractConfigData {

    /**
     * 活动编号
     */
    private int activityID;

    /**
     * 充值编号
     */
    private int rechargeId;

    /**
     * 需要月卡编号
     */
    private int cardId;

    /**
     * 上限充值次数
     */
    private int limitTimes;

    /**
     * 充值奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

}
