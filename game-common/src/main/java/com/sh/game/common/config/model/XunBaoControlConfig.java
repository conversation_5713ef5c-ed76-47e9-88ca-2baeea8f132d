package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/2/9 15:48
 */
@Getter
@Setter
@ConfigData(file = "cfg_xunbao_control")
public class XunBaoControlConfig extends AbstractConfigData {

    private int id;

    /**
     * 道具组
     */
    private int group;

    /**
     * 顺序
     */
    private int paixu;

    /**
     * 秘藏类型
     */
    private int type;

}
