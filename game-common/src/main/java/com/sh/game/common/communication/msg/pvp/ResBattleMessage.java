package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.communication.msg.pvp.bean.MatchTeamBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回对战双方信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBattleMessage extends AbsProtostuffMessage {
  /**
   * 队伍id
   */
  private long battleId;

  /**
   * pvp类型
   */
  private int pvpType;

  /**
   * 队伍A
   */
  private MatchTeamBean teamA = new MatchTeamBean();

  /**
   * 队伍B
   */
  private MatchTeamBean teamB = new MatchTeamBean();

  @Override
  public int getId() {
    return 125042;
  }

  public void setBattleId(long battleId) {
    this.battleId = battleId;
  }

  public long getBattleId() {
    return this.battleId;
  }

  public void setPvpType(int pvpType) {
    this.pvpType = pvpType;
  }

  public int getPvpType() {
    return this.pvpType;
  }

  public void setTeamA(MatchTeamBean teamA) {
    this.teamA = teamA;
  }

  public MatchTeamBean getTeamA() {
    return this.teamA;
  }

  public void setTeamB(MatchTeamBean teamB) {
    this.teamB = teamB;
  }

  public MatchTeamBean getTeamB() {
    return this.teamB;
  }
}
