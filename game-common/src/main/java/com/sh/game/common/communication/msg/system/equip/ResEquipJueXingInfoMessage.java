package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 觉醒信息返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResEquipJueXingInfoMessage extends AbsProtostuffMessage {
  /**
   * 觉醒阶数
   */
  private List<CommonKeyValueBean> juexingLevels = new ArrayList<>();

  @Override
  public int getId() {
    return 13074;
  }

  public void setJuexingLevels(List<CommonKeyValueBean> juexingLevels) {
    this.juexingLevels = juexingLevels;
  }

  public List<CommonKeyValueBean> getJuexingLevels() {
    return this.juexingLevels;
  }
}
