package com.sh.game.common.communication.msg.system.union.bean;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.abc.bean.CommonSlotBean;
import java.lang.String;
import java.util.ArrayList;
import java.util.List;

public class DamageRankBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * 玩家名
   */
  private String name = new String();

  /**
   *  玩家造成的总伤害
   */
  private long damage;

  /**
   *  排名
   */
  private int rank;

  private int career;

  private int sex;

  private int useMount;

  private int zhuanShengId;

  /**
   *  上阵宠物信息，k 1 上阵 2 协同 v cfg_partner表id
   */
  private List<CommonKeyValueBean> huoBan = new ArrayList<>();

  /**
   *  不知道头像获取逻辑，现在先加一个放着，目前不用
   */
  private String param = new String();

  /**
   * 时装
   */
  private List<CommonSlotBean> fashions = new ArrayList<>();

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setDamage(long damage) {
    this.damage = damage;
  }

  public long getDamage() {
    return this.damage;
  }

  public void setRank(int rank) {
    this.rank = rank;
  }

  public int getRank() {
    return this.rank;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }

  public void setUseMount(int useMount) {
    this.useMount = useMount;
  }

  public int getUseMount() {
    return this.useMount;
  }

  public void setZhuanShengId(int zhuanShengId) {
    this.zhuanShengId = zhuanShengId;
  }

  public int getZhuanShengId() {
    return this.zhuanShengId;
  }

  public void setHuoBan(List<CommonKeyValueBean> huoBan) {
    this.huoBan = huoBan;
  }

  public List<CommonKeyValueBean> getHuoBan() {
    return this.huoBan;
  }

  public void setParam(String param) {
    this.param = param;
  }

  public String getParam() {
    return this.param;
  }

  public void setFashions(List<CommonSlotBean> fashions) {
    this.fashions = fashions;
  }

  public List<CommonSlotBean> getFashions() {
    return this.fashions;
  }
}
