package com.sh.game.common.communication.msg.system.fire;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回异火附魂变化信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFireChangeMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  /**
   * 宝石id,宝石id为0表示没有附魂
   */
  private int gemId;

  @Override
  public int getId() {
    return 383009;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setGemId(int gemId) {
    this.gemId = gemId;
  }

  public int getGemId() {
    return this.gemId;
  }
}
