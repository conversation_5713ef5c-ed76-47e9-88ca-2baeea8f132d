package com.sh.game.common.communication.msg.system.welfare;

import com.sh.game.common.communication.msg.system.welfare.bean.SignBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送签到领奖信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSignRewardInfoMessage extends AbsProtostuffMessage {
  /**
   * 签到列表
   */
  private List<SignBean> signBeanList = new ArrayList<>();

  /**
   * 累计天数奖励已领取列表
   */
  private List<Integer> drawDaysList = new ArrayList<>();

  /**
   * 永久签到天数
   */
  private int signDaysForever;

  @Override
  public int getId() {
    return 27001;
  }

  public void setSignBeanList(List<SignBean> signBeanList) {
    this.signBeanList = signBeanList;
  }

  public List<SignBean> getSignBeanList() {
    return this.signBeanList;
  }

  public void setDrawDaysList(List<Integer> drawDaysList) {
    this.drawDaysList = drawDaysList;
  }

  public List<Integer> getDrawDaysList() {
    return this.drawDaysList;
  }

  public void setSignDaysForever(int signDaysForever) {
    this.signDaysForever = signDaysForever;
  }

  public int getSignDaysForever() {
    return this.signDaysForever;
  }
}
