package com.sh.game.common.communication.msg.system.fightforchangan.bean;

import java.lang.String;

public class PlayScoreRankingBean {
  /**
   * 玩家id
   */
  private long roleID;

  /**
   * 玩家名字
   */
  private String roleName = new String();

  /**
   * 所属行会ID
   */
  private long unionID;

  /**
   * 行会名字
   */
  private String unionName = new String();

  /**
   * 所得积分
   */
  private int score;

  /**
   * 排名
   */
  private int nRank;

  public void setRoleID(long roleID) {
    this.roleID = roleID;
  }

  public long getRoleID() {
    return this.roleID;
  }

  public void setRoleName(String roleName) {
    this.roleName = roleName;
  }

  public String getRoleName() {
    return this.roleName;
  }

  public void setUnionID(long unionID) {
    this.unionID = unionID;
  }

  public long getUnionID() {
    return this.unionID;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setNRank(int nRank) {
    this.nRank = nRank;
  }

  public int getNRank() {
    return this.nRank;
  }
}
