package com.sh.game.common.communication.msg.system.build;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 临时背包道具挪动存储(自然资源采集背包130,委任生产背包131,主公府仓库132,角色默认背包11)
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBuildTempBagWarehousingMessage extends AbsProtostuffMessage {
  /**
   * 来源背包
   */
  private int fromWhere;

  private int itemId;

  private int itemCount;

  /**
   * 目标背包
   */
  private int toWhere;

  @Override
  public int getId() {
    return 503006;
  }

  public void setFromWhere(int fromWhere) {
    this.fromWhere = fromWhere;
  }

  public int getFromWhere() {
    return this.fromWhere;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setItemCount(int itemCount) {
    this.itemCount = itemCount;
  }

  public int getItemCount() {
    return this.itemCount;
  }

  public void setToWhere(int toWhere) {
    this.toWhere = toWhere;
  }

  public int getToWhere() {
    return this.toWhere;
  }
}
