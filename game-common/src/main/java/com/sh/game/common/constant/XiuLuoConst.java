package com.sh.game.common.constant;

/**
 * <AUTHOR>
 * @date 2022/8/15 15:58
 */
public interface XiuLuoConst {

    enum JieSuan{
        WIN(1),
        LOSE(2),
        PING_JU(3),
        USE_ITEM(4),
        ;

        int status;

        Jie<PERSON><PERSON>(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }

    interface RewardType {
        /**
         * 赛季胜场
         */
        int SHENG_CHANG = 1;
        /**
         * 杀人数
         */
        int KILL_COUNT = 2;
        /**
         * 旗子数
         */
        int QI_ZI_COUNT = 3;
        /**
         * 日常胜场
         */
        int DAILY_SHENG_LI = 4;
    }
}
