package com.sh.game.common.communication.msg.system.yuanjing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回源晶小铺信息
 * 该文件由工具根据 yuanjing.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResYuanJingStoreInfoMessage extends AbsProtostuffMessage {
  /**
   * 已抽卡次数
   */
  private int count;

  @Override
  public int getId() {
    return 416001;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
