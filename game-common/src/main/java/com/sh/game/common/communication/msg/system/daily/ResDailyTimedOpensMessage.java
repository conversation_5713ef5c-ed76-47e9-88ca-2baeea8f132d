package com.sh.game.common.communication.msg.system.daily;

import com.sh.game.common.communication.msg.system.daily.bean.DailyTimedStatusBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDailyTimedOpensMessage extends AbsProtostuffMessage {
  /**
   * 活动开放列表
   */
  private List<DailyTimedStatusBean> opens = new ArrayList<>();

  @Override
  public int getId() {
    return 175004;
  }

  public void setOpens(List<DailyTimedStatusBean> opens) {
    this.opens = opens;
  }

  public List<DailyTimedStatusBean> getOpens() {
    return this.opens;
  }
}
