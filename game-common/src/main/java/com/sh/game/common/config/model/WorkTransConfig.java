package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 建筑改造表
 */
@Getter
@Setter
@ConfigData(file = "cfg_worktrans")
public class WorkTransConfig extends AbstractConfigData {
    private int id;

    /**
     * 单次改造消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> transCost;

    /**
     * 单次改造产出
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> trans;

    //单次改造 可选最大数量
    private int transMax;

    //单次改造消耗时间,单位毫秒
    private long transTime;

    /**
     * 单次改造取消返还
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> transBack;
}
