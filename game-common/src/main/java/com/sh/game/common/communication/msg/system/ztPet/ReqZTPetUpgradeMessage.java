package com.sh.game.common.communication.msg.system.ztPet;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求宠物升阶
 * 该文件由工具根据 ztPet.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZTPetUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 宠物配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 414006;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
