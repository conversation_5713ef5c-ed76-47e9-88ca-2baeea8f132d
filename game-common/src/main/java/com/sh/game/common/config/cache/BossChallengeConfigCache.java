package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.MapSpawnConfig;
import com.sh.game.common.config.model.MonsterConfig;

import java.util.*;

/**
 * boss挑战表缓存
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-01-20
 **/
@ConfigCache
public class BossChallengeConfigCache implements IConfigCache {

    /**
     * key: 怪物类型
     * value: MonsterId列表
     */
    private Map<Integer, Set<Integer>> monsterTypeMap = new HashMap<>();

    /**
     * key: bossType统计类型
     * value: MonsterId列表
     */
    private Map<Integer, Set<Integer>> bossTypeMap = new HashMap<>();


    /**
     * key: MonsterId
     * value: 所在地图id列表
     */
    private Map<Integer, Set<Integer>> monsterMap = new HashMap<>();


    @Override
    public void build() {
        monsterTypeMap.clear();
        bossTypeMap.clear();
        monsterMap.clear();

        List<MonsterConfig> monsterList = ConfigDataManager.getInstance().getList(MonsterConfig.class);
        List<MapSpawnConfig> mapSpawnConfigs = ConfigDataManager.getInstance().getList(MapSpawnConfig.class);
        for (MapSpawnConfig mapSpawnConfig : mapSpawnConfigs) {
            if (mapSpawnConfig.getBosscount() > 0) {
                monsterMap.putIfAbsent(mapSpawnConfig.getMid(), new HashSet<>());
                monsterMap.get(mapSpawnConfig.getMid()).add(mapSpawnConfig.getMapid());
            }
        }

        for (MonsterConfig config : monsterList) {
            if (monsterMap.keySet().contains(config.getId())){
                monsterTypeMap.putIfAbsent(config.getType(), new HashSet<>());
                monsterTypeMap.get(config.getType()).add(config.getId());

                bossTypeMap.putIfAbsent(config.getBossType(), new HashSet<>());
                bossTypeMap.get(config.getBossType()).add(config.getId());
            }
            // 绑定id的怪 归入父级
            if ((config.getBondid() > 0 && monsterMap.keySet().contains(config.getBondid()))) {
                monsterMap.get(config.getBondid()).addAll(monsterMap.getOrDefault(config.getId(), new HashSet<>()));
                monsterMap.remove(monsterMap.get(config.getId()));
            }
        }

    }

    public Set<Integer> findMonsterIdSet(int monsterType) {
        return monsterTypeMap.getOrDefault(monsterType, new HashSet<>());
    }

    /**
     * 根据统计怪物类型获取怪物id列表
     *
     * @param bossType 怪物统计类型
     * @return Set<Integer>
     */
    public Set<Integer> findMonsterIdSetByBossType(int bossType) {
        return bossTypeMap.getOrDefault(bossType, new HashSet<>());
    }

    public Set<Integer> findMonsterFromMapSet(int monsterId) {
        return monsterMap.getOrDefault(monsterId, new HashSet<>());
    }
}
