package com.sh.game.common.communication.msg.system.store;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求打开商店
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqOpenStoreByTypeMessage extends AbsProtostuffMessage {
  /**
   * storeClassId
   */
  private int storeClassId;

  @Override
  public int getId() {
    return 16007;
  }

  public void setStoreClassId(int storeClassId) {
    this.storeClassId = storeClassId;
  }

  public int getStoreClassId() {
    return this.storeClassId;
  }
}
