package com.sh.game.common.communication.msg.system.lottery;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.lottery.bean.RewardCountBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;

import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回抽奖详情
 * 该文件由工具根据 lottery.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResLotteryInfo extends AbsProtostuffMessage {
    /**
     * 已经挖取的次数，会重置
     */
    private List<RewardCountBean> rewardCounts = new ArrayList<>();

    /**
     * 个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     */
    private List<CommonKeyValueBean> selfMaxCounts = new ArrayList<>();

    @Override
    public int getId() {
        return 409004;
    }

    public List<RewardCountBean> getRewardCounts() {
        return this.rewardCounts;
    }

    public void setRewardCounts(List<RewardCountBean> rewardCounts) {
        this.rewardCounts = rewardCounts;
    }

    public List<CommonKeyValueBean> getSelfMaxCounts() {
        return this.selfMaxCounts;
    }

    public void setSelfMaxCounts(List<CommonKeyValueBean> selfMaxCounts) {
        this.selfMaxCounts = selfMaxCounts;
    }
}
