package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.StepOutConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

@ConfigCache
public class SlotUnlockConfigCache implements IConfigCache {
    // key:index,value:每个slot的解锁条件
    Table<Integer,Integer,SlotUnlockInfo> slotUnlockConditions = HashBasedTable.create();

    @AllArgsConstructor
    @Getter
    public static class SlotUnlockInfo{
        int index;
        List<int[]> condition;
        // 限制的职业, 0 表示不限制
        int job;
    }
    @Override
    public void build() {
        List<StepOutConfig> configs =  ConfigDataManager.getInstance().getList(StepOutConfig.class)
                .stream()
                .sorted(Comparator.comparingInt(StepOutConfig::getStep))
                .collect(Collectors.toList());
        for (StepOutConfig config : configs) {
            slotUnlockConditions.put(config.getStep(),config.getPosition(),new SlotUnlockInfo(config.getPosition(),config.getCondition(),config.getChooseJob()));
        }
    }

    public SlotUnlockInfo queryConditions(int lineupIndex,int slotIndex){
        return slotUnlockConditions.get(lineupIndex,slotIndex);
    }

    public int lineUpSize(){
        return slotUnlockConditions.rowMap().size();
    }
}
