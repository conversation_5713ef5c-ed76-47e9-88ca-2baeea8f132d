package com.sh.game.common.config.model;


import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * 假行会配置表
 */
@Getter
@Setter
@ConfigData(file = "cfg_union_guide", keys="id")
public class UnionGuideConfig extends AbstractConfigData {

    private int id;

    /**
     * 行会名称
     */
   private String unionid;

    /**
     * 会长名称
     */
   private String leaderid;

    /**
     * 行会等级
     */
   private int level;

    /**
     * 会长职业
     */
   private int career;

    /**
     * 会长性别
     */
   private int sex;

    /**
     * 会长等级
     */
   private int level2;

    /**
     * 入会条件
     */
   private String condition;
}
