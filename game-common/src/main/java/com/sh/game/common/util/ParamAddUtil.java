package com.sh.game.common.util;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.config.model.SkillSpecialConfig;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

public class ParamAddUtil {

    //增强SkillCondition的参数效果
    public static int calSkillConditionParam(Map<Integer, Set<Integer>> skillSpecEffectMap, SkillConditionConfig skillConditionConfig, int group, int index) {
        if (group >= skillConditionConfig.getParam().length) {
            return 0;
        }

        int[] ints = skillConditionConfig.getParam()[group];
        if (index >= ints.length) {
            return 0;
        }

        int param = ints[index];

        if (skillSpecEffectMap != null && !skillSpecEffectMap.isEmpty()) {
            Set<Integer> skillSpecIds = skillSpecEffectMap.get(skillConditionConfig.getSkillId());
            if (CollectionUtils.isEmpty(skillSpecIds)) {
                return param;
            }

            for (Integer skillSpecId : skillSpecIds) {
                SkillSpecialConfig skillSpecialConfig = ConfigDataManager.getInstance().getById(SkillSpecialConfig.class, skillSpecId);
                if (skillSpecialConfig == null) {
                    continue;
                }

                //group#index
                int[] paramOrder = skillSpecialConfig.getParamorder();
                if (paramOrder == null || paramOrder.length != 2 || (paramOrder[0] - 1) != group || (paramOrder[1] - 1) != index) {
                    continue;
                }

                param += skillSpecialConfig.getParamvalue();
            }
        }

        return param;
    }

    //增强SkillCondition的参数效果
    public static int[] calSkillConditionParams(Map<Integer, Set<Integer>> skillSpecEffectMap, SkillConditionConfig skillConditionConfig, int group) {
        if (group >= skillConditionConfig.getParam().length) {
            return new int[]{};
        }

        int[] ints = skillConditionConfig.getParam()[group];

        if (skillSpecEffectMap != null && !skillSpecEffectMap.isEmpty()) {
            Set<Integer> skillSpecIds = skillSpecEffectMap.get(skillConditionConfig.getSkillId());
            if (CollectionUtils.isEmpty(skillSpecIds)) {
                return ints;
            }

            int[] ret = null;

            for (Integer skillSpecId : skillSpecIds) {
                SkillSpecialConfig skillSpecialConfig = ConfigDataManager.getInstance().getById(SkillSpecialConfig.class, skillSpecId);
                if (skillSpecialConfig == null) {
                    continue;
                }

                int[] paramOrder = skillSpecialConfig.getParamorder();
                if (paramOrder == null || paramOrder.length != 2 || (paramOrder[0] - 1) != group) {
                    continue;
                }

                int index = paramOrder[1] - 1;
                if (index >= ints.length) {
                    continue;
                }

                if (ret == null) {
                    ret = Arrays.copyOf(ints, ints.length);
                }
                ret[index] = ret[index] + skillSpecialConfig.getParamvalue();
            }

            if (ret != null) {
                return ret;
            }
        }

        return ints;
    }

    //增强SkillCondition的参数效果
    public static int[][] calSkillConditionAllParams(Map<Integer, Set<Integer>> skillSpecEffectMap, SkillConditionConfig skillConditionConfig) {
        int[][] paramArrays = skillConditionConfig.getParam();

        if (skillSpecEffectMap != null && !skillSpecEffectMap.isEmpty()) {
            Set<Integer> skillSpecIds = skillSpecEffectMap.get(skillConditionConfig.getSkillId());
            if (CollectionUtils.isEmpty(skillSpecIds)) {
                return paramArrays;
            }

            int[][] ret = null;

            for (Integer skillSpecId : skillSpecIds) {
                SkillSpecialConfig skillSpecialConfig = ConfigDataManager.getInstance().getById(SkillSpecialConfig.class, skillSpecId);
                if (skillSpecialConfig == null) {
                    continue;
                }

                int[] paramOrder = skillSpecialConfig.getParamorder();
                if (paramOrder == null || paramOrder.length != 2) {
                    continue;
                }

                int group = paramOrder[0] - 1;
                int index = paramOrder[1] - 1;
                if (group >= paramArrays.length) {
                    continue;
                }
                int[] ints = paramArrays[group];
                if (index >= ints.length) {
                    continue;
                }

                if (ret == null) {
                    ret = new int[paramArrays.length][];
                    for (int i = 0; i < paramArrays.length; i++) {
                        ret[i] = Arrays.copyOf(paramArrays[i], paramArrays[i].length);
                    }
                }
                ret[group][index] = ret[group][index] + skillSpecialConfig.getParamvalue();
            }

            if (ret != null) {
                return ret;
            }
        }

        return paramArrays;
    }

    //增强SkillCondition的buff效果
    public static int[] calSkillConditionBuff(Map<Integer, Set<Integer>> skillSpecEffectMap, SkillConditionConfig skillConditionConfig) {
        int[] buffers = skillConditionConfig.getBuffers();

        if (skillSpecEffectMap != null && !skillSpecEffectMap.isEmpty()) {
            Set<Integer> skillSpecIds = skillSpecEffectMap.get(skillConditionConfig.getSkillId());
            if (CollectionUtils.isEmpty(skillSpecIds)) {
                return buffers;
            }

            List<Integer> buffAddList = null;
            for (Integer skillSpecId : skillSpecIds) {
                SkillSpecialConfig skillSpecialConfig = ConfigDataManager.getInstance().getById(SkillSpecialConfig.class, skillSpecId);
                if (skillSpecialConfig == null) {
                    continue;
                }

                Map<Integer, int[]> buffAddMap = skillSpecialConfig.getBuffadd();
                if (buffAddMap == null || buffAddMap.isEmpty()) {
                    continue;
                }

                int[] buffAddInts = buffAddMap.get(skillConditionConfig.getSkillId());
                if (buffAddInts == null || buffAddInts.length == 0) {
                    continue;
                }

                if (buffAddList == null) {
                    buffAddList = new LinkedList<>();
                }
                for (int buffId : buffAddInts) {
                    buffAddList.add(buffId);
                }
            }

            if (buffAddList != null) {
                int[] ret = new int[buffers.length + buffAddList.size()];
                System.arraycopy(buffers, 0, ret, 0, buffers.length);
                for (int i = 0; i < buffAddList.size(); i++) {
                    ret[buffers.length + i] = buffAddList.get(i);
                }
                return ret;
            }
        }

        return buffers;
    }

}
