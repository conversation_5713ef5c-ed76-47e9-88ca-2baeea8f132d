package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/1/22 14:18
 */
@Getter
@Setter
@ConfigData(file = "cfg_union_chest_progress", keys = "boxID")
public class UnionChestProgressConfig extends AbstractConfigData {

    private int id;

    private int boxID;

    private int type;

    private int progress;

    private int chest_point;

    private int countdown;
}
