package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ConfigData(file = "cfg_pet_rank", keys = "petId#rank")
public class ZTPetRankConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 宠物id
     */
    private int petId;

    /**
     * 阶级
     */
    private int rank;

    /**
     * 下一阶级
     */
    private int nextRank;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * 技能
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> skill;

    /**
     * 消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> cost;
}
