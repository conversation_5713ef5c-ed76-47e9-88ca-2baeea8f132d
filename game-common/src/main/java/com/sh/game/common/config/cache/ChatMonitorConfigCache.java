package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.ChatMonitorConfig;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 聊天监控配置表
 * <AUTHOR>
 * @date 2022/5/30
 */
@ConfigCache
@Log4j2
public class ChatMonitorConfigCache implements IConfigCache {
    private Map<Integer, ChatMonitorConfig> cache = new TreeMap<>();

    @Override
    public void build() {
        cache.clear();
        List<ChatMonitorConfig> list = ConfigDataManager.getInstance().getList(ChatMonitorConfig.class);
        for (ChatMonitorConfig item: list){
            for (Integer platID : item.getPlatformarea()){
                if (cache.containsKey(platID)){
                    log.error("聊天监控配置出现了重复的平台id {}", platID);
                    continue;
                }
                cache.put(platID, item);
            }
        }
    }

    public  ChatMonitorConfig getChatMonitorConfig(int platID){
        return cache.get(platID);
    }
}
