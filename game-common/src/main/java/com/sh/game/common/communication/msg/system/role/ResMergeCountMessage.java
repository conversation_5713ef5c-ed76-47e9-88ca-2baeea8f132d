package com.sh.game.common.communication.msg.system.role;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回合服次数
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMergeCountMessage extends AbsProtostuffMessage {
  /**
   * 合服次数
   */
  private int count;

  @Override
  public int getId() {
    return 8061;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
