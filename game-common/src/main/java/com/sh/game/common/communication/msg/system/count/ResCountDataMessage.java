package com.sh.game.common.communication.msg.system.count;

import com.sh.game.common.communication.msg.system.count.bean.CountBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回数量的列表
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCountDataMessage extends AbsProtostuffMessage {
  /**
   * 数量列表
   */
  private List<CountBean> countList = new ArrayList<>();

  @Override
  public int getId() {
    return 21001;
  }

  public void setCountList(List<CountBean> countList) {
    this.countList = countList;
  }

  public List<CountBean> getCountList() {
    return this.countList;
  }
}
