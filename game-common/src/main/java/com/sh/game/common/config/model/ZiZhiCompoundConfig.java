package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 资质合成
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-09-22
 **/
@Getter
@Setter
@ConfigData(file = "cfg_zizhi_compound")
public class ZiZhiCompoundConfig extends AbstractConfigData {

    /**
     * 合成表id
     */
    private int id;

    /**
     * 合成消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemId;

    /**
     * 合成产出
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> product;

    /**
     * 成功几率万分比
     */
    private int probability;

    /**
     * 是否公告
     */
    private int announce;

}
