package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 飘字效果通知
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPiaoZiEffectMessage extends AbsProtostuffMessage {
  /**
   * 目标id
   */
  private long targetId;

  /**
   * 参数
   */
  private long param;

  /**
   * 飘字类型
   */
  private int piaozi;

  @Override
  public int getId() {
    return 67055;
  }

  public void setTargetId(long targetId) {
    this.targetId = targetId;
  }

  public long getTargetId() {
    return this.targetId;
  }

  public void setParam(long param) {
    this.param = param;
  }

  public long getParam() {
    return this.param;
  }

  public void setPiaozi(int piaozi) {
    this.piaozi = piaozi;
  }

  public int getPiaozi() {
    return this.piaozi;
  }
}
