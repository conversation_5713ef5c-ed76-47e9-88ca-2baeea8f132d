package com.sh.game.common.communication.msg.system.ringGem;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求镶嵌宝石
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqRingStoneInlaidGemMessage extends AbsProtostuffMessage {
  /**
   * 材料id
   */
  private int itemId;

  /**
   * 特戒装备位pos
   */
  private int equipIndex;

  /**
   * 槽位
   */
  private int index;

  @Override
  public int getId() {
    return 342003;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setEquipIndex(int equipIndex) {
    this.equipIndex = equipIndex;
  }

  public int getEquipIndex() {
    return this.equipIndex;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }
}
