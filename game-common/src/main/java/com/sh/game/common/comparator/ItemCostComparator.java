package com.sh.game.common.comparator;

import com.sh.game.common.entity.backpack.item.Item;

import java.util.Comparator;

/**
 * 这个用来扣道具时候排序用
 */
public class ItemCostComparator implements Comparator<Item> {

    public static final ItemCostComparator COMPARATOR = new ItemCostComparator();

    @Override
    public int compare(Item o1, Item o2) {
        if (o1.getWhere() == o2.getWhere()) {
            if (o1.findItemConfig().getBind() == o2.findItemConfig().getBind()) {
                return 0;
            }
            return o2.findItemConfig().getBind() - o1.findItemConfig().getBind();
        }
        return o2.getWhere() - o1.getWhere();
    }
}
