package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.map.MonsterHpDto;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@Notice
public class PlayerExitNotice extends ProcessNotice {

    private long rid;

    private int cfgId;

    private List<MonsterHpDto> hps = new ArrayList<>();

    /**
     * 个人副本创建时间（ms）
     */
    private long createTime;
}
