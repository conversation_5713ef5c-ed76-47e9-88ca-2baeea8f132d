package com.sh.game.common.communication.msg.system.climbTower;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 投骰子
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRollClimbTowerMessage extends AbsProtostuffMessage {
  /**
   *  1：免费或者普通 2：金
   */
  private int type;

  /**
   * 金骰子时选择的值
   */
  private int val;

  @Override
  public int getId() {
    return 309004;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setVal(int val) {
    this.val = val;
  }

  public int getVal() {
    return this.val;
  }
}
