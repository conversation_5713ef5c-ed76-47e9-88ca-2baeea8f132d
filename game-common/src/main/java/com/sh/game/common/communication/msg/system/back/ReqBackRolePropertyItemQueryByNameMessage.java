package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求玩家道具信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBackRolePropertyItemQueryByNameMessage extends AbsProtostuffMessage {
  /**
   * 玩家名字
   */
  private String uName = new String();

  @Override
  public int getId() {
    return 43125;
  }

  public void setUName(String uName) {
    this.uName = uName;
  }

  public String getUName() {
    return this.uName;
  }
}
