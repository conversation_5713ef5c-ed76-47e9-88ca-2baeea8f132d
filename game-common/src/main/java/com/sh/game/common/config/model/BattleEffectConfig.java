
package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;

/**
 * effect 表
 * <AUTHOR>
 * @date 2023/9/8
 */
@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_battleeffect")
public class BattleEffectConfig extends AbstractConfigData {
    /**
     * effect id
     */
    private int id;

    /**
     * effect type
     */
    private int type;

    /**
     * 参数
     */
    private int[] param;

    /**
     * 状态数
     */
    private int[] stateParam;

    /**
     * 作用目标类型
     */
    private int targetType;

    /**
     * 优先级
     */
    private int priority;

    private int roundCheck;

    /**
     * 每轮生效次数
     */
    private int roundTimi;

    private int stopState;

    /**
     * 全局可触发次数
     */
    private int triggerCount;

    private int detection;
}