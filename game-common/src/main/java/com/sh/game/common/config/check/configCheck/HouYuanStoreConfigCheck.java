package com.sh.game.common.config.check.configCheck;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.check.ConfigCheckHelper;
import com.sh.game.common.config.check.IConfigCheck;
import com.sh.game.common.config.model.HouYuanStoreConfig;
import com.sh.game.common.config.model.RechargeConfig;

import java.util.List;

public class HouYuanStoreConfigCheck implements IConfigCheck {
    @Override
    public void check() {
        List<HouYuanStoreConfig> list = ConfigDataManager.getInstance().getList(HouYuanStoreConfig.class);
        for (HouYuanStoreConfig config : list) {
            if (config.getRechargeid() > 0) {
                ConfigCheckHelper.assertConfigExists("cfg_houyuan_store", "rechargeid", config.getId(), RechargeConfig.class, config.getRechargeid());
            }

            if (config.getNextId() > 0) {
                ConfigCheckHelper.assertConfigExists("cfg_houyuan_store", "nextId", config.getId(), HouYuanStoreConfig.class, config.getNextId());
            }
        }
    }
}
