package com.sh.game.common.entity.backpack.committype;

import com.sh.game.common.entity.backpack.committype.impl.CoinCommitHandler;
import com.sh.game.common.entity.backpack.committype.impl.EquipCommitHandler;
import com.sh.game.common.entity.backpack.constant.BackpackConst;

import java.util.HashMap;
import java.util.Map;

public class CommitHandleFactory {

    private static final Map<Integer, AbstractCommitHandler> ITEM_HANDLER_MAP = new HashMap<>();


    static {
        ITEM_HANDLER_MAP.put(BackpackConst.Place.EQUIP.getWhere(), new EquipCommitHandler());
        ITEM_HANDLER_MAP.put(BackpackConst.Place.COIN.getWhere(), new CoinCommitHandler());
    }

    /**
     * 获取道具处理类
     *
     * @param place
     * @return
     */
    public static AbstractCommitHandler getItemHandler(int place) {
        AbstractCommitHandler itemHandler = ITEM_HANDLER_MAP.get(place);
        //默认返回装备处理器
        return itemHandler == null ? ITEM_HANDLER_MAP.get(BackpackConst.Place.EQUIP.getWhere()) : itemHandler;
    }
}
