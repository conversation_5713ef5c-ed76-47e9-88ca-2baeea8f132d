package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

/**
 * ATO：wiley <br>;
 * 时间：2021/1/13 9:58<br>;
 * 版本：1.0<br>;
 * 描述：忠诚度条件检测
 */
public class LoyaltyConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params == null || params.length < 2) {
            return true;
        }
        return avatar.checkLoyalty(params[1]);
    }
}
