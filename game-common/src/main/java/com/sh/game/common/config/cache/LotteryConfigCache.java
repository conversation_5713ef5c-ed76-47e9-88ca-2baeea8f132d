package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.LotteryRewardConfig;
import lombok.Getter;

import java.util.*;

/**
 * description:
 * date: 2024/5/10
 * author: chenBin
 */
@Getter
@ConfigCache
public class LotteryConfigCache implements IConfigCache {
    /**
     * key: rewardGroup
     * value: LotteryRewardConfigs
     */
    private final Map<Integer, List<LotteryRewardConfig>> rewards = new HashMap<>();

    /**
     * key: rewardGroup
     * value: LotteryRewardConfigs
     */
    private final Map<Integer, List<LotteryRewardConfig>> firstRewards = new HashMap<>();

    /**
     * 活动类型
     */
    private final Set<Integer> actTypes = new HashSet<>();

    @Override
    public void build() {
        rewards.clear();
        firstRewards.clear();

        for (LotteryRewardConfig config : ConfigDataManager.getInstance().getList(LotteryRewardConfig.class)) {
            rewards.computeIfAbsent(config.getRewardGroup(), k -> new ArrayList<>()).add(config);

            if (config.getFirstTime() == 1) {
                firstRewards.computeIfAbsent(config.getRewardGroup(), k -> new ArrayList<>()).add(config);
            }
            actTypes.add(config.getActType());
        }
    }


    public List<LotteryRewardConfig> getRewards(int group) {
        return rewards.getOrDefault(group, new ArrayList<>());
    }

    public LotteryRewardConfig getFirstReward(int group) {
        if (!firstRewards.containsKey(group)) {
            return null;
        }
        if (!firstRewards.get(group).isEmpty()) {
            return firstRewards.get(group).get(0);
        }
        return null;
    }
}
