package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回玩家战力
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleFightPowerMessage extends AbsProtostuffMessage {
  /**
   * 角色id
   */
  private long rid;

  /**
   * 战斗力
   */
  private long fightPower;

  @Override
  public int getId() {
    return 43168;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setFightPower(long fightPower) {
    this.fightPower = fightPower;
  }

  public long getFightPower() {
    return this.fightPower;
  }
}
