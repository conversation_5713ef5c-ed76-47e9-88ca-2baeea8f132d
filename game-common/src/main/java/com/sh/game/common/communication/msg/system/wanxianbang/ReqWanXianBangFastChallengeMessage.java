package com.sh.game.common.communication.msg.system.wanxianbang;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求速战
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqWanXianBangFastChallengeMessage extends AbsProtostuffMessage {
  /**
   *  挑战列表中的rank
   */
  private int rank;

  /**
   *  速战次数
   */
  private int count;

  @Override
  public int getId() {
    return 396004;
  }

  public void setRank(int rank) {
    this.rank = rank;
  }

  public int getRank() {
    return this.rank;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
