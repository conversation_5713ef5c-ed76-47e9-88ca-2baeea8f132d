package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Long;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 拾取地图道具
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqAutoPickUpMapItemMessage extends AbsProtostuffMessage {
  /**
   * 地面道具的唯一id
   */
  private List<Long> objIds = new ArrayList<>();

  @Override
  public int getId() {
    return 67082;
  }

  public void setObjIds(List<Long> objIds) {
    this.objIds = objIds;
  }

  public List<Long> getObjIds() {
    return this.objIds;
  }
}
