package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.ShuXianIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/23 9:40
 */
@Getter
@Setter
@ConfigData(file = "cfg_tianxuan")
public class TianXuanConfig extends AbstractConfigData {

    private int id;

    private int activityId;

    private int activityType;

    private int type;

    private int rank;

    @ConfigField(converter = ShuXianIntArrayConverter.class)
    private int[] goals;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    private int mail;
}
