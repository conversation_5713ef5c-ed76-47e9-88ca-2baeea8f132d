package com.sh.game.common.communication.msg.system.handbooksublimation;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Long;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求升华
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHandBookSublimationUpLevelMessage extends AbsProtostuffMessage {
  /**
   * 升华类型
   */
  private int type;

  /**
   * 消耗道具唯一id
   */
  private List<Long> itemIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 345001;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setItemIdList(List<Long> itemIdList) {
    this.itemIdList = itemIdList;
  }

  public List<Long> getItemIdList() {
    return this.itemIdList;
  }
}
