package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ConfigData(file = "cfg_qianghuashoujue")
public class QiangHuaShouJueConfig extends AbstractConfigData {

    /**
     * 兽决装备itemCfgId
     */
    private int id;

    private int nextId;

    private int itemid;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    private int[] buffId;

    private int level;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> needItem;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    private int announce;


}
