package com.sh.game.common.communication.msg.system.jiutian;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求九天之巅杀人数排行信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toScene")
public class ReqJiuTianKillRankMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long rid;

  @Override
  public int getId() {
    return 351004;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }
}
