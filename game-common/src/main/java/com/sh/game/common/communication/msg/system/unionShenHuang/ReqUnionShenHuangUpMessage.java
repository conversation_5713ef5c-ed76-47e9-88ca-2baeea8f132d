package com.sh.game.common.communication.msg.system.unionShenHuang;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求解锁特戒灵石槽位
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUnionShenHuangUpMessage extends AbsProtostuffMessage {
  /**
   * 次数
   */
  private int count;

  @Override
  public int getId() {
    return 361003;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
