package com.sh.game.common.communication.msg.system.ztPet;

import com.sh.game.common.communication.msg.system.ztPet.bean.ZTPetBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回宠物系统信息
 * 该文件由工具根据 ztPet.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResZTPetInfoMessage extends AbsProtostuffMessage {
  /**
   * 已选择的跟随宠物
   */
  private int selectPet;

  /**
   * 已购买宠物信息
   */
  private List<ZTPetBean> ZTPetBean = new ArrayList<>();

  @Override
  public int getId() {
    return 414002;
  }

  public void setSelectPet(int selectPet) {
    this.selectPet = selectPet;
  }

  public int getSelectPet() {
    return this.selectPet;
  }

  public void setZTPetBean(List<ZTPetBean> ZTPetBean) {
    this.ZTPetBean = ZTPetBean;
  }

  public List<ZTPetBean> getZTPetBean() {
    return this.ZTPetBean;
  }
}
