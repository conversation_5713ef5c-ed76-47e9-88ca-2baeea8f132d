package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * boss挑战
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-01-19
 **/
@Setter
@Getter
@ConfigData(file = "cfg_boss_tiaozhan")
public class BossChallengeConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 挑战类型
     */
    private int type1;

    /**
     * 怪物id
     */
    private int monster;

    /**
     * 地图id
     */
    private int mapid;
}
