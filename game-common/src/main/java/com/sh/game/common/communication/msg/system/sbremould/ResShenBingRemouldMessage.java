package com.sh.game.common.communication.msg.system.sbremould;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回神兵改造结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResShenBingRemouldMessage extends AbsProtostuffMessage {
  /**
   * 部位
   */
  private int pos;

  /**
   * 是否成功
   */
  private boolean flag;

  @Override
  public int getId() {
    return 368004;
  }

  public void setPos(int pos) {
    this.pos = pos;
  }

  public int getPos() {
    return this.pos;
  }

  public void setFlag(boolean flag) {
    this.flag = flag;
  }

  public boolean getFlag() {
    return this.flag;
  }
}
