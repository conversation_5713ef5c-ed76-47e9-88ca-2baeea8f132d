package com.sh.game.common.communication.msg.map;

import com.sh.game.common.communication.msg.map.bean.RoundServantBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 英雄进入视野
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResServantEnterViewMessage extends AbsProtostuffMessage {
  /**
   * servant
   */
  private RoundServantBean servant = new RoundServantBean();

  @Override
  public int getId() {
    return 67007;
  }

  public void setServant(RoundServantBean servant) {
    this.servant = servant;
  }

  public RoundServantBean getServant() {
    return this.servant;
  }
}
