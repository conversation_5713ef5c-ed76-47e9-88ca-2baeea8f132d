package com.sh.game.common.communication.msg.system.injectsoul;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回注灵升级结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResInjectSoulResultMessage extends AbsProtostuffMessage {
  /**
   * 是否升级成功
   */
  private boolean result;

  @Override
  public int getId() {
    return 327004;
  }

  public void setResult(boolean result) {
    this.result = result;
  }

  public boolean getResult() {
    return this.result;
  }
}
