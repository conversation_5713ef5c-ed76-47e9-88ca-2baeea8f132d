package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class BattleUnionBean extends KryoBean {

	/**
	 * 战斗服id
	 */
	private int hostId;
	/**
	 * 帮会id
	 */
	private long unionId;
	/**
	 * 内皇宫副本id
	 */
	private int innerMapId;
	/**
	 * 外皇宫地图id
	 */
	private int outMapId;
	/**
	 * 战斗服ip
	 */
	private String ip;
	/**
	 * 战斗服端口
	 */
	private int port;
	/**
	 * 战斗类型
	 */
	private int matchType;
	/**
	 * 敌对帮会简要信息
	 */
	private List<SampleUnionBean> sampleUnionBean = new ArrayList<>();

	public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public long getUnionId() {
		return unionId;
	}

	public void setUnionId(long unionId) {
		this.unionId = unionId;
	}

		public int getInnerMapId() {
		return innerMapId;
	}

	public void setInnerMapId(int innerMapId) {
		this.innerMapId = innerMapId;
	}

		public int getOutMapId() {
		return outMapId;
	}

	public void setOutMapId(int outMapId) {
		this.outMapId = outMapId;
	}

		public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

		public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

		public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

		public List<SampleUnionBean> getSampleUnionBean() {
		return sampleUnionBean;
	}

	public void setSampleUnionBean(List<SampleUnionBean> sampleUnionBean) {
		this.sampleUnionBean = sampleUnionBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.hostId = readInt(buf, false);
		this.unionId = readLong(buf);
		this.innerMapId = readInt(buf, false);
		this.outMapId = readInt(buf, false);
		this.ip = readString(buf);
		this.port = readInt(buf, false);
		this.matchType = readInt(buf, false);
		int sampleUnionBeanLength = readShort(buf);
		for (int sampleUnionBeanI = 0; sampleUnionBeanI < sampleUnionBeanLength; sampleUnionBeanI++) {
			if (readByte(buf) == 0) { 
				this.sampleUnionBean.add(null);
			} else {
				SampleUnionBean sampleUnionBean = new SampleUnionBean();
				sampleUnionBean.read(buf);
				this.sampleUnionBean.add(sampleUnionBean);
			}
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, hostId, false);
		this.writeLong(buf, unionId);
		this.writeInt(buf, innerMapId, false);
		this.writeInt(buf, outMapId, false);
		this.writeString(buf, ip);
		this.writeInt(buf, port, false);
		this.writeInt(buf, matchType, false);
		writeShort(buf, this.sampleUnionBean.size());
		for (int sampleUnionBeanI = 0; sampleUnionBeanI < this.sampleUnionBean.size(); sampleUnionBeanI++) {
			this.writeBean(buf, this.sampleUnionBean.get(sampleUnionBeanI));
		}
		return true;
	}
}
