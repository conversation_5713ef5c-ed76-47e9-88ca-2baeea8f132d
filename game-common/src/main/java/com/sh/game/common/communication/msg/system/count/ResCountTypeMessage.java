package com.sh.game.common.communication.msg.system.count;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 交易，丢弃，上架次数限制
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCountTypeMessage extends AbsProtostuffMessage {
  /**
   * 消息id
   */
  private int helpId;

  @Override
  public int getId() {
    return 21006;
  }

  public void setHelpId(int helpId) {
    this.helpId = helpId;
  }

  public int getHelpId() {
    return this.helpId;
  }
}
