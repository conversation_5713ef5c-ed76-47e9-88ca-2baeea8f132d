package com.sh.game.common.constant;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 * author: xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 17/6/23 13:52
 * desc  : 常量类型
 * Copyright(©) 2017 by xiaomo.
 */
public interface FightConst {

    /**
     * 战斗模式
     */
    @Deprecated
    interface FightModel {
        /**
         * 切换地图时保持不变
         */
        int KEEP = 0;
        /**
         * 和平模式
         */
        int PEACE = 1;
        /**
         * 全体模式
         */
        int ALL = 2;
        /**
         * 善恶模式
         */
        int TARGET_ONLY = 3;
        /**
         * 组队模式
         */
        int TEAM = 4;
        /**
         * 帮会模式
         */
        int UNION = 5;
        /**
         * 同盟模式
         */
        int ALLIANCE = 6;
        /**
         * 阵营(特殊玩法里)
         */
        int GROUP = 7;
        /**
         * 行会阵营
         */
        int UNION_CAMP = 8;

        static boolean exist(int fightModule) {
            return fightModule >= PEACE && fightModule <= GROUP;
        }

        static boolean canSwitch(int fightModule) {
            return fightModule >= PEACE && fightModule <= UNION_CAMP;
        }
    }

    int YELLOW_SHOW_PK_VALUE = 2;

    /**
     * 伤害类型
     */
    @Deprecated
    enum HurtType {
        /**
         * 普通伤害
         */
        NORMAL_HURT(1, true, 0),
        /**
         * 致命一击
         */
        DEAD_ATK(2, true, 10),
        /**
         * 闪避
         */
        DODGE(3, true, 99),
        /**
         * 回血
         */
        RECOVER_HP(4),
        /**
         * 格挡
         */
        BLOCK(5, true, 1),
        /**
         * 无敌
         */
        WU_DI(6, true, 1),
        /**
         * 神圣伤害
         */
        HOLY(7),
        /**
         * 暴击
         */
        CRITICAL(8, true, 30),
        /**
         * 压制
         */
        PRESS(9, true, 20),
        /**
         * 麻痹
         */
        MA_BI(10),
        /**
         * 重力
         */
        GRAVITY(11),
        /**
         * 冰冻
         */
        FROZEN(12),
        /**
         * 合击
         */
        COMB(13, true, 98),

        /**
         * 战意伤害类型
         */
        ZHANYI_HURT(18, true, 1),
        /**
         * 麒麟臂改变伤害类型
         */
        QILINBI(19, true, 1),
        /**
         * 连续斩杀
         */
        LIAN_ZHAN(20, true, 1),

        /**
         * 超神
         */
        CHAO_SHEN(21, true, 1),


        /**
         * 杀猪刀用，读取配置表
         */
        SHAZHUDAO(22, true, 1),

        /**
         * 流星火雨，分段伤害类型
         */
        HUOYU(23, true, 1),

        /**
         * 神威压制
         */
        YAZHI(25, true, 1),

        /**
         * 护盾
         */
        SHIELD(27, true, 1),

//        /**
//         * 切割
//         */
//        QIE_GE(25),

        /**
         * 重击(临时)
         */
        THUMP(996, true, 40),
        /**
         * 护盾抵挡
         */
        SHIELD_RESIST(997),
        /**
         * 万剑归宗buff万分比增伤
         */
        WAN_JIAN_GUI_ZONG_HURT_RATE(998),
        /**
         * 反伤
         */
        REBOUND(999),

        /**
         * 特殊重击飘字
         */
        SP_THUMP(34, true, 41),

        /**
         * 刀刀元宝
         */
        GOLD(36, false, 2),

        /**
         * 新流星火雨，分段伤害类型(客户端无伤害延迟，服务端无区别)
         */
        NEW_HUOYU(37, true, 1),
        ;

        /**
         * 类型
         */
        private final int type;

        /**
         * 是否能被覆盖
         */
        private final boolean cover;
        /**
         * 优先级
         */
        private final int level;

        HurtType(int type) {
            this(type, false, 0);
        }

        HurtType(int type, boolean cover, int level) {
            this.type = type;
            this.cover = cover;
            this.level = level;
        }

        public int type() {
            return this.type;
        }

        public int level() {
            return this.level;
        }

        public boolean isCover() {
            return cover;
        }

        public static HurtType parse(int type) {
            for (HurtType hurtType : HurtType.values()) {
                if (hurtType.type() == type) {
                    return hurtType;
                }
            }
            return NORMAL_HURT;
        }
    }

    /**
     * 目标类型
     */
    class TargetType {

        /**
         * 自己
         */
        public static int SELF = 1;

        /**
         * 索敌目标
         */
        public static int ENEMY = 2;

        /**
         * 索敌目标为中心的 技能效果范围内的全部敌人
         */
        public static int ENEMY_RANGE = 3;


        /**
         * 施法者为中心的 技能效果范围内的全部敌人
         */
        public static int SELF_RANGE = 4;

    }


    /**
     * 飘字类型
     */
    enum FloaterType {
        /**
         * 普通伤害
         */
        NORMAL(1, 2),

        /**
         * 暴击
         */
        CRITICAL(2, 3),

        /**
         * 治疗
         */
        CURE(3, 4),

        /**
         * 蓝量
         */
        RECOVER_MP(5, 4),
        /**
         * 处决
         */
        CHU_JUE(8,8),
        /**
         * 火焰伤害
         */
        FIRE_ELEMENT_HURT(420005, 3, AttributeEnum.ZTXY_FIRE_DAMAGE.getType()),
        /**
         * 冰寒伤害
         */
        COLD_ELEMENT_HURT(420006, 3, AttributeEnum.ZTXY_COLD_DAMAGE.getType()),
        /**
         * 闪电伤害
         */
        LIGHTNING_ELEMENT_HURT(420007, 3, AttributeEnum.ZTXY_LIGHTNING_DAMAGE.getType()),
        /**
         * 神圣伤害
         */
        HOLY_ELEMENT_HURT(420008, 3, AttributeEnum.ZTXY_HOLY_DAMAGE.getType()),
        /**
         * 毒素伤害
         */
        POISON_ELEMENT_HURT(420009, 3, AttributeEnum.ZTXY_POISON_DAMAGE_REDUCTION.getType()),


        /**
         * 闪避
         */
        DODGE(101, 5),

        /**
         * 眩晕
         */
        DIZZINESS(102, 7),

        /**
         * 免疫
         */
        WUDI(201, 6);


        /**
         * 类型
         */
        private final int type;

        /**
         * 优先级
         */
        private final int level;

        /**
         * 元素类型
         */
        private final int elementAttrType;

        FloaterType(int type, int level) {
            this.type = type;
            this.level = level;
            this.elementAttrType = -1;
        }

        FloaterType(int type, int level, int elementAttrType) {
            this.type = type;
            this.level = level;
            this.elementAttrType = elementAttrType;
        }

        public int type() {
            return this.type;
        }

        public int level() {
            return this.level;
        }

        public int elementAttrType() {
            return this.elementAttrType;
        }

        public static FloaterType parse(int type) {
            for (FloaterType floaterType : FloaterType.values()) {
                if (floaterType.type() == type) {
                    return floaterType;
                }
            }
            return NORMAL;
        }

        public static FloaterType elementAttrType(int elementAttrType) {
            for (FloaterType floaterType : FloaterType.values()) {
                if (floaterType.elementAttrType() == elementAttrType) {
                    return floaterType;
                }
            }
            return NORMAL;
        }

    }


}
