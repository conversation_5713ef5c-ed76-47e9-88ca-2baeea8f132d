package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_kachi_gift")
public class KaChiGiftConfig extends AbstractConfigData {
    private int id;

    /**
     * 1-普通 2-自选
     */
    private int type;

    private int weight;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemid;
}
