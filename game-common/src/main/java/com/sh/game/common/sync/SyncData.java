package com.sh.game.common.sync;

import com.sh.game.common.communication.notice.SyncDataNotice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public interface SyncData {
    Logger LOG = LoggerFactory.getLogger(SyncData.class);
    Map<Class<?>, Map<String, SyncUpdate>> SYNC_UPDATE_CACHE = new ConcurrentHashMap<>();

    long getId();

    default long getRoleId() {
        return getId();
    }

    void sendSyncDataNotice(SyncDataNotice notice);

    default void afterDeserializer() {

    }

    default void updateSync(List<SyncBean> syncBeans) {
        if (syncBeans == null) {
            return;
        }
        syncBeans.forEach(syncBean -> updateSync(syncBean.getName(), syncBean.getValue()));
    }

    default void updateSync(String name, Object value) {
        Map<String, SyncUpdate> syncUpdateMap = SYNC_UPDATE_CACHE.computeIfAbsent(this.getClass(), k -> new ConcurrentHashMap<>());
        SyncUpdate syncUpdate = syncUpdateMap.get(name);
        if (syncUpdate == null) {
            Field field = null;
            Method method = null;

            Class clazz = this.getClass();
            while (clazz != null) {
                try {
                    field = clazz.getDeclaredField(name);
                    break;
                } catch (Exception e) {
                    clazz = clazz.getSuperclass();
                }
            }
            char[] nameArray = name.toCharArray();
            nameArray[0] -= 32;
            String callbackName = "onSet" + String.valueOf(nameArray);
            clazz = this.getClass();
            while (clazz != null) {
                try {
                    method = clazz.getDeclaredMethod(callbackName);
                    break;
                } catch (Exception e) {
                    clazz = clazz.getSuperclass();
                }
            }

            if (field != null) {
                syncUpdate = new SyncUpdate();
                field.setAccessible(true);
                syncUpdate.field = field;

                if (method != null) {
                    method.setAccessible(true);
                    syncUpdate.method = method;
                }
                syncUpdateMap.put(name, syncUpdate);
            }
        }

        if (syncUpdate == null) {
            LOG.warn("no such field 【{}】",  name);
            return;
        }

        try {
            syncUpdate.field.set(this, value);
            if (syncUpdate.method != null) {
                syncUpdate.method.invoke(this);
            }
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }


}
