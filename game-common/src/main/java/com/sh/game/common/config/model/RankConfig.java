package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/24 19:14<br>;
 * 版本：1.0<br>;
 * 描述：排行榜配置
 */
@Setter
@Getter
@ConfigData(file = "cfg_rank")
public class RankConfig extends AbstractConfigData {
    /**
     * 排行榜编号
     */
    private int id;

    /**
     * 参与排行榜条件
     */
    private int	condition;

    /**
     * 最大排名人数
     */
    private int num;

    /**
     * 第一名奖励称号
     */
    private int reward;

    /**
     * 第一名奖励称号
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward1;
}
