package com.sh.game.common.communication.msg.system.escort;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求押镖
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqDailyEscortStartMessage extends AbsProtostuffMessage {
  /**
   * 请求押镖id
   */
  private int escortId;

  @Override
  public int getId() {
    return 105002;
  }

  public void setEscortId(int escortId) {
    this.escortId = escortId;
  }

  public int getEscortId() {
    return this.escortId;
  }
}
