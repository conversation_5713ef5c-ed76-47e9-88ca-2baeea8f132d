package com.sh.game.common.communication.msg.system.jianzhong;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>剑冢排行榜</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqJianZhongRankMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 312008;
	}
	
	/**
	 * 职业
	 */
	private int career;

	public int getCareer() {
		return career;
	}

	public void setCareer(int career) {
		this.career = career;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.career = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, career, false);
		return true;
	}
}
