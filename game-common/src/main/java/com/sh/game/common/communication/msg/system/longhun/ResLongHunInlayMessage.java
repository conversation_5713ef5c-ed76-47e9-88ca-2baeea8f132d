package com.sh.game.common.communication.msg.system.longhun;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回龙魂镶嵌融合信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResLongHunInlayMessage extends AbsProtostuffMessage {
  /**
   * 装备道具唯一id
   */
  private long equipId;

  /**
   * 融合龙魂itemId列表
   */
  private List<Integer> longhunGemIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 352003;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setLonghunGemIdList(List<Integer> longhunGemIdList) {
    this.longhunGemIdList = longhunGemIdList;
  }

  public List<Integer> getLonghunGemIdList() {
    return this.longhunGemIdList;
  }
}
