package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 聊天禁言
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBandChatMessage extends AbsProtostuffMessage {
  /**
   * 对象的uid
   */
  private String uid = new String();

  /**
   * 禁言理由
   */
  private String banReason = new String();

  /**
   * 禁聊时间(要禁言的秒数)
   */
  private int bandTime;

  @Override
  public int getId() {
    return 43030;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public String getUid() {
    return this.uid;
  }

  public void setBanReason(String banReason) {
    this.banReason = banReason;
  }

  public String getBanReason() {
    return this.banReason;
  }

  public void setBandTime(int bandTime) {
    this.bandTime = bandTime;
  }

  public int getBandTime() {
    return this.bandTime;
  }
}
