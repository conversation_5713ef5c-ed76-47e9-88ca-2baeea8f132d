package com.sh.game.common.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class MessageCounter {
    static final MessageCounter INSTANCE = new MessageCounter();

    static final  ConcurrentHashMap<Integer, AtomicInteger> counts = new ConcurrentHashMap<>();

    public void count(Integer msgId){
        counts.compute(msgId,(k,v)->{
            if(v==null){
                return new AtomicInteger(1);
            }
            return  new AtomicInteger(v.incrementAndGet());
        });
    }

    public static MessageCounter getInstance(){
        return INSTANCE;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        counts.forEach((k,v)->{
            sb.append(String.format("%8d,%8d\n",k,v.get()));
        });
        return sb.toString();
    }
}
