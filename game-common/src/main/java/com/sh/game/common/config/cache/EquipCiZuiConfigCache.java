package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.EquipCiZuiConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description:
 * create: 2025/6/12
 * author: chen bin
 */
@ConfigCache
public class EquipCiZuiConfigCache implements IConfigCache {
    private Map<Integer, List<EquipCiZuiConfig>> groupId2Config;

    @Override
    public void build() {
        groupId2Config = ConfigDataManager.getInstance().getList(EquipCiZuiConfig.class).stream()
                .collect(Collectors.groupingBy(EquipCiZuiConfig::getGroupId));

    }

    public List<EquipCiZuiConfig> getAll(int groupId) {
        return groupId2Config.getOrDefault(groupId, new ArrayList<>());
    }
}
