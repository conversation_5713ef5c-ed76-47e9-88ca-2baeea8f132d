package com.sh.game.common.communication.msg.system.fightforchangan;

import com.sh.game.common.communication.msg.system.fightforchangan.bean.StrongholdInfoBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 战场基本信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFightForChangAnInfoMessage extends AbsProtostuffMessage {
  /**
   * 副本配置id
   */
  private int cfgId;

  /**
   * 副本id（自增）
   */
  private long instnceId;

  /**
   * 据点信息
   */
  private List<StrongholdInfoBean> strongholdList = new ArrayList<>();

  @Override
  public int getId() {
    return 343001;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }

  public void setInstnceId(long instnceId) {
    this.instnceId = instnceId;
  }

  public long getInstnceId() {
    return this.instnceId;
  }

  public void setStrongholdList(List<StrongholdInfoBean> strongholdList) {
    this.strongholdList = strongholdList;
  }

  public List<StrongholdInfoBean> getStrongholdList() {
    return this.strongholdList;
  }
}
