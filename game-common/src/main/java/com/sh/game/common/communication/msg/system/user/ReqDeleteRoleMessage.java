package com.sh.game.common.communication.msg.system.user;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求删除角色
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqDeleteRoleMessage extends AbsProtostuffMessage {
  /**
   * 角色id
   */
  private long rid;

  @Override
  public int getId() {
    return 1035;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }
}
