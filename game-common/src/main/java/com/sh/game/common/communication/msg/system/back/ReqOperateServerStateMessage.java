package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求操作服务器状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqOperateServerStateMessage extends AbsProtostuffMessage {
  /**
   * 操作类型 1 查询 2修改
   */
  private int type;

  /**
   * 0 关闭 1 维护 2 开放
   */
  private int state;

  @Override
  public int getId() {
    return 43016;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }
}
