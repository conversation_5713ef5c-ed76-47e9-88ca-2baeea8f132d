package com.sh.game.common.communication.msg.system.team;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 邀请通知
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResInvitedMessage extends AbsProtostuffMessage {
  /**
   * 玩家编号
   */
  private long rid;

  /**
   * 玩家名称
   */
  private String playerName = new String();

  /**
   * 接受结束时间，时间戳
   */
  private int endTime;

  @Override
  public int getId() {
    return 101007;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setPlayerName(String playerName) {
    this.playerName = playerName;
  }

  public String getPlayerName() {
    return this.playerName;
  }

  public void setEndTime(int endTime) {
    this.endTime = endTime;
  }

  public int getEndTime() {
    return this.endTime;
  }
}
