package com.sh.game.common.communication.msg.system.jiutian;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求进入九天之巅地图
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEnterJiuTianMapMessage extends AbsProtostuffMessage {
  /**
   * jiutian表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 351001;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
