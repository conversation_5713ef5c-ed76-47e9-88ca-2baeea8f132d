package com.sh.game.common.communication.msg.system.monster;

import com.sh.game.common.communication.msg.system.monster.bean.MapMonsterCountBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回怪物数量详情信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMonsterCountInfoMessage extends AbsProtostuffMessage {
  /**
   * 怪物cid
   */
  private int monsterCid;

  private List<MapMonsterCountBean> beans = new ArrayList<>();

  @Override
  public int getId() {
    return 191021;
  }

  public void setMonsterCid(int monsterCid) {
    this.monsterCid = monsterCid;
  }

  public int getMonsterCid() {
    return this.monsterCid;
  }

  public void setBeans(List<MapMonsterCountBean> beans) {
    this.beans = beans;
  }

  public List<MapMonsterCountBean> getBeans() {
    return this.beans;
  }
}
