package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceStatusBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResAppearanceInfoMessage extends AbsProtostuffMessage {
  private List<AppearanceStatusBean> appearance = new ArrayList<>();

  @Override
  public int getId() {
    return 200011;
  }

  public void setAppearance(List<AppearanceStatusBean> appearance) {
    this.appearance = appearance;
  }

  public List<AppearanceStatusBean> getAppearance() {
    return this.appearance;
  }
}
