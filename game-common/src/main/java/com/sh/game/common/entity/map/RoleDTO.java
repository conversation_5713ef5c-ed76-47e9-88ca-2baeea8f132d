package com.sh.game.common.entity.map;

import com.sh.common.jdbc.ProtostuffSerializable;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.avatar.AvatarBackState;
import com.sh.game.common.entity.buff.Buffs;
import com.sh.game.common.sync.SyncBean;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2018/8/30 17:11
 */
@Getter
@Setter
public class RoleDTO implements ProtostuffSerializable {

    @Tag(6)

    private int createTime;
    @Tag(7)
    private List<SyncBean> syncBeans;

    @Tag(8)
    private HeroDTO heroDTO;

    @Tag(9)
    private Attribute attribute;

    @Tag(10)
    private Map<Integer, SkillDTO> skillMap = new HashMap<>();

    @Tag(14)
    private Buffs buffs = new Buffs();

    @Tag(15)
    private Map<Integer, EquipDTO> equipMap = new HashMap<>();


    @Tag(20)
    private List<ServantDTO> servantDTOS;


    @Tag(24)
    private long hp;

    @Tag(25)
    private long mp;

//    @Tag(26)
//    private int wingIllusion;

    @Tag(31)
    private int fightModel;

    @Tag(33)
    private int pkValue;

    @Tag(34)
    private int pkValueTotalTime;

    @Tag(35)
    private int mount;

    @Tag(36)
    private int dragonWingStrengthLevel;

    @Tag(37)
    private int xialvId;

    @Tag(38)
    private int career;

    @Tag(39)
    private int xialvGuanghuan;

    @Tag(40)
    private int xialvUpTeXiao;

    @Tag(41)
    private int mingWang;

    @Tag(42)
    private int xiaLvGuardianLevel;

    @Tag(43)
    private List<Integer> xiaLvEquipList = new ArrayList<>();

    @Tag(44)
    private long xiaLvLastDieTime;

    /**
     * 侠侣血量
     */
    @Tag(45)
    private long xiaLvHp;

    /**
     * 是否开启狂暴之力
     */
    @Tag(46)
    private boolean isSuperMan;

    /**
     * 是否开启狂暴之力回城保护
     */
    @Tag(47)
    private boolean isProtect;

    /**
     * 神威突破属性，key:type,value:神威突破表cfgId
     */
    @Tag(48)
    private Map<Integer,Integer> shenWei = new HashMap<>();

    /**
     * 宠物配置
     */
    @Tag(49)
    private int petCfgId;

    /**
     * 法器等级
     */
    @Tag(50)
    private int faQiLevel;

    /**
     * 是否开启狂暴之力随机保护
     */
    @Tag(51)
    private boolean isRandomProtect;

    /**
     * 护盾值
     */
    @Tag(52)
    private long shield;

    /**
     * 玉佩id
     */
    @Tag(53)
    private int yuPeiId;

    /**
     * 刀刀元宝每日获取数
     */
    @Tag(54)
    private long goldCount;

    /**
     * 神皇id
     */
    @Tag(55)
    private int shenHuangId;

    /**
     * 阵营类型
     */
    @Tag(56)
    private int campType;

    /**
     * 军衔配置id
     */
    @Tag(57)
    private int militaryRankCfgId;

    /**
     * 境界配置id
     */
    @Tag(58)
    private int realmId;

    /**
     * 性别
     */
    @Tag(59)
    private int sex;

    @Tag(60)
    private Set<Integer> equipSuits = new HashSet<>();

    @Tag(61)
    private ZTPetDTO ztPet;

    @Exclude
    private AvatarBackState avatarBackState = new AvatarBackState();

}
