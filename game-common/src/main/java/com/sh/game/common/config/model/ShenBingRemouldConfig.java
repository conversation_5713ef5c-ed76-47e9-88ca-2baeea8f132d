package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 神兵改造配置
 *
 * <AUTHOR>
 * @date 2022/09/20 21:55
 */
@Setter
@Getter
@ConfigData(file="cfg_sbgaizao")
public class ShenBingRemouldConfig extends AbstractConfigData {

    private int id;

    /**
     * 下一级id
     */
    private int nextId;

    /**
     * 部位
     */
    private int pos;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    /**
     * 改造成功率(万分比)
     */
    private int probability;

    /**
     * 失败增加成功率(万分比)
     */
    private int probabilityUp;

    /**
     * 等级
     */
    private int level;

    /**
     * 改造消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> needItem;

    /**
     * 改造条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 公告id
     */
    private int announce;
}
