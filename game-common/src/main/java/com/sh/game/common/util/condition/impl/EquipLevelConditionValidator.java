package com.sh.game.common.util.condition.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.converter.list.ShuXianYuHaoAndJinHaoListConverter;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

import java.util.List;
import java.util.Map;

/**
 * 强化等级校验
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-01-05
 **/
public class EquipLevelConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null || params.length < 3) {
            return false;
        }

        Backpack backpack = avatar.getBackpack();
        if (backpack == null) {
            return false;
        }
        Storage storage = backpack.fetchStorage(BackpackConst.Place.EQUIP);
        Map<Integer, Item> equipMap = storage.getData();
        GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.STRENGTHEN_CONDITION);
        List<List<int[]>> globalCondition = new ShuXianYuHaoAndJinHaoListConverter().convert(config.getValue());
        int level = 0;
        Item item = equipMap.get(params[1]);
        if (item != null) {
            level = item.findItemConfig().getUpgradeLevel();
        }

        for (List<int[]> ints : globalCondition) {
            if (params[1] != ints.get(0)[0]) {
                continue;
            }
            level = jisuanLevel(ints.get(1), equipMap);
        }
        // 下限判断
        if (level < params[2]) {
            return false;
        }

        // 还需要判断上限
        if (params.length == 4) {
            return level <= params[3];
        }
        return true;
    }

    /**
     * 计算多个部位的强化等级
     * @param indexes
     * @return
     */
    private static int jisuanLevel(int[] indexes ,Map<Integer, Item> equipMap){
        int level = 0;
        for (int index : indexes) {
            Item item = equipMap.get(index);
            level += item == null ? 0 : item.findItemConfig().getUpgradeLevel();
        }
        return level;
    }
}
