package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * description:
 * create: 2025/6/12
 * author: chen bin
 */
@Getter
@Setter
@ConfigData(file = "cfg_equip_cizui")
public class EquipCiZuiConfig extends AbstractConfigData {

    private int id;

    /**
     * 词缀库id
     */
    private int groupId;

    /**
     * 该库下，属性id
     */
    private int attributeId;

    /**
     * 属性下限数值，随机时，保底最低的数值。
     */
    private long attributeNum1;

    /**
     * 属性上限数值 随机时，最高出现的数值
     */
    private long attributeNum2;

    /**
     * 随机波动值单位 主要针对万分比数值，100=每次以1%为最小单位值波动，50=每次以0.5%的最小单位值波动。
     */
    private int unit;

    private int probability;

    private int type;
}
