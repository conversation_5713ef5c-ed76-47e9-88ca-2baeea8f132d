package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * description:
 * create: 2025/6/12
 * author: chen bin
 */
@Getter
@Setter
@ConfigData(file = "cfg_equip_cizui")
public class EquipCiZuiConfig extends AbstractConfigData {

    private int id;

    /**
     * 词缀库id
     */
    private int groupId;

    /**
     * 
     */
    private int attributeId;

    private long attributeNum1;

    private long attributeNum2;

    private int unit;

    private int probability;

    private int type;
}
