package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求获取专属宝箱奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZhuanShuBoxRewardMessage extends AbsProtostuffMessage {
  /**
   * 专属装备宝箱奖励表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 13079;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
