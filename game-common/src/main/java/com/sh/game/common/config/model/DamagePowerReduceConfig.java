package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.ShuXianIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

/**
 * 战力伤害减免
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/8/12.
 */
@Getter
@Setter
@ConfigData(file = "cfg_damage_power_reduce", keys = "id")
public class DamagePowerReduceConfig extends AbstractConfigData {
    private int id;

    @ConfigField(converter = ShuXianIntArrayConverter.class)
    private int[] powerRange;

    //战士减免
    private int reduce;

}
