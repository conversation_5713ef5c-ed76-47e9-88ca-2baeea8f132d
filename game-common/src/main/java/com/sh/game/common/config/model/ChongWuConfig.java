package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ConfigData(file = "cfg_chongwu")
public class ChongWuConfig extends AbstractConfigData {

    private int id;

    private int level;

    private String name;

    /**
     * 宠物类型
     */
    private int type;

    /**
     * 第一级id
     */
    private int firstId;

    /**
     * 条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 宠物id
     */
    private int monsterid;

    /**
     * 下一级id
     */
    private int nextId;

    /**
     * 与主角保持的距离
     */
    private int distance;

    /**
     * 距离主人的距离（大于此距离就视为脱离攻击范围）
     */
    private int attackrange;


    /**
     * 距离主人的距离（大于此距离就传送回主人身边）
     */
    private int transfer;

    /**
     * 死亡后复活时间(秒)
     */
    private int relivetime;

    /**
     * 重新选择宠物消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 升级消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> costitem;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer,Long>[] attr;

    /**
     * buffID
     */
    private int[] buffid;

}
