package com.sh.game.common.communication.msg.system.union;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求抢红包
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqRedPackGetMessage extends AbsProtostuffMessage {
  /**
   * 红包id
   */
  private long lid;

  @Override
  public int getId() {
    return 23305;
  }

  public void setLid(long lid) {
    this.lid = lid;
  }

  public long getLid() {
    return this.lid;
  }
}
