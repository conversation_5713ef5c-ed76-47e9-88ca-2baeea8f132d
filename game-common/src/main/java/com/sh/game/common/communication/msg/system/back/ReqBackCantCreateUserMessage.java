package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 注册限制
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBackCantCreateUserMessage extends AbsProtostuffMessage {
  /**
   * 天数
   */
  private int days;

  @Override
  public int getId() {
    return 43142;
  }

  public void setDays(int days) {
    this.days = days;
  }

  public int getDays() {
    return this.days;
  }
}
