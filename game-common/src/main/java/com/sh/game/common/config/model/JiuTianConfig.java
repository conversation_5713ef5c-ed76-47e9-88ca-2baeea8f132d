package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/3 9:48
 */
@Getter
@Setter
@ConfigData(file = "cfg_jiutian")
public class JiuTianConfig extends AbstractConfigData {

    private int id;

    /**
     * type 类型（1积分 2杀人数 3女神眷顾 4击杀排行）备注：积分为进入下一层条件，达到可进入下一层
     */
    private int type;

    /**
     * 参数（根据类型而定:1积分填写积分数，2杀人填写杀人数，3女神眷顾填写时间，单位秒）
     */
    private int param;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    private int mail;

    private int mapId;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] pos;

    private int range;

    private int num;

    private int announce;
}
