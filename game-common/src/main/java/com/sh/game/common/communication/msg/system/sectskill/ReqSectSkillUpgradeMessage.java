package com.sh.game.common.communication.msg.system.sectskill;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求门派技能升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSectSkillUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 组别
   */
  private int group;

  @Override
  public int getId() {
    return 339003;
  }

  public void setGroup(int group) {
    this.group = group;
  }

  public int getGroup() {
    return this.group;
  }
}
