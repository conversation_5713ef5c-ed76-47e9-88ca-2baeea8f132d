package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 21:16
 */
@Getter
@Setter
@ConfigData(file = "cfg_million")
public class MillionConfig extends AbstractConfigData {

    private int id;

    private int type;

    private int day;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rate;

    private int announce;


    private int day2;

    /**
     * 活动id
     */
    private int actId;

    /**
     * 达到多少钱
     */
    private int money;
}
