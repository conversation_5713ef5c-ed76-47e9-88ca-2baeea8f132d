package com.sh.game.common.communication.msg.system.militaryRank;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取军衔俸禄
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqMilitaryRankDailyRewardMessage extends AbsProtostuffMessage {
  /**
   * 是否多倍领取
   */
  private boolean isMulti;

  @Override
  public int getId() {
    return 366005;
  }

  public void setIsMulti(boolean isMulti) {
    this.isMulti = isMulti;
  }

  public boolean getIsMulti() {
    return this.isMulti;
  }
}
