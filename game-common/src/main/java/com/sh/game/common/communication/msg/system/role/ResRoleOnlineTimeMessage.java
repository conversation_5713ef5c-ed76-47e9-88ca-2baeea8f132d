package com.sh.game.common.communication.msg.system.role;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回玩家在线时长信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleOnlineTimeMessage extends AbsProtostuffMessage {
  /**
   * 在线时长秒
   */
  private int onlineTime;

  @Override
  public int getId() {
    return 8066;
  }

  public void setOnlineTime(int onlineTime) {
    this.onlineTime = onlineTime;
  }

  public int getOnlineTime() {
    return this.onlineTime;
  }
}
