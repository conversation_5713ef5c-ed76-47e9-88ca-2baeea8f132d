package com.sh.game.common.communication.msg.system.daily;

import com.sh.game.common.communication.msg.system.daily.bean.ChangAnMiJingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 长安秘境道具信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResChangAnMiJingItemMessage extends AbsProtostuffMessage {
  /**
   * 道具信息
   */
  private List<ChangAnMiJingBean> bean = new ArrayList<>();

  /**
   * 剩余未抽取道具总堆数
   */
  private int count;

  @Override
  public int getId() {
    return 175110;
  }

  public void setBean(List<ChangAnMiJingBean> bean) {
    this.bean = bean;
  }

  public List<ChangAnMiJingBean> getBean() {
    return this.bean;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
