package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取怪物图鉴进度奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqCardProgressRewardMessage extends AbsProtostuffMessage {
  /**
   * 怪物图鉴进度表id
   */
  private int cid;

  @Override
  public int getId() {
    return 316003;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
