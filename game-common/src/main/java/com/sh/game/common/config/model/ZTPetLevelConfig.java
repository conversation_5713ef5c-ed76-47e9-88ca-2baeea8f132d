package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@ConfigData(file = "cfg_pet_level", keys = "petId#petLevel")
public class ZTPetLevelConfig  extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 宠物id
     */
    private int petId;

    /**
     * 等级
     */
    private int petLevel;

    /**
     * 下一等级
     */
    private int nextLevel;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * 消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> cost;
}
