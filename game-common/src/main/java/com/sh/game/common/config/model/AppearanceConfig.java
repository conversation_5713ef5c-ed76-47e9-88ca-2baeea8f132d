package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


/**
 * appearance
 */
@Getter
@Setter
@ConfigData(file = "cfg_fashion")
public class AppearanceConfig extends AbstractConfigData {

    /**
     * 编号
     */
    private int id;

    /**
     * 解锁所需道具
     */
    private int itemId;

    /**
     * 类型：1幻武2装扮3足迹4称号9:侠侣皮肤
     */
    private int type;

    /**
     * 限时，秒，默认不限时
     */
    private int time;

    /**
     * 父称号
     */
    private int father;

    /**
     * 顺序
     */
    private int order;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    /**
     * 时装名称
     */
    private String name;

    /**
     * 是否全服唯一称号
     */
    private int serverTarget;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> unlock;

    private int announce;

    /**
     * 购买消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     *个人称号系统:是否为系统可升级称号
     */
    private int isSys;

    /**
     * 个人称号系统升级的等级
     */
    private int level;

    /**
     * 时装模型id
     */
    private int model;

    /**
     * buffId
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> buff;

    /**
     * 道具解锁坐骑专用condition
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> typeCondition;

    /**
     * 表示当前时装是否可升级
     */
    private int upGrades;

    /**
     * 拥有即生效，buffId
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> haveBuff;
}
