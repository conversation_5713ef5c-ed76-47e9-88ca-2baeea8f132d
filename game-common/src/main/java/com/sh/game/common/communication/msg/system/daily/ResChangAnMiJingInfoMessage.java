package com.sh.game.common.communication.msg.system.daily;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 长安秘境信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResChangAnMiJingInfoMessage extends AbsProtostuffMessage {
  /**
   * 购买次数
   */
  private int buyCount;

  /**
   * 传送次数
   */
  private int teleport;

  @Override
  public int getId() {
    return 175109;
  }

  public void setBuyCount(int buyCount) {
    this.buyCount = buyCount;
  }

  public int getBuyCount() {
    return this.buyCount;
  }

  public void setTeleport(int teleport) {
    this.teleport = teleport;
  }

  public int getTeleport() {
    return this.teleport;
  }
}
