package com.sh.game.common.communication.msg.system.unionCamp.bean;

import java.lang.String;

public class PersonalScoreBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * 玩家姓名
   */
  private String name = new String();

  /**
   * 玩家分数
   */
  private int score;

  /**
   * 玩家最后更新分数时间
   */
  private int updateTime;

  /**
   * 排名
   */
  private int rank;

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setUpdateTime(int updateTime) {
    this.updateTime = updateTime;
  }

  public int getUpdateTime() {
    return this.updateTime;
  }

  public void setRank(int rank) {
    this.rank = rank;
  }

  public int getRank() {
    return this.rank;
  }
}
