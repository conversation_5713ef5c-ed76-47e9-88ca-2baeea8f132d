package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 地图停留时间更新
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMapUpdateKeepTimeMessage extends AbsProtostuffMessage {
  /**
   * 可停留时间
   */
  private int keepTime;

  @Override
  public int getId() {
    return 67085;
  }

  public void setKeepTime(int keepTime) {
    this.keepTime = keepTime;
  }

  public int getKeepTime() {
    return this.keepTime;
  }
}
