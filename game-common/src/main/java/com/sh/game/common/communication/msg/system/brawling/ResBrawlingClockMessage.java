package com.sh.game.common.communication.msg.system.brawling;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 发送皇宫乱斗时钟信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBrawlingClockMessage extends AbsProtostuffMessage {
  /**
   * boss刷新时间(剩余毫秒数)
   */
  private int bossTime;

  /**
   * buff刷新时间(剩余毫秒数)
   */
  private int buffTime;

  /**
   * 阵营刷新时间(剩余毫秒数)
   */
  private int groupTime;

  @Override
  public int getId() {
    return 182002;
  }

  public void setBossTime(int bossTime) {
    this.bossTime = bossTime;
  }

  public int getBossTime() {
    return this.bossTime;
  }

  public void setBuffTime(int buffTime) {
    this.buffTime = buffTime;
  }

  public int getBuffTime() {
    return this.buffTime;
  }

  public void setGroupTime(int groupTime) {
    this.groupTime = groupTime;
  }

  public int getGroupTime() {
    return this.groupTime;
  }
}
