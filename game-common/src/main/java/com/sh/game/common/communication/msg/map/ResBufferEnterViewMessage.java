package com.sh.game.common.communication.msg.map;

import com.sh.game.common.communication.msg.map.bean.RoundBufferBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * buff进入视野
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBufferEnterViewMessage extends AbsProtostuffMessage {
  /**
   * buffer
   */
  private RoundBufferBean buffer = new RoundBufferBean();

  @Override
  public int getId() {
    return 67005;
  }

  public void setBuffer(RoundBufferBean buffer) {
    this.buffer = buffer;
  }

  public RoundBufferBean getBuffer() {
    return this.buffer;
  }
}
