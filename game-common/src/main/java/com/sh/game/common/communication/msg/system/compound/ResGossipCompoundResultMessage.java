package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 八卦装备合成
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResGossipCompoundResultMessage extends AbsProtostuffMessage {
  /**
   * 合成后新八卦装备id
   */
  private int itemIdList;

  @Override
  public int getId() {
    return 31007;
  }

  public void setItemIdList(int itemIdList) {
    this.itemIdList = itemIdList;
  }

  public int getItemIdList() {
    return this.itemIdList;
  }
}
