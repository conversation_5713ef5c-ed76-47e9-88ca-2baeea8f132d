package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求法宝打造</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqCompoundMagicWeaponMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 31016;
	}
	
	/**
	 * cfg_fabaohecheng表id
	 */
	private int configId;

	public int getConfigId() {
		return configId;
	}

	public void setConfigId(int configId) {
		this.configId = configId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.configId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, configId, false);
		return true;
	}
}
