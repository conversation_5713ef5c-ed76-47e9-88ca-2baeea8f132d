package com.sh.game.common.communication.msg.system.teleport;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求传送（通过deliver配置）
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqTeleportMessage extends AbsProtostuffMessage {
  /**
   * deliver配置id
   */
  private int teleportID;

  @Override
  public int getId() {
    return 174001;
  }

  public void setTeleportID(int teleportID) {
    this.teleportID = teleportID;
  }

  public int getTeleportID() {
    return this.teleportID;
  }
}
