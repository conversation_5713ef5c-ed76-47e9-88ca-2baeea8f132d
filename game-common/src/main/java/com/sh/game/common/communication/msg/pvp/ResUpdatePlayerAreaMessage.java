package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.PlayerAreaBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>更新游戏服高级赛区玩家赛区</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResUpdatePlayerAreaMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125025;
	}
	
	/**
	 * 更新信息
	 */
	private List<PlayerAreaBean> beans = new ArrayList<>();

	public List<PlayerAreaBean> getBeans() {
		return beans;
	}

	public void setBeans(List<PlayerAreaBean> beans) {
		this.beans = beans;
	}

	@Override
	public boolean read(KryoInput buf) {

		int beansLength = readShort(buf);
		for (int beansI = 0; beansI < beansLength; beansI++) {
			if (readByte(buf) == 0) { 
				this.beans.add(null);
			} else {
				PlayerAreaBean playerAreaBean = new PlayerAreaBean();
				playerAreaBean.read(buf);
				this.beans.add(playerAreaBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.beans.size());
		for (int beansI = 0; beansI < this.beans.size(); beansI++) {
			this.writeBean(buf, this.beans.get(beansI));
		}
		return true;
	}
}
