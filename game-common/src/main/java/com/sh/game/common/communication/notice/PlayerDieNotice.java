package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 玩家死亡通知
 */

@Getter
@Setter
@Notice
public class PlayerDieNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 所在地图
     */
    private long mapId;

    /**
     * 坐标
     */
    private int x;

    /**
     * 坐标
     */
    private int y;

    /**
     * 掉落属性加成
     */
    private int dropAdd;

    /**
     * 掉落属性减免
     */
    private int dropResist;
    /**
     * 击杀者
     */
    private long killerId;

    /**
     * 击杀者名字
     */
    private String killerName;

}
