package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-02-17
 **/
@Getter
@Setter
@ConfigData(file = "cfg_xialvbuff", keys = "buffid")
public class XiaLvBuffConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * buff
     */
    private int buffid;

    /**
     * 装备id
     */
    private int equipid;

    /**
     * 替换后的buff
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> rebuffid;

}
