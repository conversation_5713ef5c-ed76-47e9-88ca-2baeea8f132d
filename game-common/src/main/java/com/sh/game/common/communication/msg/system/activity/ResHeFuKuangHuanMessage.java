package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回合服狂欢信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHeFuKuangHuanMessage extends AbsProtostuffMessage {
  /**
   * 活动类型
   */
  private int activityType;

  /**
   * 合服次数
   */
  private int mergeCount;

  /**
   * 活动开启第几天
   */
  private int day;

  /**
   * 每天充值最大金额，key:天数 value:充值金额
   */
  private List<CommonKeyValueBean> rechargeData = new ArrayList<>();

  /**
   * 已领取的奖励cfg_hefukuanghuan表的id
   */
  private List<Integer> reward = new ArrayList<>();

  /**
   * 已领取的奖励cfg_hefulianchong_reward表的id
   */
  private List<Integer> progressReward = new ArrayList<>();

  @Override
  public int getId() {
    return 4226;
  }

  public void setActivityType(int activityType) {
    this.activityType = activityType;
  }

  public int getActivityType() {
    return this.activityType;
  }

  public void setMergeCount(int mergeCount) {
    this.mergeCount = mergeCount;
  }

  public int getMergeCount() {
    return this.mergeCount;
  }

  public void setDay(int day) {
    this.day = day;
  }

  public int getDay() {
    return this.day;
  }

  public void setRechargeData(List<CommonKeyValueBean> rechargeData) {
    this.rechargeData = rechargeData;
  }

  public List<CommonKeyValueBean> getRechargeData() {
    return this.rechargeData;
  }

  public void setReward(List<Integer> reward) {
    this.reward = reward;
  }

  public List<Integer> getReward() {
    return this.reward;
  }

  public void setProgressReward(List<Integer> progressReward) {
    this.progressReward = progressReward;
  }

  public List<Integer> getProgressReward() {
    return this.progressReward;
  }
}
