package com.sh.game.common.communication.msg.system.work;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求完成建筑升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqCompleteWorkLevelUpMessage extends AbsProtostuffMessage {
  private int type;

  @Override
  public int getId() {
    return 408017;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
