package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求玩家货币信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqBackRolePropertyCurrencyQueryMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long rid;

  @Override
  public int getId() {
    return 43111;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }
}
