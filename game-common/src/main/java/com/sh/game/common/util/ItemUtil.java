package com.sh.game.common.util;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.*;
import com.sh.game.common.config.cache.EquipCiZuiConfigCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.*;
import com.sh.game.common.intf.IAvatar;
import jdk.nashorn.internal.runtime.regexp.joni.Config;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ItemUtil {
    public static Item create(Item item, LogAction action, IAvatar avatar, Object... params) {
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        if (config == null) {
            return null;
        }

        item.setId(IDUtil.getId(IDConst.PERSISTENT));
        item.setTime(TimeUtil.getNowOfSeconds());
        int[] expireParam = config.getOverTime();
        if (expireParam != null && expireParam.length >= 2) {
            int expire = 1;
            switch (expireParam[0]) {
                case 1:
                    expire = TimeUtil.getNowOfSeconds() + expireParam[1];
                    break;
                case 2:
                    expire = expireParam[1];
                    break;
                case 3:
                    expire = TimeUtil.dayZeroSecondsFromNow() + (expireParam[1] + 1) * (int) TimeUtil.ONE_DAY_IN_SECONDS;
                    break;
            }
            item.setExpire(expire);
        }
        if (config.getItemtype() == BagConst.ItemType.EQUIP) {
            if (item.getEquipData() == null) {
                item.setEquipData(new EquipData());
            }
            item.getEquipData().setLevel(config.getLevel());
            Map<Integer, Long> attr = ItemUtil.createAttrByLevelAndLocation(config.getId(), config.getLevel(), config.getQuality(), config.getType());
            item.getEquipData().getAttribute().merge(attr);
            Map<Integer, Long> ciZuiAttr = createCiZuiAttr(item.getCfgId());
            item.getEquipData().getCiZuiAttribute().merge(ciZuiAttr);
        }
        return item;
    }

    public static Item create(int id, long count, LogAction action) {
        return create(id, count, action, null);
    }

    public static Item create(int id, long count, LogAction action, IAvatar avatar, Object... params) {
        Item item = new Item();
        item.setCfgId(id);
        item.setCount((int) count);
        return create(item, action, avatar, params);
    }

    /**
     * 根据装备的等级、品质和部位计算该装备的属性
     */
    public static Map<Integer, Long> createAttrByLevelAndLocation(int id, int level, int equipQuality, int equipLocation) {
        Map<Integer, Long> attrMap = new HashMap<>();
        // 先根据id匹配一个（一些特殊道具）,如果没有的话根据level匹配
        AttrCreatConfig attrCreatConfig = ConfigDataManager.getInstance().getById(AttrCreatConfig.class, id);
        if (attrCreatConfig == null) {
            attrCreatConfig = ConfigDataManager.getInstance().getById(AttrCreatConfig.class, level);
        }
        EquipLocationConfig equipLocationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, equipLocation);
        if (equipLocationConfig == null) {
            return attrMap;
        }

        AttrQuliatyConfig attrQuliatyConfig = ConfigDataManager.getInstance().getById(AttrQuliatyConfig.class, equipQuality);
        if (attrQuliatyConfig == null) {
            return attrMap;
        }
        int[] attr1 = attrCreatConfig.getAttr1();
        if (2 > attr1.length || attr1[0] > attr1[1]) {
            return attrMap;
        }
        int baseTotalValue = RandomUtil.random(attr1[0], attr1[1]);

        int[] base_scale = equipLocationConfig.getBase_scale();
        int totalScale = 0;
        for (int i : base_scale) {
            totalScale += i;
        }
        if (totalScale == 0) {
            return attrMap;
        }
        double locationBase = equipLocationConfig.getBase();
        double qualityBase = attrQuliatyConfig.getBase();
        for (int i = 0; i < attrCreatConfig.getBase().size(); i++) {
            if (i >= base_scale.length) {
                return attrMap;
            }
            int attr = attrCreatConfig.getBase().get(i);
            int attrParam = base_scale[i];
            double doubleValue = baseTotalValue * attrParam * locationBase * qualityBase / totalScale;
            int finalValue = (int) doubleValue;
            attrMap.compute(attr, (k, v) -> {
                if (v == null) {
                    v = 0L;
                }
                v += finalValue;
                return v;
            });
        }
        List<Integer> spe1 = attrCreatConfig.getSpe1();
        if (!spe1.isEmpty()) {
            Integer spe1Attr = RandomUtil.randomElement(spe1);
            int[] attr3 = attrCreatConfig.getAttr3();
            if (2 > attr3.length || attr3[0] > attr3[1]) {
                return attrMap;
            }
            int spe1Value = RandomUtil.random(attr3[0], attr3[1]);
            int finalSpe1Value = (int) (spe1Value * equipLocationConfig.getSpe1() * attrQuliatyConfig.getSpe1());
            attrMap.compute(spe1Attr, (k, v) -> {
                if (v == null) {
                    v = 0L;
                }
                v += finalSpe1Value;
                return v;
            });
        }

        List<Integer> spe2 = attrCreatConfig.getSpe2();
        if (!spe2.isEmpty()) {
            Integer spe2Attr = RandomUtil.randomElement(spe2);
            int[] attr4 = attrCreatConfig.getAttr4();
            if (2 > attr4.length || attr4[0] > attr4[1]) {
                return attrMap;
            }
            int spe2Value = RandomUtil.random(attr4[0], attr4[1]);
            double finalSpe2Value = spe2Value * equipLocationConfig.getSpe2() * attrQuliatyConfig.getSpe2();
            attrMap.compute(spe2Attr, (k, v) -> {
                if (v == null) {
                    v = 0L;
                }
                v += (int) finalSpe2Value;
                return v;
            });
        }
        return attrMap;
    }

    public static Map<Integer, Long> createCiZuiAttr(int id) {
        return generateCiZuiAttributes(id);
    }

    /**
     * 根据图片规则生成词缀属性
     * 规则：
     * 1. 独立类型(type=1)必出，优先放入位置1
     * 2. 一般类型(type=0)普通随机
     * 3. 同源类型(type=2)如果随机到，同一装备的同源词条数值必须随机
     * 4. 返回的Map按位置顺序排列
     */
    public static Map<Integer, Long> generateCiZuiAttributes(int id) {
        // 使用LinkedHashMap保持插入顺序，确保按位置排列
        Map<Integer, Long> attrMap = new LinkedHashMap<>();

        EquipSuiJiAttributeConfig suiJiAttributeConfig = ConfigDataManager.getInstance().getById(EquipSuiJiAttributeConfig.class, id);
        if (suiJiAttributeConfig == null) {
            return attrMap;
        }

        int czkid = suiJiAttributeConfig.getCzkid();
        if (czkid == 0) {
            return attrMap;
        }

        EquipCiZuiConfigCache ciZuiConfigCache = ConfigCacheManager.getInstance().getCache(EquipCiZuiConfigCache.class);
        List<EquipCiZuiConfig> ciZuiConfigs = ciZuiConfigCache.getAll(czkid);
        if (CollectionUtils.isEmpty(ciZuiConfigs)) {
            return attrMap;
        }

        int cznum = suiJiAttributeConfig.getCznum();
        if (cznum <= 0) {
            return attrMap;
        }

        // 第一步：处理独立类型(type=1)，必出，放在位置1
        EquipCiZuiConfig independentConfig = ciZuiConfigs.stream()
                .filter(config -> config.getType() == 1)
                .findFirst()
                .orElse(null);

        if (independentConfig != null) {
            long value = (long) generateAttributeValue(
                    independentConfig.getAttributeNum1(),
                    independentConfig.getAttributeNum2(),
                    independentConfig.getUnit()
            );
            attrMap.put(independentConfig.getAttributeId(), value);
        }

        // 第二步：从剩余配置中随机选择其他词条
        List<EquipCiZuiConfig> remainingConfigs = ciZuiConfigs.stream()
                .filter(config -> config.getType() != 1)
                .collect(Collectors.toList());

        if (remainingConfigs.isEmpty()) {
            return attrMap;
        }

        // 计算还需要随机的词条数量
        int remainingSlots = cznum - (independentConfig != null ? 1 : 0);

        // 随机选择剩余词条
        List<EquipCiZuiConfig> selectedConfigs = selectRandomConfigs(remainingConfigs, remainingSlots);

        // 第三步：处理同源类型(type=2)的特殊逻辑
        Map<Integer, Double> tongYuanValues = new HashMap<>();

        // 先为同源类型生成统一的随机值
        for (EquipCiZuiConfig config : selectedConfigs) {
            if (config.getType() == 2) {
                // 同源类型使用相同的随机比例
                if (!tongYuanValues.containsKey(config.getType())) {
                    double randomRatio = Math.random(); // 0.0 到 1.0 的随机比例
                    tongYuanValues.put(config.getType(), randomRatio);
                }
            }
        }

        // 第四步：为选中的词条生成属性值
        for (EquipCiZuiConfig config : selectedConfigs) {
            long value;
            if (config.getType() == 2 && tongYuanValues.containsKey(config.getType())) {
                // 同源类型使用统一的随机比例
                double ratio = tongYuanValues.get(config.getType());
                double range = config.getAttributeNum2() - config.getAttributeNum1();
                value = (long) (config.getAttributeNum1() + range * ratio);
            } else {
                // 一般类型正常随机
                value = (long) generateAttributeValue(
                        config.getAttributeNum1(),
                        config.getAttributeNum2(),
                        config.getUnit()
                );
            }
            attrMap.put(config.getAttributeId(), value);
        }

        return attrMap;
    }

    /**
     * 从配置列表中随机选择指定数量的配置
     */
    private static List<EquipCiZuiConfig> selectRandomConfigs(List<EquipCiZuiConfig> configs, int count) {
        if (configs.isEmpty() || count <= 0) {
            return new ArrayList<>();
        }

        if (count >= configs.size()) {
            return new ArrayList<>(configs);
        }

        List<EquipCiZuiConfig> result = new ArrayList<>();
        List<Integer> probabilities = configs.stream()
                .map(EquipCiZuiConfig::getProbability)
                .collect(Collectors.toList());

        Set<Integer> selectedIndexes = new HashSet<>();

        while (selectedIndexes.size() < count && selectedIndexes.size() < configs.size()) {
            int index = RandomUtil.randomIndexByProb(probabilities);
            if (index >= 0 && index < configs.size()) {
                selectedIndexes.add(index);
            }
        }

        for (Integer index : selectedIndexes) {
            result.add(configs.get(index));
        }

        return result;
    }

    public static boolean isItemChanged(IAvatar avatar, Item item1, Item item2) {
        if (item1 == null) {
            return item2 != null ? true : false;
        } else if (item2 == null) {
            return true;
        }

        if (item1.getCfgId() != item2.getCfgId()) {
            return true;
        }

        EquipData equipData1 = item1.eData();
        EquipData equipData2 = item2.eData();
        if (equipData1 == null) {
            return equipData2 != null ? true : false;
        } else if (equipData2 == null) {
            return true;
        }
        // 由于装备改动，现在只检查猫狗新属性是否变化
        // if (equipData1.getDurable() != equipData2.getDurable() && equipData1.getDurable() * equipData2.getDurable() == 0) {
        //     return true;
        // }
        // if (equipData1.getStarLevel() != equipData2.getStarLevel()) {
        //     return true;
        // }
        // if (equipData1.getStarLocked() != equipData2.getStarLocked()) {
        //     return true;
        // }
        // if (isAttributeChanged(avatar, equipData1.getSuperAttribute(), equipData2.getSuperAttribute())) {
        //     return true;
        // }
        // if (isAttributeChanged(avatar, equipData1.getIdentifyAttribute(), equipData2.getIdentifyAttribute())) {
        //     return true;
        // }
        // if (isAttributeChanged(avatar, equipData1.getZhufuAttribute(), equipData2.getZhufuAttribute())) {
        //     return true;
        // }
        // if (isAttributeChanged(avatar, equipData1.getXilianAttr(), equipData2.getXilianAttr())) {
        //     return true;
        // }
        if (isAttributeChanged(avatar, equipData1.getChaiJiaAttribute(), equipData2.getChaiJiaAttribute())) {
            return true;
        }
        // if (equipData1.getAptitudeValue() != equipData2.getAptitudeValue()) {
        //     return true;
        // }
        if (equipData1.getLevel() != equipData2.getLevel()) {
            return true;
        }
        // if (equipData1.getTjId() != equipData2.getTjId()) {
        //     return true;
        // }
        // if (equipData1.getDemonEquipId() != equipData2.getDemonEquipId()) {
        //     return true;
        // }
        // if (equipData1.getGemId() != equipData2.getGemId()) {
        //     return true;
        // }
        // if (equipData1.getToubao() != equipData2.getToubao()) {
        //     return true;
        // }
        // if (equipData1.isFengyin() != equipData2.isFengyin()) {
        //     return true;
        // }
        // if (equipData1.getTiTanGemId() != equipData2.getTiTanGemId()) {
        //     return true;
        // }
        // if (equipData1.getShouJueLevel() != equipData2.getShouJueLevel()) {
        //     return true;
        // }

        // if (!equipData1.getExtendSuitIdList().containsAll(equipData2.getExtendSuitIdList())
        //         || !equipData2.getExtendSuitIdList().containsAll(equipData1.getExtendSuitIdList())) {
        //     return true;
        // }
        // if (equipData1.getTmdzCfgId() != equipData2.getTmdzCfgId()
        //         || equipData1.getTmdzAddRate() != equipData2.getTmdzAddRate()
        //         || isAttributeChanged(avatar,equipData1.getDiZaoAttribute(),equipData2.getDiZaoAttribute())
        //         || equipData1.getXingMingId() != equipData2.getXingMingId()) {
        //     return true;
        // }
        // if (!equipData1.getMagicWeaponMap().equals(equipData2.getMagicWeaponMap())) {
        //     return true;
        // }
        // if (!equipData1.getLonghunGem().equals(equipData2.getLonghunGem())) {
        //     return true;
        // }
        // if (equipData1.getRefinementLevel() != equipData2.getRefinementLevel()) {
        //     return true;
        // }
        // if (equipData1.getRefinementRate() != equipData2.getRefinementRate()) {
        //     return true;
        // }
        // if (equipData1.getDisarmTime() != equipData2.getDisarmTime()) {
        //     return true;
        // }
        // if (equipData1.getPetEquipRate() != equipData2.getPetEquipRate()) {
        //     return true;
        // }
        // if (equipData1.getPetBuff() != equipData2.getPetBuff()) {
        //     return true;
        // }
        // if (equipData1.getChaoticGemId() != equipData2.getChaoticGemId()) {
        //     return true;
        // }
        // if (!equipData1.getXiahunGem().equals(equipData2.getXiahunGem())) {
        //     return true;
        // }
        // if (equipData1.getChaoticPowerLevel() != equipData2.getChaoticPowerLevel()) {
        //     return true;
        // }
        // if (equipData1.getShenBingZhuHunLevel() != equipData2.getShenBingZhuHunLevel()) {
        //     return true;
        // }
        // if (equipData1.getBirthChartLevel() != equipData2.getBirthChartLevel()) {
        //     return true;
        // }
        return false;
    }

    public static boolean isAttributeChanged(IAvatar avatar, EquipAttribute attribute1, EquipAttribute attribute2) {
        if (attribute1 == null) {
            return attribute2 != null ? true : false;
        } else if (attribute2 == null) {
            return true;
        }
        int[] careers = new int[]{avatar.getCareer(), 0};
        for (int career : careers) {
            Map<Integer, Long> attr1 = attribute1.getAttributes().get(career);
            Map<Integer, Long> attr2 = attribute2.getAttributes().get(career);
            if (attr1 == null && attr2 == null) {
                continue;
            }
            if ((attr1 == null && attr2 != null) || (attr1 != null && attr2 == null)) {
                return true;
            }
            if (attr1.size() != attr2.size()) {
                return true;
            }
            for (Map.Entry<Integer, Long> entry : attr1.entrySet()) {
                if (attr2.getOrDefault(entry.getKey(), 0L) != entry.getValue()) {
                    return true;
                }
            }
        }

        return false;
    }

    public static boolean isAttributeChanged(IAvatar avatar, IdentifyAttribute attribute1, IdentifyAttribute attribute2) {
        if (attribute1 == null) {
            return attribute2 != null ? true : false;
        } else if (attribute2 == null) {
            return true;
        }
        if (attribute1.getAttributes().size() != attribute2.getAttributes().size()) {
            return true;
        }

        for (Map.Entry<Integer, IdentifyAttributeValue> entry : attribute1.getAttributes().entrySet()) {
            if (!attribute2.getAttributes().containsKey(entry.getKey())) {
                return true;
            }
            IdentifyAttributeValue tuple1 = entry.getValue();
            IdentifyAttributeValue tuple2 = attribute2.getAttributes().get(entry.getKey());
            if (tuple1.getCareer() != tuple2.getCareer() || tuple1.getType() != tuple2.getType() || tuple1.getValue() != tuple2.getValue()) {
                return true;
            }
        }

        if (attribute1.getBuffs() == null) {
            return attribute2.getBuffs() != null;
        } else if (attribute2.getBuffs() == null) {
            return true;
        }

        if (attribute1.getBuffs().size() != attribute2.getBuffs().size()) {
            return true;
        }

        for (Map.Entry<Integer, IdentifyAttributeValue> entry : attribute1.getBuffs().entrySet()) {
            if (!attribute2.getBuffs().containsKey(entry.getKey())) {
                return true;
            }
            IdentifyAttributeValue tuple1 = entry.getValue();
            IdentifyAttributeValue tuple2 = attribute2.getBuffs().get(entry.getKey());
            if (tuple1.getCareer() != tuple2.getCareer() || tuple1.getType() != tuple2.getType() || tuple1.getValue() != tuple2.getValue()) {
                return true;
            }
        }

        return false;
    }

    public static Item copy(Item item, long count) {
        Item copy = new Item();
        copy.setId(item.getId());
        copy.setCfgId(item.getCfgId());
        copy.setCount(count);
        copy.setExpire(item.getExpire());
        copy.setUTimes(item.getUTimes());
        copy.setWhere(item.getWhere());
        copy.setIndex(item.getIndex());
        // 复制拾取时间
        copy.setTime(item.getTime());

        EquipData equipData = item.getEquipData();
        if (equipData != null) {
            EquipData data = new EquipData();
            copy.setEquipData(data);
            data.setLevel(equipData.getLevel());
//            data.setDurable(equipData.getDurable());
//            data.setStarLevel(equipData.getStarLevel());
//            data.setStarLocked(equipData.getStarLocked());
//            data.setStarExp(equipData.getStarExp());

            // 复制来源信息
            data.setOriginInfoType(equipData.getOriginInfoType());
            data.setOriginInfo(equipData.getOriginInfo());

//            // super attr
//            EquipAttribute superAttr = equipData.getSuperAttribute();
//            if (superAttr != null) {
//                EquipAttribute attribute = new EquipAttribute();
//                attribute.merge(superAttr);
//                data.setSuperAttribute(attribute);
//            }
//            // identify attr
//            IdentifyAttribute identifyAttr = equipData.getIdentifyAttribute();
//            if (identifyAttr != null) {
//                IdentifyAttribute attribute = new IdentifyAttribute();
//                attribute.clone(identifyAttr);
//                data.setIdentifyAttribute(attribute);
//            }
//            EquipAttribute zhufuAttribute = equipData.getZhufuAttribute();
//            if (zhufuAttribute != null) {
//                EquipAttribute attribute = new EquipAttribute();
//                attribute.merge(zhufuAttribute);
//                data.setZhufuAttribute(attribute);
//            }
            if(equipData.getAttribute() != null){
                data.setAttribute(equipData.getAttribute());
            }
//            data.setXilianAttr(equipData.getXilianAttr());
//
//            data.setZhufu(equipData.getZhufu());
//            data.setTjId(equipData.getTjId());
//            data.setMagicBloodStoneValue(equipData.getMagicBloodStoneValue());
//            data.setDemonEquipId(equipData.getDemonEquipId());
//            data.setGemId(equipData.getGemId());
//            data.setToubao(equipData.getToubao());

//            // 复制资质信息
//            data.setAptitudeCfgId(equipData.getAptitudeCfgId());
//            data.setAptitudeValue(equipData.getAptitudeValue());
//
//            data.getExtendSuitIdList().clear();
//            data.getExtendSuitIdList().addAll(equipData.getExtendSuitIdList());

            // 天命缔造属性
//            data.setTmdzAddRate(equipData.getTmdzAddRate());
//            data.setXingMingId(equipData.getXingMingId());
//            data.setDiZaoAttribute(equipData.getDiZaoAttribute());
//            data.setTmdzCfgId(equipData.getTmdzCfgId());
//
//            data.setFengyin(equipData.isFengyin());
//
//            //法宝属性
//            data.setMagicWeaponAttributeRate(equipData.getMagicWeaponAttributeRate());
//            data.setMagicWeaponBuffId(equipData.getMagicWeaponBuffId());
//            data.setMagicWeaponMap(equipData.getMagicWeaponMap());
//            data.setShouJueLevel(equipData.getShouJueLevel());
//            data.setTiTanGemId(equipData.getTiTanGemId());

            //龙魂宝石
//            data.setLonghunGem(equipData.getLonghunGem());
//
//            //淬炼
//            data.setRefinementLevel(equipData.getRefinementLevel());
//            data.setRefinementRate(equipData.getRefinementRate());
//            //缴械
//            data.setDisarmTime(equipData.getDisarmTime());
//            data.setPetBuff(equipData.getPetBuff());
//            data.setPetEquipRate(equipData.getPetEquipRate());
//
//            //混沌宝石
//            data.setChaoticGemId(equipData.getChaoticGemId());
//
//            //侠魂宝石
//            data.setXiahunGem(equipData.getXiahunGem());
//
//            //混沌灵源-赋灵
//            data.setChaoticPowerLevel(equipData.getChaoticPowerLevel());
//            //神兵铸魂
//            data.setShenBingZhuHunLevel(equipData.getShenBingZhuHunLevel());
//
//            //命格等级
//            data.setBirthChartLevel(equipData.getBirthChartLevel());
        }

        if (item.getParams() != null) {
            copy.setParams(new ArrayList<>());
            copy.getParams().addAll(item.getParams());
        }

        return copy;
    }

    public static Item copy(Item item) {
        return copy(item, item.findCountLong());
    }

    public static Item fission(Item item, int count) {
        if (count > item.findCount()) {
            count = item.findCount();
        }
        Item copy = copy(item, count);
        if (count < item.findCount()) {
            copy.setId(IDUtil.getId(IDConst.PERSISTENT));
            copy.setWhere(0);
            copy.setIndex(0);
        }

        return copy;
    }

    public static CommonItemBean packCommonItemBean(Item item) {
        //todo 相关方法修改
        if (item == null) {
            return null;
        }

        CommonItemBean builder = new CommonItemBean();
        builder.setUid(item.getId());
        builder.setItemId(item.getCfgId());
        builder.setCount(item.findCount());
        builder.setExpire(item.getExpire());
        builder.setUTimes(item.getUTimes());
        builder.setTime(item.getTime());
        if (item.getParams() != null) {
            for (int param : item.getParams()) {
                builder.getParams().add(param);
            }
        }

        // 装备添加来源信息
        EquipData equipData = item.getEquipData();
        if (equipData != null) {
            // 复制来源信息
            ItemFromBean itemFromBean = new ItemFromBean();
            itemFromBean.setAction(equipData.getOriginInfoType());
            itemFromBean.getParams().addAll(equipData.getOriginInfo());
            builder.setFrom(itemFromBean);
        }

        builder.setEquipData(packEquipDataBean(equipData));
        return builder;
    }

    /**
     * 打包协议装备信息
     *
     * @param equipData
     * @return
     */
    public static EquipDataBean packEquipDataBean(EquipData equipData) {
        EquipDataBean bean = new EquipDataBean();
        if (equipData == null) {
            return bean;
        }
        // bean.setDurable(equipData.getDurable());
        // bean.setStarLevel(equipData.getStarLevel());
        // bean.setStarLock(equipData.getStarLocked());
        // bean.setStarExp(equipData.getStarExp());
        // if (equipData.getSuperAttribute() != null) {
        //     bean.addAllSuperAttrs(packRandomAttributeBean(equipData.getSuperAttribute()));
        // }
        // if (equipData.getIdentifyAttribute() != null) {
        //     bean.addAllIdentifyAttrs(packIdentifyAttributeBean(equipData.getIdentifyAttribute().getAttributes()));
        //     bean.addAllIdentifyBuffs(packIdentifyAttributeBean(equipData.getIdentifyAttribute().getBuffs()));
        // }
        // if (equipData.getZhufuAttribute() != null) {
        //     bean.addAllZhufuAttrs(packRandomAttributeBean(equipData.getZhufuAttribute()));
        // }
        // bean.setFengyin(equipData.isFengyin() ? 1 : 0);
        // bean.setZhufu(equipData.getZhufu());
        // bean.setTjId(equipData.getTjId());
        // bean.setMagicBloodStoneValue(equipData.getMagicBloodStoneValue());
        // if (equipData.getXilianAttr() != null) {
        //     bean.addAllXilianAttrs(packRandomAttributeBean(equipData.getXilianAttr()));
        // }
        // bean.setDemonEquipId(equipData.getDemonEquipId());
        // bean.setGemId(equipData.getGemId());
        // bean.setToubao(equipData.getToubao());
        // bean.setAptitudeCfgId(equipData.getAptitudeCfgId());
        // bean.setAptitudeValue(equipData.getAptitudeValue());

        // EquipAttribute aptitudeAttr = ItemUtil.findAptitudeAttr(equipData);
        // if (aptitudeAttr != null) {
        //     bean.addAllAptitudeAttrs(packRandomAttributeBean(aptitudeAttr));
        // }
        bean.getSuitIdList().addAll(equipData.getExtendSuitIdList());
        // bean.setXingMingId(equipData.getXingMingId());
        // if (equipData.getDiZaoAttribute() != null) {
        //     bean.addAllDiZaoAttribute(packRandomAttributeBean(equipData.getDiZaoAttribute()));
        // }
        // bean.setRate(equipData.getTmdzAddRate());
        // bean.setMagicWeaponRate(equipData.getMagicWeaponAttributeRate());
        // bean.setMagicWeaponBuffId(equipData.getMagicWeaponBuffId());
        // List<AbcProtos.MagicWeaponBlendBean> magicWeaponBlendBeanList = new ArrayList<>();
        // equipData.getMagicWeaponMap().forEach((slotConfigId, magicWeapon) -> {
        //     AbcProtos.MagicWeaponBlendBean.Builder magicWeaponBlendBean = AbcProtos.MagicWeaponBlendBean.newBuilder();
        //     magicWeaponBlendBean.setSlotConfigId(slotConfigId);
        //     magicWeaponBlendBean.setItemConfigId(magicWeapon.getItemConfigId());
        //     magicWeaponBlendBean.setMagicWeaponRate(magicWeapon.getMagicWeaponAttributeRate());
        //     magicWeaponBlendBean.setMagicWeaponBuffId(magicWeapon.getMagicWeaponBuffId());
        //     magicWeaponBlendBeanList.add(magicWeaponBlendBean.build());
        // });
        // bean.addAllMagicWeaponBlendList(magicWeaponBlendBeanList);
        // bean.setShouJueLevel(equipData.getShouJueLevel());
        // bean.setTaiTanGemId(equipData.getTiTanGemId());
        // bean.addAllLonghunGemIdList(equipData.getLonghunGem() == null ? new ArrayList<>() : new ArrayList<>(equipData.getLonghunGem().values()));
        // bean.setRefinementLevel(equipData.getRefinementLevel());
        // bean.setRefinementRate(equipData.getRefinementRate());
        // bean.setDisarmTime(equipData.getDisarmTime());
        // bean.setPetEquipRate(equipData.getPetEquipRate());
        // bean.setPetBuff(equipData.getPetBuff());
        // bean.setChaoticGemId(equipData.getChaoticGemId());
        // bean.addAllXiahunGemIdList(equipData.getXiahunGem() == null ? new ArrayList<>() : new ArrayList<>(equipData.getXiahunGem().values()));
        // bean.setChaoticPowerLevel(equipData.getChaoticPowerLevel());
        // bean.setShenBingZhuHunLevel(equipData.getShenBingZhuHunLevel());
        // bean.setBirthChartLevel(equipData.getBirthChartLevel());
        bean.setLevel(equipData.getLevel());
        if(equipData.getAttribute() != null){
            bean.getXilianAttrs().addAll(packRandomAttributeBean(equipData.getAttribute()));
        }
        return bean;
    }

    public static List<RandAttributeBean> packRandomAttributeBean(EquipAttribute attribute) {
        List<RandAttributeBean> attrs = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, Long>> entry : attribute.getAttributes().entrySet()) {
            int career = entry.getKey();
            for (Map.Entry<Integer, Long> attrEntry : entry.getValue().entrySet()) {
                RandAttributeBean bean = new RandAttributeBean();
                bean.setCareer(career);
                bean.setAttributeType(attrEntry.getKey());
                bean.setAttributeValue(attrEntry.getValue());
                attrs.add(bean);
            }
        }

        return attrs;
    }

    public static List<CommonKeyValueLongBean> packAttributeBean(Attribute attribute) {
        List<CommonKeyValueLongBean> attrs = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : attribute.getAttributeMap().entrySet()) {
            int attr = entry.getKey();
            Long value = entry.getValue();
            CommonKeyValueLongBean bean = new CommonKeyValueLongBean();
            bean.setKey(attr);
            bean.setValue(value);
            attrs.add(bean);
        }

        return attrs;
    }

    public static List<IdentifyAttributeBean> packIdentifyAttributeBean(Map<Integer, IdentifyAttributeValue> map) {
        List<IdentifyAttributeBean> attrs = new ArrayList<>();
        for (Map.Entry<Integer, IdentifyAttributeValue> entry : map.entrySet()) {
            int pos = entry.getKey();
            IdentifyAttributeValue tuple = entry.getValue();
            IdentifyAttributeBean bean1 = new IdentifyAttributeBean();
            bean1.setPos(pos);
            RandAttributeBean bean = new RandAttributeBean();
            bean.setCareer(tuple.getCareer());
            bean.setAttributeType(tuple.getType());
            bean.setAttributeValue(tuple.getValue());
            bean1.setAttrBean(bean);
            attrs.add(bean1);
        }

        return attrs;
    }

    /**
     * 属性Map转属性Bean
     */
    public static List<RandAttributeBean> attrTransformRandAttributeBean(Map<Integer, Long> attributeMap) {
        List<RandAttributeBean> attrs = new ArrayList<>();
        for (Map.Entry<Integer, Long> attrEntry : attributeMap.entrySet()) {
            RandAttributeBean bean = new RandAttributeBean();
            bean.setAttributeType(attrEntry.getKey());
            bean.setAttributeValue(attrEntry.getValue());
            attrs.add(bean);
        }
        return attrs;
    }


    public static EquipAttribute findAptitudeAttr(EquipData equipData) {
        return findAptitudeAttr(equipData.getAptitudeCfgId(), equipData.getAptitudeValue());
    }

    /**
     * 统计装备资质属性
     * @param cfgId 资质表id
     * @param aptitudeValue 资质值
     * @return 资质属性
     */
    public static EquipAttribute findAptitudeAttr(int cfgId, int aptitudeValue) {
        ZiZhiConfig ziZhiConfig = ConfigDataManager.getInstance().getById(ZiZhiConfig.class, cfgId);
        if (ziZhiConfig == null) {
            return null;
        }

        // 装备的资质属性
        EquipAttribute aptitudeAttribute = new EquipAttribute();
        // 增加随机属性
        for (int i = 0; i < ziZhiConfig.getRangeAttr().length; i++) {
            Map<Integer, Long> attrMap = ziZhiConfig.getRangeAttr()[i];
            Map<Integer, Long> careerAttr = aptitudeAttribute.getAttributes().computeIfAbsent(i, k -> new HashMap<>());
            attrMap.forEach((k, v) -> {
                long attrValue = (long) Math.floor(aptitudeValue / (ziZhiConfig.getMax() * 1.0) * v);
                careerAttr.put(k, attrValue);
            });
        }

        // 增加基本属性
        for (int i = 0; i < ziZhiConfig.getBaseAttr().length; i++) {
            Map<Integer, Long> attrMap = ziZhiConfig.getBaseAttr()[i];
            Map<Integer, Long> careerAttr = aptitudeAttribute.getAttributes().computeIfAbsent(i, k -> new HashMap<>());
            attrMap.forEach((k, v) -> {
                careerAttr.computeIfAbsent(k, key -> v);
                careerAttr.computeIfPresent(k, (key, value) -> value + v);
            });
        }
        return aptitudeAttribute;
    }

    public static CommonItemBean toCommonItemBean(Item item) {
        if (item == null) {
            return null;
        }

        CommonItemBean builder = new CommonItemBean();
        builder.setUid(item.getId());
        builder.setItemId(item.getCfgId());
        builder.setCount(item.findCount());
        builder.setExpire(item.getExpire());
        builder.setUTimes(item.getUTimes());
        builder.setTime(item.getTime());
        if (item.getParams() != null) {
            for (int param : item.getParams()) {
                builder.getParams().add(param);
            }
        }

        // 装备添加来源信息
        EquipData equipData = item.getEquipData();
        if (equipData != null) {
            // 复制来源信息
            ItemFromBean itemFromBean = new ItemFromBean();
            itemFromBean.setAction(equipData.getOriginInfoType());
            itemFromBean.getParams().addAll(equipData.getOriginInfo());
            builder.setFrom(itemFromBean);
        }

        builder.setEquipData(toEquipDataBean(equipData));
        return builder;
    }
    /**
     * 打包协议装备信息
     *
     * @param equipData
     * @return
     */
    public static EquipDataBean toEquipDataBean(EquipData equipData) {
        EquipDataBean bean = new EquipDataBean();
        if (equipData == null) {
            return bean;
        }
        // bean.setDurable(equipData.getDurable());
        // bean.setStarLevel(equipData.getStarLevel());
        // bean.setStarLock(equipData.getStarLocked());
        // bean.setStarExp(equipData.getStarExp());
        // if (equipData.getSuperAttribute() != null) {
        //     bean.addAllSuperAttrs(packRandomAttributeBean(equipData.getSuperAttribute()));
        // }
        // if (equipData.getIdentifyAttribute() != null) {
        //     bean.addAllIdentifyAttrs(packIdentifyAttributeBean(equipData.getIdentifyAttribute().getAttributes()));
        //     bean.addAllIdentifyBuffs(packIdentifyAttributeBean(equipData.getIdentifyAttribute().getBuffs()));
        // }
        // if (equipData.getZhufuAttribute() != null) {
        //     bean.addAllZhufuAttrs(packRandomAttributeBean(equipData.getZhufuAttribute()));
        // }
        // bean.setFengyin(equipData.isFengyin() ? 1 : 0);
        // bean.setZhufu(equipData.getZhufu());
        // bean.setTjId(equipData.getTjId());
        // bean.setMagicBloodStoneValue(equipData.getMagicBloodStoneValue());
        // if (equipData.getXilianAttr() != null) {
        //     bean.addAllXilianAttrs(packRandomAttributeBean(equipData.getXilianAttr()));
        // }
        // bean.setDemonEquipId(equipData.getDemonEquipId());
        // bean.setGemId(equipData.getGemId());
        // bean.setToubao(equipData.getToubao());
        // bean.setAptitudeCfgId(equipData.getAptitudeCfgId());
        // bean.setAptitudeValue(equipData.getAptitudeValue());

        // EquipAttribute aptitudeAttr = ItemUtil.findAptitudeAttr(equipData);
        // if (aptitudeAttr != null) {
        //     bean.addAllAptitudeAttrs(packRandomAttributeBean(aptitudeAttr));
        // }
        bean.getSuitIdList().addAll(equipData.getExtendSuitIdList());
        // bean.setXingMingId(equipData.getXingMingId());
        // if (equipData.getDiZaoAttribute() != null) {
        //     bean.addAllDiZaoAttribute(packRandomAttributeBean(equipData.getDiZaoAttribute()));
        // }
        // bean.setRate(equipData.getTmdzAddRate());
        // bean.setMagicWeaponRate(equipData.getMagicWeaponAttributeRate());
        // bean.setMagicWeaponBuffId(equipData.getMagicWeaponBuffId());
        // List<AbcProtos.MagicWeaponBlendBean> magicWeaponBlendBeanList = new ArrayList<>();
        // equipData.getMagicWeaponMap().forEach((slotConfigId, magicWeapon) -> {
        //     AbcProtos.MagicWeaponBlendBean.Builder magicWeaponBlendBean = AbcProtos.MagicWeaponBlendBean.newBuilder();
        //     magicWeaponBlendBean.setSlotConfigId(slotConfigId);
        //     magicWeaponBlendBean.setItemConfigId(magicWeapon.getItemConfigId());
        //     magicWeaponBlendBean.setMagicWeaponRate(magicWeapon.getMagicWeaponAttributeRate());
        //     magicWeaponBlendBean.setMagicWeaponBuffId(magicWeapon.getMagicWeaponBuffId());
        //     magicWeaponBlendBeanList.add(magicWeaponBlendBean.build());
        // });
        // bean.addAllMagicWeaponBlendList(magicWeaponBlendBeanList);
        // bean.setShouJueLevel(equipData.getShouJueLevel());
        // bean.setTaiTanGemId(equipData.getTiTanGemId());
        // bean.addAllLonghunGemIdList(equipData.getLonghunGem() == null ? new ArrayList<>() : new ArrayList<>(equipData.getLonghunGem().values()));
        // bean.setRefinementLevel(equipData.getRefinementLevel());
        // bean.setRefinementRate(equipData.getRefinementRate());
        // bean.setDisarmTime(equipData.getDisarmTime());
        // bean.setPetEquipRate(equipData.getPetEquipRate());
        // bean.setPetBuff(equipData.getPetBuff());
        // bean.setChaoticGemId(equipData.getChaoticGemId());
        // bean.addAllXiahunGemIdList(equipData.getXiahunGem() == null ? new ArrayList<>() : new ArrayList<>(equipData.getXiahunGem().values()));
        // bean.setChaoticPowerLevel(equipData.getChaoticPowerLevel());
        // bean.setShenBingZhuHunLevel(equipData.getShenBingZhuHunLevel());
        // bean.setBirthChartLevel(equipData.getBirthChartLevel());


        bean.setLevel(equipData.getLevel());
        if(equipData.getAttribute() != null){
            bean.getXilianAttrs().addAll(packRandomAttributeBean(equipData.getAttribute()));
        }
        return bean;
    }

    public static List<RandAttributeBean> toRandomAttributeBean(EquipAttribute attribute) {
        List<RandAttributeBean> attrs = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, Long>> entry : attribute.getAttributes().entrySet()) {
            int career = entry.getKey();
            for (Map.Entry<Integer, Long> attrEntry : entry.getValue().entrySet()) {
                RandAttributeBean bean = new RandAttributeBean();
                bean.setCareer(career);
                bean.setAttributeType(attrEntry.getKey());
                bean.setAttributeValue(attrEntry.getValue());
                attrs.add(bean);
            }
        }

        return attrs;
    }

    /**
     * 根据最小值、最大值和单位进行随机
     * 单位值决定最小波动精度：
     * unit=100 表示每次以1%为最小单位波动
     * unit=50 表示每次以0.5%为最小单位波动
     *
     * @param min 最小值
     * @param max 最大值
     * @param unit 单位值，决定最小波动精度
     * @return 随机结果
     */
    public static double randomByUnit(long min, long max, int unit) {
        if (min >= max) {
            return min;
        }

        // 计算最小波动单位
        double step = (double) (max - min) / unit;

        // 随机波动单位数
        int randomUnits = RandomUtil.random(0, unit);

        // 计算最终随机值
        return min + (step * randomUnits);
    }

    public static double generateAttributeValue(double min, double max, int unit) {
        if (min >= max) {
            return min;
        }
        // 计算最小单位值
        double unitValue = (max - min) * (unit / 10000.0);

        // 计算可波动的单位数
        int unitsInRange = (int) ((max - min) / unitValue);

        // 生成一个随机的单位数
        int randomUnits = (int) (Math.random() * (unitsInRange + 1));

        // 计算最终的属性值

        return min + randomUnits * unitValue;
    }

    public static void main(String[] args) {
        System.out.println(generateAttributeValue(1000, 1500, 100));
    }
}
