package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求设置二级密码
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSetPasswordMessage extends AbsProtostuffMessage {
  /**
   * 密码
   */
  private String password = new String();

  /**
   * 确认密码
   */
  private String surePassword = new String();

  @Override
  public int getId() {
    return 280003;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getPassword() {
    return this.password;
  }

  public void setSurePassword(String surePassword) {
    this.surePassword = surePassword;
  }

  public String getSurePassword() {
    return this.surePassword;
  }
}
