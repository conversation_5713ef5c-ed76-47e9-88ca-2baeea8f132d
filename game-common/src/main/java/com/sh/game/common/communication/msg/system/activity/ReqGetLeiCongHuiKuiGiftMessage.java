package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 领取累充奖励, 领取成功会自动返回查询数据
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqGetLeiCongHuiKuiGiftMessage extends AbsProtostuffMessage {
  /**
   * 传入需要领取的奖励id
   */
  private int cid;

  @Override
  public int getId() {
    return 4283;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
