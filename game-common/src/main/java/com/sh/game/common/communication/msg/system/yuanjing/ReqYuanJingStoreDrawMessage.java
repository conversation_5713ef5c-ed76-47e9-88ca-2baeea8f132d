package com.sh.game.common.communication.msg.system.yuanjing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求源晶小铺抽卡
 * 该文件由工具根据 yuanjing.xml 文件自动生成，不可修改
 */
@RPC("toServer")
public class ReqYuanJingStoreDrawMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cid;

  /**
   * 抽卡次数
   */
  private int count;

  @Override
  public int getId() {
    return 416003;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
