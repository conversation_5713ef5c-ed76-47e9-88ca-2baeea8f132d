package com.sh.game.common.communication.msg.system.compound;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Long;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 背包合成(装备消耗传唯一id,,,帝装分解专用
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqDiBingDecomposeMessage extends AbsProtostuffMessage {
  /**
   * cfg_compound配置id
   */
  private int configId;

  /**
   * 一次合成的数量
   */
  private int count;

  /**
   * 职业
   */
  private int career;

  /**
   * 0主角1英雄
   */
  private boolean hero;

  /**
   * 可选原料itemCfgid
   */
  private int materialCid;

  /**
   * 是否是快捷合成,1 是
   */
  private int quick;

  /**
   * 是否必定成功
   */
  private boolean isPerfect;

  /**
   * 消耗装备唯一id列表
   */
  private List<Long> uids = new ArrayList<>();

  @Override
  public int getId() {
    return 31022;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setHero(boolean hero) {
    this.hero = hero;
  }

  public boolean getHero() {
    return this.hero;
  }

  public void setMaterialCid(int materialCid) {
    this.materialCid = materialCid;
  }

  public int getMaterialCid() {
    return this.materialCid;
  }

  public void setQuick(int quick) {
    this.quick = quick;
  }

  public int getQuick() {
    return this.quick;
  }

  public void setIsPerfect(boolean isPerfect) {
    this.isPerfect = isPerfect;
  }

  public boolean getIsPerfect() {
    return this.isPerfect;
  }

  public void setUids(List<Long> uids) {
    this.uids = uids;
  }

  public List<Long> getUids() {
    return this.uids;
  }
}
