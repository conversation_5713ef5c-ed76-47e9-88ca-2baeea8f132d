package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求强制退出联盟
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRPCForceQuitAllianceMessage extends AbsProtostuffMessage {
  /**
   * 帮主id
   */
  private long rid;

  /**
   * 联盟id
   */
  private long allianceId;

  /**
   * 退出的帮会id
   */
  private long quitUnionId;

  @Override
  public int getId() {
    return 82056;
  }

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setAllianceId(long allianceId) {
    this.allianceId = allianceId;
  }

  public long getAllianceId() {
    return this.allianceId;
  }

  public void setQuitUnionId(long quitUnionId) {
    this.quitUnionId = quitUnionId;
  }

  public long getQuitUnionId() {
    return this.quitUnionId;
  }
}
