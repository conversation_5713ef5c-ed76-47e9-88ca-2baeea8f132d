package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/9 15:48
 */
@Getter
@Setter
@ConfigData(file = "cfg_xunbaobox")
public class XunBaoBoxConfig extends AbstractConfigData {

    private int id;

    /**
     * 道具组
     */
    private int group;

    private int box_id;

    /**
     * box权重
     */
    private int box_weight;

    private int type;


    private int notice;

    private int base_award;

    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> itemAnnounce;

}
