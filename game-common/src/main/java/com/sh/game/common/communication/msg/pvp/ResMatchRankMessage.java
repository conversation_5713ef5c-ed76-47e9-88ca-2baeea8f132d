package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.communication.msg.pvp.bean.MatchRankBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回修罗排行
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMatchRankMessage extends AbsProtostuffMessage {
  /**
   * 匹配类型
   */
  private int pvpType;

  /**
   * 排行详情
   */
  private List<MatchRankBean> rankList = new ArrayList<>();

  @Override
  public int getId() {
    return 125044;
  }

  public void setPvpType(int pvpType) {
    this.pvpType = pvpType;
  }

  public int getPvpType() {
    return this.pvpType;
  }

  public void setRankList(List<MatchRankBean> rankList) {
    this.rankList = rankList;
  }

  public List<MatchRankBean> getRankList() {
    return this.rankList;
  }
}
