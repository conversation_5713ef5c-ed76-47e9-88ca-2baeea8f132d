package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 查询大侠状态信息返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDaXiaInfoMessage extends AbsProtostuffMessage {
  /**
   * 当前等级
   */
  private int level;

  /**
   * 活动ID
   */
  private int activityID;

  /**
   * 当前累计进度值
   */
  private int count;

  /**
   * 奖励已领取列表
   */
  private List<Integer> receiveRec = new ArrayList<>();

  @Override
  public int getId() {
    return 4291;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setActivityID(int activityID) {
    this.activityID = activityID;
  }

  public int getActivityID() {
    return this.activityID;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setReceiveRec(List<Integer> receiveRec) {
    this.receiveRec = receiveRec;
  }

  public List<Integer> getReceiveRec() {
    return this.receiveRec;
  }
}
