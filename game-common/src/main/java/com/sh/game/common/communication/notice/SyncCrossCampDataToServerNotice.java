package com.sh.game.common.communication.notice;

import com.sh.game.common.unionCamp.CrossCampData;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-05-06
 **/
@Notice
@Getter
@Setter
public class SyncCrossCampDataToServerNotice extends ProcessNotice {
    private CrossCampData campData;
    private List<Long> unionIdList = new ArrayList<>();
}
