package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求订单号
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqRechargeOrderIdMessage extends AbsProtostuffMessage {
  /**
   * 商品id
   */
  private int product_id;

  @Override
  public int getId() {
    return 39033;
  }

  public void setProduct_id(int product_id) {
    this.product_id = product_id;
  }

  public int getProduct_id() {
    return this.product_id;
  }
}
