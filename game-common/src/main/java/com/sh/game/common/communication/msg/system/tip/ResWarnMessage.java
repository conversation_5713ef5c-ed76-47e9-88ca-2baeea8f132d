package com.sh.game.common.communication.msg.system.tip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 警告
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWarnMessage extends AbsProtostuffMessage {
  /**
   * 信息
   */
  private String warn = new String();

  @Override
  public int getId() {
    return 7002;
  }

  public void setWarn(String warn) {
    this.warn = warn;
  }

  public String getWarn() {
    return this.warn;
  }
}
