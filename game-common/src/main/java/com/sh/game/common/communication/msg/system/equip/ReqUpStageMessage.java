package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Long;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求升阶装备
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUpStageMessage extends AbsProtostuffMessage {
  /**
   * 升阶类型，读表id
   */
  private int type;

  /**
   * 放入的装备id
   */
  private List<Long> lid = new ArrayList<>();

  @Override
  public int getId() {
    return 13050;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setLid(List<Long> lid) {
    this.lid = lid;
  }

  public List<Long> getLid() {
    return this.lid;
  }
}
