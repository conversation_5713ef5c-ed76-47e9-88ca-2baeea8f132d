package com.sh.game.common.communication.msg.system.season;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求进入赛季副本
 * 该文件由工具根据 season.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEnterSeasonDuplicateMessage extends AbsProtostuffMessage {
  /**
   * 副本id
   */
  private int duplicateId;

  @Override
  public int getId() {
    return 413001;
  }

  public void setDuplicateId(int duplicateId) {
    this.duplicateId = duplicateId;
  }

  public int getDuplicateId() {
    return this.duplicateId;
  }
}
