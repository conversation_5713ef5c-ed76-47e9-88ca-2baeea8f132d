package com.sh.game.common.communication.msg.system.monster;

import com.sh.game.common.communication.msg.system.monster.bean.MonsterDescBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回怪物状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMonsterStateMessage extends AbsProtostuffMessage {
  private List<MonsterDescBean> monsters = new ArrayList<>();

  @Override
  public int getId() {
    return 191002;
  }

  public void setMonsters(List<MonsterDescBean> monsters) {
    this.monsters = monsters;
  }

  public List<MonsterDescBean> getMonsters() {
    return this.monsters;
  }
}
