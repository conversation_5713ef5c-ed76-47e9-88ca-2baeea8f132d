package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求召唤护卫
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqCallProtectMessage extends AbsProtostuffMessage {
  /**
   * 1弓箭手2流动护卫3两个都要
   */
  private int type;

  /**
   * 生效方式 1立即2活动开始生效
   */
  private int useType;

  @Override
  public int getId() {
    return 275013;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setUseType(int useType) {
    this.useType = useType;
  }

  public int getUseType() {
    return this.useType;
  }
}
