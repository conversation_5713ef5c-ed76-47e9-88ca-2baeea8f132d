package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回当日奖励信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResActivityDiscountGiftDailyInfoMessage extends AbsProtostuffMessage {
  /**
   * 充值状态
   */
  private List<Integer> purchaseStatus = new ArrayList<>();

  /**
   * 领取状态
   */
  private List<Integer> acquireStatus = new ArrayList<>();

  @Override
  public int getId() {
    return 4132;
  }

  public void setPurchaseStatus(List<Integer> purchaseStatus) {
    this.purchaseStatus = purchaseStatus;
  }

  public List<Integer> getPurchaseStatus() {
    return this.purchaseStatus;
  }

  public void setAcquireStatus(List<Integer> acquireStatus) {
    this.acquireStatus = acquireStatus;
  }

  public List<Integer> getAcquireStatus() {
    return this.acquireStatus;
  }
}
