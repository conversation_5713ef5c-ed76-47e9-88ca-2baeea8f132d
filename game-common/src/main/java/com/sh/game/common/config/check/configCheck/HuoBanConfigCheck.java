package com.sh.game.common.config.check.configCheck;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.check.ConfigCheckHelper;
import com.sh.game.common.config.check.IConfigCheck;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.HuoBanConfig;

import java.util.List;

public class HuoBanConfigCheck implements IConfigCheck {
    @Override
    public void check() {
        List<HuoBanConfig> list = ConfigDataManager.getInstance().getList(HuoBanConfig.class);
        for (HuoBanConfig config : list) {
            if (config.getFashionId() > 0) {
                ConfigCheckHelper.assertConfigExists("cfg_partner", "fashionId", config.getId(), AppearanceConfig.class, config.getFashionId());
            }
        }
    }
}
