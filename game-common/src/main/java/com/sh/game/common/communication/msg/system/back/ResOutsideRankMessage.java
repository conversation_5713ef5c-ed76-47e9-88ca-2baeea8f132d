package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回当前玩家状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResOutsideRankMessage extends AbsProtostuffMessage {
  /**
   * 当前状态值
   */
  private int state;

  @Override
  public int getId() {
    return 43071;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }
}
