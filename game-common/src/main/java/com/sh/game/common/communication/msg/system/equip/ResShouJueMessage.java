package com.sh.game.common.communication.msg.system.equip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回成功与失败
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResShouJueMessage extends AbsProtostuffMessage {
  /**
   * 锁定的孔位列表
   */
  private List<Integer> locked = new ArrayList<>();

  /**
   * 当前总可锁定数量
   */
  private int totalCount;

  @Override
  public int getId() {
    return 340006;
  }

  public void setLocked(List<Integer> locked) {
    this.locked = locked;
  }

  public List<Integer> getLocked() {
    return this.locked;
  }

  public void setTotalCount(int totalCount) {
    this.totalCount = totalCount;
  }

  public int getTotalCount() {
    return this.totalCount;
  }
}
