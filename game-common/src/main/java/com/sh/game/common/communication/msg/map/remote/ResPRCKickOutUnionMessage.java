package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 被踢出联盟帮会处理
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPRCKickOutUnionMessage extends AbsProtostuffMessage {
  /**
   * 联盟id
   */
  private long allianceId;

  /**
   * 被踢帮会id
   */
  private long unionId;

  @Override
  public int getId() {
    return 82038;
  }

  public void setAllianceId(long allianceId) {
    this.allianceId = allianceId;
  }

  public long getAllianceId() {
    return this.allianceId;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }
}
