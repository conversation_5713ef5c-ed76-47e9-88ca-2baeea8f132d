package com.sh.game.common.communication.msg.system.xunbao;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求寻宝奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqXunBaoRewardMessage extends AbsProtostuffMessage {
  /**
   * 第几次寻宝
   */
  private int order;

  @Override
  public int getId() {
    return 317003;
  }

  public void setOrder(int order) {
    this.order = order;
  }

  public int getOrder() {
    return this.order;
  }
}
