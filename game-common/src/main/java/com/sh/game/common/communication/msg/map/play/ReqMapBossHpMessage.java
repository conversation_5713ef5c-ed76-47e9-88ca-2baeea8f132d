package com.sh.game.common.communication.msg.map.play;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求地图boss血量信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqMapBossHpMessage extends AbsProtostuffMessage {
  /**
   * 地图id
   */
  private int mapId;

  @Override
  public int getId() {
    return 87002;
  }

  public void setMapId(int mapId) {
    this.mapId = mapId;
  }

  public int getMapId() {
    return this.mapId;
  }
}
