package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求沙巴克分配
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqShaBaKeAllocMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long uid;

  /**
   * 道具id
   */
  private long itemId;

  @Override
  public int getId() {
    return 275033;
  }

  public void setUid(long uid) {
    this.uid = uid;
  }

  public long getUid() {
    return this.uid;
  }

  public void setItemId(long itemId) {
    this.itemId = itemId;
  }

  public long getItemId() {
    return this.itemId;
  }
}
