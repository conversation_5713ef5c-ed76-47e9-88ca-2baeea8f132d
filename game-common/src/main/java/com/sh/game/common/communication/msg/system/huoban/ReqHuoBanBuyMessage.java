package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求召唤伙伴
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHuoBanBuyMessage extends AbsProtostuffMessage {
  /**
   *  刷新出来的伙伴索引
   */
  private int index;

  @Override
  public int getId() {
    return 388003;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }
}
