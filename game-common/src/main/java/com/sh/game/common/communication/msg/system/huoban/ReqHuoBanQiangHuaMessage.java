package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求伙伴强化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHuoBanQiangHuaMessage extends AbsProtostuffMessage {
  /**
   *  伙伴的配置id
   */
  private int configId;

  /**
   *  技能类型 1主动、2被动
   */
  private int type;

  @Override
  public int getId() {
    return 388007;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
