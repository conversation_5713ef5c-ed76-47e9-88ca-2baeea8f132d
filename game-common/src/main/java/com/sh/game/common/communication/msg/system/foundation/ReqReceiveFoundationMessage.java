package com.sh.game.common.communication.msg.system.foundation;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取基金
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqReceiveFoundationMessage extends AbsProtostuffMessage {
  /**
   * 配置id 为0时代表一键领取
   */
  private int cid;

  /**
   * 基金类型 cid为0时必填
   */
  private int type;

  @Override
  public int getId() {
    return 403001;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
