package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqActivityEquipRecycleAcquireMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 4042;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
