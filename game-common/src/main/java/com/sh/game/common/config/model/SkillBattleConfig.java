package com.sh.game.common.config.model;


import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.bool.BooleanConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_battle_skill")
public class SkillBattleConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * 技能等级
     */
    private int level;

    /**
     * 技能名称
     */
    private String name;
    /**
     * 释放类型
     * 默认&不填主动，被动1
     */
    private int releaseType;

    /**
     * 技能效果
     * 可关联多个
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> skillEffect;

    /**
     * 技能cd
     */
    private int skillCd;


    /**
     * 技能优先级
     */
    private int priority;


    /**
     * 技能前摇时间 or 吟唱
     */
    private int qianyao;


    /**
     * 技能后摇时间
     */
    private int releaseCdTime;


    /**
     * 范围
     */
    private int range;

    /**
     * 免公共CD
     */
    @ConfigField(converter = BooleanConverter.class)
    private boolean avoidPublicCd;

    /**
     * 常规技能，目标由服务器选择（包括碰撞前置技能 ）   枚举值 ：0
     * 客户端目标技能（前置条件是释放了前置技能），目标由客户端提供   枚举值 ：1
     * 服务器子弹技能,这个技能只能由服务器释放，目标由服务器选择，枚举值：2
     */
    private int type;

    /**
     * 技能归属
     * 1. 玩家
     * 2. 伙伴
     * 3. 怪物
     */
    private int exclusive;

    private int group;

    /**
     * 技能所需点数
     */
    private int skillPoint;

    /**
     * 能量消耗
     */
    private int consumeMP;
}
