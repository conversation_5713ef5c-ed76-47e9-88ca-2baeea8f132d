package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 热更提示
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUpdateTipMessage extends AbsProtostuffMessage {
  /**
   * 类型1000热更
   */
  private int type;

  @Override
  public int getId() {
    return 43141;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
