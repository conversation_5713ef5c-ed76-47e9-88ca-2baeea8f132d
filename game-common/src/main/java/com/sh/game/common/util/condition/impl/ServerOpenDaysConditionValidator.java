package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

public class ServerOpenDaysConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (ctx == null) {
            return false;
        }

        int startups = ctx.getOpenDays();
        if (params.length > 1 && startups < params[1]) {
            return false;
        }
        if (params.length > 2 && startups > params[2]) {
            return false;
        }

        return true;
    }
}
