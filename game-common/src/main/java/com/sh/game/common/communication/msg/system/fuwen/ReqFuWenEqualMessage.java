package com.sh.game.common.communication.msg.system.fuwen;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求兑换符文
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqFuWenEqualMessage extends AbsProtostuffMessage {
  /**
   * 道具id
   */
  private int itemId;

  /**
   * 数量
   */
  private int count;

  @Override
  public int getId() {
    return 218009;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
