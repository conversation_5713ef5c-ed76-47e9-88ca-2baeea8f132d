package com.sh.game.common.communication.msg.system.worship;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回膜拜信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWorshipMessage extends AbsProtostuffMessage {
  /**
   * 已使用膜拜次数
   */
  private int usedWorship;

  /**
   * 鄙视次数
   */
  private int disdainTotal;

  /**
   * 膜拜次数
   */
  private int adoreTotal;

  @Override
  public int getId() {
    return 228006;
  }

  public void setUsedWorship(int usedWorship) {
    this.usedWorship = usedWorship;
  }

  public int getUsedWorship() {
    return this.usedWorship;
  }

  public void setDisdainTotal(int disdainTotal) {
    this.disdainTotal = disdainTotal;
  }

  public int getDisdainTotal() {
    return this.disdainTotal;
  }

  public void setAdoreTotal(int adoreTotal) {
    this.adoreTotal = adoreTotal;
  }

  public int getAdoreTotal() {
    return this.adoreTotal;
  }
}
