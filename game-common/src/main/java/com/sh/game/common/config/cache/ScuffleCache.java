package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.ScuffleConfig;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2021/3/5 11:46
 * desc: 皇宫乱斗奖励
 */
@Getter
@ConfigCache
public class ScuffleCache implements IConfigCache {
    /**
     * 排名奖励
     */
    private Map<Integer, List<int[]>> rankMap = new HashMap<>();

    /**
     * 积分奖励
     */
    private TreeMap<Integer, List<int[]>> scoreMap = new TreeMap<>();

    @Override
    public void build() {
        rankMap.clear();
        scoreMap.clear();
        List<ScuffleConfig> list = ConfigDataManager.getInstance().getList(ScuffleConfig.class);
        Map<Integer, List<ScuffleConfig>> listMap = list.stream().collect(Collectors.groupingBy(ScuffleConfig::getType));
        rankMap = listMap.get(1).stream().collect(Collectors.toMap(ScuffleConfig::getRank, ScuffleConfig::getReward));
        scoreMap.putAll(listMap.get(2).stream().collect(Collectors.toMap(ScuffleConfig::getRank, ScuffleConfig::getReward)));
    }
}
