package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ShuxianAndYuHaoTwoArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinHaoYuHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * description:
 * create: 2025/6/12
 * author: chen bin
 */
@Getter
@Setter
@ConfigData(file = "cfg_equip_suiji_attribute")
public class EquipSuiJiAttributeConfig extends AbstractConfigData {

    private int id;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> attribute1;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> attribute2;

    private int probability;

    private int cznum;

    private int czkid;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> needitem;

    private int effect;
}
