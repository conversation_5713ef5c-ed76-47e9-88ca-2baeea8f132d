package com.sh.game.common.communication.msg.map.bean;

import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import lombok.Getter;
import lombok.Setter;

import java.lang.String;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RoundPlayerBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * id
   */
  private long lid;

  private int x;

  private int y;

  /**
   * 朝向
   */
  private int dir;

  private String ownerName = new String();

  private String name = new String();

  /**
   * 职业
   */
  private int career;

  /**
   * 发型
   */
  private int hair;

  /**
   * 性别
   */
  private int sex;

  /**
   * 等级
   */
  private int level;

  /**
   * 装备信息
   */
  private List<PerformerEquipBean> equips = new ArrayList<>();

  /**
   * 时装穿戴
   */
  private List<AppearanceWearBean> wears = new ArrayList<>();

  /**
   * 当前血量
   */
  private long hp;

  /**
   * 最大血量
   */
  private long maxhp;

  /**
   * 状态位1
   */
  private int state;

  /**
   * buff信息
   */
  private List<PerformerBufferBean> buffers = new ArrayList<>();

  /**
   * 队伍id
   */
  private long teamId;

  /**
   * 行会id
   */
  private long unionId;

  /**
   * 联盟id
   */
  private long allianceId;

  /**
   * 哪个阵营
   */
  private int group;

  /**
   * 行会名字
   */
  private String unionName = new String();

  /**
   * 行会职位
   */
  private int unionPosition;

  /**
   * pk值
   */
  private int pkValue;

  /**
   * 1变灰
   */
  private int gray;

  /**
   * 是否蓝青橘颜色显示 0不是 1是
   */
  private int superColor;

  /**
   * 是否在安全区
   */
  private boolean inSafeArea;

  /**
   * 穿过属性 0x0001可穿人 0x0010可穿怪  逻辑或组合
   */
  private int penetrable;

  /**
   * 正在采集的对象
   */
  private long collecting;

  /**
   * 是否是传送
   */
  private boolean teleport;

  /**
   * 骑乘
   */
  private int mount;

  /**
   * 封号
   */
  private int title;

  /**
   * 是否挖矿1是
   */
  private boolean digMiner;

  /**
   * 1:本服沙巴克，2:跨服沙巴克
   */
  private int shabake;

  /**
   * 天一第一击杀数
   */
  private int worldFirstKillNum;

  /**
   * 属性
   */
  private AttributeBean attr = new AttributeBean();

  /**
   * 类型 玩家1, 分身3, 英雄6
   */
  private int type;

  /**
   * 江湖名望,值未cfg_ih_prestige的id
   */
  private int mingWang;

  /**
   * 是否开启狂暴之力
   */
  private boolean isSuperMan;

  /**
   * 所在地图id
   */
  private int mapID;

  /**
   * 护盾值
   */
  private long shield;

  /**
   * 护盾值最大值
   */
  private long maxShield;

  /**
   * 神皇之力id
   */
  private int shenHuangId;

  /**
   * 阵营类型
   */
  private int campType;

  /**
   * 军衔配置表id
   */
  private int militaryRankCfgId;

  /**
   * 境界配置id
   */
  private int realmConfigId;

  private PerformerZTPetBean ztPet;

  public void setRid(long rid) {
    this.rid = rid;
  }
}
