package com.sh.game.common.communication.msg.map.bean;

import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;

import java.util.ArrayList;
import java.util.List;

public class RoundPlayerBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * id
   */
  private long lid;

  private int x;

  private int y;

  /**
   * 朝向
   */
  private int dir;

  private String ownerName = new String();

  private String name = new String();

  /**
   * 职业
   */
  private int career;

  /**
   * 发型
   */
  private int hair;

  /**
   * 性别
   */
  private int sex;

  /**
   * 等级
   */
  private int level;

  /**
   * 装备信息
   */
  private List<PerformerEquipBean> equips = new ArrayList<>();

  /**
   * 时装穿戴
   */
  private List<AppearanceWearBean> wears = new ArrayList<>();

  /**
   * 当前血量
   */
  private long hp;

  /**
   * 最大血量
   */
  private long maxhp;

  /**
   * 状态位1
   */
  private int state;

  /**
   * buff信息
   */
  private List<PerformerBufferBean> buffers = new ArrayList<>();

  /**
   * 队伍id
   */
  private long teamId;

  /**
   * 行会id
   */
  private long unionId;

  /**
   * 联盟id
   */
  private long allianceId;

  /**
   * 哪个阵营
   */
  private int group;

  /**
   * 行会名字
   */
  private String unionName = new String();

  /**
   * 行会职位
   */
  private int unionPosition;

  /**
   * pk值
   */
  private int pkValue;

  /**
   * 1变灰
   */
  private int gray;

  /**
   * 是否蓝青橘颜色显示 0不是 1是
   */
  private int superColor;

  /**
   * 是否在安全区
   */
  private boolean inSafeArea;

  /**
   * 穿过属性 0x0001可穿人 0x0010可穿怪  逻辑或组合
   */
  private int penetrable;

  /**
   * 正在采集的对象
   */
  private long collecting;

  /**
   * 是否是传送
   */
  private boolean teleport;

  /**
   * 骑乘
   */
  private int mount;

  /**
   * 封号
   */
  private int title;

  /**
   * 是否挖矿1是
   */
  private boolean digMiner;

  /**
   * 1:本服沙巴克，2:跨服沙巴克
   */
  private int shabake;

  /**
   * 天一第一击杀数
   */
  private int worldFirstKillNum;

  /**
   * 属性
   */
  private AttributeBean attr = new AttributeBean();

  /**
   * 类型 玩家1, 分身3, 英雄6
   */
  private int type;

  /**
   * 江湖名望,值未cfg_ih_prestige的id
   */
  private int mingWang;

  /**
   * 是否开启狂暴之力
   */
  private boolean isSuperMan;

  /**
   * 所在地图id
   */
  private int mapID;

  /**
   * 护盾值
   */
  private long shield;

  /**
   * 护盾值最大值
   */
  private long maxShield;

  /**
   * 神皇之力id
   */
  private int shenHuangId;

  /**
   * 阵营类型
   */
  private int campType;

  /**
   * 军衔配置表id
   */
  private int militaryRankCfgId;

  /**
   * 境界配置id
   */
  private int realmConfigId;

  /**
   * 宠物
   */
  private PerformerZTPetBean ztPet = new PerformerZTPetBean();

  /**
   * 当前蓝量
   */
  private long mp;

  /**
   * 最大蓝量
   */
  private long maxMp;

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setLid(long lid) {
    this.lid = lid;
  }

  public long getLid() {
    return this.lid;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }

  public void setDir(int dir) {
    this.dir = dir;
  }

  public int getDir() {
    return this.dir;
  }

  public void setOwnerName(String ownerName) {
    this.ownerName = ownerName;
  }

  public String getOwnerName() {
    return this.ownerName;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setHair(int hair) {
    this.hair = hair;
  }

  public int getHair() {
    return this.hair;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setEquips(List<PerformerEquipBean> equips) {
    this.equips = equips;
  }

  public List<PerformerEquipBean> getEquips() {
    return this.equips;
  }

  public void setWears(List<AppearanceWearBean> wears) {
    this.wears = wears;
  }

  public List<AppearanceWearBean> getWears() {
    return this.wears;
  }

  public void setHp(long hp) {
    this.hp = hp;
  }

  public long getHp() {
    return this.hp;
  }

  public void setMaxhp(long maxhp) {
    this.maxhp = maxhp;
  }

  public long getMaxhp() {
    return this.maxhp;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setBuffers(List<PerformerBufferBean> buffers) {
    this.buffers = buffers;
  }

  public List<PerformerBufferBean> getBuffers() {
    return this.buffers;
  }

  public void setTeamId(long teamId) {
    this.teamId = teamId;
  }

  public long getTeamId() {
    return this.teamId;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setAllianceId(long allianceId) {
    this.allianceId = allianceId;
  }

  public long getAllianceId() {
    return this.allianceId;
  }

  public void setGroup(int group) {
    this.group = group;
  }

  public int getGroup() {
    return this.group;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setUnionPosition(int unionPosition) {
    this.unionPosition = unionPosition;
  }

  public int getUnionPosition() {
    return this.unionPosition;
  }

  public void setPkValue(int pkValue) {
    this.pkValue = pkValue;
  }

  public int getPkValue() {
    return this.pkValue;
  }

  public void setGray(int gray) {
    this.gray = gray;
  }

  public int getGray() {
    return this.gray;
  }

  public void setSuperColor(int superColor) {
    this.superColor = superColor;
  }

  public int getSuperColor() {
    return this.superColor;
  }

  public void setInSafeArea(boolean inSafeArea) {
    this.inSafeArea = inSafeArea;
  }

  public boolean getInSafeArea() {
    return this.inSafeArea;
  }

  public void setPenetrable(int penetrable) {
    this.penetrable = penetrable;
  }

  public int getPenetrable() {
    return this.penetrable;
  }

  public void setCollecting(long collecting) {
    this.collecting = collecting;
  }

  public long getCollecting() {
    return this.collecting;
  }

  public void setTeleport(boolean teleport) {
    this.teleport = teleport;
  }

  public boolean getTeleport() {
    return this.teleport;
  }

  public void setMount(int mount) {
    this.mount = mount;
  }

  public int getMount() {
    return this.mount;
  }

  public void setTitle(int title) {
    this.title = title;
  }

  public int getTitle() {
    return this.title;
  }

  public void setDigMiner(boolean digMiner) {
    this.digMiner = digMiner;
  }

  public boolean getDigMiner() {
    return this.digMiner;
  }

  public void setShabake(int shabake) {
    this.shabake = shabake;
  }

  public int getShabake() {
    return this.shabake;
  }

  public void setWorldFirstKillNum(int worldFirstKillNum) {
    this.worldFirstKillNum = worldFirstKillNum;
  }

  public int getWorldFirstKillNum() {
    return this.worldFirstKillNum;
  }

  public void setAttr(AttributeBean attr) {
    this.attr = attr;
  }

  public AttributeBean getAttr() {
    return this.attr;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setMingWang(int mingWang) {
    this.mingWang = mingWang;
  }

  public int getMingWang() {
    return this.mingWang;
  }

  public void setIsSuperMan(boolean isSuperMan) {
    this.isSuperMan = isSuperMan;
  }

  public boolean getIsSuperMan() {
    return this.isSuperMan;
  }

  public void setMapID(int mapID) {
    this.mapID = mapID;
  }

  public int getMapID() {
    return this.mapID;
  }

  public void setShield(long shield) {
    this.shield = shield;
  }

  public long getShield() {
    return this.shield;
  }

  public void setMaxShield(long maxShield) {
    this.maxShield = maxShield;
  }

  public long getMaxShield() {
    return this.maxShield;
  }

  public void setShenHuangId(int shenHuangId) {
    this.shenHuangId = shenHuangId;
  }

  public int getShenHuangId() {
    return this.shenHuangId;
  }

  public void setCampType(int campType) {
    this.campType = campType;
  }

  public int getCampType() {
    return this.campType;
  }

  public void setMilitaryRankCfgId(int militaryRankCfgId) {
    this.militaryRankCfgId = militaryRankCfgId;
  }

  public int getMilitaryRankCfgId() {
    return this.militaryRankCfgId;
  }

  public void setRealmConfigId(int realmConfigId) {
    this.realmConfigId = realmConfigId;
  }

  public int getRealmConfigId() {
    return this.realmConfigId;
  }

  public void setZtPet(PerformerZTPetBean ztPet) {
    this.ztPet = ztPet;
  }

  public PerformerZTPetBean getZtPet() {
    return this.ztPet;
  }

  public void setMp(long mp) {
    this.mp = mp;
  }

  public long getMp() {
    return this.mp;
  }

  public void setMaxMp(long maxMp) {
    this.maxMp = maxMp;
  }

  public long getMaxMp() {
    return this.maxMp;
  }
}
