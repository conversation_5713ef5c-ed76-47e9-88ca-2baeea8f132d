package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回猎命信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBirthChartInfoMessage extends AbsProtostuffMessage {
  /**
   * 已获得的奖励列表,key:道具配置id,value:数量
   */
  private List<CommonKeyValueBean> rewardList = new ArrayList<>();

  /**
   * 已激活的事件id
   */
  private List<Integer> eventList = new ArrayList<>();

  /**
   * 进度
   */
  private int progress;

  @Override
  public int getId() {
    return 380002;
  }

  public void setRewardList(List<CommonKeyValueBean> rewardList) {
    this.rewardList = rewardList;
  }

  public List<CommonKeyValueBean> getRewardList() {
    return this.rewardList;
  }

  public void setEventList(List<Integer> eventList) {
    this.eventList = eventList;
  }

  public List<Integer> getEventList() {
    return this.eventList;
  }

  public void setProgress(int progress) {
    this.progress = progress;
  }

  public int getProgress() {
    return this.progress;
  }
}
