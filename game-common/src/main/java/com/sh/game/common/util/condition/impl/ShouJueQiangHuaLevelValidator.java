package com.sh.game.common.util.condition.impl;

import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

import java.util.Map;

public class ShouJueQiangHuaLevelValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params == null || params.length < 2) {
            return false;
        }
        int level = 0;
        Backpack backpack = avatar.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.SHOUJUE_EQUIP);
        for (Map.Entry<Integer, Item> entry : storage.getData().entrySet()) {
            Item item = entry.getValue();
            if (item == null) {
                continue;
            }
            if (item.getEquipData() == null) {
                continue;
            }
            int shouJueLevel = item.getEquipData().getShouJueLevel();
            level = level + shouJueLevel;
        }

        if (params.length == 2) {
            return level >= params[1];
        }

        //condition#min#max
        if (params.length == 3) {
            return level >= params[1] && level <= params[2];
        }

        return false;
    }
}
