package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.communication.msg.system.moneytree.bean.MoneyTreeRewardBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求批量领取摇钱树升级奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMoneyTreeRewardBatchMessage extends AbsProtostuffMessage {
  /**
   * 奖励配置列表
   */
  private List<MoneyTreeRewardBean> rewardList = new ArrayList<>();

  @Override
  public int getId() {
    return 362008;
  }

  public void setRewardList(List<MoneyTreeRewardBean> rewardList) {
    this.rewardList = rewardList;
  }

  public List<MoneyTreeRewardBean> getRewardList() {
    return this.rewardList;
  }
}
