package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ConfigData(file = "cfg_zajindan")
public class ZaJinDanConfig extends AbstractConfigData {

    private int id;

    private int activityId;

    /**
     * 充值表id
     */
    private int recharge;

    /**
     * 获得金蛋
     */
    private int score;
}
