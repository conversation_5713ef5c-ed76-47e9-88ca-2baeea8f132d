package com.sh.game.common.communication.msg.system.user;

import com.sh.game.common.communication.msg.system.user.bean.RoleBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回创建角色成功
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCreateRoleSuccessMessage extends AbsProtostuffMessage {
  private RoleBean roleInfo = new RoleBean();

  @Override
  public int getId() {
    return 1018;
  }

  public void setRoleInfo(RoleBean roleInfo) {
    this.roleInfo = roleInfo;
  }

  public RoleBean getRoleInfo() {
    return this.roleInfo;
  }
}
