package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 限购返利
 */
@Getter
@Setter
@ConfigData(file = "cfg_buygift_cumulative")
public class ActivityPurchasePresentRechargeConfig extends AbstractConfigData {

    /**
     * 返利编号
     */
    private int id;

    /**
     * 活动编号
     */
    private int activityID;

    /**
     * 参与添加
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 累计金额
     */
    private int count;

    /**
     * 奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 轮次
     */
    private int cycle;

}
