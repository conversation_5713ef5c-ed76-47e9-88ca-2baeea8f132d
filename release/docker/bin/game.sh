ulimit -n 65535

# 服务名
SERVER_NAME=game
MAIN_CLASS=com.sh.game.gate.GameBootstrap
STOP_MAIN_CLASS=com.sh.game.back.stat.StopServerClient
CONF=/server/conf/game.properties

# 进程依赖
LIBS=/server/libs/*
CORE=/server/core/*

# 版本号
VERSION=*******

# JVM配置
OPTS="
 -XX:MetaspaceSize=256m
 -XX:MaxMetaspaceSize=256m
 -XX:ReservedCodeCacheSize=128m
 -XX:+PrintCommandLineFlags
 -XX:-OmitStackTraceInFastThrow

 -XX:+UseG1GC
 -Xms2g
 -Xmx2g

 -verbose:gc
 -XX:+PrintGCDateStamps
 -XX:+PrintGCDetails
 -Xloggc:./logs/sgc.log
 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./heapdump.hprof

 -Dfile.encoding=UTF-8
 -Dgame.script.dev=true
 -Dgame.script.findSuperClassInterface=true
 -Dgame.log.queueLog=true

 -Dlog4j.configurationFile="./conf/log4j2-game.xml"
"

JAVA_AGENT="-javaagent:/opt/jmx/jmx-prometheus-javaagent-1.0.0.jar=${JMX_EXPORTER_PORT:-12345}:/opt/jmx/prometheus-jmx-config.yaml"

# 后台执行 JMX 上报
bash /opt/jmx/push_jmx_metrics.sh &

#启动游戏服务器
function startServer()
{
	  exec java ${JAVA_AGENT} -server ${OPTS} -cp ${LIBS}:${CORE} ${MAIN_CLASS} ${CONF} ${VERSION} ${SERVER_NAME} &
    # 获取Java进程的PID
    server_pid=$!
    echo "Java server started with PID $server_pid"
}

# 停止游戏服务器
function stopServer() {
    echo "Java server stopping!!!"
    java -server -cp ${LIBS}:${CORE} ${STOP_MAIN_CLASS} ${CONF}
    echo "Java server stopped!!!"
}

trap stopServer SIGTERM SIGINT

startServer;

# 保持脚本运行以捕捉信号
wait $server_pid
