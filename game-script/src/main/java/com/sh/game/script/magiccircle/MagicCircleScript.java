package com.sh.game.script.magiccircle;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.magiccircle.ResMagicCircleInfoMessage;
import com.sh.game.common.communication.msg.system.magiccircle.bean.MagicCircleBean;
import com.sh.game.common.config.cache.MagicCircleCache;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.FaQiHuiYuanConfig;
import com.sh.game.common.config.model.MagicCircleConfig;
import com.sh.game.common.config.model.MagicCircleSuitConfig;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.MagicCircleConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.magiccircle.IMagicCircleScript;
import com.sh.game.system.magiccircle.entity.MagicCircleInfo;
import com.sh.game.system.magiccircle.entity.RoleMagicCircle;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 法阵功能实现
 * 292属性影响法阵属性加成，所以该script的属性统计要延迟执行
 *
 * <AUTHOR> Chen
 * @since 2022/5/26 16:06
 */
@Slf4j
@Script(order = 1)
public class MagicCircleScript implements IMagicCircleScript {

    /**
     * 根据法阵类型升级法阵
     *
     * @param role 角色
     * @param type 法阵类型
     */
    @Override
    public void magicCircleLevelUp(Role role, int type) {
        RoleMagicCircle roleMagicCircle = find(role.getRoleId());
        Map<Integer, MagicCircleInfo> magicCircleMap = roleMagicCircle.getMagicCircleMap();
        MagicCircleInfo magicCircleInfo = magicCircleMap.get(type);
        //类型不存在
        if (magicCircleInfo == null) {
            return;
        }
        int currentLevel = magicCircleInfo.getLevel();
        int progress = magicCircleInfo.getProgress();
        //当前config
        int currentId = ConfigCacheManager.getInstance().getCache(MagicCircleCache.class).getConfigIdByTypeAndLevel(type, currentLevel);
        MagicCircleConfig magicCircleConfig = ConfigDataManager.getInstance().getById(MagicCircleConfig.class, currentId);

        if (magicCircleConfig == null) {
            return;
        }

        //判断condition
        if (!ConditionUtil.validate(role, magicCircleConfig.getCondition())) {
            return;
        }
        //下一级config
        int nextId = ConfigCacheManager.getInstance().getCache(MagicCircleCache.class).getConfigIdByTypeAndLevel(type, currentLevel + 1);
        MagicCircleConfig nextConfig = ConfigDataManager.getInstance().getById(MagicCircleConfig.class, nextId);
        //已满级
        if (Objects.isNull(nextConfig)) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(magicCircleConfig.getTips());
        if (!stash.commit(role, LogAction.MAGIC_CIRCLE_UP_COST)) {
            return;
        }

        magicCircleInfo.setZhuFuTime(magicCircleInfo.getZhuFuTime() + 1);


        //祝福值(进度)
        int xs = magicCircleConfig.getXs();

        List<Integer> typeList = GlobalUtil.findJingHaoList(GameConst.GlobalId.MAGIC_CIRCLE_UP_CRIT_VALID_TYPE);
        if (typeList != null && typeList.contains(type)) {
            FaQiHuiYuanConfig config = role.findFaQiHuiYuanConfig();
            int[] fazhenupdate = config.getFazhenupdate();
            if (fazhenupdate != null && fazhenupdate.length >= 3) {
                int time = fazhenupdate[2];
                if (magicCircleInfo.getZhuFuTime() >= time) {
                    int baoJiRate = fazhenupdate[0];
                    if (RandomUtil.isGenerate(10000, baoJiRate)) {
                        magicCircleInfo.setZhuFuTime(0);
                        magicCircleInfo.setBaoJi(1);
                        int upRate = fazhenupdate[1];
                        xs = (int) ((upRate + 10000D) / 10000 * xs);
                    }
                }
            }
        }

        progress += xs;
        magicCircleInfo.setType(type);
        if (progress >= magicCircleConfig.getInstruction()) {
            magicCircleInfo.setLevel(nextConfig.getLevel());
            if (magicCircleConfig.getClear() == 1) {
                progress = 0;
            }
            //解锁装扮并穿戴
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, nextConfig.getFashionId());
            if (appearanceConfig != null) {
                AppearanceManager.getInstance().onUnlock(role, appearanceConfig, null);
                AppearanceManager.getInstance().reqWear(role, nextConfig.getFashionId());
            }
            AnnounceManager.getInstance().post(nextConfig.getAnnounceid(), 0L, role, nextConfig.getName(), nextConfig.getLevel());
        } else {
            magicCircleInfo.setLevel(currentLevel);
        }
        magicCircleInfo.setProgress(progress);
        magicCircleMap.put(type, magicCircleInfo);
        DataCenter.updateData(roleMagicCircle);

        sendMagicCircleInfo(role);

        magicCircleInfo.setBaoJi(0);
        DataCenter.updateData(roleMagicCircle);

        log.info("法阵升级-roleId: {},roleName: {} 升级法阵等级 {} --> {}, 当前祝福值: {}", role.getRoleId(), role.getName(), currentLevel, magicCircleInfo.getLevel(), progress);
    }

    /**
     * 获取角色当前法阵信息
     *
     * @param role 角色
     */
    @Override
    public void sendMagicCircleInfo(Role role) {
        Map<Integer, MagicCircleInfo> magicCircleMap = find(role.getRoleId()).getMagicCircleMap();
        List<MagicCircleBean> magicCircleBeanList = magicCircleMap.values().stream().map(v -> {
            MagicCircleBean magicCircleBean = new MagicCircleBean();
            magicCircleBean.setLevel(v.getLevel());
            magicCircleBean.setType(v.getType());
            magicCircleBean.setProgress(v.getProgress());
            magicCircleBean.setBaoJi(v.getBaoJi());
            magicCircleBean.setZhuFuTime(v.getZhuFuTime());
            return magicCircleBean;
        }).collect(Collectors.toList());
        ResMagicCircleInfoMessage msg = new ResMagicCircleInfoMessage();
        msg.getMagicCircleBeanList().addAll(magicCircleBeanList);
        msg.setCircleSuitConfigId(getMagicCircleSuitId(role));
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 根据角色id获取角色当前法阵信息
     *
     * @param id 角色id roleId
     * @return {@link RoleMagicCircle} 角色当前法阵信息
     */
    @Override
    public RoleMagicCircle find(long id) {
        RoleMagicCircle roleMagicCircle = DataCenter.get(RoleMagicCircle.class, id);
        if (Objects.isNull(roleMagicCircle)) {
            roleMagicCircle = new RoleMagicCircle();
            roleMagicCircle.setId(id);
            for (MagicCircleConst.TypeEnum typeEnum : MagicCircleConst.TypeEnum.values()) {
                MagicCircleInfo magicCircleInfo = new MagicCircleInfo();
                magicCircleInfo.setType(typeEnum.getType());
                magicCircleInfo.setLevel(0);
                magicCircleInfo.setProgress(0);
                roleMagicCircle.getMagicCircleMap().put(typeEnum.getType(), magicCircleInfo);
            }
            DataCenter.insertData(roleMagicCircle, true);
        }
        return roleMagicCircle;
    }

    /***
     * 获取当前用户法阵套装组合id
     *
     * @param role 角色
     * @return {@link int} 法阵套装组合id
     **/
    private int getMagicCircleSuitId(Role role) {
        MagicCircleSuitConfig magicCircleSuitConfig = ConfigDataManager.getInstance().getList(MagicCircleSuitConfig.class).stream()
                .filter(config -> ConditionUtil.validate(role, config.getCondition()))
                .max(Comparator.comparing(MagicCircleSuitConfig::getLevel)).orElse(null);
        if (Objects.nonNull(magicCircleSuitConfig)) {
            return magicCircleSuitConfig.getId();
        }
        return 0;
    }
}
