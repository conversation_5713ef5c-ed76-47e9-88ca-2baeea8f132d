package com.sh.game.script.activity;

import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.script.activity.abc.AbstractRechargeScript;
import com.sh.game.system.activity.entity.role.ActivityRechargeData;
import com.sh.script.annotation.Script;

/**
 * <AUTHOR>
 * @date 2021/12/27 13:49
 */
@Script
public class ActivityHeFuKuangHuanScript extends AbstractRechargeScript {

    @Override
    public int getType() {
        return 1063;
    }

    @Override
    public void storeRechargeData(RoleActivity activity, RechargeConfig rechargeConfig) {
        int day = getAvailableSchedule(null).findProceedDays();
        ActivityRechargeData rechargeData = activity.getActivityRechargeDataMap().get(getType());
        int dayMaxRecharge = rechargeData.getRechargeMap().getOrDefault(day, 0);
        int count = rechargeConfig.getCount();
        rechargeData.getRechargeMap().put(day, Math.max(dayMaxRecharge, count));
    }

    @Override
    public int findMailId() {
        return EmailConst.MailId.HE_FU_KUANG_HUAN;
    }
}
