package com.sh.game.script.backpack;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.util.Symbol;
import com.sh.game.common.communication.msg.abc.bean.CommonItemBean;
import com.sh.game.common.communication.msg.abc.bean.ItemFromBean;
import com.sh.game.common.communication.msg.system.backpack.*;
import com.sh.game.common.communication.msg.system.backpack.bean.BackpackChangeBean;
import com.sh.game.common.communication.msg.system.backpack.bean.BackpackCoinBean;
import com.sh.game.common.communication.msg.system.backpack.bean.BackpackGridBean;
import com.sh.game.common.communication.msg.system.count.ResCountTypeMessage;
import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
import com.sh.game.common.communication.notice.CostItemNotice;
import com.sh.game.common.communication.notice.GetPlayerPositionRetNotice;
import com.sh.game.common.comparator.ItemCostComparator;
import com.sh.game.common.config.cache.ItemUpgradeConfigCache;
import com.sh.game.common.config.cache.SpecialRingCache;
import com.sh.game.common.config.cache.TaskCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.stash.StorageStash;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.backpack.storage.StorageGrid;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleDaily;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.common.util.condition.context.AttributeConditionContext;
import com.sh.game.data.DataCenter;
import com.sh.game.event.*;
import com.sh.game.log.entity.RoleCurrencyLog;
import com.sh.game.log.entity.RoleItemLog;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.notice.TimeProcessNotice;
import com.sh.game.notice.TimeoutHandler;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.backpack.script.IBackpackScript;
import com.sh.game.system.bore.entity.Bore;
import com.sh.game.system.condition.ConditionManager;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.equip.EquipManager;
import com.sh.game.system.equip.script.IEquipScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.identify.IdentifyManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.role.script.IRoleOnMinuteScript;
import com.sh.game.system.secondaryPassword.SecondaryPasswordManager;
import com.sh.game.system.touying.RoleLineUpManager;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.stream.Collectors;


@Script
@Slf4j
public class BackpackScript implements IBackpackScript,
        IEventOnRoleDieScript,
        IEventOnRoleLoginScript,
        IRoleOnMinuteScript,
        IEventOnRoleMidnightScript,
        IEventOnRoleCoinChangedScript,
        IEventOnRoleBeforeDropMonsterScript {

    //丢弃次数限制，help表配置id
    private static final int DiscardCountHelpId = 4013;

    @Override
    public void onRoleDie(Role role, long killerId, String killerName, MapProxy mapProxy, int x, int y, int dropAdd, int dropResist) {
        //保护不掉落条件
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.DIE_NOT_DROP_CONDITION);
        if (globalConfig != null) {
            List<int[]> conditionList = StringUtil.strToIntArrayList(globalConfig.getValue(), Symbol.AND, Symbol.JINHAO);
            if (ConditionUtil.validate(role, conditionList)) {
                return;
            }
        }

        MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, mapProxy.getCfgId());
        if (mapConfig == null) {
            return;
        }

        PkConfig pkConfig = null;
        PkConfig mxConfig = null;
        int pkValue = role.getPkValue();
        for (PkConfig config : ConfigDataManager.getInstance().getList(PkConfig.class)) {
            if (pkValue < config.getStage()) {
                pkConfig = config;
                break;
            }
            if (mxConfig == null || mxConfig.getStage() < config.getStage()) {
                mxConfig = config;
            }
        }
        if (pkConfig == null) {
            if (mxConfig != null) {
                pkConfig = mxConfig;
            }else{
                return;
            }
        }

        int[] dropCount = pkConfig.getDropCount();
        if (dropCount==null || dropCount.length < 2) {
            return;
        }
        int equipDropCount = dropCount[0];
        int bagDropCount = dropCount[1];

        int[] dropRate = pkConfig.getDropRate();
        if (dropRate==null || dropRate.length < 2) {
            return;
        }
        int equipDropRate = dropRate[0];
        int bagDropRate = dropRate[1];

        int[] deathDrop = mapConfig.getDeathDrop() == null ? new int[0] : mapConfig.getDeathDrop();
        if (deathDrop.length < 2) {
            return;
        }
        int equipMapRate = deathDrop[0];
        int bagMapRate = deathDrop[1];

        if (equipMapRate == 0 && bagMapRate == 0) {
            return;
        }

        int bodyCount = calDropCount(equipDropCount, equipDropRate, dropAdd, dropResist, equipMapRate);
        int packCount = calDropCount(bagDropCount, bagDropRate, dropAdd, dropResist, bagMapRate);

        List<Item> drops = new ArrayList<>();
        BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(role);
        drops.addAll(getDropItems(role, equipPlace, bodyCount));
        drops.addAll(getDropItems(role, BackpackConst.Place.BACKPACK, packCount));
        drops.addAll(getDropItems(role, BackpackConst.Place.CHONGWU_EQUIP, bodyCount));
        //策划要求不掉落宠物背包装备 2022/10/8
        //drops.addAll(getDropItems(role, BackpackConst.Place.CHONGWU_BACKPACK, packCount));


        if (drops.isEmpty()) {
            return;
        }
//        drops.forEach(item -> item.setWhere(BackpackConst.Place.BACKPACK.getWhere()));

        List<Item> toubaos = new ArrayList<>();
        SpecialRingCache cache = ConfigCacheManager.getInstance().getCache(SpecialRingCache.class);
        if (cache != null) {
            Iterator<Item> iterator = drops.iterator();
            while (iterator.hasNext()) {
                Item it = iterator.next();
                it.setWhere(BackpackConst.Place.BACKPACK.getWhere());
                // 非破碎特戒掉落，邮件返还
                if (cache.isSpecialRing(it.getCfgId()) && it.eData().getDurable() != it.findItemConfig().getDuraMax()) {
                    iterator.remove();
                    it.eData().setDurable(it.findItemConfig().getDuraMax());
                    MailManager.getInstance().sendMail(role.getId(), SpecialRingConst.TEJIE_BROKEN_MAIL, Collections.singletonList(it));
                }
                // 融合特戒装备掉落
                if (it.eData() != null && cache.isSpecialRing(it.eData().getTjId())) {
                    Item tj = ItemUtil.create(it.eData().getTjId(), 1, LogAction.RING_BROKEN);
                    tj.eData().setDurable(tj.findItemConfig().getDuraMax());
                    it.eData().setTjId(0);
                    MailManager.getInstance().sendMail(role.getId(), SpecialRingConst.TEJIE_BROKEN_MAIL, Collections.singletonList(tj));
                }
                // 非破碎魔器掉落
                if (cache.isDemonEquip(it.getCfgId()) && it.eData().getDurable() != it.findItemConfig().getDuraMax()) {
                    iterator.remove();
                    it.eData().setDurable(it.findItemConfig().getDuraMax());
                    MailManager.getInstance().sendMail(role.getId(), SpecialRingConst.DEMON_EQUIP_BROKEN_MAIL, Collections.singletonList(it));
                }
                // 融合魔器装备掉落
                if (it.eData() != null && cache.isDemonEquip(it.eData().getDemonEquipId())) {
                    Item demonItem = ItemUtil.create(it.eData().getDemonEquipId(), 1, LogAction.DEMON_EQUIP_BROKEN);
                    demonItem.eData().setDurable(demonItem.findItemConfig().getDuraMax());
                    it.eData().setDemonEquipId(0);
                    MailManager.getInstance().sendMail(role.getId(), SpecialRingConst.DEMON_EQUIP_BROKEN_MAIL, Collections.singletonList(demonItem));
                }
                // 投保装备掉落
                if (it.eData() != null && it.eData().getToubao() == 1) {
                    iterator.remove();
                    it.eData().setToubao(0);
                    toubaos.add(it);
                    MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.TOUBAO_DROP,
                            Collections.singletonList(it), mapConfig.getMapName(), killerName, it.findItemConfig().getName());
                }
            }
        }


        StringBuffer sb = new StringBuffer();
        for (Item item : drops) {
            sb.append("、").append(item.findItemConfig().getName());
        }

        if (drops.size() > 0) {
            if (killerName == null || killerName.isEmpty()) {
                MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.DIE_DROP_MAIL_NONAME, null, sb.substring(1));
            } else {
                MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.DIE_DROP_MAIL, null, killerName, sb.substring(1));
            }
        }

        for (Item toubao : toubaos) {
            EquipTouBaoConfig touBaoConfig = ConfigDataManager.getInstance().getById(EquipTouBaoConfig.class,
                    toubao.getCfgId());
            if (touBaoConfig != null) {
                drops.addAll(BoxUtil.openBox(touBaoConfig.getDropBox()));
            }
        }

        mapProxy.dropItem(x, y, drops);
    }

    /**
     * 掉落数量计数
     * 在配表的掉落数量上做概率判断获取真实的掉落数量
     *
     * @param dropCount  掉落数量
     * @param dropRate   pk值对应掉落概率
     * @param dropAdd    击杀玩家增加掉落概率
     * @param dropResist 被击杀玩家抵扣值
     * @param mapRate    地图掉落万分数
     * @return 真实掉落数量
     */
    private int calDropCount(int dropCount, int dropRate, int dropAdd, int dropResist, int mapRate) {
        int count = 0;
        // 张子豪要求这两个值缩小100倍
        int roleDropValue = (dropAdd - dropResist) / 100;

        int finalDropRate = (dropRate + roleDropValue) * mapRate / 10000;
        if (finalDropRate < 0) {
            finalDropRate = 0;
        } else if (finalDropRate > 10000) {
            finalDropRate = 10000;
        }
        for (int i = 0; i < dropCount; i++) {
            if (RandomUtil.isGenerate(10000, finalDropRate)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取掉落道具
     *
     * @param role
     * @param place
     * @param dropCount
     * @return
     */
    private List<Item> getDropItems(Role role, BackpackConst.Place place, int dropCount) {
        List<Item> drops = new ArrayList<>();
        if (dropCount <= 0) {
            return drops;
        }

        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage storage = backpack.fetchStorage(place);
        if (storage == null || storage.getData().isEmpty()) {
            return drops;
        }
        List<Item> items = new ArrayList<>();
        for (Item item : storage.getData().values()) {
            if (item == null || item.findItemConfig().getDiaoluo() != 1) {
                continue;
            }
            //已穿戴法宝不掉落 特殊处理
            if (ArrayUtils.contains(BackpackConst.Browse.EQUIP_PACK_ALL, place)
                    && item.findItemConfig().getType() >= EquipConst.EquipIndex.MAGIC_WEAPON_1.getCls()
                    && item.findItemConfig().getType() <= EquipConst.EquipIndex.MAGIC_WEAPON_8.getCls()) {
                continue;
            }

            items.add(item);
        }
        Collections.shuffle(items);
        int count = Math.min(dropCount, items.size());
        for (int i = 0; i < count; i++) {
            /**
             *  道具有可能where 是错误的， 导致不能扣除，
             *  所以本处强制给where背包物品设置where
             */
            Item dropItem = items.get(i);
            if (dropItem.getWhere() != place.getWhere()) {
                dropItem.setWhere(place.getWhere());
            }

            drops.add(dropItem);
        }

        if (!drops.isEmpty()) {
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(drops);
            if (!stash.commit(role, LogAction.DISCARD, false, null)) {
                drops.clear();
            }
        }
        return drops;
    }

    @Override
    public void onRoleCoinChanged(Role role, Map<Integer, Long> changes, int action) {
        if (!changes.containsKey(BagConst.ItemId.HORSE_COIN)) {
            return;
        }
        role.getRoleDaily().getCoinGains().merge(BagConst.ItemId.HORSE_COIN, changes.get(BagConst.ItemId.HORSE_COIN), Long::sum);
        DataCenter.updateData(role.getRoleDaily());
    }

    @Override
    public void onRoleLogin(Role role) {
        clearOverdueItem(role);
//        repairData(role);
    }

    @Override
    public void onRoleMinute(Role role) {
        clearOverdueItem(role);
    }

    /**
     * 清理过期道具
     *
     * @param role
     */
    private void clearOverdueItem(Role role) {
        Backpack backpack = role.getBackpack();
        int now = TimeUtil.getNowOfSeconds();
        Item item;
        List<Item> decr = new ArrayList<>();
        for (Map.Entry<Integer, Storage> entry : backpack.getData().entrySet()) {
            for (Map.Entry<Integer, Item> iEntity : entry.getValue().getData().entrySet()) {
                item = iEntity.getValue();
                if (item == null || item.getExpire() <= 0 || item.getExpire() > now) {
                    continue;
                }
                item.setWhere(entry.getKey());
                decr.add(item);
            }
        }
        if (decr.isEmpty()) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        for (Item i : decr) {
            stash.decrease(i);
        }
        if (!stash.commit(role, LogAction.ITEM_EXPIRE)) {
            return;
        }
        ItemConfig config;
        int[] overTime;
        for (Item i : decr) {
            config = i.findItemConfig();
            if (config == null) {
                continue;
            }
            overTime = config.getOverTime();
            if (overTime == null || overTime.length < 4) {
                continue;
            }
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.ITEM_OVERDUE,
                    Arrays.asList(ItemUtil.create(overTime[2], overTime[3] * i.findCount(), LogAction.ITEM_EXPIRE)), config.getName());
        }

    }

    @Override
    public void onRoleMidnight(Role role) {
        role.getRoleDaily().getPickups().clear();
        role.getRoleDaily().getCoinGains().clear();
        role.getRoleDaily().setDiscardCount(0);
        role.getRoleDaily().setDailyShenWeiBossDropCount(0);
        role.getRoleDaily().setDailyEdictCount(0);
        role.getRoleDaily().setDailyPhysicalPower(false);
        role.getRoleDaily().setGoldDailyCount(0);
        DataCenter.updateData(role.getRoleDaily());
        Backpack backpack = role.getBackpack();
        BackpackStash stash = new BackpackStash(role);
        for (Storage storage : backpack.getData().values()) {
            for (Item item : storage.getData().values()) {
                if (item == null || item.findItemConfig() == null) {
                    continue;
                }
                if (item.findItemConfig().getUseType() == 212) {
                    stash.update(item, update -> {
                        List<Integer> params = update.getParams();
                        if (params == null) {
                            params = new ArrayList<>();
                            params.add(0);
                            update.setParams(params);
                        }
                        params.set(0, params.get(0) + 1);
                    });
                }
            }
        }
        stash.commit(role, LogAction.ITEM_UPDATE);
        //祭坛次数处理
        int limit = Integer.parseInt(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_COUNT).split(Symbol.JINHAO)[0]);
        if (role.getJiTanCount() < limit) {
            int buyCount = CountManager.getInstance().getCount(role, CountConst.CountType.JI_TAN_BOSS_BUY_COUNT);
            int changeCount = CountManager.getInstance().getCount(role, CountConst.CountType.JI_TAN_BOSS_CHANGE_COUNT);
            CountManager.getInstance().count(role, CountConst.CountType.JI_TAN_BOSS_BUY_COUNT, CountConst.KEY_VALUE_DEFAULT, -buyCount);
            CountManager.getInstance().count(role, CountConst.CountType.JI_TAN_BOSS_CHANGE_COUNT, CountConst.KEY_VALUE_DEFAULT, -changeCount);
        }
        role.proxyCall(proxy -> {
            proxy.updateJiTanCount(role.getId(), role.getJiTanCount());
            proxy.updateShenWeiBossDropCount(role.getRoleDaily().getDailyShenWeiBossDropCount(), role.findFaQiHuiYuanConfig().getShenwei());
            proxy.updateGoldDailyCount(role.getRoleDaily().getGoldDailyCount());
        });

        //清理积分道具防止数量溢出
        long scoreNum = backpack.getCoin().getOrDefault(ItemConst.ItemId.SCORE, 0L);
        if (scoreNum > 0) {
            BackpackStash scoreStash = new BackpackStash(role);
            scoreStash.decrease(ItemConst.ItemId.SCORE, scoreNum);
            scoreStash.commit(role, LogAction.ITEM_UPDATE);
        }

        //新号送一次暗黑神殿时间
        List<Integer> firstDarkTempleParam = GlobalUtil.findJingHaoList(GameConst.GlobalId.FIRST_DARK_TEMPLE_REWARD);
        if (firstDarkTempleParam != null && firstDarkTempleParam.size() >= 3) {
            if (firstDarkTempleParam.get(0) >= AppContext.getContext().getOpenDays()) {
                RoleDaily roleDaily = role.getRoleDaily();
                if (!roleDaily.isFirstDarkTempleReward()) {
                    log.info("暗黑神殿-第一次赠送时间, roleId: {} , roleName: {} , 开服天数: {}", role.getRoleId(), role.getName(), firstDarkTempleParam.get(0));
                    roleDaily.setFirstDarkTempleReward(true);
                    DataCenter.updateData(roleDaily);

                    BackpackStash darkTempleStash = new BackpackStash(role);
                    darkTempleStash.increase(firstDarkTempleParam.get(1), firstDarkTempleParam.get(2));
                    if (!darkTempleStash.commit(role, LogAction.DAILY_DARK_TEMPLE)) {
                        log.error("暗黑神殿-第一次赠送时间失败, roleId: {} , roleName: {} , 开服天数: {} , itemConfigId: {} , count: {}", role.getRoleId(), role.getName(), firstDarkTempleParam.get(0), firstDarkTempleParam.get(1), firstDarkTempleParam.get(2));
                    }
                }
            }
        }

        //暗黑神殿每日时间
        if (!ConditionUtil.validate(role, GlobalUtil.findConditions(GameConst.GlobalId.CONDITION_DARK_TEMPLE))) {
            return;
        }
        List<int[]> dailyDarkTemple = GlobalUtil.findItemCost(GameConst.GlobalId.DAILY_DARK_TEMPLE);
        if (dailyDarkTemple == null || dailyDarkTemple.isEmpty()) {
            return;
        }
        BackpackStash rewardStash = new BackpackStash(role);
        rewardStash.increase(dailyDarkTemple);
        if (rewardStash.commit(role, LogAction.DAILY_DARK_TEMPLE)) {
            ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
            player.buyKeepItem(role, dailyDarkTemple.get(0)[0]);
        }


    }

    @Override
    public boolean costItem(Role role, List<int[]> costItems, LogAction logAction, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place... places) {
        if (costItems.isEmpty()) {
            return true;
        }
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        if (AppContext.getProcessor() == ProcessorId.SERVER_PLAYER) {
            boolean success = doCostItem(role, costItems, logAction, places);
            if (callback != null) {
                BackpackStashCommitRetNotice retNotice = new BackpackStashCommitRetNotice();
                retNotice.setRoleId(role.getId());
                retNotice.setSuccess(success);
                callback.setNotice(retNotice);
                callback.doAction();
            }
            return success;
        } else {
            CostItemNotice notice = new CostItemNotice();
            notice.setRoleId(role.getId());
            notice.setAction(logAction);
            notice.setSourceProcessorId(AppContext.getProcessor());
            notice.setWaitTime(30000);
            notice.setCostItems(costItems);
            notice.setPalces(places);
            notice.setHandler(new TimeoutHandler() {
                @Override
                public void timeout(TimeProcessNotice notice) {
                    if (callback != null) {
                        BackpackStashCommitRetNotice retNotice = new BackpackStashCommitRetNotice();
                        retNotice.setRoleId(role.getId());
                        retNotice.setSuccess(false);
                        callback.setNotice(retNotice);
                        callback.doAction();
                    }
                }
            });
            notice.setCallback(callback);
            if (callback == null) {
                notice.setNoCallback(true);
            }
            role.sendNotice(ProcessorId.SERVER_PLAYER, notice);
            return true;
        }
    }

    @Override
    public boolean doCostItem(Role role, List<int[]> costItems, LogAction logAction, BackpackConst.Place... places) {
        if (costItems.isEmpty()) {
            return true;
        }
        if (places.length <= 0) {
            return false;
        }
        Backpack backpack = role.getBackpack();
        List<int[]> coinCost = new ArrayList<>();
        List<int[]> itemCost = new ArrayList<>();

        for (int[] costItem : costItems) {
            int itemId = costItem[0];
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            if (itemConfig == null) {
                continue;
            }
            if (itemConfig.getItemtype() == BagConst.ItemType.COIN) {
                coinCost.add(costItem);
            } else {
                itemCost.add(costItem);
            }
        }

        //判断货币够不够
        for (int[] costItem : coinCost) {
            int itemId = costItem[0];
            int count = costItem[1];
            if (count <= 0) {
                return false;
            }
            long myCount = backpack.fetchCountLByCfgId(itemId, places);
            if (myCount < count) {
                return false;
            }
        }

        //判断道具够不够
        for (int[] costItem : itemCost) {
            long total = 0;
            int itemId = costItem[0];
            int count = costItem[1];
            if (count <= 0) {
                return false;
            }
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            if (itemConfig == null) {
                continue;
            }
            for (BackpackConst.Place place : places) {
                total += backpack.fetchCountLByCfgId(itemId, place);
            }
            if (total < count) {
                return false;
            }
        }

        ResBackpackChangedMessage msg = new ResBackpackChangedMessage();
        msg.setAction(logAction.getCode());

        //处理货币
        Map<Integer, Long> coinChanges = new HashMap<>();
        Map<Integer, Long> oldValues = new HashMap<>();
        Map<Integer, Long> coin = backpack.getCoin();
        BackpackCoinBean coinBean;
        long own;
        for (int[] ints : coinCost) {
            int itemId = ints[0];
            int count = ints[1];
            //先扣绑定
            own = coin.getOrDefault(itemId, 0L);
            long firstCost = Math.min(own, count);
            if (firstCost > 0) {
                long nValue = own - firstCost;
                coin.put(itemId, nValue);

                coinChanges.put(itemId, -firstCost);
                oldValues.put(itemId, own);
                coinBean = new BackpackCoinBean();
                coinBean.setItemId(itemId);
                coinBean.setCount(nValue);
                msg.getChangedCoins().add(coinBean);
            }
            long leftCount = count - firstCost;
            //再扣非绑
            if (leftCount > 0) {
                ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
                if (itemConfig.getBind() > 0) {
                    long bindCount = coin.getOrDefault(itemConfig.getBind(), 0L);
                    if (bindCount > 0) {
                        coin.put(itemConfig.getBind(), bindCount - leftCount);

                        coinChanges.put(itemConfig.getBind(), -leftCount);
                        oldValues.put(itemConfig.getBind(), bindCount);
                        coinBean = new BackpackCoinBean();
                        coinBean.setItemId(itemConfig.getBind());
                        coinBean.setCount(coin.get(itemConfig.getBind()));
                        msg.getChangedCoins().add(coinBean);
                    }
                }
            }
        }
        //处理装备道具
        List<ItemChange> itemChanges = new ArrayList<>();
        BackpackChangeBean bean;
        for (int[] item : itemCost) {
            int itemId = item[0];
            int count = item[1];
            //这里需要把获取的道具重新排序来保证扣的顺序 因为多个背包又区分绑非绑
            for (BackpackConst.Place place : places) {
                List<Item> items = backpack.findItemByCfgId(itemId, place);
                List<Item> collect = items.stream().sorted((o1, o2) -> ItemCostComparator.COMPARATOR.compare(o1, o2)).collect(Collectors.toList());
                Storage storage = backpack.fetchStorage(place);
                bean = new BackpackChangeBean();
                bean.setWhere(place.getWhere());
                for (Item bagItem : collect) {
                    if (count <= 0) {
                        break;
                    }
                    int index = bagItem.getIndex();
                    if (bagItem.findCount() > count) {
                        bagItem.setCount(bagItem.findCount() - count);
                        count = 0;
                    } else {
                        count -= bagItem.findCount();
                        bagItem = null;
                    }

                    itemChanges.add(storage.update(index, bagItem, logAction.getCode()));

                    BackpackGridBean gridBean = new BackpackGridBean();
                    gridBean.setIndex(index);
                    gridBean.setItem(ItemUtil.packCommonItemBean(bagItem));
                    bean.getChanges().add(gridBean);
                }
                msg.getChangedItems().add(bean);
            }
        }
        role.updateBackpack(coinChanges, logAction.getCode());
        role.coinLog(coinChanges, oldValues, logAction.getCode());
        role.updateBackpack(itemChanges);
        role.itemLog(itemChanges, logAction);
        role.updateBackpack(backpack, msg, logAction);
        return true;
    }

    @Override
    public Boolean addItem(Role role, List<int[]> addItems, LogAction logAction, boolean tip, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place place) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(addItems, place);
        return stash.commit(role, logAction, tip, callback);
    }

    @Override
    public void reqBuyJiTan(Role role) {
        int buyCount = Integer.parseInt(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_BUY).split(Symbol.JINHAO)[0]);
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.JI_TAN_BOSS_BUY_COUNT);
        if (count >= buyCount) {
            return;
        }
        int itemId = Integer.parseInt(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_BUY).split(Symbol.JINHAO)[1]);
        int itemCount = Integer.parseInt(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_BUY).split(Symbol.JINHAO)[2]);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, itemCount);
        if (!stash.commit(role, LogAction.JITAN)) {
            return;
        }
        CountManager.getInstance().count(role, CountConst.CountType.JI_TAN_BOSS_BUY_COUNT);
        role.proxyCall(proxy -> proxy.updateJiTanCount(role.getId(), role.getJiTanCount()));

        ResBuyJiTanMessage msg = new ResBuyJiTanMessage();
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void itemLog(Role role, List<ItemChange> changes, LogAction logAction) {
        if (changes.isEmpty()) {
            return;
        }
        int changeCount;
        TaskCache taskCache = ConfigCacheManager.getInstance().getCache(TaskCache.class);
        for (ItemChange change : changes) {
            int itemConfigId = change.getOItem() != null ? change.getOItem().getCfgId() : change.getNItem().getCfgId();
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemConfigId);
            if (itemConfig == null) {
                continue;
            }
            int oldValue = change.getOItem() != null ? change.getOItem().findCount() : 0;
            int newValue = change.getNItem() != null ? change.getNItem().findCount() : 0;
            if (LogAction.EQUIP_UPGRADE_REWARD.getCode() == change.getLogAction()) {
                changeCount = newValue;
                itemConfigId = change.getNItem().getCfgId();
            } else {
                changeCount = newValue - oldValue;
            }
            if (changeCount == 0) {
                continue;
            }
            if (changeCount > 0) {

                if (taskCache.isItemGoal(itemConfigId)) {
                    GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_OBTAIN, itemConfigId, changeCount);
                    GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_OBTAIN_ALREADY);
                }
                if (taskCache.isItemLocationGoal(itemConfig.getType())) {
                    GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.EQUIP_LEVEL_OBTAIN, itemConfigId);
                }
                boolean isBagCondition = taskCache.isBagCondition(itemConfigId);
                boolean isBodyCondition = taskCache.isBodyCondition(itemConfigId);
                if (isBagCondition) {
                    ConditionManager.getInstance().onCondition(role, ConditionTypeConst.ITEM_ENOUGH_IN_BAG);
                }
                if (isBodyCondition) {
                    ConditionManager.getInstance().onCondition(role, ConditionTypeConst.ITEM_ENOUGH_IN_BODY);
                }
                if (isBagCondition || isBodyCondition) {
                    ConditionManager.getInstance().onCondition(role, ConditionTypeConst.ITEM_ENOUGH);
                }

                Item item = change.getOItem() != null ? change.getOItem() : change.getNItem();
                if (itemConfig.getXiyou() == ItemConst.Announce.GET.getValue()) {
                    AnnounceManager.getInstance().post(GlobalUtil.getGlobalInt(GameConst.GlobalId.ITEM_DROP_ANNOUNCE), 0, role, item);
                }

                // 萃取奖励获取公告
                if (logAction == LogAction.EXTRACT_REWARD) {
                    if (itemConfig.getXiyou() == ItemConst.Announce.EXTRACT_GET.getValue()
                            && ConditionUtil.validate(role, itemConfig.getXiyou_condition())) {
                        AnnounceManager.getInstance().post(AnnounceConst.TYPE.EXTRACT_ACQUIRE, 0, role, item);
                    }
                }

            }
            if (itemConfig.getLog() == 1) {
                long itemCount = role.getBackpack().fetchCountLByCfgId(itemConfigId, BackpackConst.Place.values());
                try {
                    String map = "";
                    Item item = change.getOItem() != null ? change.getOItem() : change.getNItem();
                    EquipData equipData = item.getEquipData();
                    if (equipData != null && equipData.getOriginInfo().size() > 1) {
                        map = equipData.getOriginInfo().get(1);
                    }
                    RoleItemLog roleItemLog = new RoleItemLog(role);
                    roleItemLog.setTime(TimeUtil.getNowOfSeconds());
                    roleItemLog.setItemId(itemConfigId);
                    roleItemLog.setOldValue(oldValue);
                    roleItemLog.setChange(changeCount);
                    roleItemLog.setZhanYiDengJi(role.getZhanYiLevel()+"+"+role.getLevel());
                    roleItemLog.setNewValue(itemCount);
                    roleItemLog.setAction(logAction.getCode());
                    roleItemLog.setItemType(itemConfig.getItemtype());
                    roleItemLog.setItemName(itemConfig.getName());
                    roleItemLog.setXingWei(logAction.getComment());
                    roleItemLog.setMap(map);
                    roleItemLog.setUniqueId(item.getId());
                    roleItemLog.setAccount(role.getAccount());
                    roleItemLog.submit();

//                     Storage storage = role.getBackpack().fetchStorage(change.getPlace());
//                     log.info("玩家获取道具日志，玩家id {} name {} where {} 总数量 {} 背包内存地址 {} 背包数据内存地址 {} 行为 {}"
//                             , role.getId(), role.getName(), change.getPlace(), itemCount,System.identityHashCode(storage), System.identityHashCode(storage.getData()), logAction);
//                     Item oItem = change.getOItem();
//                     Item nItem = change.getNItem();
//                     if (oItem != null) {
//                         log.info("玩家获取道具日志，老道具，玩家id {} name {} index {} 道具唯一id {} 配置id {} 数量 {}", role.getId(), role.getName(), oItem.getIndex(), oItem.getId(), oItem.getCfgId(), oItem.getCount());
//                     }
//                     if (nItem != null) {
//                         log.info("玩家获取道具日志，新道具，玩家id {} name {} index {} 道具唯一id {} 配置id {} 数量 {}", role.getId(), role.getName(), nItem.getIndex(), nItem.getId(), nItem.getCfgId(), nItem.getCount());
//                     }
                } catch (Exception e) {
                    log.error("道具日志序列化失败", e);
                }
            }
        }
    }

    @Override
    public void coinLog(Role role, Map<Integer, Long> changes, Map<Integer, Long> oldValues, int action) {
        //处理日志
        RoleItemLog itemLog;
        for (Map.Entry<Integer, Long> integerLongEntry : oldValues.entrySet()) {
            int key = integerLongEntry.getKey();
            long value = integerLongEntry.getValue();
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, key);
            if (itemConfig == null || itemConfig.getLog() != 1) {
                continue;
            }
            long changeCount = Math.abs(changes.getOrDefault(key, 0L));
            if (changeCount == 0) {
                continue;
            }
            if ((key == BagConst.ItemId.ROLE_EXP || key == BagConst.ItemId.HERO_EXP) && changeCount < 500000) {
                continue;
            }
            if ((key == BagConst.ItemId.COIN || key == 20) && changeCount < 10000) {
                continue;
            }
            itemLog = new RoleItemLog(role);
            itemLog.setUserId(role.getUid());
            itemLog.setItemId(key);
            itemLog.setAction(action);
            itemLog.setOldValue(value);
            itemLog.setNewValue(value + changes.get(key));
            itemLog.setChange(changes.get(key));
            itemLog.setItemType(itemConfig.getItemtype());
            itemLog.setItemName(itemConfig.getName());
            itemLog.setAccount(role.getAccount());
            itemLog.setTime(TimeUtil.getNowOfSeconds());
            itemLog.submit();
        }

        int cionType=0;
        Set<Integer> integers = changes.keySet();
        Iterator<Integer> iterator = integers.iterator();
        while (iterator.hasNext()){
            cionType=iterator.next();
        }
        if (cionType==1||cionType==2||cionType==3||cionType==4) {
            RoleCurrencyLog currencyLog = new RoleCurrencyLog(role);
            currencyLog.setUserId(role.getUid());
            currencyLog.setRoleName(role.getName());
            currencyLog.setTime(TimeUtil.getNowOfSeconds());
            currencyLog.setZhanYiDengJi(role.getZhanYiLevel() + "+" + role.getLevel());
            currencyLog.setChangeNum(changes.get(cionType));
            currencyLog.setNowNum(changes.get(cionType) + oldValues.get(cionType));
            currencyLog.setMapId(role.getMapCfgId());
            currencyLog.setCurrencyId(cionType);
            if (changes.get(cionType) < 0) {
                currencyLog.setChangeType("-");
            } else {
                currencyLog.setChangeType("+");
            }
            currencyLog.setAccount(role.getAccount());
            currencyLog.submit();
        }
    }

    /**
     * 请求领取每日体力
     *
     * @param role 角色
     */
    @Override
    public void reqDailyPhysicalPower(Role role) {
        RoleDaily roleDaily = role.getRoleDaily();
        if (roleDaily.isDailyPhysicalPower()) {
            return;
        }

        roleDaily.setDailyPhysicalPower(true);
        DataCenter.updateData(roleDaily);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(GlobalUtil.findItemCost(GameConst.GlobalId.PHYSICAL_POWER_RECEIVE));
        if (!stash.commit(role, LogAction.DAILY_PHYSICAL_POWER_RECEIVE)) {
            return;
        }

        reqPhysicalPowerInfo(role);
    }

    /**
     * 请求每日体力信息
     *
     * @param role 角色
     */
    @Override
    public void reqPhysicalPowerInfo(Role role) {
        RoleDaily roleDaily = role.getRoleDaily();
        ResPhysicalPowerInfoMessage msg = new ResPhysicalPowerInfoMessage();
        msg.setReceive(roleDaily.isDailyPhysicalPower());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 穿脱校验
     *
     * @param role         角色
     * @param sourceConfig 源道具配置
     * @param targetWhere  目标背包
     * @param targetIndex  目标位置
     * @param sourceItem   源道具
     * @return boolean true: 可穿脱    false: 不可穿脱
     */
    @Override
    public boolean mergeVerify(Role role, ItemConfig sourceConfig, int targetWhere, int targetIndex, Item sourceItem) {

        //缴械不可穿戴
        long now = TimeUtil.getNowOfMills();
        EquipData sourceEquipData = sourceItem.getEquipData();
        if (targetWhere == BackpackConst.Place.EQUIP.getWhere() && sourceEquipData != null && now < sourceEquipData.getDisarmTime()) {
            return false;
        }

        //图录道具不允许移动至其他背包
        if (sourceConfig.getItemtype() == BagConst.ItemType.MONSTER_CARD && targetWhere != BackpackConst.Place.MONSTER_CARD.getWhere()) {
            log.error("背包道具移动-图录道具不允许移动至其他背包, roleId: {} , roleName: {} , itemId: {} itemConfigId: {} , targetWhere: {} , targetIndex: {}", role.getRoleId(), role.getName(), sourceItem.getId(), sourceConfig.getId(), targetWhere, targetIndex);
            return false;
        }

        //帝王图鉴道具不允许移动至其他背包
        if (sourceConfig.getItemtype() == BagConst.ItemType.MONSTER_CARD_DIWANG && targetWhere != BackpackConst.Place.MONSTER_CARD_DIWANG.getWhere()) {
            log.error("背包道具移动-帝王图录道具不允许移动至其他背包, roleId: {} , roleName: {} , itemId: {} itemConfigId: {} , targetWhere: {} , targetIndex: {}", role.getRoleId(), role.getName(), sourceItem.getId(), sourceConfig.getId(), targetWhere, targetIndex);
            return false;
        }

        //背包神器道具不允许移动至其他背包
        if (sourceConfig.getItemtype() == BagConst.ItemType.BAG_SHENQI
                && !BagShenQiConst.findBagShenQIBackPack().contains(targetWhere)) {
            log.error("背包道具移动-背包神器道具不允许移动至其他背包, roleId: {} , roleName: {} , itemId: {} itemConfigId: {} , targetWhere: {} , targetIndex: {}", role.getRoleId(), role.getName(), sourceItem.getId(), sourceConfig.getId(), targetWhere, targetIndex);
            return false;
        }

        //普通道具不可移动到神器隐藏背包
        if (sourceConfig.getItemtype() != BagConst.ItemType.BAG_SHENQI
                && targetWhere == BackpackConst.Place.BAG_SHENQI_BACKPACK.getWhere()) {
            log.error("背包道具移动-普通道具不允许移动至神器背包, roleId: {} , roleName: {} , itemId: {} itemConfigId: {} , targetWhere: {} , targetIndex: {}", role.getRoleId(), role.getName(), sourceItem.getId(), sourceConfig.getId(), targetWhere, targetIndex);
            return false;
        }

        return true;
    }

    /**
     * Boss掉落前处理,可以对数量等进行修改等操作
     *
     * @param role          角色
     * @param monsterCfgId  怪物配置id
     * @param dropType      掉落类型
     * @param magCfgID      地图配置id
     * @param x             x坐标
     * @param y             y坐标
     * @param dropIds       掉落物品列表
     * @param hung
     */
    @Override
    public void onRoleBeforeDropMonster(Role role, int monsterCfgId, int dropType, int magCfgID, int x, int y, List<Item> dropIds, int hung, int dailyType) {
        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterCfgId);
        if (monsterConfig == null) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        if (monsterConfig.getDropCost() != null) {
            stash.decrease(monsterConfig.getDropCost());
        }
        if (!stash.commit(role, LogAction.MONSTER_DROP_COST, false)) {
            //无法扣除则不掉落
            dropIds.clear();
            TipUtil.show(role.getRoleId(), CommonTips.体力不足);
        }
    }

    /**
     * 修复背包道具数据
     *
     * @param role 角色
     */
//    private void repairData(Role role) {
//        //将所有图录移动到图录背包
//        RoleDaily roleDaily = role.getRoleDaily();
//        if (!roleDaily.isRepairMonsterCard()) {
//            //查询所有能存放图录卡片的背包
//            BackpackConst.Place[] BACKPACK_MONSTER_CARD_ALL = new BackpackConst.Place[]{BackpackConst.Place.BACKPACK
//                    , BackpackConst.Place.BACKPACK_WOW
//                    , BackpackConst.Place.BACKPACK_PRIVILEGE
//                    , BackpackConst.Place.WAREHOUSE, BackpackConst.Place.WAREHOUSE_ONE
//                    , BackpackConst.Place.WAREHOUSE_TWO, BackpackConst.Place.WAREHOUSE_THREE
//                    , BackpackConst.Place.WAREHOUSE_FOUR};
//            Backpack backpack = DataCenter.get(Backpack.class, role.getRoleId());
//            List<Item> itemList = backpack.findItemByItemType(BagConst.ItemType.MONSTER_CARD, BACKPACK_MONSTER_CARD_ALL);
//
//            //查找背包空位
//            StorageStash storageStash = new StorageStash(role, BackpackConst.Place.MONSTER_CARD);
//            Queue<Integer> empties = storageStash.getEmpties();
//            if (empties.size() < itemList.size()) {
//                log.error("修复-图录道具-格子不足等待下次登录修复, roleId: {} , roleName: {} , empties: {} < {}", role.getRoleId(), role.getName(), empties.size(), itemList.size());
//                return;
//            }
//
//            for (Item item : itemList) {
//                Integer emptyIndex = empties.remove();
//                if (emptyIndex == null) {
//                    log.error("修复-图录道具-格子为空等待下次登录修复, roleId: {} , roleName: {} , empties: {} < {}", role.getRoleId(), role.getName(), empties.size(), itemList.size());
//                    return;
//                }
//                BackpackManager.getInstance().reqMerge(role, item.getWhere(), item.getIndex(), BackpackConst.Place.MONSTER_CARD.getWhere(), emptyIndex, Math.toIntExact(item.getCount()), LogAction.ROLE_INIT.getCode());
//            }
//            BackpackManager.getInstance().reqSort(role, BackpackConst.Place.MONSTER_CARD.getWhere());
//            roleDaily.setRepairMonsterCard(true);
//            DataCenter.updateData(roleDaily);
//            log.info("修复-图录道具, roleId: {} , roleName: {} itemList: {}", role.getRoleId(), role.getName(), itemList);
//        }
//    }

    @Override
    public Item findItemByUniqueId(Role role, long uniqueId) {
        List<Item> list = role.getBackpack().findItemListByUniqueId(uniqueId);
        if (list.isEmpty()) {
            return null;
        }
        if (list.size() > 1) {
            log.error("道具使用，玩家背包出现两个唯一id相同的道具，玩家id {} name {} 道具信息 {}", role.getId(), role.getName(), JSON.toJSONString(list));
            // 一个个删，复制出来的道具一起删除或许会失败
            for (Item item : list) {
                BackpackStash stash = new BackpackStash(role);
                stash.decrease(item);
                if (!stash.commit(role, LogAction.BACK_REMOVE)) {
                    log.error("道具使用，玩家背包出现两个唯一id相同的道具，且移除失败，玩家id {} name {} ", role.getId(), role.getName());
                }
            }
            return null;
        }
        return list.get(0);
    }

    @Override
    public void reqCoin(Role role) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        ResBackpackCoinMessage msg = new ResBackpackCoinMessage();
        for (Map.Entry<Integer, Long> entry : backpack.getCoin().entrySet()) {
            BackpackCoinBean bean = new BackpackCoinBean();
            bean.setItemId(entry.getKey());
            bean.setCount(entry.getValue());
            msg.getCoins().add(bean);
        }

        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqGrid(Role role, int where) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        if (backpack == null) {
            return;
        }
        int place = where;
        if (where == 0) {
            place = RoleLineUpManager.getInstance().getUseEquip(role).getWhere();
        }
        Storage storage = backpack.fetchStorage(place);
        if (storage == null) {
            return;
        }

        ResBackpackGridMessage msg = new ResBackpackGridMessage();
        msg.setWhere(where);
        for (Map.Entry<Integer, Item> entry : storage.getData().entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            BackpackGridBean bean = new BackpackGridBean();
            bean.setIndex(entry.getKey());
            Item item = entry.getValue();
            CommonItemBean commonItemBean = ItemUtil.packCommonItemBean(item);

            // 将装备上绑定的来源信息赋予msg
            if (item != null && item.getEquipData() != null && item.getEquipData().getOriginInfo() != null) {
                ItemFromBean fromInfo = new ItemFromBean();
                fromInfo.setAction(item.getEquipData().getOriginInfoType());
                fromInfo.getParams().addAll(item.getEquipData().getOriginInfo());
                commonItemBean.setFrom(fromInfo);
            }

            bean.setItem(commonItemBean);
            msg.getItems().add(bean);
        }
        MessageUtil.sendMsg(msg, role.getId());

        ResBackpackUnlockMessage unlockMessage = new ResBackpackUnlockMessage();
        unlockMessage.setWhere(where);
        unlockMessage.setUnlocked(storage.findUnlockCount());
        MessageUtil.sendMsg(unlockMessage, role.getId());
    }

    @Override
    public void reqUnlock(Role role, int where, int count) {
        if (count <= 0) {
            return;
        }
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(where);
        if (storage == null) {
            return;
        }

        StorageGrid grid = storage.grid();
        int unlocked = storage.getUnlocked();
        List<int[]> cost = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            StorageConfig config = grid.getUnlock().get(unlocked + 1);
            if (config == null) {
                return;
            }
            if (!ConditionUtil.validate(role, config.getCondition())) {
                break;
            }
            unlocked++;
            cost.addAll(config.getCost());
        }
        // 格子数量没变化
        if (unlocked == storage.getUnlocked()) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(cost);
        if (!stash.commit(role, LogAction.UNLOCK_BAG_GRID_COST)) {
            return;
        }
        storage.setUnlocked(unlocked);
        DataCenter.updateData(backpack);
        log.info("背包#解锁格子#玩家：{}，昵称：{}，解锁背包：{}，格子数：{}，解锁后拥有格子数：{}",
                role.getRoleId(), role.getName(), where, count, unlocked);

        ResBackpackUnlockMessage msg = new ResBackpackUnlockMessage();
        msg.setWhere(where);
        msg.setUnlocked(storage.findUnlockCount());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqSort(Role role, int where) {
        disarmSort(role, where);
        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage storage = backpack.fetchStorage(where);
        if (storage == null) {
            return;
        }

        // log.info("玩家请求背包整理，整理前记录，where {} 背包详情 {}", where, JSON.toJSONString(storage));

        List<Item> items = storage.getData().values().stream().filter(Objects::nonNull).sorted(Comparator.comparingInt(Item::order)).collect(Collectors.toList());
        items.sort((o1, o2) -> {
            if (o1 == null || o2 == null) {
                return 0;
            }
            ItemConfig o1Config = ConfigDataManager.getInstance().getById(ItemConfig.class, o1.getCfgId());
            ItemConfig o2Config = ConfigDataManager.getInstance().getById(ItemConfig.class, o2.getCfgId());
            if (o1Config == null || o2Config == null) {
                return 0;
            }
            // 先根据order排序
            if (o1Config.getSort() > o2Config.getSort()) {
                return 1;
            } else if (o1Config.getSort() < o2Config.getSort()) {
                return -1;
            } else {
                if (o1Config.getId() > o2Config.getId()) {
                    return 1;
                } else if (o1Config.getId() == o2Config.getId()) {
                    return 0;
                } else {
                    return -1;
                }
//                return o1Config.getId() > o2Config.getId() ? 1 : -1;
            }
        });


        int size = items.size();
        for (int i = 0; i < size; i++) {
            Item item = items.get(i);
            if (item == null || item.getCfgId() <= 0 || item.findCountLong() <= 0) {
                continue;
            }
            ItemConfig config = item.findItemConfig();
            if (config == null) {
                item.setCfgId(0);
                continue;
            }
            int overlaying = config.getOverlying();
            if (item.findCountLong() >= overlaying) {
                continue;
            }

            for (int j = i + 1; j < size; j++) {
                Item next = items.get(j);
                if (next == null || next.findCount() <= 0 || next.findCount() >= next.findItemConfig().getOverlying()) {
                    continue;
                }
                //绑定的sort和非绑相同 所以这里注意下
                if (next.getCfgId() != item.getCfgId() && next.findItemConfig().getBind() != item.getCfgId() && config.getBind() != next.getCfgId()) {
                    break;
                }
                if (next.getCfgId() == item.getCfgId()) {
                    long count = item.findCountLong() + next.findCountLong();
                    if (count <= overlaying) {
                        next.setCount(0);
                    } else {
                        next.setCount(count-overlaying);
                        count = overlaying;
                    }
                    item.setCount(count);
                    if (count >= overlaying) {
                        break;
                    }
                }
            }
        }

        items.removeIf(item -> item == null || item.getCfgId() <= 0L || item.findCount() <= 0L);

        Map<Integer, Item> storageItems = new HashMap<>();
        for (int index = 0; index < items.size(); index++) {
            storageItems.put(index + 1, items.get(index));
        }
        storage.setData(storageItems);

        // log.info("玩家请求背包整理，整理后记录，where {} 背包详情 {}", where, JSON.toJSONString(storage));

        reqGrid(role, where);
    }

    @Override
    public void reqMerge(Role role, int sourceWhere, int sourceIndex, int targetWhere, int targetIndex, int count, int logAction) {
        if (role == null) {
            return;
        }

        boolean result = merge(role, sourceWhere, sourceIndex, targetWhere, targetIndex, count, logAction);

        ResBackpackMergeMessage msg = new ResBackpackMergeMessage();
        msg.setSourceWhere(sourceWhere);
        msg.setSourceIndex(sourceIndex);
        msg.setResult(result);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 具体穿脱的逻辑 包括所有类型宝石、背包、仓库等等 其次换位置这种也是 因为相当于脱穿
     *
     * @param role
     * @param sourceWhere
     * @param sourceIndex
     * @param targetWhere
     * @param targetIndex
     * @param count
     * @return
     */
    private boolean merge(Role role, int sourceWhere, int sourceIndex, int targetWhere, int targetIndex, int count, int logAction) {
        if (count <= 0) {
            return false;
        }
        if (sourceWhere == targetWhere && sourceIndex == targetIndex) {
            return false;
        }
        if (sourceIndex < 0 || targetIndex < 0) {
            return false;
        }

        // 特殊装备不可脱下
        if (sourceWhere == BackpackConst.Place.EQUIP.getWhere()){
            ItemUpgradeConfigCache cache = ConfigCacheManager.getInstance().getCache(ItemUpgradeConfigCache.class);
            if (cache.findUnmovableEquipPos().contains(sourceIndex)) {
                return false;
            }
        }
        //兽决(内丹)不可随意穿脱(由兽决类自己处理)
        if (sourceWhere == BackpackConst.Place.SHOUJUE_EQUIP.getWhere() || targetWhere == BackpackConst.Place.SHOUJUE_BACKPACK.getWhere()){
            return false;
        }

        //火龙之心部位不能主动穿脱 英雄不能穿玉佩
        if (sourceWhere == BackpackConst.Place.HERO_EQUIP.getWhere() && sourceIndex == EquipConst.EquipIndex.HUO_LONG.getCls()) {
            return false;
        }

        if (targetWhere == BackpackConst.Place.HERO_EQUIP.getWhere() && targetIndex == EquipConst.EquipIndex.HUO_LONG.getCls()) {
            return false;
        }

        if (targetWhere == BackpackConst.Place.HERO_EQUIP.getWhere() && targetIndex == EquipConst.EquipIndex.POS_JADE.getCls()) {
            return false;
        }

        //通天王者之戒不能主动脱下
        if (sourceWhere == BackpackConst.Place.EQUIP.getWhere() && sourceIndex == EquipConst.EquipIndex.BABEL_ROAD_RING.getCls()) {
            return false;
        }

        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage sourceStorage = backpack.fetchStorage(sourceWhere);
        Item sourceItem = sourceStorage.getData().get(sourceIndex);
        if (sourceItem == null || count > sourceItem.findCount()) {
            return false;
        }
        ItemConfig sourceConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, sourceItem.getCfgId());
        if (sourceConfig == null) {
            log.error("item config not found: {}", sourceItem.getCfgId());
            return false;
        }

        //法宝不能穿戴
        if (targetWhere == BackpackConst.Place.EQUIP.getWhere() && sourceConfig.getWuhuntype() == 4) {
            TipUtil.show(role, CommonTips.法宝_不能直接穿戴);
            return false;
        }

        if (!insertVerify(role, sourceConfig, targetWhere, targetIndex, sourceItem)) {
            return false;
        }

        // 破碎装备不能穿戴
        if (targetWhere == BackpackConst.Place.EQUIP.getWhere()) {
            SpecialRingCache cache = ConfigCacheManager.getInstance().getCache(SpecialRingCache.class);
            if (targetIndex == EquipConst.EquipIndex.POS_LEFT_RING.getCls() || targetIndex == EquipConst.EquipIndex.POS_RIGHT_RING.getCls()) {
                if (cache != null && cache.isSpecialRing(sourceItem.getCfgId()) && sourceItem.eData().getDurable() == sourceItem.findItemConfig().getDuraMax()) {
                    TipUtil.show(role, CommonTips.服务_修复特戒后才可穿戴);
                    return false;
                }
            }
            if (cache != null && cache.isDemonEquip(sourceItem.getCfgId()) && sourceItem.eData().getDurable() == sourceItem.findItemConfig().getDuraMax()) {
                TipUtil.show(role, CommonTips.服务_修复魔器后才可穿戴);
                return false;
            }
        }

        //校验
        if (!ScriptEngine.invoke1t1WithRet(IBackpackScript.class, script -> script.mergeVerify(role, sourceConfig, targetWhere, targetIndex, sourceItem))) {
            return false;
        }

        Storage targetStorage = backpack.fetchStorage(targetWhere);

        Item targetItem = targetStorage.getData().get(targetIndex);
        if (targetItem != null) {
            ItemConfig targetConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, targetItem.getCfgId());
            if (targetConfig == null) {
                log.error("item config not found: {}", targetItem.getCfgId());
                return false;
            }

            if (!insertVerify(role, targetConfig, sourceWhere, sourceIndex, targetItem)) {
                return false;
            }
            if (!spend(role, sourceWhere, sourceIndex, targetWhere, targetIndex)) {
                return false;
            }

            //背包神器道具不可被替换到普通背包
            if (targetConfig.getItemtype() == BagConst.ItemType.BAG_SHENQI
                    && !BagShenQiConst.findBagShenQIBackPack().contains(sourceWhere)) {
                return false;
            }

        }

        return executeMerge(role, backpack, sourceStorage, sourceConfig, sourceItem, sourceWhere, sourceIndex, targetStorage, targetItem, targetWhere , targetIndex, count, logAction);
    }

    @Override
    public boolean executeMerge(Role role, Backpack backpack, Storage sourceStorage, ItemConfig sourceConfig, Item sourceItem, int sourceWhere, int sourceIndex, Storage targetStorage, Item targetItem, int targetWhere, int targetIndex, int count, int logAction) {
        if (targetItem == null) {
            // move or slice
            if (count < sourceItem.findCount()) {
                // slice
                targetItem = ItemUtil.fission(sourceItem, count);
                sourceItem.setCount(sourceItem.findCount() - count);
                targetItem.setWhere(sourceItem.getWhere());
            } else {
                // move
                targetItem = sourceItem;
                sourceItem = null;
                targetItem.setWhere(targetWhere);
            }
        } else if (targetItem.getCfgId() == sourceItem.getCfgId()) {
            // merge
            int surplus = sourceConfig.getOverlying() - targetItem.findCount();
            if (surplus > 0) {
                if (count > surplus) count = surplus;
                targetItem.setCount(targetItem.findCount() + count);
                sourceItem.setCount(sourceItem.findCount() - count);
                if (sourceItem.findCount() <= 0) {
                    sourceItem = null;
                }
            } else {
                // switch
                Item temp = targetItem;
                targetItem = sourceItem;
                targetItem.setWhere(targetWhere);
                sourceItem = temp;
                sourceItem.setWhere(sourceWhere);

            }
        } else {
            // switch
            Item temp = targetItem;
            targetItem = sourceItem;
            targetItem.setWhere(targetWhere);
            sourceItem = temp;
            sourceItem.setWhere(sourceWhere);
        }

        //移动成功的时候将目标位置设置到道具上
        targetItem.setIndex(targetIndex);
        if (sourceItem != null) {
            sourceItem.setIndex(sourceIndex);
        }

        EquipData equipData = targetItem.getEquipData();
        // 穿戴装备的时候可以解封
        if (targetWhere == BackpackConst.Place.EQUIP.getWhere() && equipData != null && equipData.isFengyin()) {
            equipData.setFengyin(false);
        }

        int action = logAction <= 0 ? LogAction.BACKPACK_MERGE.getCode() : logAction;
        List<ItemChange> changes = new ArrayList<>();
        changes.add(sourceStorage.update(sourceIndex, sourceItem, action));
        changes.add(targetStorage.update(targetIndex, targetItem, action));
        DataCenter.updateData(backpack);

        ResBackpackChangedMessage msg = new ResBackpackChangedMessage();
        msg.setAction(action);
        Map<Integer, List<BackpackGridBean>> map = new HashMap<>();
        for (ItemChange change : changes) {
            if (change == null) {
                continue;
            }
            int where = change.getPlace().getWhere();
            BackpackGridBean gridBean = new BackpackGridBean();
            gridBean.setIndex(change.getIndex());
            if(change.getNItem() != null) {
                gridBean.setItem(ItemUtil.packCommonItemBean(change.getNItem()));
            }
            if (map.get(where) != null) {
                map.get(where).add(gridBean);
            } else {
                List<BackpackGridBean> list = new ArrayList<>();
                list.add(gridBean);
                map.put(where, list);
            }
        }

        for (Integer key : map.keySet()) {
            BackpackChangeBean backpackChangeBean = new BackpackChangeBean();
            backpackChangeBean.setWhere(key);
            backpackChangeBean.getChanges().addAll(map.get(key));
            msg.getChangedItems().add(backpackChangeBean);
        }

        if (LogAction.ROLE_INIT.getCode() != action) {
            MessageUtil.sendMsg(msg, role.getId());
        }

        onUpdate(role, changes);

        return true;
    }

    @Override
    public void reqDiscard(Role role, int where, int index, int count) {
        if (count <= 0) {
            return;
        }
        if (SecondaryPasswordManager.getInstance().secondPasswordCheck(role)) {
            TipUtil.show(role.getId(), CommonTips.服务_二级密码已锁定);
            return;
        }
        if (index < 0) {
            return;
        }
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null) {
            return;
        }

        player.reqPosition(new NoticeCallback<GetPlayerPositionRetNotice>() {
            @Override
            public void callback(GetPlayerPositionRetNotice notice) {
                MapProxy mapProxy = MapProxyManager.getInstance().getMap(notice.getMapId());
                if (mapProxy == null) {
                    return;
                }
                MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, mapProxy.getCfgId());
                if (mapConfig == null || mapConfig.getServerType() == 2 || mapConfig.getServerType() == 3) {
                    TipUtil.show(role, CommonTips.服务_该场景无法进行丢弃);
                    return;
                }

                Backpack backpack = DataCenter.getBackpack(role.getId());
                BackpackConst.Place place = BackpackConst.Place.getPlace(where);
                if (place == null) {
                    return;
                }
                Item item;
                if (place == BackpackConst.Place.COIN) {
                    item = new Item();
                    item.setCfgId(index);
                    item.setCount(count);
                } else {
                    Item grid = backpack.fetchItemByIndex(place, index);
                    if (grid == null) {
                        return;
                    }
                    EquipData equipData = grid.getEquipData();
                    if (equipData != null && equipData.getToubao() == 1) {
                        TipUtil.show(role.getId(), CommonTips.投保_弃保后才可丢弃);
                        return;
                    }
                    item = ItemUtil.copy(grid, count);
                }
                //获取已丢弃次数dCount，检查剩余丢弃数量rCount
                int dCount = role.getRoleDaily().getDiscardCount();
                int rCount = CountManager.getInstance().verifyRemainderCount(role, dCount, ActionCountLimitUtil.Action.DISCARD);
                if (rCount <= 0 && item.findItemConfig().getBind() == 0) {
                    ResCountTypeMessage resCountTypeMessage = new ResCountTypeMessage();
                    resCountTypeMessage.setHelpId(DiscardCountHelpId);
                    MessageUtil.sendMsg(resCountTypeMessage, role.getId());
                    return;
                }

                BackpackStash stash = new BackpackStash(role);
                stash.decrease(item);
                if (!stash.commit(role, LogAction.DISCARD, false, null)) {
                    return;
                }

                if (item.findItemConfig().getBind() == 0
                        && GlobalUtil.getGlobalInt(GameConst.GlobalId.DISCARD_ITEM_COMMAND) == 0
                        && IdentifyManager.getInstance().checkTrade(item)) {
                    item.setWhere(BackpackConst.Place.BACKPACK.getWhere());
                    mapProxy.dropItem(notice.getX(), notice.getY(), item, role.getId(), 0);
                }

                if (item.findItemConfig().getBind()==0) {
                    int discardCount = role.getRoleDaily().getDiscardCount();
                    role.getRoleDaily().setDiscardCount(discardCount + 1);
                    DataCenter.updateData(role);
                    // 返回丢弃剩余次数
                    int discardCount1 = role.getRoleDaily().getDiscardCount();
                    int remainderCount = CountManager.getInstance().verifyRemainderCount(role, discardCount1, ActionCountLimitUtil.Action.DISCARD);
                    ResBackpackDiscardMessage msg = new ResBackpackDiscardMessage();
                    msg.setRemainderCount(remainderCount);
                    MessageUtil.sendMsg(msg, role.getId());
                }

            }
        });
    }

    @Override
    public void onUpdate(Role role, List<ItemChange> changes) {
        ScriptEngine.invoke1tn(IEventOnRoleBackpackUpdateScript.class, script -> script.onRoleBackpackUpdate(role, changes));

        List<ItemChange> equipChanges = new ArrayList<>();
        List<ItemChange> boreChanges = new ArrayList<>();
        List<ItemChange> heroEquipChanges = new ArrayList<>();
        List<ItemChange> allEquipChanges = new ArrayList<>();
        List<ItemChange> xialvChanges = new ArrayList<>();
        List<ItemChange> shoujueChanges = new ArrayList<>();
        List<ItemChange> birthChartChanges = new ArrayList<>();
        for (ItemChange change : changes) {
            switch (change.getPlace()) {
                case EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        equipChanges.add(change);
                        allEquipChanges.add(change);
                    }
                    break;
                case GEM_EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        boreChanges.add(change);
                    }
                    break;
                case HERO_EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        heroEquipChanges.add(change);
                        allEquipChanges.add(change);
                    }
                    break;
                case XIALV_EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        xialvChanges.add(change);
                        allEquipChanges.add(change);
                    }
                    break;
                case SHOUJUE_EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        shoujueChanges.add(change);
                        allEquipChanges.add(change);
                    }
                    break;
                case BIRTH_CHART_EQUIP:
                    if (ItemUtil.isItemChanged(role, change.getOItem(), change.getNItem())) {
                        birthChartChanges.add(change);
                        allEquipChanges.add(change);
                    }
                    break;
                default:
            }
        }
        if (!equipChanges.isEmpty()) {
            if (RoleLineUpManager.getInstance().getUseEquip(role) == BackpackConst.Place.EQUIP) {
                role.clearEquipSuits();
                ScriptEngine.invoke1tn(IEventOnEquipChangedScript.class, script -> script.onRoleEquipChanged(role, equipChanges));
            }

            ScriptEngine.invoke1tn(IEquipScript.class, script -> script.onRoleLineUpEquipChanged(role, 1, equipChanges));
        }
        if (!boreChanges.isEmpty()) {
            ScriptEngine.invoke1tn(IEventOnRoleBoreChangedScript.class, script -> script.onRoleBoreChanged(role, boreChanges));
        }
        if (!heroEquipChanges.isEmpty() && role.getHero() != null) {
            role.getHero().clearEquipSuits();
            ScriptEngine.invoke1tn(IEventOnEquipChangedScript.class, script -> script.onHeroEquipChanged(role.getHero(), heroEquipChanges));
        }
        if (!xialvChanges.isEmpty()) {
            ScriptEngine.invoke1tn(IEventOnEquipChangedScript.class, script -> script.onXiaLvEquipChanged(role, xialvChanges));
        }
        if (!shoujueChanges.isEmpty()){
            ScriptEngine.invoke1tn(IEventOnEquipChangedScript.class, script -> script.onRoleShouJueEquipChanged(role, shoujueChanges));
        }
        if (!birthChartChanges.isEmpty()){
            ScriptEngine.invoke1tn(IEventOnEquipChangedScript.class, script -> script.onRoleBirthChartEquipChanged(role, birthChartChanges));
        }
    }

    @Override
    public void fetchTreasure(Role role, List<Long> lidList) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage sourceStorage = backpack.fetchStorage(BackpackConst.Place.TREASURE.getWhere());

        List<Item> items = new ArrayList<>();
        for (Long lid : lidList) {
            boolean find = false;
            for (Map.Entry<Integer, Item> itemEntry : sourceStorage.getData().entrySet()) {
                Item item = itemEntry.getValue();
                if (item != null && item.getId() == lid) {
                    item.setWhere(sourceStorage.getPlace().getWhere());
                    item.setIndex(itemEntry.getKey());
                    items.add(item);
                    find = true;
                    break;
                }
            }
            if (!find) {
                TipUtil.show(role.getId(), CommonTips.服务_道具不存在);
                return;
            }
        }

        BackpackStash backpackStash = new BackpackStash(role);
        for (Item item : items) {
            Item createItem = ItemUtil.create(item.getCfgId(), item.findCountLong(), LogAction.BACKPACK_FETCH_TREASURE, null);
            if (createItem == null) {
                log.error("wrong operation");
                return;
            }
            backpackStash.decrease(item);
            backpackStash.increase(createItem);
        }

        backpackStash.commit(role, LogAction.BACKPACK_FETCH_TREASURE);
    }

    /**
     * 检查条件
     *
     * @param role
     * @param config
     * @param where
     * @param index
     * @param item 当前检查装备
     * @return
     */
    private boolean insertVerify(Role role, ItemConfig config, int where, int index,Item item) {
        BackpackConst.Place place = BackpackConst.Place.getPlace(where);
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, config.getType());
        switch (place) {
            case EQUIP:
                if (locationConfig == null) {
                    return false;
                }
                if (!ArrayUtils.contains(locationConfig.getPos1(), index)) {
                    return false;
                }
                if (locationConfig.getSex() != 0 && locationConfig.getSex() != role.getSex()) {
                    TipUtil.show(role, CommonTips.服务_性别条件不匹配);
                    return false;
                }
                // 计算当前装备属性
                Storage s = new Storage();
                s.getData().put(index,item);
                Attribute attribute = EquipManager.getInstance().equipAttrCount(Collections.EMPTY_SET, s, role.getCareer(), null);
                AttributeConditionContext cxt = new AttributeConditionContext(AppContext.getContext());
                cxt.setAttribute(attribute);
                if (!ConditionUtil.validate(cxt, role, config.getNeed(), false, false)) {
                    TipUtil.show(role, CommonTips.服务_性别条件不匹配);
                    return false;
                }
                break;
            case BACKPACK:
            case BACKPACK_WOW:
            case BACKPACK_PRIVILEGE:
                Storage storage = role.getBackpack().fetchStorage(place);
                if (index > storage.capacity(role)) {
                    return false;
                }
                break;
            case XIALV_EQUIP:
                // 侠侣背包不可以穿戴非侠侣装备
                if (locationConfig == null) {
                    return false;
                }
                if (locationConfig.getPos1().length < 1) {
                    return false;
                }
                if (locationConfig.getPos1()[0] != index) {
                    return false;
                }
                break;
            case WAREHOUSE:
            case WAREHOUSE_ONE:
            case WAREHOUSE_TWO:
            case WAREHOUSE_THREE:
            case WAREHOUSE_FOUR:
                Storage warehouse = role.getBackpack().fetchStorage(place);
                if (index > warehouse.capacity(role)) {
                    return false;
                }
                if (GlobalConfig.warehouseType.contains(config.getType())) {
                    return false;
                }
                break;
            case WAREHOUSE_TREASURE:
                return false;
            case WAREHOUSE_WABAO:
            case DOCKER:
                Storage docker = role.getBackpack().fetchStorage(place);
                if (index > docker.capacity(role)) {
                    return false;
                }
                // TODO
                break;
            case TREASURE:
                log.error("wrong operation");
                return false;
            case GEM_EQUIP:
                //打了孔才让放
                Map<Integer, Bore> roleBore = role.getRoleAdvance().getRoleBore();
                Bore bore = roleBore.get(index);
                if (bore == null || !bore.isState()) {
                    return false;
                }
                //石头配置
                StoneConfig stoneConfig = ConfigDataManager.getInstance().getById(StoneConfig.class, config.getId());
                if (stoneConfig == null) {
                    return false;
                }
                //孔位配置
                BoreConfig boreConfig = ConfigDataManager.getInstance().getById(BoreConfig.class, index);
                if (boreConfig == null) {
                    return false;
                }
                //位置一样
                if (stoneConfig.getType() != boreConfig.getType()) {
                    return false;
                }
                break;
            case GEM:
                break;
            case BACKPACK_HERO:
                //判断有没有英雄
                if (role.getHero() == null) {
                    return false;
                }
                Storage heroStorge = role.getBackpack().fetchStorage(place);
                if (index > heroStorge.capacity(role.getHero())) {
                    return false;
                }
                break;
            case HERO_EQUIP:
                //判断有没有英雄
                if (role.getHero() == null) {
                    return false;
                }
                if (locationConfig == null) {
                    return false;
                }
                if (!ArrayUtils.contains(locationConfig.getPos1(), index)) {
                    return false;
                }
                if (locationConfig.getSex() != 0 && locationConfig.getSex() != role.getHero().getSex()) {
                    TipUtil.show(role, CommonTips.服务_性别条件不匹配);
                    return false;
                }
                if (!ConditionUtil.validate(role.getHero(), config.getNeed())) {
                    TipUtil.show(role, CommonTips.服务_条件不满足);
                    return false;
                }
                break;
            case SHOUJUE_EQUIP:
                break;
            case SHOUJUE_BACKPACK:
                break;
            case MONSTER_CARD:
                if (config.getItemtype() != BagConst.ItemType.MONSTER_CARD) {
                    return false;
                }
                break;
            case CHONGWU_EQUIP:
                if (config.getItemtype() != BagConst.ItemType.CHONGWUEQUIP) {
                    return false;
                }
                if (role.getRoleChongWuCfgId() <= 0) {
                    return false;
                }
                break;
            case CHONGWU_BACKPACK:
                break;
            case BAG_SHENQI_BACKPACK:
                break;
            case DISARM_HIDE:
                break;
            case MONSTER_CARD_DIWANG:
                if (config.getItemtype() != BagConst.ItemType.MONSTER_CARD_DIWANG) {
                    return false;
                }
                break;
            case ZT_EQUIP:
                return true;
            default:
                log.error("unknown storage type");
                return false;
        }

        return true;
    }

    /**
     * 宝石和宝石装备得处理
     *
     * @param role
     * @param sourceWhere
     * @param sourceIndex
     * @param targetWhere
     * @param targetIndex
     * @return
     */
    private boolean spend(Role role, int sourceWhere, int sourceIndex, int targetWhere, int targetIndex) {
        if (sourceIndex < 0 || targetIndex < 0) {
            return false;
        }
        BackpackConst.Place source = BackpackConst.Place.getPlace(sourceWhere);
        BackpackConst.Place where = BackpackConst.Place.getPlace(targetWhere);
        if (source == BackpackConst.Place.GEM_EQUIP) {
            if (where == BackpackConst.Place.GEM) {
                //拆卸
                BoreConfig boreConfig = ConfigDataManager.getInstance().getById(BoreConfig.class, sourceIndex);
                if (boreConfig == null) {
                    return false;
                }
                BackpackStash stash = new BackpackStash(role);
                stash.decrease(boreConfig.getRemove_cost());
                if (!stash.commit(role, LogAction.STONE_REMOVE, true, null)) {
                    return false;
                }
            }
        } else if (source == BackpackConst.Place.GEM) {
            if (where == BackpackConst.Place.GEM_EQUIP) {
                //安装
                BoreConfig boreConfig = ConfigDataManager.getInstance().getById(BoreConfig.class, targetIndex);
                if (boreConfig == null) {
                    return false;
                }
                BackpackStash stash = new BackpackStash(role);
                stash.decrease(boreConfig.getCondition());
                if (!stash.commit(role, LogAction.STONE_GEM, true, null)) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void reqExchangeExp(Role role, int itemId, long actorId) {
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
        if (itemConfig == null) {
            return;
        }
        Map<Integer, String> expBoxDailyTime = GlobalConfig.expBoxDailyTime;
        if (!expBoxDailyTime.containsKey(itemId)) {
            return;
        }
        String param = expBoxDailyTime.get(itemId);
        String[] split = param.split(Symbol.JINHAO);
        if (split.length < 2) {
            return;
        }

        //每日次数判断
        int dailyLimit = Integer.parseInt(split[1]);
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.DAILY_EXP_BOX_COUNT, itemId);
        if (count >= dailyLimit) {
            return;
        }

        //加主角还是英雄经验 两者道具id不同
        int needCount = Integer.parseInt(split[0]);
        int addItemId = role.getId() == actorId ? BagConst.ItemId.ROLE_EXP : BagConst.ItemId.ROLE_EXP;
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, needCount);
        stash.increase(addItemId, itemConfig.getUseParam()[0][0] * needCount);
        if (!stash.commit(role, LogAction.EXCHANGE)) {
            return;
        }
        CountManager.getInstance().count(role, CountConst.CountType.DAILY_EXP_BOX_COUNT, itemId);
        ResExchangeExpMessage message = new ResExchangeExpMessage();
        message.setItemId(itemId);
        message.setCount(CountManager.getInstance().getCount(role, CountConst.CountType.DAILY_EXP_BOX_COUNT, itemId));
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqExchangeExpInfo(Role role) {
        ResExchangeExpInfoMessage infoMessage = new ResExchangeExpInfoMessage();
        Map<Integer, String> expBoxDailyTime = GlobalConfig.expBoxDailyTime;
        for (int itemId : expBoxDailyTime.keySet()) {
            int count = CountManager.getInstance().getCount(role, CountConst.CountType.DAILY_EXP_BOX_COUNT, itemId);
            infoMessage.getItemId().add(itemId);
            infoMessage.getCount().add(count);
        }
        MessageUtil.sendMsg(infoMessage, role.getId());
    }

    /**
     * 缴械
     *
     * @param role 角色
     * @param pos  部位
     * @param time 时间
     */
    @Override
    public void disarmEquip(Role role, int pos, long time) {
        Backpack backpack = role.getBackpack();
        Item equip = backpack.fetchItemByIndex(BackpackConst.Place.EQUIP, pos);
        if (equip == null || equip.getEquipData() == null) {
            return;
        }

        int targetWhere;
        Integer targetIndex;
        Queue<Integer> backEmptyGrid = new StorageStash(role, BackpackConst.Place.BACKPACK).getEmpties();
        if (backEmptyGrid.isEmpty()) {
            //查找背包空位
            StorageStash storageStash = new StorageStash(role, BackpackConst.Place.DISARM_HIDE);
            Queue<Integer> empties = storageStash.getEmpties();
            targetIndex = empties.poll();
            if (targetIndex == null) {
                return;
            }
            targetWhere = BackpackConst.Place.DISARM_HIDE.getWhere();
        } else {
            targetWhere = BackpackConst.Place.BACKPACK.getWhere();
            targetIndex = backEmptyGrid.poll();
        }
        if (targetIndex == null) {
            return;
        }
        long disarmTime = TimeUtil.getNowOfMills() + time;

        BackpackStash equipStash = new BackpackStash(role);
        equipStash.update(equip, up -> up.getEquipData().setDisarmTime(disarmTime));
        if (!equipStash.commit(role, LogAction.DISARM)) {
            return;
        }
        role.updateBackpack(equipStash.getCommitItemChanges());
        reqMerge(role, equip.getWhere(), equip.getIndex(), targetWhere, targetIndex, Math.toIntExact(equip.getCount()), LogAction.DISARM.getCode());
        ItemConfig itemConfig = equip.findItemConfig();
        if (itemConfig != null) {
            TipUtil.show(role, CommonTips.缴械, itemConfig.getName());
        }
        log.info("缴械, roleId: {} , roleName: {} , equipId: {} , sourceWhere: {} , sourceIndex: {} , targetWhere: {} , targetIndex: {} , disarmTime: {}", role.getRoleId(), role.getName(), equip.getId(), equip.getWhere(), equip.getIndex(), targetWhere, targetIndex, disarmTime);
    }

    /**
     * 缴械整理
     *
     * @param role  角色
     * @param where 背包
     */
    private void disarmSort(Role role, int where) {
        if (where != BackpackConst.Place.BACKPACK.getWhere()) {
            return;
        }
        Storage hideStorage = DataCenter.get(Backpack.class, role.getId()).fetchStorage(BackpackConst.Place.DISARM_HIDE.getWhere());
        List<Item> copyList = new ArrayList<>(hideStorage.getData().values());
        if (copyList.isEmpty()) {
            return;
        }
        StorageStash storageStash = new StorageStash(role, BackpackConst.Place.BACKPACK);

        Queue<Integer> empties = storageStash.getEmpties();
        for (Item item : copyList) {
            if (item == null || empties.isEmpty()) {
                continue;
            }
            Integer poll = empties.poll();
            if (poll == null) {
                continue;
            }
            merge(role, item.getWhere(), item.getIndex(), where, poll, Math.toIntExact(item.getCount()), LogAction.DISARM_SORT.getCode());
        }
    }

}
