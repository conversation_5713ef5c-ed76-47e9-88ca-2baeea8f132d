package com.sh.game.script.equip;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.equip.*;
import com.sh.game.common.communication.msg.system.equip.bean.EquipCiZuiBean;
import com.sh.game.common.communication.msg.system.equip.bean.EquipCuiQuBean;
import com.sh.game.common.config.cache.ChaoticPowerCache;
import com.sh.game.common.config.cache.EquipCiZuiConfigCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.*;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleTask;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnEquipChangedScript;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleCreate;
import com.sh.game.log.entity.RoleFuHunLog;
import com.sh.game.log.entity.abs.RoleChaoticLog;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.game.system.equip.EquipManager;
import com.sh.game.system.equip.entity.XilianInfo;
import com.sh.game.system.equip.event.IEventOnEquipDurableChange;
import com.sh.game.system.equip.script.IEquipScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.recycle.RecycleManager;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.touying.RoleLineUpManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Script(order = 99)
public class EquipScript implements IEquipScript, IEventOnRoleAttributeCountScript,
        IEventOnEquipChangedScript, IEventOnEquipDurableChange, IEventOnRoleCreate {

    @Override
    public void reqEquipStarUp(Role role, long itemID) {
        //从背包取到道具
        Item item = role.getBackpack().findItemByUniqueId(itemID);
        if (item == null) return;
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) return;
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, itemConfig.getType());
        if (locationConfig == null) return;
        //升级配置
        int newStarLevel = item.getEquipData().getStarLevel() + 1;
        EquipStarLevelUpConfig config = ConfigDataManager.getInstance().getById(EquipStarLevelUpConfig.class,
                newStarLevel + Symbol.JINHAO + locationConfig.getFunctionType());
        if (config == null) {
            return;
        }
        EquipQualityConfig qualityConfig = ConfigDataManager.getInstance().getById(EquipQualityConfig.class, itemConfig.getRank());
        if (qualityConfig != null) {
            if (item.getEquipData().getStarLevel() + item.getEquipData().getStarLocked() >= qualityConfig.getStar_num())
                return;
        }

        BackpackStash stash = new BackpackStash(role);
        List<int[]> cost = config.getCost();
        if (cost != null && cost.size() > 0) {
            stash.decrease(cost);
        }
        int chance = config.getChance() + item.getEquipData().getStarExp();
        boolean generate = RandomUtil.isGenerate(10000, chance);
        stash.update(item, up -> {
            if (generate) {
                up.eData().setStarLevel(item.getEquipData().getStarLevel() + 1);
                up.eData().setStarExp(0);
                EquipManager.getInstance().onEquipStarUp(role);
            } else {
                up.eData().setStarExp(up.eData().getStarExp() + config.getBless());
            }
        });
        stash.commit(role, LogAction.EQUIP_STAR_LEVEL_UP);

        if (generate) {
            BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(role);
            if (item.getWhere() == equipPlace.getWhere()) {
                onRoleAttributeChange(role);
            }
        }
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.EQUIP_STRENGTHEN_TIMES);
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.EQUIP;
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(role);
        Storage storage = role.getBackpack().fetchStorage(equipPlace);
        Attribute attribute = equipAttrCount(role.getEquipSuits(), storage, role.getCareer(), role);

        // 装备萃取属性加成
        // todo萃取有两种type加成 type2涉及技能 暂时等技术做好了开始 先做type1纯加属性
        Storage otherStorage = role.getBackpack().fetchStorage(BackpackConst.Place.ZT_EQUIP);
        ItemCuiQuInfo cuiQuInfo = otherStorage.getCuiQuInfo();
        for (Integer cuiCuId : cuiQuInfo.getWearCuiQu().values()) {
            EquipCuiQuEffectConfig effectConfig = ConfigDataManager.getInstance().getById(EquipCuiQuEffectConfig.class, cuiCuId);
            if (effectConfig == null) {
                continue;
            }
            // type=1 纯加属性
            if (effectConfig.getType() == 1) {
                for (int[] ints : effectConfig.getAttribute1()) {
                    attribute.fixAdd(ints[0], ints[1]);
                }
            }
        }
        role.getAttributes().put(getAttributeType(), attribute);
    }


    @Override
    public void onRoleEquipChanged(Role role, List<ItemChange> changes) {
        onRoleAttributeChange(role);
        // 刷新玩家技能
        SkillManager.getInstance().updateRoleSkill(role);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.NECKLACE_ATTRIBUTE);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.MIX_MAGIC_EQUIP);
    }


    @Override
    public void onEquipDurableChange(Role role) {
        onRoleAttributeChange(role);
    }


    /**
     * 计算装备属性
     */
    @Override
    public Attribute equipAttrCount(Set<Integer> equipSuits, Storage storage, int career, IAvatar avatar) {
        Attribute attribute = new Attribute();
        storage.getData().forEach((index, equip) -> {
            if (equip == null) {
                return;
            }
            EquipData equipData = equip.eData();
            if(equipData == null){
                return;
            }
            attribute.fixAdd( equipData.getAttribute(), career);
            // 词缀属性加成
            for (CiZuiInfo value : equipData.getCiZuiAttribute().values()) {
                attribute.fixAdd(value.getAttributeId(), value.getAttributeValue());
            }
            // 装备位锻造属性加成
            if (storage.getEquipDuanZao().containsKey(index)) {
                Integer duanZaoId = storage.getEquipDuanZao().get(index);
                DuanZaoConfig duanZaoConfig = ConfigDataManager.getInstance().getById(DuanZaoConfig.class, duanZaoId);
                if (duanZaoConfig != null) {
                    attribute.fixAdd(duanZaoConfig.getAttribute(), career);
                }
            }
            // 装备赐福加成
            CiFuInfo ciFuInfo = equipData.getCiFuInfo();
            if (ciFuInfo.getCiFuId() > 0) {
                EquipCiFuConfig ciFuConfig = ConfigDataManager.getInstance().getById(EquipCiFuConfig.class, ciFuInfo.getCiFuId());
                attribute.fixAdd(ciFuConfig.getAttribute(), career);
            }
            if (!ciFuInfo.getCiFuEffect().isEmpty()) {
                ciFuInfo.getCiFuEffect().values().forEach(id -> {
                    EquipCiFuEffectConfig ciFuEffectConfig = ConfigDataManager.getInstance().getById(EquipCiFuEffectConfig.class, id);
                    if (ciFuEffectConfig != null) {
                        if (ciFuEffectConfig.getType() == 1) {
                            attribute.fixAdd(ciFuEffectConfig.getAttribute1()[0], ciFuEffectConfig.getAttribute1()[1]);
                        }
                        if (ciFuEffectConfig.getType() == 2) {
                            long attrValue = avatar.getFinalAttribute().get(AttributeEnum.valueOf(ciFuEffectConfig.getAttribute1()[0]));
                            if (attrValue >= ciFuEffectConfig.getAttribute1()[1]) {
                                attribute.fixAdd(ciFuEffectConfig.getAttribute2()[0], ciFuEffectConfig.getAttribute2()[1]);
                            }
                        }
                        if (ciFuEffectConfig.getType() == 3) {
                            long attrValue = avatar.getFinalAttribute().get(AttributeEnum.valueOf(ciFuEffectConfig.getAttribute1()[0]));
                            if (attrValue >= ciFuEffectConfig.getAttribute1()[1]) {
                                int step = ciFuEffectConfig.getAttribute2()[1];
                                long excess = attrValue - ciFuEffectConfig.getAttribute1()[1];
                                long bonusCount = excess / step;
                                attribute.fixAdd(ciFuEffectConfig.getAttribute3()[0], ciFuEffectConfig.getAttribute3()[1] * bonusCount);
                            }
                        }
                    }
                });
            }
        });
        return attribute;
    }

    private Attribute calAttrPercent(int percent,Map<Integer, Long>[] attribute,int career) {
        Attribute attr = new Attribute();
        attr.fixAdd(attribute, career);
        for (Map.Entry<Integer, Long> entry : attr.getAttributeMap().entrySet()) {
            long value = entry.getValue() * percent / 10000;
            entry.setValue(value);
        }
        return attr;
    }

    /**
     * 查找玩家的法器会员替换的秘籍buff
     * @param avatar
     * @return
     */
    private Map<Integer, List<Integer>> findFaQiHuiYuanReplaceBuff(IAvatar avatar) {
        if (!(avatar instanceof Role)) {
            return new HashMap<>(0);
        }
        Role role = (Role) avatar;
        int levelId = role.getFaQiHuYuanData().getLevelID();
        if (levelId <= 0) {
            return new HashMap<>(0);
        }
        Map<Integer, List<Integer>> ret = new HashMap<>(levelId);
        while (levelId > 0) {
            FaQiHuiYuanConfig config = ConfigDataManager.getInstance().getById(FaQiHuiYuanConfig.class, levelId);
            if (config != null) {
                List<Integer> replaceBuff = config.getReplaceBuff();
                if (replaceBuff != null) {
                    ret.put(config.getMijiid(), replaceBuff);
                }
            }
            levelId--;
        }
        return ret;
    }


    @Override
    public void createHeroEquip(Role role) {
        // Hero hero = role.getHero();
        // if (hero == null) {
        //     return;
        // }
        // CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class, hero.getCareer() + Symbol.JINHAO + hero.getSex());
        // if (characterConfig == null) {
        //     return;
        // }
        // List<int[]> initHeroEquip = characterConfig.getInitHeroEquip();
        // if (initHeroEquip == null) {
        //     return;
        // }
        // Backpack backpack = role.getBackpack();
        // List<ItemChange> itemChanges = new ArrayList<>();
        // Storage storage = backpack.fetchStorage(BackpackConst.Place.HERO_EQUIP);
        // for (int[] ints : initHeroEquip) {
        //     if (ints == null || ints.length < 2) {
        //         continue;
        //     }
        //     Integer index = ints[0];
        //     Integer itemId = ints[1];
        //     Item buildItem = ItemUtil.create(itemId, 1, LogAction.HERO_INIT);
        //     itemChanges.add(storage.update(index, buildItem, LogAction.HERO_INIT.getCode()));
        // }
        //
        // role.updateBackpack(itemChanges);
        // DataCenter.updateData(backpack);
    }

    @Override
    public void magicBloodStoneUpdate(Role role, long actorId, int pos, int magicBloodStoneValue) {
        Backpack backpack = role.getBackpack();
        BackpackConst.Place place = role.getId() == actorId ? BackpackConst.Place.EQUIP : BackpackConst.Place.HERO_EQUIP;
        Item item = backpack.fetchItemByIndex(place, pos);
        if (item == null) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        if (magicBloodStoneValue <= 0) {
            stash.decrease(item);
        } else {
            stash.update(item, update -> update.getEquipData().setMagicBloodStoneValue(magicBloodStoneValue));
        }
        stash.commit(role, LogAction.MAGIC_BLOOD_STONE_UPDATE);
    }

    @Override
    public void reqXilianEquip(Role role, long uid) {
        long roleId = role.getId();
        Backpack backpack = DataCenter.getBackpack(roleId);
        Item item = backpack.findItemByUniqueId(uid);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) {
            return;
        }
        EquipLocationConfig localConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, itemConfig.getType());
        if (localConfig == null) {
            return;
        }
        EquipXilianConfig xilianConfig = ConfigDataManager.getInstance().getById(EquipXilianConfig.class,
                itemConfig.getRank() + "#" + localConfig.getFunctionType());
        if (xilianConfig == null || xilianConfig.getIdentifyNum() == null || xilianConfig.getIdentifyNum().length == 0) {
            return;
        }

        if (xilianConfig.getCost() != null && xilianConfig.getCost().size() > 0) {
            if (!BackpackManager.getInstance().costItem(role, xilianConfig.getCost(), LogAction.EQUIP_QUENCHING, null)) {
                return;
            }
        }
        int shuxiCount = xilianConfig.getIdentifyNum()[RandomUtil.randomNumber(xilianConfig.getIdentifyNum().length)];
        if (shuxiCount < 1) {
            return;
        }

        IAvatar avatar = role.getIAvatarByItemWhere(item.getWhere());
        List<int[]> identifyAttr;
        int career = avatar.getCareer();

        switch (career) {
            case RoleConst.Career.ZHAN:
                identifyAttr = xilianConfig.getIdentifyAttr();
                break;
            case RoleConst.Career.FA:
                identifyAttr = xilianConfig.getIdentifyAttr2();
                break;
            default:
                identifyAttr = xilianConfig.getIdentifyAttr3();
                break;
        }

        Map<Integer, Long> attrMap = new HashMap<>();
        for (int i = shuxiCount; i > 0; i--) {
            randomAttrType(identifyAttr, attrMap);
        }
        TreeMap<Integer, Integer> chance = new TreeMap<>();
        int total = 0;
        for (int i = 0; i < xilianConfig.getXilianAttr().length; i++) {
            total += xilianConfig.getXilianAttr()[i];
            chance.put(total, i + 1);
        }
        Map.Entry<Integer, Integer> entry = chance.ceilingEntry(RandomUtil.randomNumber(total));
        if (entry == null) {
            entry = chance.lastEntry();
        }
        List<int[]> xilian;
        switch (entry.getValue()) {
            case 1:
                xilian = xilianConfig.getXilian1();
                break;
            case 2:
                xilian = xilianConfig.getXilian2();
                break;
            default:
                xilian = xilianConfig.getXilian3();
                break;
        }

        Map<Integer, Long> finalAttrMap = new HashMap<>();
        for (int[] ints : xilian) {
            if (!attrMap.containsKey(ints[0])) {
                continue;
            }
            finalAttrMap.put(ints[0], (long) RandomUtil.random(ints[1], ints[2]));
        }
        EquipAttribute attribute = new EquipAttribute();
        attribute.merge(0, finalAttrMap);
        EquipManager.getInstance().addXilianAttr(roleId, uid, attribute);
        ResXilianEquipMessage message = new ResXilianEquipMessage();
        message.setUid(uid);
        message.getXilianAttrs().addAll(ItemUtil.packRandomAttributeBean(attribute));
        MessageUtil.sendMsg(message, roleId);

        RoleTask task = role.getRoleTask();
        task.setXilian(task.getXilian() + 1);
        DataCenter.updateData(task);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XILIAN_TIMES);
    }

    /**
     * 随机属性类型
     *
     * @param identifyAttr
     * @param attrMap
     */
    private void randomAttrType(List<int[]> identifyAttr, Map<Integer, Long> attrMap) {
        TreeMap<Integer, Integer> chance = new TreeMap<>();
        int total = 0;
        for (int[] ints : identifyAttr) {
            if (attrMap.containsKey(ints[0])) {
                continue;
            }
            total += ints[1];
            chance.put(total, ints[0]);
        }
        Map.Entry<Integer, Integer> entry = chance.ceilingEntry(RandomUtil.randomNumber(total));
        if (entry == null) {
            entry = chance.lastEntry();
        }
        attrMap.put(entry.getValue(), 0L);
    }

    @Override
    public void reqXilianSave(Role role) {
        long roleId = role.getId();
        XilianInfo info = EquipManager.getInstance().getXilianInfo(roleId);
        if (info == null || info.getItemID() == 0) {
            return;
        }
        Backpack backpack = DataCenter.getBackpack(roleId);
        Item item = backpack.findItemByUniqueId(info.getItemID());
        if (item == null) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> up.eData().setXilianAttr(info.getAttribute()));
        stash.commit(role, LogAction.EQUIP_QUENCHING);

        if (item.getWhere() == BackpackConst.Place.EQUIP.getWhere()) {
            onRoleAttributeChange(role);
        }
    }

    @Override
    public void fuhun(Role role, long uid, int gemId) {
        Backpack backpack = DataCenter.get(Backpack.class, role.getId());
        Item item = backpack.findItemByUniqueId(uid);
        if (item == null) {
            return;
        }

        ItemConfig equipConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        if (equipConfig.getWuhuntype() != 1) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        int oldGemId = item.getEquipData().getGemId();
        if (gemId == 0) { // 将身上的卸下
            if (oldGemId > 0) {
                stash.increase(oldGemId);
                GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class,
                        GameConst.GlobalId.CHAIFEN_COST);
                List<int[]> cost = StringUtil.strToIntArrayList(config.getValue(), Symbol.AND, Symbol.JINHAO);
                stash.decrease(cost);
            }
        } else {
            // 不可重复镶嵌
            if (oldGemId > 0) {
                return;
            }
            ItemConfig gemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, gemId);
            if (gemConfig.getWuhuntype() == 3 || gemConfig.getWuhuntype() == 4) {
                if (gemConfig.getType() != equipConfig.getType()) {
                    return;
                }
            } else if (gemConfig.getWuhuntype() != 2) {
                return;
            }

            stash.decrease(gemId);
            GlobalConfig config = ConfigDataManager.getInstance().getById(GlobalConfig.class,
                    GameConst.GlobalId.FUHUN_COST);
            List<int[]> cost = StringUtil.strToIntArrayList(config.getValue(), Symbol.AND, Symbol.JINHAO);
            stash.decrease(cost);
        }
        stash.update(item, up -> {
            up.getEquipData().setGemId(gemId);
//            item.getEquipData().setGemId(gemId);
        });
        if (!stash.commit(role, LogAction.FUHUN)) {
            return;
        }

        role.updateBackpack(stash.getCommitItemChanges());

        onRoleAttributeChange(role);

        ResEquipFuHunChangeMessage msg = new ResEquipFuHunChangeMessage();
        msg.setUid(uid);
        msg.setGemId(gemId);
        MessageUtil.sendMsg(msg, role.getId());

        //打宝石日志
        RoleFuHunLog log = new RoleFuHunLog(role);
        log.setUniqueId(item.getId());
        log.setEquipCfgId(item.getCfgId());
        log.setGemCfgId(gemId);
        log.submit();
    }

    /**
     * 请求混沌装备附魂
     *
     * @param role         角色
     * @param equipId      装备唯一id
     * @param chaoticGemId 混沌宝石配置id
     */
    @Override
    public void reqChaoticInlay(Role role, long equipId, int chaoticGemId) {
        Backpack backpack = DataCenter.get(Backpack.class, role.getId());
        Item equip = backpack.findItemByUniqueId(equipId);
        if (equip == null) {
            log.error("混沌装备-镶嵌-装备不存在,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        ItemConfig equipConfig = equip.findItemConfig();
        if (equipConfig == null) {
            log.error("混沌装备-镶嵌-装备配置不存在,roleId:{},roleName:{},equipId:{},configId:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId());
            return;
        }

        if (equipConfig.getHunduntype() != 1) {
            log.error("混沌装备-镶嵌-无法镶嵌的类型装备,roleId:{},roleName:{},equipId:{},gemId:{},type:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId,equipConfig.getHunduntype());
            return;
        }

        EquipData equipData = equip.getEquipData();
        if (equipData == null) {
            log.error("混沌装备-镶嵌-不是装备,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        int oldGemId = equipData.getChaoticGemId();

        if (chaoticGemId <= 0) {
            log.error("混沌装备-镶嵌-宝石为空,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId);
            return;
        }

        if (oldGemId > 0) {
            log.error("混沌装备-镶嵌-重复镶嵌,roleId:{},roleName:{},equipId:{},gemId:{},oldGemId:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId, oldGemId);
            return;
        }

        ItemConfig gemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, chaoticGemId);
        if (gemConfig.getHunduntype() != 2) {
            log.error("混沌装备-镶嵌-无法镶嵌的类型宝石,roleId:{},roleName:{},equipId:{},gemId:{},type:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId, gemConfig.getHunduntype());
            return;
        }

        if (gemConfig.getType() != equipConfig.getType()) {
            log.error("混沌装备-镶嵌-部位错误,roleId:{},roleName:{},equipId:{},equipConfigId:{},gemId:{},type:{} {}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), chaoticGemId, gemConfig.getType(), equipConfig.getType());
            return;
        }

        List<int[]> cost = GlobalUtil.findItemCost(GameConst.GlobalId.CHAOTIC_INLAY_COST);
        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(chaoticGemId);
        costStash.decrease(cost);
        if (!costStash.commit(role, LogAction.CHAOTIC_INLAY)) {
            log.error("混沌装备-镶嵌-道具消耗失败,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId);
            return;
        }

        BackpackStash equipStash = new BackpackStash(role);
        equipStash.update(equip, up -> {
            up.getEquipData().setChaoticGemId(chaoticGemId);
        });
        if (!equipStash.commit(role, LogAction.CHAOTIC_INLAY)) {
            log.info("混沌装备-镶嵌-背包已满通过邮件发送,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId);
            equipStash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("混沌装备-镶嵌-成功,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, chaoticGemId);

        role.updateBackpack(equipStash.getCommitItemChanges());

        onRoleAttributeChange(role);

        ResEquipChaoticChangeMessage msg = new ResEquipChaoticChangeMessage();
        msg.setEquipId(equipId);
        msg.setChaoticGemId(chaoticGemId);
        MessageUtil.sendMsg(msg, role.getId());

        RoleChaoticLog baseLog = new RoleChaoticLog(role);
        baseLog.setUniqueId(equip.getId());
        baseLog.setEquipCfgId(equip.getCfgId());
        baseLog.setGemCfgId(chaoticGemId);
        baseLog.submit();
    }

    /**
     * 请求混沌装备拆分
     *
     * @param role    角色
     * @param equipId 装备唯一id
     */
    @Override
    public void reqChaoticSeparate(Role role, long equipId) {
        Backpack backpack = DataCenter.get(Backpack.class, role.getId());
        Item equip = backpack.findItemByUniqueId(equipId);
        if (equip == null) {
            log.error("混沌装备-分离-装备不存在,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        ItemConfig equipConfig = equip.findItemConfig();
        if (equipConfig == null) {
            log.error("混沌装备-分离-装备配置不存在,roleId:{},roleName:{},equipId:{},configId:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId());
            return;
        }

        if (equipConfig.getHunduntype() != 1) {
            log.error("混沌装备-分离-无法镶嵌的类型装备,roleId:{},roleName:{},equipId:{},type:{}", role.getRoleId(), role.getName(), equipId,equipConfig.getHunduntype());
            return;
        }

        EquipData equipData = equip.getEquipData();
        if (equipData == null) {
            log.error("混沌装备-分离-不是装备,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        int oldGemId = equipData.getChaoticGemId();
        if (oldGemId <= 0) {
            log.error("混沌装备-分离-宝石为空,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, oldGemId);
            return;
        }

        List<int[]> cost = GlobalUtil.findItemCost(GameConst.GlobalId.CHAOTIC_SEPARATE_COST);
        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(cost);
        if (!costStash.commit(role, LogAction.CHAOTIC_SEPARATE)) {
            log.error("混沌装备-分离-道具消耗失败,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }

        BackpackStash equipStash = new BackpackStash(role);
        equipStash.update(equip, up -> {
            up.getEquipData().setChaoticGemId(0);
        });
        if (!equipStash.commit(role, LogAction.CHAOTIC_SEPARATE)) {
            log.error("混沌装备-分离-装备宝石改变失败,roleId:{},roleName:{},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        role.updateBackpack(equipStash.getCommitItemChanges());

        onRoleAttributeChange(role);

        BackpackStash rewardStash = new BackpackStash(role);
        rewardStash.increase(oldGemId);
        if (!rewardStash.commit(role, LogAction.CHAOTIC_SEPARATE)) {
            log.info("混沌装备-分离-背包已满,通过邮件发送,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, oldGemId);
            rewardStash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("混沌装备-分离-成功,roleId:{},roleName:{},equipId:{},gemId:{}", role.getRoleId(), role.getName(), equipId, oldGemId);

        ResEquipChaoticChangeMessage msg = new ResEquipChaoticChangeMessage();
        msg.setEquipId(equipId);
        msg.setChaoticGemId(0);
        MessageUtil.sendMsg(msg, role.getId());

        RoleChaoticLog baseLog = new RoleChaoticLog(role);
        baseLog.setUniqueId(equip.getId());
        baseLog.setEquipCfgId(equip.getCfgId());
        baseLog.setGemCfgId(0);
        baseLog.submit();
    }

    /**
     * 请求混沌灵源赋灵
     *
     * @param role    角色
     * @param equipId 装备唯一id
     */
    @Override
    public void reqChaoticPower(Role role, long equipId) {
        Backpack backpack = DataCenter.get(Backpack.class, role.getId());
        Item equip = backpack.findItemByUniqueId(equipId);
        if (equip == null) {
            log.error("混沌灵源赋灵-装备不存在,role:{} {},equipId:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        ItemConfig equipConfig = equip.findItemConfig();
        if (equipConfig == null) {
            log.error("混沌灵源赋灵-装备配置不存在,role:{} {},equipId:{},configId:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId());
            return;
        }

        if (equipConfig.getFulingType() != 1) {
            log.error("混沌灵源赋灵-无法赋灵的类型装备,role:{} {},equipId:{},itemId:{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), equipConfig.getFulingType());
            return;
        }

        EquipData equipData = equip.getEquipData();
        if (equipData == null) {
            log.error("混沌灵源赋灵-不是装备,role:{} {},equipId:{},itemId:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId());
            return;
        }

        ChaoticPowerCache cache = ConfigCacheManager.getInstance().getCache(ChaoticPowerCache.class);
        int powerLevel = equipData.getChaoticPowerLevel();
        ChaoticPowerConfig powerConfig = cache.getPowerConfig(equipConfig.getType(), powerLevel);
        if (powerConfig == null) {
            log.error("混沌灵源赋灵-配置不存在,role:{} {},equipId:{},itemId:{},level:{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), powerLevel, equipConfig.getType());
            return;
        }

        if (equipConfig.getLevel() < powerConfig.getRanklimit()) {
            log.error("混沌灵源赋灵-装备等级不足,role:{} {},equipId:{},itemId:{},equipLevel:{}<{},level:{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), equipConfig.getLevel(), powerLevel, powerConfig.getRanklimit(), equipConfig.getType());
            return;
        }

        int nextId = powerConfig.getNextid();
        ChaoticPowerConfig nextConfig = ConfigDataManager.getInstance().getById(ChaoticPowerConfig.class, nextId);
        if (nextConfig == null) {
            log.error("混沌灵源赋灵-下一级配置不存在,role:{} {},equipId:{},itemId:{},level:{},nextId:{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), powerLevel, nextId, equipConfig.getType());
            return;
        }

        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(powerConfig.getCost());
        if (!costStash.commit(role, LogAction.CHAOTIC_POWER)) {
            log.error("混沌灵源赋灵-道具消耗失败,role:{} {},equipId:{},itemId:{},level:{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), powerLevel, equipConfig.getType());
            return;
        }

        BackpackStash equipStash = new BackpackStash(role);
        equipStash.update(equip, up -> {
            up.getEquipData().setChaoticPowerLevel(nextConfig.getLevel());
        });
        if (!equipStash.commit(role, LogAction.CHAOTIC_POWER)) {
            log.error("混沌灵源赋灵-装备变更失败,role:{} {},equipId:{},itemId:{},level:{}->{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), powerLevel, nextConfig.getLevel(), equipConfig.getType());
        }

        log.info("混沌灵源赋灵-成功,role:{} {},equipId:{},itemId:{},level:{}->{},type:{}", role.getRoleId(), role.getName(), equipId, equip.getCfgId(), powerLevel, nextConfig.getLevel(), equipConfig.getType());

        role.updateBackpack(equipStash.getCommitItemChanges());
        onRoleAttributeChange(role);

        ResEquipChaoticPowerMessage msg = new ResEquipChaoticPowerMessage();
        msg.setEquipId(equipId);
        msg.setChaoticPowerLevel(nextConfig.getLevel());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }


    @Override
    public void refreshAttribute(Role role) {
        onRoleAttributeChange(role);
    }

    /**
     * 投影装备
     * @param role   role
     * @param index   投影索引
     * @param changes 变化装备
     */
    public void onRoleLineUpEquipChanged(Role role, int index, List<ItemChange> changes) {
        for (ItemChange change : changes) {
            SummaryManager.getInstance().updateFacadeData(role.getId(), data -> {
                Map<Integer, Item> equipMap = data.getLineUpEquips().getOrDefault(index, null);
                if (MapUtils.isNotEmpty(equipMap)) {
                    if (change.getNItem() == null) {
                        equipMap.remove(change.getIndex());
                    } else {
                        equipMap.put(change.getIndex(), change.getNItem());
                    }
                }
            });
        }
    }

    @Override
    public void equipItem(Role role, long id) {
        Item item = role.getBackpack().findItemByItemId(id, BackpackConst.Place.ZT_EQUIP);
        if (item == null) {
            return;
        }
        equipItem(role, item);
    }

    @Override
    public void equipItem(Role role, int cid) {
        Item item = role.getBackpack().findItemByCfgId(cid, BackpackConst.Place.ZT_EQUIP).stream().findFirst().orElse(null);
        if (item == null) {
            return;
        }
        equipItem(role, item);
    }

    private void equipItem(Role role, Item item) {
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig.getItemtype() != BagConst.ItemType.EQUIP) {
            return;
        }
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, itemConfig.getType());
        if (locationConfig == null) {
            return;
        }
        // 装备部位（头盔，手套，鞋子等等）
        int pos = locationConfig.getPos1()[0];
        // 1.如果在装备栏有相同部位则替换一下位置，即从装备栏放回背包里
        Item replaceItem = role.getBackpack().findItemByItemId(item.getId(), BackpackConst.Place.EQUIP);
        if (replaceItem != null) {
            BackpackManager.getInstance().reqMerge(role,
                    BackpackConst.Place.EQUIP.getWhere(),
                    pos,
                    BackpackConst.Place.ZT_EQUIP.getWhere(),
                    item.getIndex(),
                    1,
                    LogAction.EQUIP_PUT_ON.getCode()
            );
        }
        BackpackManager.getInstance().reqMerge(role,
                item.getWhere(),
                item.getIndex(),
                BackpackConst.Place.EQUIP.getWhere(),
                pos,
                1,
                LogAction.EQUIP_PUT_ON.getCode()
        );
        AttributeManager.getInstance().attributeCount(role);
    }

    @Override
    public void batchDecomposeItem(Role role, List<Long> items) {
        BackpackStash stash = new BackpackStash(role);
        for (Long id : items) {
            Item item = role.getBackpack().findItemByItemId(id, BackpackConst.Place.ZT_EQUIP);
            if (item == null) {
                return;
            }
            ItemConfig itemConfig = item.findItemConfig();
            if (itemConfig == null) {
                return;
            }
            if (item.getEquipData().isLocked()) {
                log.error("批量分解装备-装备已锁定,role:{} {},itemId:{},type:{}", role.getRoleId(), role.getName(), item.getCfgId(), itemConfig.getType());
                return;
            }
            List<int[]> equal = itemConfig.getEqual();
            int level = item.getEquipData().getAttrcreatId() != 0 ?item.getEquipData().getAttrcreatId():item.getEquipData().getLevel();
            AttrCreatConfig attrCreatConfig =  ConfigDataManager.getInstance().getById(AttrCreatConfig.class,level);
            List<int[]> newEqual = new ArrayList<>(equal.size());
            // 根据策划要求，真实分解出的道具数量 = equal字段中道具的数量 * equalMod系数
            for (int[] ints : equal) {
                newEqual.add(new int[]{ints[0],
                        attrCreatConfig == null ? ints[1] : (int) Math.round(ints[1] * attrCreatConfig.getEqualMod())});
            }
            for (int[] param : newEqual) {
                if (param == null || param.length < 2) {
                    continue;
                }
                if (param[0] != BagConst.ItemId.LING_SHI) {
                    continue;
                }
                GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.RECYCLE_LING_SHI, param[1]);
            }
            stash.decrease(item);
            stash.increase(newEqual);
        }
        stash.commitOrToMail(role, LogAction.RECYCLE,false);
        RecycleManager.getInstance().onRecycle(role);
    }

    @Override
    public void reqDuanZaoInfo(Role role) {
        ResEquipDuanZao resEquipDuanZao = new ResEquipDuanZao();
        resEquipDuanZao.setDuanZaoId(new ArrayList<>(role.getBackpack().fetchStorage(BackpackConst.Place.EQUIP).getEquipDuanZao().values()));
        MessageUtil.sendMsg(resEquipDuanZao, role);
    }

    @Override
    public void reqDuanZao(Role role, long equipId) {
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByItemId(equipId, BackpackConst.Place.EQUIP);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) {
            return;
        }
        if (itemConfig.getQuality() < GlobalUtil.getGlobalInt(GameConst.GlobalId.DUAN_ZAO_QUALITY)) {
            return;
        }
        Storage storage = backpack.fetchStorage(BackpackConst.Place.EQUIP);
        Integer curDuanZao = storage.getEquipDuanZao().getOrDefault(itemConfig.getType(), 0);
        if (curDuanZao == 0) {
            DuanZaoConfig duanZaoConfig = ConfigDataManager.getInstance().getList(DuanZaoConfig.class).stream()
                    .filter(e -> e.getLocationId() == itemConfig.getType())
                    .min(Comparator.comparingInt(DuanZaoConfig::getLevel)).orElse(null);
            if (duanZaoConfig == null) {
                return;
            }
            curDuanZao = duanZaoConfig.getId();
        }
        DuanZaoConfig duanZaoConfig = ConfigDataManager.getInstance().getById(DuanZaoConfig.class, curDuanZao);
        if (duanZaoConfig == null) {
            return;
        }
        if (duanZaoConfig.getNext() == 0) {
            return;
        }
        if (!BackPackStashUtil.decrease(role, duanZaoConfig.getCost(), LogAction.EQUIP_DUAN_ZAO_COST)) {
            log.warn("装备-锻造#玩家:{}-{},cid:{},扣除消耗失败", role.getId(), role.getName(), equipId);
            return;
        }
        storage.getEquipDuanZao().put(itemConfig.getType(), duanZaoConfig.getNext());
        DataCenter.updateData(backpack);

        reqDuanZaoInfo(role);
        AttributeManager.getInstance().attributeCount(role);
    }

    @Override
    public void reqEquipCiZuiInfo(Role role, long equipId) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            log.error("请求词缀信息-未找到该装备#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        EquipData equipData = item.getEquipData();
        Map<Integer, CiZuiInfo> ciZuiAttribute = equipData.getCiZuiAttribute();
        ResEquipCiZuiInfo resEquipCiZuiInfo = new ResEquipCiZuiInfo();
        resEquipCiZuiInfo.setEquipId(equipId);
        resEquipCiZuiInfo.setBeans(ciZuiAttribute.entrySet().stream().map(entry -> {
            EquipCiZuiBean equipCiZuiBean = new EquipCiZuiBean();
            equipCiZuiBean.setIndex(entry.getKey());
            equipCiZuiBean.setCiZuiId(entry.getValue().getCiZuiId());
            equipCiZuiBean.setAttrValue(entry.getValue().getAttributeValue());
            return equipCiZuiBean;
        }).collect(Collectors.toList()));
        resEquipCiZuiInfo.setCurIndex(equipData.getCiZuiLastSaveIndex());
        MessageUtil.sendMsg(resEquipCiZuiInfo, role);
    }

    @Override
    public void reqEquipCiZuiSave(Role role, long equipId) {
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(equipId);
        if (item == null) {
            log.error("请求保存词缀洗炼-未找到该装备#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        EquipData equipData = item.getEquipData();
        TwoTuple<Integer, CiZuiInfo> tempCiZuiAttr = equipData.getTempCiZuiAttr();
        if (tempCiZuiAttr.getFirst() == 0) {
            log.error("请求保存词缀洗炼-暂存区没有数据#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> {
            up.eData().getCiZuiAttribute().remove(tempCiZuiAttr.getFirst());
            up.eData().getCiZuiAttribute().put(tempCiZuiAttr.getFirst(), tempCiZuiAttr.getSecond());
            up.eData().setCiZuiLastSaveIndex(tempCiZuiAttr.getFirst());
            up.eData().setTempCiZuiAttr(new TwoTuple<>(0, new CiZuiInfo()));
        });
        stash.commit(role, LogAction.EQUIP_QUENCHING);

        onRoleAttributeChange(role);

        reqEquipCiZuiInfo(role, equipId);
    }

    @Override
    public void reqEquipCiZuiXiLian(Role role, long equipId, int index) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            log.error("请求词缀洗炼-未找到该装备#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        EquipSuiJiAttributeConfig suiJiAttributeConfig = ConfigDataManager.getInstance().getById(EquipSuiJiAttributeConfig.class, item.getCfgId());
        if (suiJiAttributeConfig == null) {
            log.error("请求词缀洗炼-未找到该随机装备配置#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        if (CollectionUtils.isEmpty(suiJiAttributeConfig.getNeeditem())) {
            log.error("请求词缀洗炼-没有消耗物品不可洗炼#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        EquipData equipData = item.getEquipData();
        Map<Integer, CiZuiInfo> ciZuiAttribute = equipData.getCiZuiAttribute();
        if (!ciZuiAttribute.containsKey(index)) {
            log.error("请求词缀洗炼-位置未找到词缀#玩家:{}-{},装备唯一Id:{},index:{}", role.getId(), role.getName(), equipId, index);
            return;
        }
        CiZuiInfo ciZuiInfo = ciZuiAttribute.get(index);
        EquipCiZuiConfig ciZuiConfig = ConfigDataManager.getInstance().getById(EquipCiZuiConfig.class, ciZuiInfo.getCiZuiId());
        if (ciZuiConfig == null) {
            log.error("请求词缀洗炼-未找到词缀配置#玩家:{}-{},装备唯一Id:{},index:{}", role.getId(), role.getName(), equipId, index);
            return;
        }
        boolean hasTongYuan = ciZuiAttribute.values().stream()
                .map(info -> ConfigDataManager.getInstance().getById(EquipCiZuiConfig.class, info.getCiZuiId()))
                .anyMatch(config -> config.getType() == 2);
        List<EquipCiZuiConfig> ciZuiConfigs = ConfigCacheManager.getInstance().getCache(EquipCiZuiConfigCache.class).getAll(ciZuiConfig.getGroupId());
        switch (ciZuiConfig.getType()) {
            case 0: // 如果是普通类型，可以随机普通和同源两个类型，但不能有独立类型，要是已有同源，则不能有同源
               ciZuiConfigs = ciZuiConfigs.stream().filter(config -> hasTongYuan ? config.getType() == 0 : config.getType() != 1).collect(Collectors.toList());
                break;
            case 1: // 如果是独立类型，只能随独立类型词缀（一个组里只会有一个独立类型）
                ciZuiConfigs = ciZuiConfigs.stream().filter(config -> config.getType() == 1).collect(Collectors.toList());
                break;
            case 2: // 如果是同源类型，可以随机同源和普通两个类型，但不能有独立类型，独立只能有一个并且是必出
                ciZuiConfigs = ciZuiConfigs.stream().filter(config -> config.getType() != 1).collect(Collectors.toList());
                break;
            default:
                return;
        }
        List<EquipCiZuiConfig> randomConfigs = ItemUtil.selectRandomConfigs(ciZuiConfigs, 1);
        if (CollectionUtils.isEmpty(randomConfigs)) {
            log.error("请求词缀洗炼-随机不到配置#玩家:{}-{},装备唯一Id:{},index:{}", role.getId(), role.getName(), equipId, index);
            return;
        }
        if (!BackPackStashUtil.decrease(role, suiJiAttributeConfig.getNeeditem(), LogAction.ZT_EQUIP_CI_ZUI_COST)) {
            return;
        }
        ciZuiConfig = randomConfigs.get(0);
        TwoTuple<Integer, CiZuiInfo> tempCiZuiAttr = equipData.getTempCiZuiAttr();
        tempCiZuiAttr.setFirst(index);

        CiZuiInfo tempInfo = new CiZuiInfo();
        tempInfo.setCiZuiId(ciZuiConfig.getId());
        tempInfo.setAttributeId(ciZuiConfig.getAttributeId());
        tempInfo.setAttributeValue(ItemUtil.randomByUnit(ciZuiConfig.getAttributeNum1(), ciZuiConfig.getAttributeNum2(), ciZuiConfig.getUnit()));
        tempCiZuiAttr.setSecond(tempInfo);

        EquipCiZuiBean equipCiZuiBean = new EquipCiZuiBean();
        equipCiZuiBean.setIndex(index);
        equipCiZuiBean.setCiZuiId(ciZuiConfig.getId());
        equipCiZuiBean.setAttrValue(tempInfo.getAttributeValue());
        ResEquipCiZuiXiLian res = new ResEquipCiZuiXiLian();
        res.setBean(equipCiZuiBean);
        MessageUtil.sendMsg(res, role);
    }

    @Override
    public void reqEquipLock(Role role, long equipId, boolean lock) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            log.error("请求装备锁定-未找到该装备#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> up.eData().setLocked(lock));
        stash.commit(role, LogAction.ZT_EQUIP_LOOK);
    }

    @Override
    public void reqEquipTakeOff(Role role, long equipId) {
        Item item = role.getBackpack().findItemByItemId(equipId, BackpackConst.Place.EQUIP);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) {
            return;
        }
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, itemConfig.getType());
        if (locationConfig == null) {
            return;
        }
        BackpackManager.getInstance().reqMerge(role,
                BackpackConst.Place.EQUIP.getWhere(),
                locationConfig.getPos1()[0],
                BackpackConst.Place.ZT_EQUIP.getWhere(),
                item.getIndex(),
                1,
                LogAction.EQUIP_PUT_ON.getCode()
        );
        AttributeManager.getInstance().attributeCount(role);
    }

    @Override
    public void reqEquipCiFu(Role role, long equipId) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            return;
        }
        List<EquipCiFuConfig> equipCiFuConfigs = ConfigDataManager.getInstance().getList(EquipCiFuConfig.class).stream()
                .filter(config -> config.getItemid().contains(item.getCfgId()))
                .collect(Collectors.toList());
        if (equipCiFuConfigs.isEmpty()) {
            log.error("请求装备赐福-该道具未找到赐福配置#玩家:{}-{},装备唯一Id:{}", role.getId(), role.getName(), equipId);
            return;
        }
        EquipData equipData = item.getEquipData();
        CiFuInfo info = equipData.getCiFuInfo();
        EquipCiFuConfig equipCiFuConfig = equipCiFuConfigs.stream().filter(config -> config.getLevel() == info.getLevel() + 1).findFirst().orElse(null);
        if (equipCiFuConfig == null) {
            log.error("请求装备赐福-该道具未找到下一个等级的配置#玩家:{}-{},装备唯一Id:{},当前等级:{}", role.getId(), role.getName(), equipId, info.getLevel());
            return;
        }
        if (!BackPackStashUtil.decrease(role, equipCiFuConfig.getNeeditem1(), LogAction.ZT_EQUIP_CI_FU_COST)) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> {
            CiFuInfo ciFuInfo = up.eData().getCiFuInfo();
            ciFuInfo.setCiFuId(equipCiFuConfig.getId());
            ciFuInfo.setLevel(equipCiFuConfig.getLevel());
            if (CollectionUtils.isNotEmpty(equipCiFuConfig.getNeeditem3()) && CollectionUtils.isNotEmpty(equipCiFuConfig.getEffect())) {
                List<int[]> effect = equipCiFuConfig.getEffect();
                List<Integer> prob = effect.stream().map(value -> value[1]).collect(Collectors.toList());
                int random = RandomUtil.randomIndexByProb(prob);
                ciFuInfo.getCiFuEffect().put(equipCiFuConfig.getId(), effect.get(random)[0]);
            }
        });
        stash.commit(role, LogAction.ZT_EQUIP_CI_FU);

        onRoleAttributeChange(role);
    }

    @Override
    public void reqEquipCiFuReset(Role role, long equipId) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            return;
        }
        EquipData equipData = item.getEquipData();
        CiFuInfo ciFuInfo = equipData.getCiFuInfo();
        if (ciFuInfo.getCiFuId() <= 0) {
            return;
        }
        EquipCiFuConfig equipCiFuConfig = ConfigDataManager.getInstance().getById(EquipCiFuConfig.class, ciFuInfo.getCiFuId());
        if (!BackPackStashUtil.decrease(role, equipCiFuConfig.getNeeditem2(), LogAction.ZT_EQUIP_CI_FU_RESET_COST)) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> {
            CiFuInfo info = up.eData().getCiFuInfo();
            info.setCiFuId(0);
            info.setLevel(0);
            info.getCiFuEffect().clear();
        });
        stash.commit(role, LogAction.ZT_EQUIP_CI_FU);

        onRoleAttributeChange(role);
    }

    @Override
    public void reqEquipCiFuReforging(Role role, long equipId, int ciFuId) {
        Item item = role.getBackpack().findItemByUniqueId(equipId);
        if (item == null) {
            return;
        }
        CiFuInfo ciFuInfo = item.getEquipData().getCiFuInfo();
        if (ciFuInfo.getCiFuId() <= 0) {
            return;
        }
        if (!ciFuInfo.getCiFuEffect().containsKey(ciFuId)) {
            return;
        }
        EquipCiFuConfig equipCiFuConfig = ConfigDataManager.getInstance().getById(EquipCiFuConfig.class, ciFuId);
        if (!BackPackStashUtil.decrease(role, equipCiFuConfig.getNeeditem3(), LogAction.ZT_EQUIP_CI_FU_REFORGING_COST)) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.update(item, up -> {
            CiFuInfo info = up.eData().getCiFuInfo();
            List<int[]> effect = equipCiFuConfig.getEffect();
            List<Integer> prob = effect.stream().map(value -> value[1]).collect(Collectors.toList());
            int random = RandomUtil.randomIndexByProb(prob);
            info.getCiFuEffect().put(ciFuId, effect.get(random)[0]);
        });
        stash.commit(role, LogAction.ZT_EQUIP_CI_FU);

        onRoleAttributeChange(role);
    }

    @Override
    public void reqCuiQuInfo(Role role) {
        Storage storage = role.getBackpack().fetchStorage(BackpackConst.Place.ZT_EQUIP);
        ItemCuiQuInfo cuiQuInfo = storage.getCuiQuInfo();

        EquipCuiQuBean equipCuiQuBean = new EquipCuiQuBean();
        equipCuiQuBean.setWearIds(cuiQuInfo.getWearCuiQu().entrySet().stream().map(entry -> {
            CommonKeyValueBean bean = new CommonKeyValueBean();
            bean.setKey(entry.getKey());
            bean.setValue(entry.getValue());
            return bean;
        }).collect(Collectors.toList()));
        equipCuiQuBean.setCuiQuIds(new ArrayList<>(cuiQuInfo.getCuiQuList()));

        ResEquipCuiQuInfo resEquipCuiQuInfo = new ResEquipCuiQuInfo();
        resEquipCuiQuInfo.setBean(equipCuiQuBean);
        MessageUtil.sendMsg(resEquipCuiQuInfo, role);
    }

    @Override
    public void reqCuiQu(Role role, long equipId) {
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.ZT_EQUIP);
        ItemCuiQuInfo cuiQuInfo = storage.getCuiQuInfo();
        Item item = backpack.findItemByItemId(equipId, BackpackConst.Place.ZT_EQUIP);
        if (item == null) {
            log.error("请求装备萃取-未找到该装备,角色:{},昵称:{},装备id:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        EquipSuiJiAttributeConfig suiJiAttributeConfig = ConfigDataManager.getInstance().getById(EquipSuiJiAttributeConfig.class, item.getCfgId());
        if (suiJiAttributeConfig == null) {
            log.error("请求装备萃取-未找到该装备的配置,角色:{},昵称:{},装备id:{}", role.getRoleId(), role.getName(), item.getCfgId());
            return;
        }
        EquipCuiQuEffectConfig effectConfig = ConfigDataManager.getInstance().getById(EquipCuiQuEffectConfig.class, suiJiAttributeConfig.getEffect());
        if (effectConfig == null) {
            log.error("请求装备萃取-未找到该萃取效果的配置,角色:{},昵称:{},装备id:{},萃取:{}", role.getRoleId(), role.getName(), item.getCfgId(), suiJiAttributeConfig.getEffect());
            return;
        }
        if (cuiQuInfo.getCuiQuList().contains(effectConfig.getId())) {
            log.error("请求装备萃取-已获得该萃取,角色:{},昵称:{},萃取id:{}", role.getRoleId(), role.getName(), effectConfig.getId());
            return;
        }
        int[] ints = effectConfig.getNeedequip().stream().filter(x -> x[0] == item.getCfgId()).findFirst().orElse(null);
        if (ints == null) {
            log.error("请求装备萃取-未找到该萃取的消耗装备配置,角色:{},昵称:{},萃取id:{},装备id:{}", role.getRoleId(), role.getName(), effectConfig.getId(), item.getCfgId());
            return;
        }
        List<int[]> needItem = new ArrayList<>();
        needItem.add(ints);
        if (CollectionUtils.isNotEmpty(effectConfig.getNeeditem())) {
            needItem.addAll(effectConfig.getNeeditem());
        }
        if (!BackPackStashUtil.decrease(role, needItem, LogAction.EQUIP_CU_QU_COST)) {
            log.error("请求装备萃取-道具不足,角色:{},昵称:{},配置id:{}", role.getRoleId(), role.getName(), effectConfig.getId());
            return;
        }
        cuiQuInfo.getCuiQuList().add(effectConfig.getId());
        DataCenter.updateData(backpack);

        reqCuiQuInfo(role);
    }

    @Override
    public void reqCuiQuWear(Role role, int cuiQuId) {
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.ZT_EQUIP);
        ItemCuiQuInfo cuiQuInfo = storage.getCuiQuInfo();
        if (!cuiQuInfo.getCuiQuList().contains(cuiQuId)) {
            log.error("请求穿戴萃取-未找到该萃取,角色:{},昵称:{},萃取id:{}", role.getRoleId(), role.getName(), cuiQuId);
            return;
        }
        EquipCuiQuEffectConfig effectConfig = ConfigDataManager.getInstance().getById(EquipCuiQuEffectConfig.class, cuiQuId);
        cuiQuInfo.getWearCuiQu().put(effectConfig.getLocationtype(), cuiQuId);
        DataCenter.updateData(backpack);

        onRoleAttributeChange(role);

        reqCuiQuInfo(role);
    }

    @Override
    public void reqCuiQuTakeOff(Role role, int cuiQuId) {
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.ZT_EQUIP);
        ItemCuiQuInfo cuiQuInfo = storage.getCuiQuInfo();
        EquipCuiQuEffectConfig effectConfig = ConfigDataManager.getInstance().getById(EquipCuiQuEffectConfig.class, cuiQuId);
        cuiQuInfo.getWearCuiQu().remove(effectConfig.getLocationtype());
        DataCenter.updateData(backpack);

        onRoleAttributeChange(role);

        reqCuiQuInfo(role);
    }

    @Override
    public void onRoleCreate(Role role) {
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.EQUIP);

        List<DuanZaoConfig> duanZaoConfigs = ConfigDataManager.getInstance().getList(DuanZaoConfig.class);
        Map<Integer, Integer> firstIdMap = duanZaoConfigs.stream()
                .collect(Collectors.groupingBy(
                        DuanZaoConfig::getLocationId,
                        Collectors.minBy(Comparator.comparing(DuanZaoConfig::getId))
                ))
                .entrySet().stream()
                .filter(entry -> entry.getValue().isPresent())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get().getId()
                ));
        storage.getEquipDuanZao().putAll(firstIdMap);
        DataCenter.updateData(backpack);
        reqDuanZaoInfo(role);
    }
}
