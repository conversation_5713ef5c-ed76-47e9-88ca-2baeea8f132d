package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivityTreasuryCountConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractWaBaoCountRewardScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

@Script
public class ActivityChristmasWaBaoScript extends AbstractWaBaoCountRewardScript {
    @Override
    public int getType() {
        return ActivityConst.CHRISTMAS_WABAO;
    }

    /**
     * 发送领取次数奖励公告
     *
     * @param role      角色
     * @param config    次数奖励配置
     */
    @Override
    protected void sendCountRewardAnnounce(Role role, ActivityTreasuryCountConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
