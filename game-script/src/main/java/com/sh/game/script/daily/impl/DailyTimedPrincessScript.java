package com.sh.game.script.daily.impl;

import com.sh.game.script.daily.abc.AbstractDailyTimedScript;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.script.annotation.Script;

@Script
public class DailyTimedPrincessScript extends AbstractDailyTimedScript {

    @Override
    public int getType() {
        return 125;
    }

    @Override
    public void onScheduleBegin(DailySchedule schedule) {
        MapProxyManager.getInstance().alloc(11014);
    }

    @Override
    public void onScheduleEnd(DailySchedule schedule) {
        MapProxyManager.getInstance().destroy(11014);
    }
}
