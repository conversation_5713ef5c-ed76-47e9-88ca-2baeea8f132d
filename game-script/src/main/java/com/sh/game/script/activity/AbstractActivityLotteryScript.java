package com.sh.game.script.activity;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.lottery.ResLottery;
import com.sh.game.common.communication.msg.system.lottery.ResLotteryInfo;
import com.sh.game.common.communication.msg.system.lottery.bean.RewardCountBean;
import com.sh.game.common.config.cache.ActivityScheduleConfigCache;
import com.sh.game.common.config.cache.LotteryConfigCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.CountConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnActivityStatistic;
import com.sh.game.log.entity.ActivityLotteryLog;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityLotteryScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.lottery.entity.Lottery;
import com.sh.game.system.lottery.entity.LotteryConfigItem;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.turn_based.constant.TurnBasedConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description:
 * date: 2024/5/9
 * author: chenBin
 */
@Slf4j
public abstract class AbstractActivityLotteryScript extends AbstractActivityScript implements IActivityLotteryScript,
        IEventOnActivityStatistic {

    @Override
    public void reqLotteryInfo(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        Map<Integer, Lottery> lotteryMap = role.getRoleActivity().getLotteryMap();
        Lottery lottery = lotteryMap.computeIfAbsent(schedule.getActivityID(), k -> new Lottery());
        ResLotteryInfo msg = new ResLotteryInfo();
        msg.setRewardCounts(buildRewardCount(lottery));
        msg.setSelfMaxCounts(buildSelfMaxCount(lottery));
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqLottery(Role role, int configId, int count) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        LotteryConfig lotteryConfig = ConfigDataManager.getInstance().getById(LotteryConfig.class, configId);
        if (lotteryConfig == null) {
            log.error("请求抽奖-配置错误-配置不存在,roleId:{},roleName:{},configId:{}", role.getId(), role.getName(), configId);
            return;
        }
        if (lotteryConfig.getActType() != getType()) {
            return;
        }
        if (lotteryConfig.getActId() > 0) {
            if (!isOpen(lotteryConfig.getActId())) {
                log.error("请求抽奖-配置错误-不在活动时间,roleId:{},roleName:{},configId:{}", role.getId(), role.getName(), configId);
                return;
            }
            onLottery(role, lotteryConfig, count, lotteryConfig.getActId());
        } else {
            ActivityScheduleConfigCache cache = ConfigCacheManager.getInstance().getCache(ActivityScheduleConfigCache.class);
            List<ActivityScheduleConfig> configs = cache.getByType(lotteryConfig.getActType());
            if (CollectionUtils.isEmpty(configs)) {
                log.error("请求抽奖-配置错误-找不到活动类型,roleId:{},roleName:{},configId:{}", role.getId(), role.getName(), configId);
                return;
            }
            for (ActivityScheduleConfig c : configs) {
                if (!isOpen(c.getId())) {
                    continue;
                }
                onLottery(role, lotteryConfig, count, c.getId());
            }
        }
    }

    private void onLottery(Role role, LotteryConfig lotteryConfig, int count, int activityId) {
        RoleActivity roleActivity = role.getRoleActivity();
        Lottery lottery = roleActivity.getLotteryMap().computeIfAbsent(activityId, k -> new Lottery());

        LotteryConfigCache cache = ConfigCacheManager.getInstance().getCache(LotteryConfigCache.class);
        List<LotteryRewardConfig> rewards = cache.getRewards(lotteryConfig.getRewardGroup());

        List<LotteryConfigItem> configItems = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            if (!checkCount(role, lotteryConfig, activityId, i)) {
                log.error("请求抽奖-次数不足,roleId:{},roleName:{},configId:{}", role.getId(), role.getName(), lotteryConfig.getId());
                break;
            }
            if (CollectionUtils.isNotEmpty(lotteryConfig.getCost())) {
                if (!BackPackStashUtil.decrease(role, lotteryConfig.getCost(), LogAction.RAFFLE)) {
                    log.error("请求抽奖-扣除消耗失败, roleId:{},roleName:{},configId:{}", role.getId(), role.getName(), lotteryConfig.getId());
                    break;
                }
            }
            doLottery(role, lottery, rewards, lotteryConfig, configItems);
        }
        if (configItems.isEmpty()) {
            log.error("请求抽奖-抽取不到最终奖励,roleId:{},roleName:{},配置ID：{},奖励池{}", role.getId(), role.getName(), lotteryConfig.getId(), lotteryConfig.getRewardGroup());
            return;
        }
        // 实际抽取的次数
        count = configItems.size();
        // 执行额外操作
        increaseCount(role, lotteryConfig, count, activityId);
        afterGoals(role, count);
        // 最终奖励
        List<int[]> itemIds = configItems.stream()
                .map(LotteryConfigItem::getConfigItems)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<int[]> finalItems = new ArrayList<>();
        // 如果抽到已拥有的伙伴需要分解掉
        for (int[] items : itemIds) {
            int itemId = items[0];
            int itemCount = items[1];
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            if (itemConfig.getItemtype() == BagConst.ItemType.HUO_BAN) {
                Map<Integer, HuoBan> huoBanBag = role.findHuoBan().getHuoBanBag();
                HuoBanConfig huoBanConfig = ConfigDataManager.getInstance().getById(HuoBanConfig.class, itemConfig.getUseParam()[0][0]);
                if (huoBanBag.containsKey(huoBanConfig.getId())) {
                    for (int j = 0; j < itemCount; j++) {
                        finalItems.addAll(huoBanConfig.getCall_recycle());
                    }
                } else {
                    finalItems.add(new int[]{itemId, itemCount});
                }
            } else {
                finalItems.add(new int[]{itemId, itemCount});
            }
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(finalItems);
        stash.commitOrToMail(role, LogAction.RAFFLE, false);
        DataCenter.updateData(roleActivity);
        List<CommonKeyValueBean> items = finalItems.stream()
                .map(x -> {
                    CommonKeyValueBean bean = new CommonKeyValueBean();
                    bean.setKey(x[0]);
                    bean.setValue(x[1]);
                    return bean;
                })
                .collect(Collectors.toList());
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZHAO_MU_HUO_BAN);
        ResLottery msg = new ResLottery();
        msg.setActType(getType());
        msg.setActivityId(activityId);
        msg.setRewardCounts(buildRewardCount(lottery));
        msg.setSelfMaxCounts(buildSelfMaxCount(lottery));
        msg.setItems(items);
        MessageUtil.sendMsg(msg, role.getId());
    }

    private boolean checkCount(Role role, LotteryConfig lotteryConfig, int activityId, int index) {
        CountConst.CountType countType = getCountType(role, lotteryConfig);
        if (countType == null) {
            return false;
        }
        int count = CountManager.getInstance().getCount(role, countType, activityId) + index;
        if (count >= lotteryConfig.getLimit()) {
            // 次数已达上限
            if (countType.equals(CountConst.CountType.LOTTERY_DAY_COUNT)) {
                TipUtil.show(role, CommonTips.服务_今日抽奖次数达到上限);
            } else {
                TipUtil.show(role, CommonTips.服务_活动抽奖次数达到上限);
            }
            return false;
        }
        return true;
    }

    private CountConst.CountType getCountType(Role role, LotteryConfig lotteryConfig) {
        CountConst.CountType countType;
        switch (lotteryConfig.getLimitType()) {
            case 1:
                countType = CountConst.CountType.LOTTERY_DAY_COUNT;
                break;
            case 2:
            case 3:
                countType = CountConst.CountType.LOTTERY_TOTAL_COUNT;
                break;
            default:
                log.error("请求抽奖-找不到CountType,roleId:{},roleName:{},配置ID:{}", role.getId(), role.getName(), lotteryConfig.getId());
                return null;
        }
        return countType;
    }

    private void increaseCount(Role role, LotteryConfig lotteryConfig, int count, int activityId) {
        CountConst.CountType countType = getCountType(role, lotteryConfig);
        if (countType == null) {
            return;
        }
        int totalCount = CountManager.getInstance().count(role, countType, activityId, count);
        afterCount(role, totalCount);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.DIG_TREASURE_TIMES, count);
    }

    private void doLottery(Role role, Lottery lottery, List<LotteryRewardConfig> rewards, LotteryConfig lotteryConfig, List<LotteryConfigItem> configItems) {
        int lotteryCount = lottery.getLotteryCount();
        TwoTuple<LotteryRewardConfig, Boolean> tuple = findReward(role, lotteryConfig, lottery, lotteryCount);
        if (tuple == null) {
            return;
        }
        LotteryRewardConfig rewardConfig = tuple.first;
        // 增加保底次数
        addConfigCount(tuple, lottery, rewards, lotteryCount);
        // 增加奖励次数
        Integer rewardCount = lottery.getRewardCounts().getOrDefault(rewardConfig.getId(), 0);
        lottery.getRewardCounts().put(rewardConfig.getId(), rewardCount + 1);
        // 增加抽卡次数
        lottery.setLotteryCount(lotteryCount + 1);
        // 获取配置的items
        List<int[]> itemIds = getRewards(role, rewardConfig);
        // 添加日志
        addLog(role, lottery.getLotteryCount(), itemIds);

        configItems.add(new LotteryConfigItem(rewardConfig.getId(), itemIds));
    }

    private TwoTuple<LotteryRewardConfig, Boolean> findReward(Role role, LotteryConfig config, Lottery lottery, int lotteryCount) {
        int rewardGroup = config.getRewardGroup();
        LotteryConfigCache cache = ConfigCacheManager.getInstance().getCache(LotteryConfigCache.class);

        // 首次抽卡奖励
        if (lotteryCount == 0 && cache.getFirstReward(config.getRewardGroup()) != null) {
            LotteryRewardConfig firstReward = cache.getFirstReward(config.getRewardGroup());
            return new TwoTuple<>(firstReward, false);
        }

        List<LotteryRewardConfig> rewards = cache.getRewards(rewardGroup).stream()
                .filter(reward -> ConditionUtil.validate(role, reward.getConditions()) &&
                        (reward.getSelfMax() <= 0 || lottery.getRewardCounts().getOrDefault(reward.getId(), 0) < reward.getSelfMax()))
                .collect(Collectors.toList());
        if (rewards.isEmpty()) {
            log.error("请求抽奖-第{}次抽奖-筛选奖励为空,roleId:{},roleName:{},配置id:{},奖池组:{}", role.getId(), role.getName(), lotteryCount, config.getId(), rewardGroup);
            return null;
        }
        // 是否保底
        Optional<LotteryRewardConfig> optional = rewards.stream()
                .filter(reward -> reward.getNum() > 0
                        && lottery.getIdCounts().getOrDefault(reward.getId(), 0) + 1 >= reward.getNum())
                .max(Comparator.comparingInt(LotteryRewardConfig::getNum));
        if (optional.isPresent()) {
            return new TwoTuple<>(optional.get(), true);
        }
        // 随机
        List<Integer> rates = rewards.stream().map(LotteryRewardConfig::getWight).collect(Collectors.toList());
        int index = RandomUtil.randomIndexByProb(rates);
        return new TwoTuple<>(rewards.get(index), false);
    }

    private void addConfigCount(TwoTuple<LotteryRewardConfig, Boolean> tuple, Lottery lottery, List<LotteryRewardConfig> rewards, int lotteryCount) {
        LotteryRewardConfig rewardConfig = tuple.first;
        // 判断计数是否重置
        boolean isReset = rewardConfig.getReset() == 1 || tuple.second;
        if (isReset && rewardConfig.getNum() > 0) {
            lottery.getIdCounts().put(rewardConfig.getId(), 0);
        }
        // 增加保底次数
        rewards.stream()
                .filter(reward -> reward.getId() != rewardConfig.getId() && !isReset && reward.getNum() > 0)
                .forEach(reward -> {
                    int idCount;
                    if (reward.getClearNum() == 0) {
                        idCount = lottery.getIdCounts().getOrDefault(reward.getId(), lotteryCount >= reward.getNum() ? 0 : lotteryCount) + 1;
                    } else {
                        idCount = lottery.getIdCounts().getOrDefault(reward.getId(), 0) + 1;
                    }
                    lottery.getIdCounts().put(reward.getId(), idCount);
                });
    }

    private List<int[]> getRewards(Role role, LotteryRewardConfig config) {
        List<int[]> addItem = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(config.getItemId())) {
            addItem.addAll(config.getItemId());
        } else {
            List<int[]> boxId = config.getBoxId();
            for (int[] box : boxId) {
                List<Item> boxItems = BoxUtil.openBox(role, box[0], box[1]);
                List<int[]> items = boxItems.stream().map(item -> new int[]{item.getCfgId(), item.findCount()}).collect(Collectors.toList());
                addItem.addAll(items);
            }
        }

        return addItem;
    }

    private void addLog(Role role, int lotteryCount, List<int[]> itemId) {
        ActivityLotteryLog log = new ActivityLotteryLog(role);
        log.setAccount(role.getAccount());
        log.setRoleId(role.getId());
        log.setRoleName(role.getName());
        log.setTime(TimeUtil.getNowOfSeconds());
        log.setCount(lotteryCount);
        log.setReward(itemId);
        log.submit();
    }

    private List<RewardCountBean> buildRewardCount(Lottery lottery) {
        List<RewardCountBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : lottery.getIdCounts().entrySet()) {
            RewardCountBean bean = new RewardCountBean();
            bean.setRewardConfigId(entry.getKey());
            bean.setCount(entry.getValue());
            beans.add(bean);
        }
        return beans;
    }

    private List<CommonKeyValueBean> buildSelfMaxCount(Lottery lottery) {
        List<CommonKeyValueBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : lottery.getRewardCounts().entrySet()) {
            CommonKeyValueBean bean = new CommonKeyValueBean();
            bean.setKey(entry.getKey());
            bean.setValue(entry.getValue());
            beans.add(bean);
        }
        return beans;
    }

    public void afterCount(Role role, int count) {
    }
}
