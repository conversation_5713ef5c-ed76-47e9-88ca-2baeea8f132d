package com.sh.game.script.mix;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.mix.ResMixInfoMessage;
import com.sh.game.common.communication.msg.system.mix.ResMixResultMessage;
import com.sh.game.common.communication.msg.system.mix.bean.MixInfoBean;
import com.sh.game.common.config.model.EquipLocationConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.advance.mix.script.IMixScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Map;


@Slf4j
@Script
public class MixScript implements IMixScript {

    @Override
    public void reqInfo(Role role) {
        ResMixInfoMessage msg = new ResMixInfoMessage();
        for (Map.Entry<Integer, Integer> entry : role.getRoleAdvance().getMixMap().entrySet()) {
            MixInfoBean bean = new MixInfoBean();
            bean.setIndex(entry.getKey());
            bean.setId(entry.getValue());
            msg.getMix().add(bean);
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqMerge(Role role, int index, long itemId) {
        Item item = role.getBackpack().findItemByUniqueId(itemId);
        if (item == null) {
            return;
        }

        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, item.findItemConfig().getType());
        if (locationConfig == null) {
            return;
        }
        if (!ArrayUtils.contains(locationConfig.getPos1(), index)) {
            return;
        }
        if (locationConfig.getSex() != 0 && locationConfig.getSex() != role.getSex()) {
            TipUtil.show(role, CommonTips.脚本_性别不匹配);
            return;
        }
        if (!ConditionUtil.validate(role, item.findItemConfig().getNeed())) {
            TipUtil.show(role, CommonTips.脚本_条件不满足);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(item);
        int exist = role.getRoleAdvance().getMixMap().getOrDefault(index, 0);
        if (exist > 0) {
            stash.increase(exist);
        }
        if (!stash.commit(role, LogAction.MIX_MERGE)) {
            return;
        }

        role.getRoleAdvance().getMixMap().put(index, item.getCfgId());
        DataCenter.updateData(role.getRoleAdvance());

        ResMixResultMessage msg = new ResMixResultMessage();
        MixInfoBean bean = new MixInfoBean();
        bean.setIndex(index);
        bean.setId(item.getCfgId());
        msg.setMix(bean);
        MessageUtil.sendMsg(msg, role.getId());
    }


    @Override
    public void reqBreak(Role role, int index) {
        int exist = role.getRoleAdvance().getMixMap().getOrDefault(index, 0);
        if (exist <= 0) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(exist);
        if (!stash.commit(role, LogAction.MIX_BREAK)) {
            return;
        }

        role.getRoleAdvance().getMixMap().remove(index);
        DataCenter.updateData(role.getRoleAdvance());

        ResMixResultMessage msg = new ResMixResultMessage();
        MixInfoBean bean = new MixInfoBean();
        bean.setIndex(index);
        bean.setId(0);
        msg.setMix(bean);
        MessageUtil.sendMsg(msg, role.getId());
    }
}
