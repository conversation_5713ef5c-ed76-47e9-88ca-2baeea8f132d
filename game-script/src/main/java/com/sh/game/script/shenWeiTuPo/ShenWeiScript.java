package com.sh.game.script.shenWeiTuPo;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.shenWeiTuPo.ResShenWeiInfoMessage;
import com.sh.game.common.communication.msg.system.shenWeiTuPo.bean.ShenWeiBean;
import com.sh.game.common.config.model.ShenWeiConfig;
import com.sh.game.common.config.model.ShenWeiYaZhiConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.ShenWeiConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.shenWeiTuPo.script.IShenWeiScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 神威突破
 * @Date 2022-05-06 14:14
 **/

@Script
@Slf4j
public class ShenWeiScript implements IShenWeiScript {


    @Override
    public void reqShenWeiUp(Role role, int type) {
        if (!ShenWeiConst.findShenWeiTypeMap().containsKey(type)) {
            log.error("神威突破-类型错误,传入类型错误,角色:{},昵称:{},类型:{}", role.getId(), role.getName(), type);
            return;
        }
        reqShenWeiTuPoUp(role, type);
    }

    @Override
    public void reqShenWeiInfo(Role role) {
        sendMsg(role);
    }

    /**
     * 神威突破
     *
     * @param role
     * @param type
     */
    private void reqShenWeiTuPoUp(Role role, int type) {
        //如果未找到就初始化等级
        Integer roleShenWeiCfgId = role.getRoleShenWei().computeIfAbsent(type, k -> ShenWeiConst.findInItCfgId(type));
        ShenWeiConfig config = ConfigDataManager.getInstance().getById(ShenWeiConfig.class, roleShenWeiCfgId);
        if (config == null) {
            log.error("神威突破-找不到配置,找不到配置表,角色:{},昵称:{}", role.getId(), role.getName());
            return;
        }

        if (!ConditionUtil.validate(role, config.getConditions())) {
            return;
        }

        ShenWeiConfig nextConfig = ConfigDataManager.getInstance().getById(ShenWeiConfig.class, config.getNextId());
        if (nextConfig == null) {
            log.error("神威突破-找不到下一级配置,找不到配置表,角色:{},昵称:{}", role.getId(), role.getName());
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.SHENWEI_UP)) {
            log.error("神威突破-提交失败,扣除道具失败,角色:{},昵称:{},扣除道具:{}", role.getId(), role.getName(), config.getCost());
            return;
        }
        role.getRoleShenWei().put(type, nextConfig.getId());
        DataCenter.updateData(role);
        //修改属性
        //通知场景服修改属性
        role.proxyCall(proxyPlayer -> proxyPlayer.shenWeiUpdate(role.getRoleShenWei()));
        sendMsg(role);
    }

    /**
     * 返回神威突破信息
     *
     * @param role
     */
    private void sendMsg(Role role) {
        ResShenWeiInfoMessage msg = new ResShenWeiInfoMessage();
        //神威压制buff
        ShenWeiYaZhiConfig shenWeiYaZhiConfig = ConfigDataManager.getInstance().getById(ShenWeiYaZhiConfig.class, findShenWeiYaZhiCfgId(role));
        if (shenWeiYaZhiConfig != null) {
            msg.setYaZhiLevel(shenWeiYaZhiConfig.getLevel());
        }
        List<ShenWeiBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : role.getRoleShenWei().entrySet()) {
            Integer type = entry.getKey();
            Integer shenWeiCfgId = entry.getValue();
            ShenWeiConfig shenWeiConfig = ConfigDataManager.getInstance().getById(ShenWeiConfig.class, shenWeiCfgId);
            if (shenWeiConfig == null) {
                continue;
            }
            ShenWeiBean bean = new ShenWeiBean();
            bean.setCfgId(shenWeiConfig.getId());
            bean.setType(type);
            bean.setLevel(shenWeiConfig.getLevel());
            beans.add(bean);
        }
        msg.getBeans().addAll(beans);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 计算神威压制等级返回压制表cfgId
     *
     * @param role
     * @return
     */
    private int findShenWeiYaZhiCfgId(Role role) {
        return ConfigDataManager.getInstance().getList(ShenWeiYaZhiConfig.class).stream()
                .filter(config -> verifyShenWeiYaZhiLevel(role, config))
                .max(Comparator.comparing(ShenWeiYaZhiConfig::getLevel))
                .map(ShenWeiYaZhiConfig::getId).orElse(0);
    }

    /**
     * 校验该等级的压制是否符合
     *
     * @param role
     * @param config
     * @return
     */
    private boolean verifyShenWeiYaZhiLevel(Role role, ShenWeiYaZhiConfig config) {
        Map<Integer, Integer> roleShenWei = role.getRoleShenWei();
        if (!roleShenWei.keySet().containsAll(ShenWeiConst.Type.SHENSHOU_TYPES)) {
            return false;
        }
        List<int[]> filterResult = config.getTypeLv().stream().filter(v -> {
            if (v.length < 2) {
                return true;
            }
            Integer shenShouCfgId = roleShenWei.getOrDefault(v[0], 0);
            ShenWeiConfig shenWeiConfig = ConfigDataManager.getInstance().getById(ShenWeiConfig.class, shenShouCfgId);
            if (shenWeiConfig == null) {
                return true;
            }
            //如果有一个不符合就被过滤
            return shenWeiConfig.getLevel() < v[1];
        }).collect(Collectors.toList());

        return filterResult.size() <= 0;
    }
}
