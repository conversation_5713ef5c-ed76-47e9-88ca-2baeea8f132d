package com.sh.game.script.battle;

import com.sh.game.GameContext;
import com.sh.game.event.IEventOnServerStartUpScript;
import com.sh.game.option.Remote;
import com.sh.game.server.ConnectionConst;
import com.sh.game.system.battle.IBattleScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/13.
 */
@Script
@Slf4j
public class BattleScript implements IBattleScript , IEventOnServerStartUpScript {
    @Override
    public void onSeverStartUp() {

    }

    public void connect(Remote remote) {
        if (remote == null) {
            return;
        }
        log.info("开始连接跨服,ip:{},port:{}",remote.getHost(), remote.getPort());
        GameContext.getGameServer().getSceneModule().getClient().startModule(ConnectionConst.TYPE.Battle, remote.getHost(), remote.getPort());

    }

    @Override
    public void cancel() {

    }
}
