package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.CommonSlotBean;
import com.sh.game.common.communication.msg.system.activity.ResWeekendHelpInfoMessage;
import com.sh.game.common.communication.msg.system.activity.ResWeekendLimitRecardMessage;
import com.sh.game.common.config.model.ActivityWeekHelpConfig;
import com.sh.game.common.config.model.ActivityWeekPrivilegeConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.WeekEndData;
import com.sh.game.system.activity.script.IActivityWeekendRechargeScript;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.task.TaskManager;
import com.sh.script.annotation.Script;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/14 16:23<br>;
 * 版本：1.0<br>;
 * 描述：周末充值活动
 */
@Log4j2
@Script
public class ActivityWeekEndScript extends AbstractActivityScript implements IActivityWeekendRechargeScript,
        IEventOnRoleLoginScript, IEventOnRoleRechargedScript, IEventOnRoleMidnightScript {

    @Override
    public int getType() {
        return 1019;
    }

    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        super.onScheduleBeginPrivate(schedule, role);
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        weekEnd.setAddTask(true);
        DataCenter.updateData(roleActivity);
        TaskManager.getInstance().addAcitityLoop(role);
    }

    @Override
    protected void onScheduleEndPrivate(ActivitySchedule schedule, Role role) {
        super.onScheduleEndPrivate(schedule, role);
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        mailHelpReward(role, weekEnd, schedule.getActivityID());
        weekEnd.clear();
        DataCenter.updateData(roleActivity);
        TaskManager.getInstance().removeActivityLoop(role);
    }

    /**
     * 周末助力奖励
     *
     * @param role
     * @param weekEnd
     * @param activityID
     */
    private void mailHelpReward(Role role, WeekEndData weekEnd, int activityID) {
        List<ActivityWeekHelpConfig> list = ConfigDataManager.getInstance().getList(ActivityWeekHelpConfig.class);
        if (list == null || list.isEmpty()) {
            return;
        }
        List<int[]> temp = new ArrayList<>();
        for (ActivityWeekHelpConfig config : list) {
            if (config.getActivityID() != activityID || config.getNeedRmb() > weekEnd.getTotalRechargeRmb()
                    || weekEnd.getReceiveSet().contains(config.getRechargeId())) {
                continue;
            }
            temp.addAll(config.getReward());
        }
        if (temp.isEmpty()) {
            return;
        }
        MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.WEEK_END_HELP,
                EmailConst.toMailAttach(temp, LogAction.ACTIVITY_REWARD));
    }

    @Override
    public void onRoleLogin(Role role) {
        if (isNotInActivity(role)) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        sendMessage(weekEnd, role.getId());
    }

    /**
     * 发送消息
     *
     * @param weekEnd
     * @param rid
     */
    private void sendMessage(WeekEndData weekEnd, long rid) {
        ResWeekendHelpInfoMessage message = new ResWeekendHelpInfoMessage();
        message.setTotalRmb(weekEnd.getTotalRechargeRmb());
        message.getConfigList().addAll(weekEnd.getReceiveSet());
        MessageUtil.sendMsg(message, rid);

        ResWeekendLimitRecardMessage msg = new ResWeekendLimitRecardMessage();
        for (Map.Entry<Integer, Integer> entry : weekEnd.getMonthCardGift().entrySet()) {
            CommonSlotBean commonSlotBean = new CommonSlotBean();
            commonSlotBean.setId(entry.getKey());
            commonSlotBean.setIndex(entry.getValue());
            msg.getBuyList().add(commonSlotBean);
        }
        MessageUtil.sendMsg(msg, rid);
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        int activityID = getCurrentActivityID(role);
        if (activityID == 0) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        weekEnd.setTotalRechargeRmb(weekEnd.getTotalRechargeRmb() + rechargeConfig.getCount());
        int rechargeID = rechargeConfig.getId();
        ActivityWeekPrivilegeConfig config = ConfigDataManager.getInstance().getById(ActivityWeekPrivilegeConfig.class,
                activityID + Symbol.JINHAO + rechargeID);
        if (config != null) {
            int count = weekEnd.getMonthCardGift().getOrDefault(rechargeID, 0);
            if (config.getLimitTimes() > count) {
                weekEnd.getMonthCardGift().put(rechargeID, count + 1);
                sendActivityReward(role, config.getReward());
            } else {
                log.error("[Error_Recharge] recharge={},reason=[{}]", rechargeConfig.getId(), "limit=" + config.getLimitTimes() + ",count=" + count);
            }
        } else {
            log.error("[Error_Recharge] recharge={},reason=[{}]", rechargeConfig.getId(), "not found config");
        }
        DataCenter.updateData(roleActivity);
        sendMessage(weekEnd, role.getId());
    }

    @Override
    public void reqReceiveHelpReward(Role role, int configID) {
        int activityID = getCurrentActivityID(role);
        if (activityID == 0) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        if (weekEnd.getReceiveSet().contains(configID)) {
            return;
        }
        ActivityWeekHelpConfig config = ConfigDataManager.getInstance().getById(ActivityWeekHelpConfig.class, activityID + Symbol.JINHAO + configID);
        if (config == null || weekEnd.getTotalRechargeRmb() < config.getNeedRmb()) {
            return;
        }
        weekEnd.getReceiveSet().add(configID);
        DataCenter.updateData(roleActivity);
        sendActivityReward(role, config.getReward());
        ResWeekendHelpInfoMessage message = new ResWeekendHelpInfoMessage();
        message.setTotalRmb(weekEnd.getTotalRechargeRmb());
        message.getConfigList().addAll(weekEnd.getReceiveSet());
        MessageUtil.sendMsg(message, role.getId());
    }

    /**
     * 发送活动奖励
     *
     * @param role
     * @param reward
     */
    private void sendActivityReward(Role role, List<int[]> reward) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(reward);
        if (!stash.commit(role, LogAction.ACTIVITY_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.RECHARGE_REWARD,
                    EmailConst.toMailAttach(reward, LogAction.ACTIVITY_REWARD));
        }
    }

    @Override
    public void onRoleMidnight(Role role) {
        if (isNotInActivity(role)) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        WeekEndData weekEnd = roleActivity.getWeekEnd();
        sendMessage(weekEnd, role.getId());
    }
}
