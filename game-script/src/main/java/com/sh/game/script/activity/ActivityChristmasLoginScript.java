package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivityHolidayLoginConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractActivityLoginRewardScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

@Script
public class ActivityChristmasLoginScript extends AbstractActivityLoginRewardScript {

    @Override
    public int getType() {
        return ActivityConst.CHRISTMAS_LOGIN;
    }

    /**
     * 发送公告
     *
     * @param role   角色
     * @param config 节日登录配置
     */
    @Override
    protected void sendAnnounce(Role role, ActivityHolidayLoginConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
