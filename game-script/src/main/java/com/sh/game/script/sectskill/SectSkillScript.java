package com.sh.game.script.sectskill;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.sectskill.ResSectSkillInfoMessage;
import com.sh.game.common.config.cache.SectSkillCache;
import com.sh.game.common.config.model.SectConfig;
import com.sh.game.common.config.model.SectSkillConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleTransferScript;
//import com.sh.game.protos.SectSkillProtos;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.sectskill.entity.RoleSectSkill;
import com.sh.game.system.sectskill.script.ISectSkillScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Chen
 * @date 2022/6/13 22:38
 */
@Script
@Slf4j
public class SectSkillScript implements ISectSkillScript, IEventOnRoleTransferScript {


    /**
     * 发送门派技能信息
     *
     * @param role 角色
     */
    @Override
    public void sendSectSkillInfo(Role role) {
        RoleSectSkill sectSkill = find(role.getRoleId());
        ResSectSkillInfoMessage msg = new ResSectSkillInfoMessage();
        msg.getConfigIdList().addAll(sectSkill.getSkillMap().values());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 门派技能升级
     *
     * @param role  角色
     * @param group 技能组别
     */
    @Override
    public void sectSkillUpgrade(Role role, int group) {
        RoleSectSkill sectSkill = find(role.getRoleId());
        int currentId = sectSkill.getSkillMap().getOrDefault(group, 0);
        SectSkillConfig currentConfig = ConfigDataManager.getInstance().getById(SectSkillConfig.class, currentId);
        SectSkillConfig nextConfig;

        //不存在则为未学习
        if (currentConfig == null) {
            SectConfig sectConfig = ConfigDataManager.getInstance().getById(SectConfig.class, group);
            if (sectConfig == null) {
                return;
            }
            nextConfig =  ConfigDataManager.getInstance().getById(SectSkillConfig.class, sectConfig.getInitId());
        } else {
            nextConfig = ConfigDataManager.getInstance().getById(SectSkillConfig.class, currentConfig.getNextSkill());
        }
        //下一级不存在 或 不能学习非本职业技能
        if (nextConfig == null || nextConfig.getJob() != role.getCareer()) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(nextConfig.getNeed());
        if (!stash.commit(role, LogAction.SECT_SKILL_COST)) {
            return;
        }

        sectSkill.getSkillMap().put(group, nextConfig.getId());
        DataCenter.updateData(sectSkill);

        AttributeManager.getInstance().attributeCount(role);

        sendSectSkillInfo(role);
    }

    /**
     * 根据角色id获取角色当前门派技能
     *
     * @param roleId 角色id
     * @return RoleSectSkill 角色当前门派技能
     */
    @Override
    public RoleSectSkill find(long roleId) {
        RoleSectSkill sectSkill = DataCenter.get(RoleSectSkill.class, roleId);
        if (sectSkill == null) {
            sectSkill = new RoleSectSkill();
            sectSkill.setId(roleId);
        }
        return sectSkill;
    }

    /**
     * 角色转职事件
     *
     * @param role   角色
     * @param career 转职后职业
     */
    @Override
    public void onRoleTransfer(Role role, int career) {
        RoleSectSkill sectSkill = find(role.getRoleId());
        Map<Integer, Integer> skillMap = sectSkill.getSkillMap();
        SectSkillCache sectSkillCache = ConfigCacheManager.getInstance().getCache(SectSkillCache.class);
        Map<Integer, Integer> finalSkillMap = new HashMap<>(skillMap.size());
        for (Map.Entry<Integer, Integer> entry : skillMap.entrySet()) {
            int group = entry.getKey();
            int sectSkillConfigId = entry.getValue();
            SectConfig sectConfig = ConfigDataManager.getInstance().getById(SectConfig.class, group);
            SectSkillConfig sectSkillConfig = ConfigDataManager.getInstance().getById(SectSkillConfig.class, sectSkillConfigId);
            //配置不存在
            if (sectConfig == null || sectSkillConfig == null) {
                continue;
            }

            int targetGroup;
            switch (career) {
                case RoleConst.Career.ZHAN:
                    targetGroup = sectConfig.getZhanshi();
                    break;
                case RoleConst.Career.FA:
                    targetGroup = sectConfig.getFashi();
                    break;
                case RoleConst.Career.DAO:
                    targetGroup = sectConfig.getDaoshi();
                    break;
                default:
                    //职业不存在
                    targetGroup = 0;
            }

            //通用技能不用转
            if (sectSkillConfig.getJob() == RoleConst.Career.COMMON) {
                targetGroup = group;
            }

            //目标技能配置id
            int targetSectSkillConfigId = sectSkillCache.getConfigIdByGroupAndLevel(targetGroup, sectSkillConfig.getLevel());
            if (targetSectSkillConfigId <= 0) {
                log.error("门派技能-转职-目标技能不存在,roleId: {}, roleName: {} group: {} -> targetGroup: {}, level {}", role.getRoleId(), role.getName(), group, targetGroup, sectSkillConfig.getLevel());
                continue;
            }

            finalSkillMap.put(targetGroup, targetSectSkillConfigId);
        }
        sectSkill.setSkillMap(finalSkillMap);
        DataCenter.updateData(sectSkill);
        AttributeManager.getInstance().attributeCount(role);
    }
}
