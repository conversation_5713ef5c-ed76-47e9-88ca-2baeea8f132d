package com.sh.game.script.roleMount;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.roleMount.ResRoleMountInfoMessage;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.FashionConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnMonsterDie;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.roleMount.entity.RoleMount;
import com.sh.game.system.roleMount.script.IRoleMountScript;
import com.sh.game.system.skill.SkillManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Script
@Slf4j
public class RoleMountScript implements
        IRoleMountScript,
        IEventOnMonsterDie {

    @Override
    public void reqDressOrDisrobeRoleMount(Role role) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        //未穿戴坐骑
        Integer fashionId = appearance.getWears().get(FashionConst.AppearanceType.ROLE_MOUNT);
        if (fashionId == null) {
            return;
        }
        AppearanceConfig fashionConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
        if (fashionConfig == null) {
            return;
        }
        RoleMount roleMount = role.getRoleMount();
        //使用状态
        int useingMount = roleMount.getUseingMount();
        roleMount.setUseingMount(useingMount > 0 ? 0 : fashionConfig.getModel());
        DataCenter.updateData(role);
        //向场景服发送上下坐骑
        role.proxyCall(proxy -> proxy.mountUpdateNotice(role.getRoleMount().getUseingMount() > 0 ? fashionConfig.getModel() : 0));
        SkillManager.getInstance().updateRoleSkill(role);

        ResRoleMountInfoMessage msg = new ResRoleMountInfoMessage();
        msg.setRidingStatus(roleMount.getUseingMount() > 0);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqRoleMountInfo(Role role) {
        RoleMount roleMount = role.getRoleMount();
        ResRoleMountInfoMessage msg = new ResRoleMountInfoMessage();
        msg.setRidingStatus(roleMount.getUseingMount() > 0);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }


    @Override
    public void onMonsterDie(int mapID, int monsterID, int diePointX, int diePointY, long killerID, long ownerID, Set<Long> allThreat, Set<Long> threatAllSet, Set<Long> hurtMap) {
        List<Integer> monsterList = GlobalUtil.findJingHaoList(GameConst.GlobalId.ZUOQI_ZANZHU_MONSTER_LIST);
        if (!monsterList.contains(monsterID)) {
            return;
        }
        Role role = DataCenter.get(Role.class, killerID);
        if (role == null) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Appearance appearance = roleAdvance.getAppearance();

        int now = TimeUtil.getNowOfSeconds();
        AppearanceConfig maxFashionConfig =  null;
        for (Map.Entry<Integer, Integer> entry : appearance.getStatus().entrySet()) {
            if (entry.getValue() >= 0 && entry.getValue() < now) {
                continue;
            }

            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, entry.getKey());
            if (config == null) {
                continue;
            }
            if (config.getType() != FashionConst.AppearanceType.ROLE_MOUNT) {
                continue;
            }
            if (maxFashionConfig == null) {
                maxFashionConfig = config;
                continue;
            }
            if (maxFashionConfig.getLevel() < config.getLevel()) {
                maxFashionConfig = config;
                continue;
            }
        }

        if (maxFashionConfig == null) {
            return;
        }

        //根据时装给赞助点
        String globalValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.ZUOQI_ZANZHU_SCORE);
        List<int[]> list = new JinHaoAndShuXianListConverter().convert(globalValue);
        int score = 0;
        for (int[] ints : list) {
            if (ints.length != 2) {
                continue;
            }
            if (ints[0] != maxFashionConfig.getId()) {
                continue;
            }
            score = ints[1];
            break;
        }
        if (score <= 0) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(BagConst.ItemId.ZANZHUDIAN, score);
        if (!stash.commit(role, LogAction.ZUOQI_ZANZHUDIAN_REWARD)) {
            log.error("坐骑赞助点,击杀boss获得赞助点异常,角色:{},昵称:{},本次击杀获得赞助点:{}", role.getRoleId(), role.getName(), score);
            return;
        }
    }
}
