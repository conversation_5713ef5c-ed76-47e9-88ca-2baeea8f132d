package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.wabao.ResDisplayItemMessage;
import com.sh.game.common.communication.msg.system.wabao.bean.WaBaoGroupBean;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.config.model.WaBaoCostConfig;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.script.activity.abc.AbstractWaBaoScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.game.system.wabao.entity.RoleWabao;
import com.sh.game.system.wabao.entity.WaBao;
import com.sh.script.annotation.Script;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/5 13:23
 */
@Script
public class ActivityLongMaiBaoDiScript extends AbstractWaBaoScript {

    @Override
    public int getType() {
        return 1049;
    }

    @Override
    public void sendDisPlayItems(Role role, List<int[]> addItem, int lastCostId) {
        if (addItem.isEmpty()) {
            return;
        }
        ResDisplayItemMessage msg = new ResDisplayItemMessage();
        msg.setRid(role.getId());
        for (int[] item : addItem) {
            if (item.length < 2) {
                continue;
            }
            for (int i = 0; i < item[1]; i++) {
                msg.getItemId().add(item[0]);
            }
        }
        msg.setLastCostId(lastCostId);
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null) {
            return;
        }
        MessageUtil.sendMsgToMap(msg, player.getMapCfgId());
    }

    @Override
    public void handleBeanList(List<WaBaoGroupBean> beanList, CommonData data, WaBao waBao) {
        beanList.clear();
        for (Map.Entry<Integer, Integer> entry : waBao.getIdCounts().entrySet()) {
            WaBaoGroupBean bean = new WaBaoGroupBean();
            bean.setRewardConfigId(entry.getKey());
            bean.setCount(entry.getValue());
            beanList.add(bean);
        }
        for (Map.Entry<Integer, Integer> entry : data.getWabaoCounts().entrySet()) {
            WaBaoGroupBean bean = new WaBaoGroupBean();
            bean.setRewardConfigId(entry.getKey());
            bean.setCount(entry.getValue());
            beanList.add(bean);
        }
    }

    @Override
    protected void onScheduleEndPrivate(ActivitySchedule schedule, Role role) {
        super.onScheduleEndPrivate(schedule, role);
        RoleWabao roleWaBao = findRoleWaBao(role.getId());
        WaBao waBao = findWaBao(roleWaBao, schedule);
        waBao.setAdditionReward(false);
        DataCenter.updateData(roleWaBao);
        // 活动结束回家
        MapConfig config = ConfigDataManager.getInstance().getById(MapConfig.class, MapConst.LONG_MAI_BAO_DI);
        if (config == null) {
            return;
        }
        int[] homeMap = config.getHomeMap();
        if (homeMap == null || homeMap.length < 3) {
            return;
        }
        if (role.getMapCfgId() != MapConst.LONG_MAI_BAO_DI) {
            return;
        }
        TeleportManager.getInstance().teleport(role, homeMap[0], 0, homeMap[1], homeMap[2]);

    }

    @Override
    public void afterCount(Role role, WaBaoCostConfig costConfig, WaBao wabao,int count) {
        int bonusTime = costConfig.getBonusTimes();
        if (bonusTime <= 0) {
            return;
        }
        if (bonusTime > count || wabao.isAdditionReward()) {
            return;
        }
        AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, costConfig.getFashion());
        if (appearanceConfig == null) {
            return;
        }
        AppearanceManager.getInstance().onUnlock(role, appearanceConfig, null);
        AppearanceManager.getInstance().reqWear(role, appearanceConfig.getId());
        wabao.setAdditionReward(true);
    }
}
