package com.sh.game.script.activity.giftpack;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.activity.ResGiftPackInfoMessage;
import com.sh.game.common.communication.msg.system.activity.bean.ActivityGiftPackBean;
import com.sh.game.common.config.cache.GiftPackConfigCache;
import com.sh.game.common.config.model.ActivityScheduleConfig;
import com.sh.game.common.config.model.GiftPackConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

/**
 * 用该类型临时处理快捷类型活动
 */
@Slf4j
@Script(order = 1)
public class ActivityFreePackScript extends AbstractGiftPackScript {
    @Override
    public int getType() {
        return ActivityConst.FREE_PACK;
    }

    /**
     * 校验活动类型
     *
     * @param config GiftPackConfig
     * @return true:校验未通过 false:校验已通过
     */
    @Override
    protected boolean checkActType(GiftPackConfig config) {
        GiftPackConfigCache cache = ConfigCacheManager.getInstance().getCache(GiftPackConfigCache.class);
        Set<Integer> actIdSet = cache.getFastActIdSet();
        if (CollectionUtils.isNotEmpty(actIdSet) && actIdSet.contains(config.getActivityId())) {
            return false;
        }
        return config.getActType() != getType();
    }

    public void sendMessage(Role role, boolean redPoint) {
        super.sendMessage(role, redPoint);
        sendFastActMsg(role, redPoint);
    }

    /**
     * 发送快捷活动信息
     *
     * @param role     role
     * @param redPoint 是否需要红点
     */
    private void sendFastActMsg(Role role, boolean redPoint) {
        GiftPackConfigCache cache = ConfigCacheManager.getInstance().getCache(GiftPackConfigCache.class);
        Set<Integer> actIdSet = cache.getFastActIdSet();
        ResGiftPackInfoMessage msg = new ResGiftPackInfoMessage();
        for (int actId : actIdSet) {
            if (!isOpen(actId)) {
                continue;
            }
            ActivityScheduleConfig scheduleConfig = ConfigDataManager.getInstance().getById(ActivityScheduleConfig.class, actId);
            msg.setRedPoint(redPoint);
            msg.setActType(scheduleConfig.getType());

            Set<GiftPackConfig> configSet = cache.getConfigByActId(actId);
            for (GiftPackConfig config : configSet) {
                if (!ConditionUtil.validate(role, config.getCondition())) {
                    continue;
                }
                ActivityGiftPackBean bean = new ActivityGiftPackBean();
                bean.setId(config.getId());
                bean.setLimit(getCount(role, config.getId()));
                msg.getBeanList().add(bean);
            }

            MessageUtil.sendMsg(msg, role.getId());
        }
    }
}
