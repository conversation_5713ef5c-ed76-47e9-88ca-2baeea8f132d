package com.sh.game.script.activity;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.activity.ResShaChengJuanXianInfoMessage;
import com.sh.game.common.communication.msg.system.activity.bean.ActivityRankDataBean;
import com.sh.game.common.config.cache.ShaChengJuanXianConfigCache;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.config.model.ShaChengJuanXianConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.entity.sys.ShachengJuanXianData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityRankData;
import com.sh.game.system.activity.entity.role.RoleJuanXianData;
import com.sh.game.system.activity.script.IActivityShaChengJuanXianScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.role.RoleManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 沙城捐献活动
 */
@Script
@Slf4j
public class ActivityShaChengJuanXianScript extends AbstractActivityScript implements IActivityShaChengJuanXianScript,
        IEventOnRoleLoginScript {

    @Override
    public int getType() {
        return ActivityConst.SHACHENG_JUANXIAN;
    }


    private RoleJuanXianData findRoleJuanxianData(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        RoleActivity roleActivity = role.getRoleActivity();
        return roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
    }

    /**
     * 请求沙城捐献
     *
     * @param role
     * @param count
     */
    @Override
    public void reqSubmitJuanXian(Role role, int count) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            log.error("沙城捐献活动提交捐献-活动未开启,角色:{},昵称:{}", role.getId(), role.getName());
            return;
        }

        RoleActivity roleActivity = role.getRoleActivity();
        RoleJuanXianData selfJuanXianData = roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());

        if (count <= 0) {
            log.error("沙城捐献活动提交捐献-捐献数量异常,角色:{},昵称:{},角色已捐献:{},提交捐献:{}", role.getId(), role.getName(), selfJuanXianData.getCount(), count);
            return;
        }
        //判断首次捐献数量
        int firstJuanXianCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHACHENG_JUANXIAN);
        if (selfJuanXianData.getCount() < firstJuanXianCount && count < firstJuanXianCount) {
            log.error("沙城捐献活动提交捐献-首次捐献数量异常,角色:{},昵称:{},角色已捐献:{},提交捐献:{}", role.getId(), role.getName(), selfJuanXianData.getCount(), count);
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(BagConst.ItemId.MONEY_BIND, count);
        if (!stash.commit(role, LogAction.SHACHENG_JUANXIAN)) {
            log.error("沙城捐献活动提交捐献-道具提交失败,角色:{},昵称:{},角色已捐献:{},提交捐献:{}", role.getId(), role.getName(), selfJuanXianData.getCount(), count);
            return;
        }

        //捐献后的数量
        int addCount = selfJuanXianData.getCount() + count;
        selfJuanXianData.setCount(addCount);

        int historyCount = selfJuanXianData.getHistoryCount() + count;
        selfJuanXianData.setHistoryCount(historyCount);
        DataCenter.updateData(roleActivity);
        log.info("沙城捐献活动提交捐献-道具提交成功,角色:{},昵称:{},角色已捐献:{},提交捐献:{}", role.getId(), role.getName(), selfJuanXianData.getCount(), count);

        //计算属性

        //排行榜
        setActivityRankData(role, addCount, schedule);

        //返回信息
        sendMsg(role, schedule);
    }

    /**
     * 请求沙城捐献信息和排行榜信息
     *
     * @param role
     */
    @Override
    public void reqJuanXianInfo(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            log.error("沙城捐献活动请求信息-活动未开启,角色:{},昵称:{}", role.getId(), role.getName());
            return;
        }
        sendMsg(role, schedule);

    }

    /**
     * 返回沙城贡献信息
     */
    private void sendMsg(Role role, ActivitySchedule schedule) {
        ShachengJuanXianData shachengJuanXianData = SysDataProvider.get(ShachengJuanXianData.class);
        List<ActivityRankData> rankDatas = shachengJuanXianData.getJuanXianRankData().getOrDefault(schedule.getActivityID(), new ArrayList<>());

        ResShaChengJuanXianInfoMessage msg = new ResShaChengJuanXianInfoMessage();

        RoleJuanXianData roleJuanxianData = findRoleJuanxianData(role);
        roleJuanxianData.setCount(roleJuanxianData.getCount());
        roleJuanxianData.setHistoryCount(roleJuanxianData.getHistoryCount());

        List<ActivityRankDataBean> msgRankBean = new ArrayList<>();
        for (ActivityRankData rankData : rankDatas) {
            ActivityRankDataBean bean = new ActivityRankDataBean();
            bean.setRid(rankData.getRid());
            bean.setCount(rankData.getCount());
            Role rankRole = DataCenter.get(Role.class, rankData.getRid());
            if (rankRole != null) {
                bean.setName(rankRole.getName());
            } else {
                bean.setName(rankData.getName());
            }
            bean.setRank(rankData.getRank());
            bean.setTime(rankData.getTime());
            msgRankBean.add(bean);
        }
        msg.getRankBean().addAll(msgRankBean);
        MessageUtil.sendMsg(msg, role.getId());
    }


    /**
     * 将排行榜内的玩家称号更改
     */
    private void changeJuanXianRankAppearData(ActivitySchedule schedule) {
        ShaChengJuanXianConfigCache cache = ConfigCacheManager.getInstance().getCache(ShaChengJuanXianConfigCache.class);
        ShachengJuanXianData shachengJuanXianData = SysDataProvider.get(ShachengJuanXianData.class);
        List<ActivityRankData> activityRankData = shachengJuanXianData.getJuanXianRankData().get(schedule.getActivityID());
        //更换排行榜内
        for (ActivityRankData rankdata : activityRankData) {
            ShaChengJuanXianConfig newRankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, rankdata.getRank());

            //获取玩家信息
            Role rankRole = DataCenter.get(Role.class, rankdata.getRid());
            RoleJuanXianData roleActivityData = findRoleJuanxianData(rankRole);

            if (roleActivityData.getRank() == rankdata.getRank()) {
                continue;
            }
            //前三名特殊奖励时装变动
            //原有活动时装
            int oldExtraFashionId = 0;
            ShaChengJuanXianConfig oldExtraAppConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, roleActivityData.getRank());
            if (oldExtraAppConfig != null && oldExtraAppConfig.getExtraFashionId() > 0) {
                oldExtraFashionId = oldExtraAppConfig.getExtraFashionId();
            }

            //更新排行榜玩家时装
            AppearanceConfig newExtraAppConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, newRankConfig.getExtraFashionId());
            int newExtraFashionId = 0;
            if (newExtraAppConfig != null) {
                newExtraFashionId = newExtraAppConfig.getId();
            }
            AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, rankRole.getId(), oldExtraFashionId, newExtraFashionId, -1);

            //更新玩家活动存储时装
            Map<Integer, Integer> wears = findRoleJuanxianData(rankRole).getWears();
            if (newExtraAppConfig != null) {
                wears.put(newExtraAppConfig.getType(), newExtraAppConfig.getId());
                DataCenter.updateData(rankRole);
            }
            //已经激活永久称号不更改称号
            if (roleActivityData.isYongJiuFashion() || yongJiuFashion(rankRole)) {
                continue;
            }

            //原有活动称号
            int oldFashionId = 0;
            ShaChengJuanXianConfig oldAppConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, roleActivityData.getRank());
            if (oldAppConfig != null && oldAppConfig.getFashionId() > 0) {
                oldFashionId = oldAppConfig.getFashionId();
            }

            //更新排行榜玩家称号
            AppearanceConfig newAppConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, newRankConfig.getFashionId());
            if (newAppConfig == null) {
                continue;
            }
            AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, rankRole.getId(), oldFashionId, newAppConfig.getId(), -1);

            //更新玩家活动存储称号
            wears.put(newAppConfig.getType(), newAppConfig.getId());

            DataCenter.updateData(rankRole);
        }
    }

    /**
     * 排行榜排序，排名变动的发邮件
     * 此时公共数据排名为新数据，个人数据排名为旧数据
     */
    private void addRankSendMail(ActivitySchedule schedule) {
        //公共数据
        ShachengJuanXianData shachengJuanXianData = SysDataProvider.get(ShachengJuanXianData.class);
        List<ActivityRankData> rankData = shachengJuanXianData.getJuanXianRankData().get(schedule.getActivityID());

        ShaChengJuanXianConfigCache cache = ConfigCacheManager.getInstance().getCache(ShaChengJuanXianConfigCache.class);
        for (ActivityRankData newData : rankData) {
            //个人数据
            Role rankRole = DataCenter.get(Role.class, newData.getRid());
            RoleJuanXianData oldData = findRoleJuanxianData(rankRole);

            //已经激活永久称号不发公告邮件
            if (oldData.isYongJiuFashion() || yongJiuFashion(rankRole)) {
                continue;
            }

            //入榜奖励
            if (oldData.getRank() == 0) {
                ShaChengJuanXianConfig rankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, newData.getRank());
                AppearanceConfig fashionConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, rankConfig.getFashionId());
                if (fashionConfig == null) {
                    continue;
                }
                AppearanceConfig fashionExtraConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, rankConfig.getExtraFashionId());
                if (newData.getRank() <= 3 && fashionExtraConfig == null) {
                    continue;
                }

                //排名提升发公告
                if (newData.getRank() > oldData.getRank()) {
                    AnnounceManager.getInstance().post(rankConfig.getAnnounceId(), 0, rankRole, rankConfig.getRank(), fashionConfig.getName());
                }
                if (newData.getRank() <= 3) {
                    MailManager.getInstance().sendMail(rankRole.getId(), EmailConst.MailId.SHACHENG_JUANXIAN_SP_ADDRANK, null, newData.getRank(), fashionConfig.getName(), fashionExtraConfig.getName());
                } else {
                    MailManager.getInstance().sendMail(rankRole.getId(), EmailConst.MailId.SHACHENG_JUANXIAN_ADDRANK, null, newData.getRank(), fashionConfig.getName());
                }

                continue;
            }

            //榜内变动
            if (newData.getRank() != oldData.getRank()) {
                //老排名奖励
                ShaChengJuanXianConfig oldRankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, oldData.getRank());
                AppearanceConfig oldFashionConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, oldRankConfig.getFashionId());
                if (oldFashionConfig == null) {
                    continue;
                }
                //新排名奖励
                ShaChengJuanXianConfig newRankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, newData.getRank());
                AppearanceConfig newFashionConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, newRankConfig.getFashionId());
                if (newFashionConfig == null) {
                    continue;
                }

                //排名提升发公告
                if (newData.getRank() < oldData.getRank()) {
                    AnnounceManager.getInstance().post(newRankConfig.getAnnounceId(), 0, rankRole, newRankConfig.getRank(), newFashionConfig.getName());
                }

                MailManager.getInstance().sendMail(rankRole.getId(), EmailConst.MailId.SHACHENG_JUANXIAN_CHANGERANK, null, oldData.getRank(), newData.getRank());

            }
        }

    }

    /**
     * 放入activityRankData排行榜数据
     */
    private void setActivityRankData(Role role, int addCount, ActivitySchedule schedule) {
        ShaChengJuanXianConfigCache cache = ConfigCacheManager.getInstance().getCache(ShaChengJuanXianConfigCache.class);
        List<ShaChengJuanXianConfig> rankConfig = cache.getTypeConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE);

        ShachengJuanXianData juanxianRankData = SysDataProvider.get(ShachengJuanXianData.class);
        juanxianRankData.getJuanXianRankData().compute(schedule.getActivityID(), (k, v) -> {
            if (v == null) {
                v = new LinkedList<>();
            }
            //先判断榜内是否有此人的数据，如果有将该人的等级覆盖
            for (ActivityRankData rankData : v) {
                if (rankData.getRid() != role.getId()) {
                    continue;
                }
                rankData.setCount(addCount);
                rankData.setTime(TimeUtil.getNowOfSeconds());
                v.sort(findComparator());
                break;
            }
            //先判断榜内是否有此人的数据
            List<ActivityRankData> filterRankData = v.stream().filter(juanXianRankData -> juanXianRankData.getRid() == role.getId()).collect(Collectors.toList());
            if (filterRankData.size() == 0) {
                if (v.size() + 1 <= rankConfig.size()) {
                    addRankData(role, addCount, v);
                } else {
                    //要排序看排行榜中等级最低的玩家
                    Collections.sort(v, findComparator());
                    if (addCount > v.get(v.size() - 1).getCount()) {
                        //移除排行榜的玩家的称号清除
                        ActivityRankData activityRankData = v.remove(v.size() - 1);
                        //出榜
                        removeRank(activityRankData);
                        addRankData(role, addCount, v);
                    }
                }
            }
            for (ActivityRankData activityRank : v) {
                activityRank.setRank(v.indexOf(activityRank) + 1);
            }
            return v;
        });

        //榜内发邮件
        addRankSendMail(schedule);
        DataCenter.updateData(juanxianRankData);

        //将排行榜内的玩家称号更改
        changeJuanXianRankAppearData(schedule);

        //将排名存入个人数据
        for (ActivityRankData activityRankData : juanxianRankData.getJuanXianRankData().get(schedule.getActivityID())) {
            Role rankRole = DataCenter.get(Role.class, activityRankData.getRid());
            RoleActivity rankRoleData = rankRole.getRoleActivity();
            RoleJuanXianData rankJuanxianData = rankRoleData.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
            rankJuanxianData.setRank(activityRankData.getRank());
            DataCenter.updateData(rankRoleData);
        }
    }

    /**
     * 将数据存入排行榜
     */
    private void addRankData(Role role, int addCount, List<ActivityRankData> v) {
        ActivityRankData rankData = new ActivityRankData();
        rankData.setRid(role.getId());
        rankData.setName(role.getName());
        rankData.setTime(TimeUtil.getNowOfSeconds());
        rankData.setCount(addCount);
        v.add(rankData);
        Collections.sort(v, findComparator());
    }

    /**
     * 出榜
     * @param activityRankData
     */
    private void removeRank(ActivityRankData activityRankData){
        Role removeRankRole = DataCenter.get(Role.class, activityRankData.getRid());
        if (removeRankRole == null) {
            return;
        }
        RoleActivity roleActivity = removeRankRole.getRoleActivity();
        ActivitySchedule schedule = getAvailableSchedule(removeRankRole);
        RoleJuanXianData selfJuanXianData = roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
        //已经激活永久称号不发公告邮件
        if (!selfJuanXianData.isYongJiuFashion()) {
            delActivityRoleFashion(activityRankData.getRid());
            //出榜发公告
            MailManager.getInstance().sendMail(activityRankData.getRid(), EmailConst.MailId.SHACHENG_JUANXIAN_DELRANK, null, 0);
        }
        selfJuanXianData.setRank(0);
        DataCenter.updateData(removeRankRole);
        log.info("沙城捐献活动出榜,角色:{},昵称:{},捐献数量:{}", removeRankRole.getRoleId(), removeRankRole.getName(), activityRankData.getCount());
    }

    /**
     * 移除活动玩家称号
     */
    private void delActivityRoleFashion(long rid) {
        Role role = DataCenter.get(Role.class, rid);
        RoleActivity roleActivity = role.getRoleActivity();
        ActivitySchedule schedule = getAvailableSchedule(role);
        RoleJuanXianData selfJuanXianData = roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());

        //删除玩家称号
        for (Map.Entry<Integer, Integer> entry : selfJuanXianData.getWears().entrySet()) {
            AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, rid, 0, entry.getValue());
        }

        //删除玩家活动存储称号
        selfJuanXianData.getWears().clear();

        DataCenter.updateData(roleActivity);
    }

    protected Comparator<ActivityRankData> findComparator() {
        return (e1, e2) -> {
            if (e1.getCount() > e2.getCount()) {
                return -1;
            }
            if (e1.getCount() == e2.getCount()) {
                if (e1.getTime() < e2.getTime()) {
                    return -1;
                }
            }
            return 1;
        };
    }

    /**
     * 活动开始
     *
     * @param schedule
     */
    @Override
    protected void onScheduleBegin(ActivitySchedule schedule) {
        ShachengJuanXianData shachengJuanXianData = SysDataProvider.get(ShachengJuanXianData.class);
        shachengJuanXianData.getJuanXianRankData().compute(schedule.getActivityID(), (k, v) -> v = new ArrayList<>());
        DataCenter.updateData(shachengJuanXianData);
    }

    /**
     * 活动开始前处理
     *
     * @param schedule
     * @param role
     */
    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        //玩家活动储存合服次数
        RoleActivity roleActivity = role.getRoleActivity();
        roleActivity.getJuanXianDataMap().computeIfAbsent(schedule.getActivityID(), k -> new RoleJuanXianData());
        DataCenter.updateData(roleActivity);
    }


    /**
     * 玩家登录事件
     *
     * @param role
     */
    @Override
    public void onRoleLogin(Role role) {

        //玩家活动储存合服次数
        RoleActivity roleActivity = role.getRoleActivity();
        ActivitySchedule schedule = getAvailableSchedule(role);
        RoleJuanXianData roleJuanXianData = roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
        int roleMergeCount = roleJuanXianData.getMergeCount();
        //系统记录合服次数
        OtherData otherData = DataCenter.getOtherData();
        int sysMergeCount = otherData.getMergeCount();

        //判断是否合过服
        if (sysMergeCount != roleMergeCount) {
            //合服后清除个人活动数据
            if (!roleJuanXianData.isYongJiuFashion()) {
                delActivityRoleFashion(role.getId());
            }
            log.info("合服清除沙捐数据,角色:{},昵称:{},捐献数量:{},排名:{},合服次数:{}",role.getId(), role.getName(), roleJuanXianData.getCount(), roleJuanXianData.getRank(), roleJuanXianData.getMergeCount());
            roleJuanXianData.setRank(0);
            roleJuanXianData.setCount(0);
            //更新玩家活动存储合服次数
            roleJuanXianData.setMergeCount(sysMergeCount);
        }
        DataCenter.updateData(roleActivity);

        //矫正个人排名
        ShachengJuanXianData shachengJuanXianData = SysDataProvider.get(ShachengJuanXianData.class);
        List<ActivityRankData> activityRankData = shachengJuanXianData.getJuanXianRankData().getOrDefault(schedule.getActivityID(), new ArrayList<>());
        List<ActivityRankData> selfData = activityRankData.stream().filter(k -> k.getRid() == role.getRoleId()).collect(Collectors.toList());

        ShaChengJuanXianConfigCache cache = ConfigCacheManager.getInstance().getCache(ShaChengJuanXianConfigCache.class);
        //检测玩家斗笠修复fix
        if (selfData.size() > 0) {
            ShaChengJuanXianConfig rankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, selfData.get(0).getRank());
            if (rankConfig != null && !roleJuanXianData.getWears().containsKey(13)) {
                AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, role.getRoleId(), 0, rankConfig.getExtraFashionId(), -1);
                roleJuanXianData.getWears().put(rankConfig.getType(), rankConfig.getExtraFashionId());
                DataCenter.updateData(role);
            }
        }
        if (roleJuanXianData.getCount() > 0 && selfData.size() == 0) {
            log.info("沙捐数据修复-排名矫正,角色:{},昵称:{},修改前排名:{}",role.getRoleId(), role.getName(), roleJuanXianData.getRank());
            roleJuanXianData.setRank(0);
        }
        yongJiuFashion(role);


        //兼容线上数据，前三名解锁斗笠
        if (!roleJuanXianData.isCheckFashion()) {
            if (selfData.size() <= 0) {
                return;
            }
            ActivityRankData selfRankData = selfData.get(0);
            //符合给斗笠
            if (selfRankData.getRank() > 3) {
                return;
            }
            ShaChengJuanXianConfig rankConfig = cache.getRankConfig(ShaChengJuanXianConst.JuanXianType.RANK_TYPE, selfRankData.getRank());
            if (rankConfig == null) {
                return;
            }
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, rankConfig.getExtraFashionId());
            if (appearanceConfig == null) {
                return;
            }
            AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, role.getId(), 0, appearanceConfig.getId(), -1);
            roleJuanXianData.getWears().put(appearanceConfig.getType(), appearanceConfig.getId());
            roleJuanXianData.setCheckFashion(true);
            DataCenter.updateData(roleActivity);
        }

    }

    /**
     * 校验是否激活永久称号
     * @param role
     * @return
     */
    private boolean yongJiuFashion(Role role) {
        //系统记录合服次数
        OtherData otherData = DataCenter.getOtherData();
        int sysMergeCount = otherData.getMergeCount();

        RoleActivity roleActivity = role.getRoleActivity();
        ActivitySchedule schedule = getAvailableSchedule(role);
        RoleJuanXianData roleJuanXianData = roleActivity.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
        ShaChengJuanXianConfigCache cache = ConfigCacheManager.getInstance().getCache(ShaChengJuanXianConfigCache.class);
        List<ShaChengJuanXianConfig> configList = cache.getTypeConfig(ShaChengJuanXianConst.JuanXianType.YONGJIU_FASHION_TYPE);
        ShaChengJuanXianConfig juanXianConfig = null;
        if (configList.size() > 0) {
            juanXianConfig = configList.get(0);
        }
        //判断发放永久称号条件是否满足
        if (sysMergeCount > 0
                && juanXianConfig != null
                && roleJuanXianData.getHistoryCount() >= juanXianConfig.getGear()
                && !roleJuanXianData.isYongJiuFashion()) {

            RoleActivity roleActivity1 = role.getRoleActivity();
            RoleJuanXianData roleJuanXianData1 = roleActivity1.getJuanXianDataMap().getOrDefault(schedule.getActivityID(), new RoleJuanXianData());
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, juanXianConfig.getFashionId());
            if (appearanceConfig == null) {
                return false;
            }
            delActivityRoleFashion(role.getRoleId());
            //删除玩家称号（只删除称号
            for (Map.Entry<Integer, Integer> entry : roleJuanXianData1.getWears().entrySet()) {
                if (entry.getKey() != FashionConst.AppearanceType.APPEARANCE_TYPE) {
                    continue;
                }
                AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, role.getRoleId(), 0, entry.getValue());
                roleJuanXianData1.getWears().remove(entry.getKey());
            }

            //删除玩家活动存储称号


            roleJuanXianData1.setYongJiuFashion(true);
            roleJuanXianData1.getWears().put(appearanceConfig.getType(), appearanceConfig.getId());
            DataCenter.updateData(roleActivity1);
            AppearanceManager.getInstance().modifyFashion(LogAction.RANK_UPDATE_REMOVE_TITLE, role.getRoleId(), 0, juanXianConfig.getFashionId(), -1);
            MailManager.getInstance().sendMail(role.getRoleId(), juanXianConfig.getMailid(), null, appearanceConfig.getName());
            log.info("沙捐活动类型:{},玩家激活永久称号,角色:{},昵称:{},历史捐献数量:{}", schedule.getActivityType(), role.getRoleId(), role.getName(), roleJuanXianData1.getHistoryCount());
            return true;
        }
        return false;
    }
}
