package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ActivityTreasuryCountConfig;
import com.sh.game.common.config.model.WaBaoCostConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractWaBaoCountRewardScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Script
@Slf4j
public class ActivityMingYunZhiLunScript extends AbstractWaBaoCountRewardScript {
    @Override
    public int getType() {
        return ActivityConst.MING_YUN_ZHI_LUN;
    }

    /**
     * 发送本次挖宝结果道具信息
     *
     * @param role    角色
     * @param addItem 道具
     */
    @Override
    public void sendWabaoItems(Role role, List<int[]> addItem) {
    }

    /**
     * 发送领取次数奖励公告
     *
     * @param role   角色
     * @param config 次数奖励配置
     */
    @Override
    protected void sendCountRewardAnnounce(Role role, ActivityTreasuryCountConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }

    @Override
    public void reqWaBao(Role role, int wabaoId) {
        if (!canWaBao(role, wabaoId)) {
            return;
        }
        super.reqWaBao(role, wabaoId);
    }

    private boolean canWaBao(Role role, int wabaoId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return false;
        }

        WaBaoCostConfig costConfig = ConfigDataManager.getInstance().getById(WaBaoCostConfig.class, wabaoId);
        if (costConfig == null) {
            return false;
        }

        int daily = role.getRechargeDaily();
        int count = daily / 50;

        int usedCount = getWaBaoLimitCount(role, getType(), schedule);

        return count >= usedCount + costConfig.getTimes();
    }

}
