package com.sh.game.script.equip;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.EquipTouBaoConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.log.entity.RoleTouBaoLog;
import com.sh.game.system.equip.script.IEquipTouBaoScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Script
public class EquipTouBaoScript implements IEquipTouBaoScript {


    @Override
    public void toubao(Role role, long lid) {
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(lid);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        if (itemConfig == null) {
            log.error("道具配置表id不存在->{}", item.getCfgId());
            return;
        }
        EquipTouBaoConfig touBaoConfig = ConfigDataManager.getInstance().getById(EquipTouBaoConfig.class, item.getCfgId());
        if (touBaoConfig == null) {
            log.error("道具配置表id不能投保->{}", item.getCfgId());
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        if (touBaoConfig.getCost() != null && touBaoConfig.getCost().size() > 0) {
            stash.decrease(touBaoConfig.getCost());
        }
        stash.update(item, up -> {
            up.getEquipData().setToubao(1);
        });
        if (!stash.commit(role, LogAction.TOUBAO)) {
            return;
        }

        role.updateBackpack(stash.getCommitItemChanges());
        touBaoLog(role, itemConfig, 1, touBaoConfig.getCost());
    }

    @Override
    public void qibao(Role role, long lid) {
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(lid);
        if (item == null) {
            return;
        }
        if (item.getEquipData() == null || item.getEquipData().getToubao() <= 0) {
            return;
        }
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        if (itemConfig == null) {
            log.error("道具配置表id不存在->{}", item.getCfgId());
            return;
        }
        EquipTouBaoConfig touBaoConfig = ConfigDataManager.getInstance().getById(EquipTouBaoConfig.class, item.getCfgId());
        if (touBaoConfig == null) {
            log.error("道具配置表id不能投保->{}", item.getCfgId());
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        if (touBaoConfig.getCost2() != null && touBaoConfig.getCost2().size() > 0) {
            stash.decrease(touBaoConfig.getCost2());
        }
        stash.update(item, up -> {
            up.getEquipData().setToubao(0);
        });
        if (!stash.commit(role, LogAction.QIBAO)) {
            return;
        }

        role.updateBackpack(stash.getCommitItemChanges());

        // 邮件返还保金
        List<Item> list = new ArrayList<>();
        for (int[] cost : touBaoConfig.getCost()) {
            Item costItem = ItemUtil.create(cost[0], cost[1], LogAction.QIBAO);
            list.add(costItem);
        }

        MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.TOUBAO_RETURN, list, item.findItemConfig().getName());
        touBaoLog(role, itemConfig, 0, touBaoConfig.getCost2());
    }

    /**
     * 玩家投保记录
     * @param role
     * @param itemConfig
     * @param operateType
     */
    private void touBaoLog(Role role, ItemConfig itemConfig, int operateType, List<int[]> cost) {
        if (role == null || itemConfig == null) {
            return;
        }
        RoleTouBaoLog log = new RoleTouBaoLog(role);
        log.setRoleId(role.getId());
        log.setRoleName(role.getName());
        log.setUid(role.getUid());
        log.setAccount(role.getAccount());
        log.setEquipName(itemConfig.getName());
        log.setPlace(itemConfig.getType());
        log.setOperateType(operateType);
        log.setTime(TimeUtil.getNowOfSeconds());
        log.setCost(cost);
        log.submit();
    }
}
