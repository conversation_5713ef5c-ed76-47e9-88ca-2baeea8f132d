package com.sh.game.script.daily.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.HinderRewardConfig;
import com.sh.game.common.config.model.MountConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.system.hinder.script.IHinderScript;
import com.sh.script.annotation.Script;

/**
 * <AUTHOR>
 * @data 2020/9/24 19:43
 */
@Script
public class HinderScript implements IHinderScript {

    @Override
    public void hinderEnd(Role role, int rank) {
        HinderRewardConfig config = ConfigDataManager.getInstance().getById(HinderRewardConfig.class, rank);
        if (config == null) {
            config = ConfigDataManager.getInstance().getById(HinderRewardConfig.class, 0);
            if (config == null) {
                return;
            }
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        stash.commitToMail(role.getId(), EmailConst.MailId.HINDER_END);
    }

    @Override
    public void hinderEnter(Role role) {
        MountConfig config = ConfigDataManager.getInstance().getById(MountConfig.class, role.getRoleAdvance().getMount());
        if (config == null) {
            config = ConfigDataManager.getInstance().getById(MountConfig.class, 1);
            if (config == null) return;
        }

        MountConfig finalConfig = config;
        role.proxyCall(proxy -> proxy.mountUpdateNotice(finalConfig.getId()));
    }
}
