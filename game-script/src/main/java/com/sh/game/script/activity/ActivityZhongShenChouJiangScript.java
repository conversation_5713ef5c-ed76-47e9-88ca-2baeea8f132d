package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivityTreasuryCountConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractWaBaoCountRewardScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Script
@Slf4j
public class ActivityZhongShenChouJiangScript extends AbstractWaBaoCountRewardScript {
    @Override
    public int getType() {
        return ActivityConst.ZHONG_SHEN_CHOU_JIANG;
    }

    /**
     * 发送本次挖宝结果道具信息
     *
     * @param role    角色
     * @param addItem 道具
     */
    @Override
    public void sendWabaoItems(Role role, List<int[]> addItem) {
    }

    /**
     * 发送领取次数奖励公告
     *
     * @param role      角色
     * @param config    次数奖励配置
     */
    @Override
    protected void sendCountRewardAnnounce(Role role, ActivityTreasuryCountConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }

    /**
     * 有些活动要根据玩家创角天数走，所有当前必须是系统永久开启，再通过玩家判断是否开始
     *
     * @param role          角色
     * @param available     活动
     * @return  活动
     */
    @Override
    protected ActivitySchedule getRoleOpen(Role role, ActivitySchedule available) {
        return defaultRoleOpen(role, available);
    }
}
