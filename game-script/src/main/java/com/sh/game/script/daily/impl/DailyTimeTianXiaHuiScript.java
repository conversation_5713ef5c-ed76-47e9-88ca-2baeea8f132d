package com.sh.game.script.daily.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.entity.map.MapAllocContext;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.script.daily.abc.AbstractDailyTimedScript;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.script.annotation.Script;

import java.util.List;

import static com.sh.game.common.constant.DailyConst.DailyType.TIAN_XIA_HUI;
import static com.sh.game.common.constant.MapConst.DUPLICATE_TIAN_XIA_HUI;

/**
 * 日常天下会
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-12-07
 **/
@Script
public class DailyTimeTianXiaHuiScript extends AbstractDailyTimedScript {
    @Override
    public int getType() {
        return TIAN_XIA_HUI;
    }

    @Override
    protected void onScheduleBegin(DailySchedule schedule) {
        super.onScheduleBegin(schedule);
        List<MapConfig> list = ConfigDataManager.getInstance().getList(MapConfig.class);
        for (MapConfig mapConfig : list) {
            if (mapConfig.getMapCode() == DUPLICATE_TIAN_XIA_HUI) {
                MapAllocContext context = new MapAllocContext();
                context.setMapCfgId(mapConfig.getMapCode());
                context.setSys(true);
                context.setDailyType(schedule.getConf().getType());
                MapProxyManager.getInstance().alloc(context);
            }
        }
    }
}
