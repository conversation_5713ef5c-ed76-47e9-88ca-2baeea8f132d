package com.sh.game.script.welfare;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.welfare.ResLoginPushOnlineRewardMessage;
import com.sh.game.common.config.model.OnlineRewardConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleLogoutScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.system.welfare.entity.Reward;
import com.sh.game.system.welfare.script.IOnlineRewardScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;

@Slf4j
@Script
public class OnlineRewardScript implements IOnlineRewardScript, IEventOnRoleLoginScript, IEventOnRoleLogoutScript, IEventOnRoleMidnightScript {

    @Override
    public void sendOnlineRewardInfo(Role role) {
        Reward reward = role.getRoleDaily().getReward();
        ResLoginPushOnlineRewardMessage msg = new ResLoginPushOnlineRewardMessage();
        msg.setOnlineSeconds(updateOnlineTime(role));
        msg.getReceivedRewards().addAll(reward.getAcquired());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void receiveReward(Role role, int confId) {
        OnlineRewardConfig config = ConfigDataManager.getInstance().getById(OnlineRewardConfig.class, confId);
        if (config == null) {
            return;
        }
        if (config.getTime() > updateOnlineTime(role)) {
            TipUtil.show(role, CommonTips.脚本_条件不满足);
            return;
        }
        if (role.getRoleDaily().getReward().getAcquired().contains(config.getId())) {
            TipUtil.show(role.getId(), CommonTips.脚本_重复领取);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.ONLINE_REWARD)) {
            return;
        }

        role.getRoleDaily().getReward().getAcquired().add(confId);
        DataCenter.updateData(role.getRoleDaily());

        sendOnlineRewardInfo(role);
    }

    private int updateOnlineTime(Role role) {
        int now = TimeUtil.getNowOfSeconds();
        Reward reward = role.getRoleDaily().getReward();
        int loginTime = reward.getLoginTime();
        if (loginTime <= 0) {
            loginTime = now;
        }
        int onlineSeconds = now - loginTime + reward.getOnlineSeconds();
        reward.setLoginTime(now);
        reward.setOnlineSeconds(onlineSeconds);
        DataCenter.updateData(role.getRoleDaily());

        return onlineSeconds;
    }

    @Override
    public void onRoleLogin(Role role) {
        role.getRoleDaily().getReward().setLoginTime(TimeUtil.getNowOfSeconds());
    }

    @Override
    public void onRoleLogout(Role role) {
        updateOnlineTime(role);
    }

    @Override
    public void onRoleMidnight(Role role) {
        role.getRoleDaily().getReward().setLoginTime(0);
        onRoleLogin(role);
        sendOnlineRewardInfo(role);
    }
}
