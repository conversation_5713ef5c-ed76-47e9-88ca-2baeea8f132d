package com.sh.game.script.activity.giftpack;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * 神魔历练
 * <AUTHOR>
 * @date 2023/12/6
 */
@Slf4j
@Script(order = 1)
public class ActivityChaoZhiLiBaoScript extends AbstractGiftPackScript {
    /**
     * 活动类型
     *
     * @return
     */
    @Override
    public int getType() {
        return ActivityConst.CHAO_ZHI_LI_BAO;
    }
}
