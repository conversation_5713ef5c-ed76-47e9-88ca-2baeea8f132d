package com.sh.game.script.fusion;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.fusion.ResFusionBuyMessage;
import com.sh.game.common.communication.msg.system.fusion.ResFusionViewMessage;
import com.sh.game.common.communication.msg.system.fusion.bean.FusionItemBean;
import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.ItemConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.FusionData;
import com.sh.game.common.entity.sys.FusionSchedule;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.fusion.entity.FusionItem;
import com.sh.game.system.fusion.script.IFusionScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


@Script
@Slf4j
public class FusionScript implements IFusionScript, IEventScheduleUpdateOnMinuteScript {

    @Override
    public void reqSell(Role role, List<Long> uids) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        BackpackStash stash = new BackpackStash(role);

        List<Item> fusions = new ArrayList<>();
        for (long uid : uids) {
            Item item = backpack.findItemByUniqueId(uid);
            if (item == null) {
                TipUtil.show(role.getId(), CommonTips.脚本_参数错误);
                return;
            }
            FusionConfig conf = ConfigDataManager.getInstance().getById(FusionConfig.class, item.getCfgId());
            if (conf == null) {
                TipUtil.show(role.getId(), CommonTips.脚本_参数错误);
                return;
            }

            stash.decrease(item);
            stash.decrease(conf.getRongliancost());
            stash.increase(conf.getRonglianreward());
            fusions.add(item);
        }

        stash.commit(role, LogAction.FUSION_SELL, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
            @Override
            public void callback(BackpackStashCommitRetNotice notice) {
                long unionId = role.getUnion().getId();
                fusions.forEach(one -> fusionInsert(one, unionId, 0, false, 0));

                for (Item item : stash.findGenerates()) {
                    if (ItemConst.Announce.isAnnounceOf(item.findItemConfig().getXiyou(), ItemConst.Announce.FUSION) &&
                            ConditionUtil.validate(item.findItemConfig().getXiyou_condition())) {
                        AnnounceManager.getInstance().post(2092, 0, role, item);
                    }
                }
            }
        });
    }

    @Override
    public void reqView(Role role) {
        FusionData data = DataCenter.getFusionData();

        List<FusionItemBean> fusions = new ArrayList<>();
        for (FusionItem fusionItem : data.getFusions().values()) {
            Item item = fusionItem.getItem();
            if (item == null) {
                continue;
            }

            FusionItemBean bean = new FusionItemBean();
            bean.setType(fusionItem.getType());
            bean.setGuildId(fusionItem.getGuild());
            bean.setItem(ItemUtil.packCommonItemBean(fusionItem.getItem()));
            fusions.add(bean);
        }

        ResFusionViewMessage msg = new ResFusionViewMessage();
        msg.getItems().addAll(fusions);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqBuy(Role role, long uid) {
        FusionData data = DataCenter.getFusionData();
        FusionItem fusionItem = data.getFusions().get(uid);
        if (fusionItem == null) {
            TipUtil.show(role.getId(), CommonTips.脚本_该商品不存在);
            return;
        }
        final Item item = fusionItem.getItem();
        if (item == null) {
            TipUtil.show(role.getId(), CommonTips.脚本_该商品不存在);
            return;
        }
        FusionConfig conf = ConfigDataManager.getInstance().getById(FusionConfig.class, item.getCfgId());
        if (conf == null) {
            return;
        }

        double multiple = 1.0;
        if (role.getUnionId() > 0 && role.getUnionId() == fusionItem.getGuild()) {
            multiple = GlobalConfig.fusionDiscount;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(conf.getHonour(), multiple);
        stash.increase(item);
        stash.commit(role, LogAction.FUSION_BUY, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
            @Override
            public void callback(BackpackStashCommitRetNotice notice) {
                if (!notice.isSuccess()) {
                    return;
                }

                data.getFusions().remove(fusionItem.getId());
                DataCenter.updateData(data);
                onFusionItemEp(fusionItem);

                ResFusionBuyMessage msg = new ResFusionBuyMessage();
                msg.setUid(fusionItem.getId());
                MessageUtil.sendMsg(msg, role.getId());
            }
        });
    }

    private boolean fusionInsert(Item fromItem, long guildId, int type, boolean neverSpill, int order) {
//        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, fromItem.getCfgId());
//        if (itemConfig == null) {
//            return false;
//        }
//
//        if (fromItem.getId() <= 0) {
//            ItemUtil.buildItem(fromItem);
//        } else {
//            fromItem = ItemUtil.copy(fromItem);
//        }
//
//        FusionData data = DataCenter.getFusionData();
//
//        FusionItem item = new FusionItem();
//        item.setId(fromItem.getId());
//        item.setItem(fromItem);
//        item.setType(type);
//        item.setGuild(guildId);
//        item.setNeverSpill(neverSpill);
//        // calculate order
//        if (order <= 0) {
//            order = getItemLocationOrder(itemConfig);
//        }
//        item.setOrder(itemConfig.getRank() * 1000 + order);
//
//        if (data.getFusions().size() >= 720) {
//            FusionItem spill = null;
//            for (FusionItem one: data.getFusions().values()) {
//                if (one.getItem() == null) {
//                    spill = one;
//                    break;
//                }
//
//                if (one.isNeverSpill()) {
//                    continue;
//                }
//                if (spill == null || spill.getOrder() < one.getOrder()) {
//                    spill = one;
//                }
//            }
//
//            if ((spill == null || spill.getOrder() <= item.getOrder()) && spill.getItem() != null) {
//                return false;
//            }
//
//            data.getFusions().remove(spill.getId());
//            onFusionItemEp(spill);
//        }
//
//        data.getFusions().put(item.getId(), item);
//        DataCenter.updateData(data);
        return true;
    }

    private int getItemLocationOrder(ItemConfig item) {
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, item.getType());
        if (locationConfig == null) {
            return 999;
        }
        WeaponPosConfig posConfig = ConfigDataManager.getInstance().getById(WeaponPosConfig.class, locationConfig.getPos1()[0]);
        if (posConfig == null) {
            return 999;
        }
        return posConfig.getRank();
    }

    private void onFusionItemEp(FusionItem fusionItem) {
        if (fusionItem.getType() <= 0) {
            return;
        }
        FusionScheduleConfig config = ConfigDataManager.getInstance().getById(FusionScheduleConfig.class, fusionItem.getType());
        if (config == null) {
            return;
        }

        FusionSchedule schedule = SysDataProvider.get(FusionSchedule.class);
        schedule.getSchedule().put(config.getId(), TimeUtil.getNowOfSeconds() + config.getTime());
        DataCenter.updateData(schedule);
    }

    @Override
    public void scheduleUpdateOnMinute() {
        FusionSchedule schedule = SysDataProvider.get(FusionSchedule.class);
        int now = TimeUtil.getNowOfSeconds();
        boolean update = false;

        for (FusionScheduleConfig config : ConfigDataManager.getInstance().getList(FusionScheduleConfig.class)) {
            if (!schedule.getSchedule().containsKey(config.getId())) {
                schedule.getSchedule().put(config.getId(), now + config.getTime());
                update = true;
            }
            int time = schedule.getSchedule().get(config.getId());
            if (time < 0 || time > now) {
                continue;
            }

            if (fusionInsert(ItemUtil.create(config.getItem(), 1, LogAction.FUSION_SELL), 0L, config.getId(), config.getDestroy() == 0, config.getOrder())) {
                time = -1;
            } else {
                time = now + config.getTime();
            }
            schedule.getSchedule().put(config.getId(), time);
            update = true;
        }

        if (update) {
            DataCenter.updateData(schedule);
        }
    }
}
