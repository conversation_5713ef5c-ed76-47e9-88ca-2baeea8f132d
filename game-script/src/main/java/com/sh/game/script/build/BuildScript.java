package com.sh.game.script.build;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueKVBean;
import com.sh.game.common.communication.msg.system.build.ResBuildInfoMessage;
import com.sh.game.common.config.model.BuildConfig;
import com.sh.game.common.config.model.BuildLevelConfig;
import com.sh.game.common.config.model.LevelConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.common.util.BackPackStashUtil;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.build.BuildManager;
import com.sh.game.system.build.entity.BuildData;
import com.sh.game.system.build.script.IBuildScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.laytrad.BuildWeiRenAttrProduceManager;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 建筑-开拓、建造
 * <AUTHOR>
 * @Date 2024/8/26 16:52
 */
@Slf4j
@Script
public class BuildScript implements IBuildScript, IEventOnRoleAttributeCountScript {

    @Override
    public void reqBuildInfo(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
        BuildData buildData = roleExtend.getBuildData();
        Map<Integer, Integer> buildExploit = buildData.getBuildExploit();
        Map<Integer, TwoTuple<Integer, Integer>> buildLevel = buildData.getBuildLevel();

        sendBuildInfo(role, buildExploit, buildLevel, -1);
    }

    private void sendBuildInfo(Role role, Map<Integer, Integer> buildExploit, Map<Integer, TwoTuple<Integer, Integer>> buildLevel, int buildId) {
        ResBuildInfoMessage message = new ResBuildInfoMessage();
        if(!MapUtils.isEmpty(buildExploit)){
            buildExploit.forEach((k,v)-> {
                if(buildId <=0 || buildId == k) {
                    CommonKeyValueBean keyValueBean = new CommonKeyValueBean();
                    keyValueBean.setKey(k);
                    keyValueBean.setValue(v);
                    message.getBuildExploits().add(keyValueBean);
                }
            });
        }

        if(!MapUtils.isEmpty(buildLevel)){
            buildLevel.forEach((k,v)-> {
                if(buildId <=0 || buildId == k) {
                    CommonKeyValueKVBean keyValueKVBean = new CommonKeyValueKVBean();
                    keyValueKVBean.setKey(k);

                    CommonKeyValueBean keyValueBean = new CommonKeyValueBean();
                    keyValueBean.setKey(v.getFirst());
                    keyValueBean.setValue(v.getSecond());
                    keyValueKVBean.setValue(keyValueBean);

                    message.getBuildLevels().add(keyValueKVBean);
                }
            });
        }

        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqBuildExploitOperate(Role role, int buildId, int itemCount) {
//        BuildConfig buildConfig = ConfigDataManager.getInstance().getById(BuildConfig.class, buildId);
//        if(buildConfig == null) {
//            return;
//        }
//        if(!ConditionUtil.validate(role, buildConfig.getExploitCondition())) {
//            return;
//        }
//        int[] exploitCost = buildConfig.getExploitCost().get(0);
//        final int itemId = exploitCost[0], needItemCount = exploitCost[1];
//        itemCount = Math.min(itemCount, needItemCount);
//
//        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
//        Map<Integer, Integer> buildExploitMap = roleExtend.getBuildData().getBuildExploit();
//        int oldExploitExp = buildExploitMap.getOrDefault(buildId, 0);
//        if(oldExploitExp >= needItemCount) {
//            return;
//        }
//
//        int difference = decreaseItem(role, itemId, itemCount, LogAction.BUILD_EXPLOIT_BUILDING);
//        int curExploitExp = oldExploitExp + (itemCount - difference);
//        buildExploitMap.put(buildId, curExploitExp);
//
//        Map<Integer, TwoTuple<Integer, Integer>> buildLevelMap = null;
//        if(curExploitExp >= needItemCount) {
//            buildLevelMap = roleExtend.getBuildData().getBuildLevel();
//            buildLevelMap.put(buildId, new TwoTuple<>(0,0));
//        }
//
//        DataCenter.updateData(roleExtend);
//        log.info("玩家id:{},name:{},建筑-开拓, buildId {}, buildData {}", role.getId(), role.getName(), buildId, JSON.toJSONString(roleExtend.getBuildData()));
//
//        sendBuildInfo(role, buildExploitMap, buildLevelMap, buildId);
    }

    @Override
    public void reqBuildOperate(Role role, int buildId, int itemCount) {
        BuildConfig buildConfig = ConfigDataManager.getInstance().getById(BuildConfig.class, buildId);
        if(buildConfig == null) {
            return;
        }

        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
        Map<Integer, TwoTuple<Integer, Integer>> buildLevelMap = roleExtend.getBuildData().getBuildLevel();
        //7584 【11.15 后端】建筑优化 取消建筑建造前的开拓步骤
//        if(!buildLevelMap.containsKey(buildId)) {
//            return;
//        }

        TwoTuple<Integer, Integer> levelExpTwoTuple = buildLevelMap.computeIfAbsent(buildId, k-> new TwoTuple<>(0,0));
        int oldLevel = levelExpTwoTuple.getFirst();
        int oldExp = levelExpTwoTuple.getSecond();
        BuildLevelConfig buildLevelConfig = ConfigDataManager.getInstance().getById(BuildLevelConfig.class, buildId + Symbol.JINHAO + oldLevel);
        if(buildLevelConfig == null || buildLevelConfig.getNextid() <= 0) {
            return;
        }
        if(!ConditionUtil.validate(role, buildLevelConfig.getCondition())) {
            return;
        }
        int[] buildCost = buildLevelConfig.getCost().get(0);
        final int itemId = buildCost[0], needItemCount = buildCost[1];
        itemCount = Math.min(itemCount, needItemCount);

        int difference = decreaseItem(role, itemId, itemCount, LogAction.BUILD_BUILDING);
        int curLevelExp = oldExp + (itemCount - difference);
        levelExpTwoTuple.setSecond(curLevelExp);

        if(curLevelExp >= needItemCount) {
            //升级
            levelExpTwoTuple.setFirst(oldLevel + 1);
            levelExpTwoTuple.setSecond(0);

            AttributeManager.getInstance().attributeCount(role);
            BuildWeiRenAttrProduceManager.getInstance().updateBuildWeirenAttr(role, true);

            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.BUILD_ID_LEVEL, buildId);
        }

        DataCenter.updateData(roleExtend);
        log.info("玩家id:{},name:{},建筑-建造升级, 配置id {}, buildLevelData {}", role.getId(), role.getName(), buildId, JSON.toJSONString(buildLevelMap));

        sendBuildInfo(role, null, buildLevelMap, buildId);
    }

    @Override
    public void reqBuildAssistProduction(Role role, int buildId, int cdRound) {
        if(!checkClickValid(role.getId())) {
            return;
        }
        BuildConfig buildConfig = ConfigDataManager.getInstance().getById(BuildConfig.class, buildId);
        if(buildConfig == null || CollectionUtils.isEmpty(buildConfig.getItemCount())) {
            return;
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
        Map<Integer, TwoTuple<Integer, Integer>> buildLevelMap = roleExtend.getBuildData().getBuildLevel();
        if(!buildLevelMap.containsKey(buildId)) {
            return;
        }
        TwoTuple<Integer, Integer> levelExpTwoTuple = buildLevelMap.get(buildId);
        if(levelExpTwoTuple.getFirst() <= 0) {
            return;
        }

        //TODO cdRound有效性判断 主角 状态 位置
        //role.getX();role.getY();

        //消耗
        int[] buildCost = buildConfig.getSellCost().get(0);
        final int itemId = buildCost[0], needItemCount = buildCost[1];
        long curZtNrBagItemCount = role.getBackpack().fetchCountLByCfgId(itemId, BackpackConst.Place.ZT_NR_HARVEST);
        long curRoleBagItemCount = role.getBackpack().fetchCountLByCfgId(itemId, BackpackConst.Place.BACKPACK);
        if(needItemCount > curZtNrBagItemCount + curRoleBagItemCount) {
            return;
        }
        int decreaseItemCount = needItemCount;
        if(curZtNrBagItemCount > 0) {
            int decrCount = (int)Math.min(decreaseItemCount, curZtNrBagItemCount);
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(itemId, decrCount, BackpackConst.Place.ZT_NR_HARVEST.getWhere());
            if(stash.commit(role, LogAction.BUILD_TEMP_BAG_WAREHOUSING, true)) {
                decreaseItemCount -= decrCount;
            }
        }
        if(decreaseItemCount > 0) {
            int decrCount = (int) Math.min(decreaseItemCount, curRoleBagItemCount);
            if(BackPackStashUtil.decrease(role, itemId, decrCount, LogAction.BUILD_ASSIST_PRODUCTION_COST)) {
                decreaseItemCount -= decrCount;
            }
        }

        BackPackStashUtil.increase(role, buildConfig.getItemCount(), cdRound, LogAction.BUILD_ASSIST_PRODUCTION, false);
        for (int[] ints : buildConfig.getItemCount()) {
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.MACHINING_ID_COUNT, ints[0], ints[1] * cdRound);
        }

        DataCenter.updateData(roleExtend);
        log.info("玩家id:{},name:{},建筑-生产, 配置id {}, cdRound {}, cost {}, item {}", role.getId(), role.getName(), buildId, cdRound, buildConfig.getSellCost(), buildConfig.getItemCount());
    }

    @Override
    public void reqBuildTempBagWarehousing(Role role, int fromWhere, int itemId, int itemCount, int toWhere) {
        BackpackConst.Place fromPlace = BackpackConst.Place.getPlace(fromWhere);
        BackpackConst.Place toPlace = BackpackConst.Place.getPlace(toWhere);
        if(fromPlace == null || toPlace == null) {
            return;
        }

        //TODO 客户端让暂时不判断 后面要打开
//        if((fromPlace == BackpackConst.Place.ZT_NR_HARVEST && toPlace == BackpackConst.Place.BACKPACK)
//            || (fromPlace == BackpackConst.Place.ZT_WEIREN_PRODUCTION && toPlace == BackpackConst.Place.ZT_WEIREN_ZGF_WAREHOUSE)
//                || (fromPlace == BackpackConst.Place.ZT_WEIREN_ZGF_WAREHOUSE && toPlace == BackpackConst.Place.BACKPACK)){

            //原木#小麦
            int[] nrItemIdArray = GlobalUtil.findJinghaoIntArray(GameConst.GlobalId.NATURAL_RESOURCES_ITEM_ID);
            int cfgWoodItemId = nrItemIdArray[0];
            int cfgWheatItemId = nrItemIdArray[1];
            int curFromBagItemCount = (int)role.getBackpack().fetchCountLByCfgId(itemId, fromPlace);
            if(itemCount <= 0 || curFromBagItemCount <= 0) {
                return;
            }
            itemCount = Math.min(curFromBagItemCount, itemCount);

            //判断容量
            RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
            Map<Integer, Integer> buildWeiRenAttribute = roleExtend.getBuildWeiRenAttribute();
            long maxLimit = 100000;
            switch (toPlace) {
                case ZT_WEIREN_ZGF_WAREHOUSE:
                    maxLimit = buildWeiRenAttribute.getOrDefault(BuildWeiRenConst.BuildWeiRenAttrEnum.ZGF_BAG_COUNT_ADD.getType(),0);
                    break;
                case BACKPACK:
                    if(itemId == cfgWheatItemId) {
                        maxLimit = buildWeiRenAttribute.getOrDefault(BuildWeiRenConst.BuildWeiRenAttrEnum.WHEAT_ROLE_BAG_COUNT_ADD.getType(),0);
                    }
                    else if(itemId == cfgWoodItemId) {
                        maxLimit = buildWeiRenAttribute.getOrDefault(BuildWeiRenConst.BuildWeiRenAttrEnum.WOOD_ROLE_BAG_COUNT_ADD.getType(),0);
                    }
                    break;
            }

            long curToBagItemCount = role.getBackpack().fetchCountLByCfgId(itemId, toPlace);
            long canAddCount = Math.min(maxLimit - curToBagItemCount, itemCount);
            if(canAddCount <= 0) {
                return;
            }

            BackpackStash stash = new BackpackStash(role);
            stash.decrease(itemId, canAddCount, fromPlace.getWhere());
            if(stash.commit(role, LogAction.BUILD_TEMP_BAG_WAREHOUSING, true)) {
                stash = new BackpackStash(role);
                stash.increase(itemId, canAddCount, toPlace);
                boolean increase = stash.commit(role, LogAction.BUILD_TEMP_BAG_WAREHOUSING, true);

                GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.WAREHOUSING_ID_COUNT, itemId, (int)canAddCount);

                log.info("玩家id:{},name:{},临时背包仓储挪动, fromPlace {}, toPlace {}, itemId {}, itemCount {}, canAddCount {}, increase {}",
                        role.getId(), role.getName(), fromPlace, toPlace, itemId, itemCount, canAddCount, increase);
            }
//        }
    }

    @Override
    public int getBuildLevel(Role role, int buildId) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
        Map<Integer, TwoTuple<Integer, Integer>> buildLevelMap = roleExtend.getBuildData().getBuildLevel();
        TwoTuple<Integer, Integer> levelExpTwoTuple = buildLevelMap.computeIfAbsent(buildId, k-> new TwoTuple<>(0,0));
        return levelExpTwoTuple.getFirst();
    }

    /** @return 扣完剩余差值 */
    public int decreaseItem(Role role, int itemId, int itemCount, LogAction logAction) {
        int decreaseItemCount = itemCount;
        //先扣除身上自然资源采集临时资源背包130
        long curZtNrBagItemCount = role.getBackpack().fetchCountLByCfgId(itemId, BackpackConst.Place.ZT_NR_HARVEST);
        if(curZtNrBagItemCount > 0) {
            int decrCount = (int)Math.min(decreaseItemCount, curZtNrBagItemCount);
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(itemId, itemCount, BackpackConst.Place.ZT_NR_HARVEST.getWhere());
            if(stash.commit(role, logAction, true)) {
                decreaseItemCount -= decrCount;
                if(decreaseItemCount <= 0) {
                    return 0;
                }
            }
        }

        //再扣除角色背包11
        long curRoleBagItemCount = role.getBackpack().fetchCountLByCfgId(itemId, BackpackConst.Place.BACKPACK);
        if(curRoleBagItemCount >= 0) {
            int decrCount = (int) Math.min(decreaseItemCount, curRoleBagItemCount);
            if(BackPackStashUtil.decrease(role, itemId, decrCount, logAction)) {
                decreaseItemCount -= decrCount;
            }
        }

        return decreaseItemCount;
    }

    private boolean checkClickValid(long roleId) {
        int cfgSecondsClickCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.NATURAL_RESOURCES_HARVEST_SPEED);
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        Pair<Integer, AtomicInteger> harvestCountPair = BuildManager.CHEAT_CHECKER_TIMER.computeIfAbsent(roleId, k -> Pair.of(nowOfSeconds, new AtomicInteger()));
        int harvestCount = harvestCountPair.getRight().incrementAndGet();
        if(harvestCountPair.getLeft() == nowOfSeconds && harvestCount > cfgSecondsClickCount) {
            //每秒过快
            return false;
        }
        else if(harvestCountPair.getLeft() != nowOfSeconds) {
            BuildManager.CHEAT_CHECKER_TIMER.put(roleId, Pair.of(nowOfSeconds, new AtomicInteger(1)));
        }
        return true;
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.ARCHITECTURE;
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        Attribute attribute = new Attribute();
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role);
        Map<Integer, TwoTuple<Integer, Integer>> buildLevelMap = roleExtend.getBuildData().getBuildLevel();
        for (Map.Entry<Integer, TwoTuple<Integer, Integer>> entry : buildLevelMap.entrySet()) {
            BuildLevelConfig config = ConfigDataManager.getInstance().getById(BuildLevelConfig.class, entry.getKey() + Symbol.JINHAO + entry.getValue().getFirst());
            attribute.fixAdd(config.getAttr(), role.getCareer());
        }
        role.getAttributes().put(getAttributeType(), attribute);
    }
}
