package com.sh.game.script.activity;

import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.activity.script.IActivityWaBaoBagScript;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.game.system.using.UsingManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.sh.game.common.entity.backpack.constant.BackpackConst.Place.BACKPACK;
import static com.sh.game.common.entity.backpack.constant.BackpackConst.Place.WAREHOUSE_WABAO;

/**
 * <AUTHOR>
 * @date 2021/11/5 19:27
 */
@Script
@Slf4j
public class ActivityWaBaoBagScript implements IActivityWaBaoBagScript {
    /**
     * 取出
     *
     * @param role
     */
    @Override
    public void reqTake(Role role) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage treasureStorage = backpack.fetchStorage(WAREHOUSE_WABAO.getWhere());
        Storage storage = backpack.fetchStorage(BACKPACK.getWhere());
        if (treasureStorage.getData().isEmpty()) {
            return;
        }
        // 背包已占位置的index
        List<Integer> indexList = new ArrayList<>();
        for (Map.Entry<Integer, Item> entry : storage.getData().entrySet()) {
            if (entry.getValue() != null) {
                indexList.add(entry.getKey());
            }
        }

        if (indexList.size() >= storage.capacity(role)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足);
            return;
        }

        int emptySize = storage.capacity(role) - indexList.size();
        int storageSize = treasureStorage.getData().size();
        List<Integer> list = new ArrayList<>();
        for (int i = 1; i <= storage.capacity(role); i++) {
            list.add(i);
        }
        // 空格子的index
        list.removeIf(indexList::contains);

        int index = 0;
        // 背包空余格子数 >= 仓库道具数 仓库道具全部转到背包
        if (emptySize >= storageSize) {
            for (Map.Entry<Integer, Item> entry : treasureStorage.getData().entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                BackpackManager.getInstance().reqMerge(role, WAREHOUSE_WABAO.getWhere(), entry.getKey(), BACKPACK.getWhere(),
                        list.get(index), entry.getValue().findCount(), 0);
                index++;
            }
        } else {
            for (Map.Entry<Integer, Item> entry : treasureStorage.getData().entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                BackpackManager.getInstance().reqMerge(role, WAREHOUSE_WABAO.getWhere(), entry.getKey(), BACKPACK.getWhere(),
                        list.get(index), entry.getValue().findCount(), 0);
                index++;
                if (index >= emptySize) {
                    break;
                }
            }
        }
    }

    /**
     * 使用元宝
     *
     * @param role
     */
    @Override
    public void reqUseGold(Role role) {
        // 查找元宝
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(WAREHOUSE_WABAO.getWhere());
        for (Map.Entry<Integer, Item> entry : storage.getData().entrySet()) {
            Item item = entry.getValue();
            if (item == null) {
                continue;
            }
            if (item.findItemConfig().getUseType() == 200) {
                UsingManager.getInstance().reqUsing(role, item.getId(), item.findCount(), Collections.emptyList());
            }
        }
    }
}
