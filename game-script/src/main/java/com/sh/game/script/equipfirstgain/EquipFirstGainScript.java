package com.sh.game.script.equipfirstgain;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.equipfirstgain.ResPersonalEquipFirstGainInfoMessage;
import com.sh.game.common.communication.msg.system.equipfirstgain.bean.PersonalEquipFirstGainBean;
import com.sh.game.common.config.model.FirstDropConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.WelfareConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleDropMonsterScript;
import com.sh.game.system.equipfirstgain.entity.RoleEquipFirstGain;
import com.sh.game.system.equipfirstgain.script.IEquipFirstGainScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @since 2022-04-20 20:06
 **/
@Slf4j
@Script
public class EquipFirstGainScript implements IEquipFirstGainScript, IEventOnRoleDropMonsterScript {

    /**
     * 请求一键获取个人装备首爆奖励
     *
     * @param role 角色
     */
    @Override
    public void reqGainAllReward(Role role) {
        RoleEquipFirstGain roleEquipFirstGain = find(role.getRoleId());
        Map<Integer, Integer> rewardStatus = roleEquipFirstGain.getRewardStatus();

        List<FirstDropConfig> configList = ConfigDataManager.getInstance().getList(FirstDropConfig.class);
        if (configList == null || configList.isEmpty()) {
            return;
        }
        //首爆系统配置
        List<FirstDropConfig> systemConfigList = configList.stream()
                //系统
                .filter(config -> config.getType() == 2)
                .collect(Collectors.toList());

        BackpackStash stash = new BackpackStash(role);

        //暂存奖励道具防止发送失败后提供邮箱附件
        List<int[]> itemList = new ArrayList<>();

        systemConfigList.forEach(config -> {
            int status = rewardStatus.computeIfAbsent(config.getId(), k -> WelfareConst.SignRewardState.NOT_GET);
            //需在可领取状态下领取
            if (status == WelfareConst.SignRewardState.CAN_GET) {
                rewardStatus.put(config.getId(), WelfareConst.SignRewardState.GOT);
                stash.increase(config.getReward());
                itemList.addAll(config.getReward());
            }
        });
        roleEquipFirstGain.setRewardStatus(rewardStatus);
        DataCenter.updateData(roleEquipFirstGain);

        //提交奖励
        if (!stash.commit(role, LogAction.SYSTEM_FIRST_DROP_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(itemList, LogAction.SYSTEM_FIRST_DROP_REWARD));
            log.error("roleId:{} 用户名:{} 通过邮件领取【个人装备首爆】奖励", role.getRoleId(), role.getName());
        }

        ResPersonalEquipFirstGainInfoMessage msg = new ResPersonalEquipFirstGainInfoMessage();
        msg.getPersonalEquipFirstGainBeanList().addAll(converterPersonalEquipFirstGainBeanList(roleEquipFirstGain.getRewardStatus()));
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 请求个人装备首爆奖励领取信息
     *
     * @param role 角色
     */
    @Override
    public void reqPersonalEquipFirstGainInfo(Role role) {
        RoleEquipFirstGain roleEquipFirstGain = find(role.getRoleId());
        ResPersonalEquipFirstGainInfoMessage msg = new ResPersonalEquipFirstGainInfoMessage();
        msg.getPersonalEquipFirstGainBeanList().addAll(converterPersonalEquipFirstGainBeanList(roleEquipFirstGain.getRewardStatus()));
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 首爆信息Map转换成首爆信息列表
     *
     * @param rewardStatus rewardStatus
     * @return {@link List<PersonalEquipFirstGainBean>} 个人装备首爆信息列表
     */
    private List<PersonalEquipFirstGainBean> converterPersonalEquipFirstGainBeanList(Map<Integer, Integer> rewardStatus) {
        List<PersonalEquipFirstGainBean> beanList = new ArrayList<>();
        rewardStatus.forEach((cfgId, status) -> {
            PersonalEquipFirstGainBean bean = new PersonalEquipFirstGainBean();
            bean.setId(cfgId);
            bean.setStatus(status);
            beanList.add(bean);
        });
        return beanList;
    }

    /**
     * 获取个人装备首爆
     *
     * @param roleId 角色Id
     * @return {@link RoleEquipFirstGain} 个人装备首爆信息
     */
    @Override
    public RoleEquipFirstGain find(long roleId) {
        RoleEquipFirstGain roleEquipFirstGain = DataCenter.get(RoleEquipFirstGain.class, roleId);
        if (null == roleEquipFirstGain) {
            roleEquipFirstGain = new RoleEquipFirstGain();
            roleEquipFirstGain.setId(roleId);
            DataCenter.insertData(roleEquipFirstGain, true);
        }
        return roleEquipFirstGain;
    }

    /**
     * 装备爆出事件(将首爆从未爆出不可领取改为可领取)
     *
     * @param role 角色
     * @param drops 装备列表
     */
    @Override
    public void onRoleDropMonster(Role role, List<Item> drops) {
        if (drops == null || drops.isEmpty()) {
            return;
        }
        List<FirstDropConfig> configList = ConfigDataManager.getInstance().getList(FirstDropConfig.class);
        if (configList == null || configList.isEmpty()) {
            return;
        }
        Set<Integer> dropItems = new HashSet<>();
        drops.forEach(item -> dropItems.add(item.getCfgId()));

        //根据活动id、获取道具、系统类型过滤配置
        List<FirstDropConfig> collect = configList.stream()
                .filter(config -> dropItems.contains(config.getItem()) && config.getType() == 2)
                .collect(Collectors.toList());

        RoleEquipFirstGain roleEquipFirstGain = find(role.getRoleId());
        Map<Integer, Integer> rewardStatus = roleEquipFirstGain.getRewardStatus();

        for (FirstDropConfig config : collect) {
            synchronized (config) {
                //改变首爆奖励状态为可领取
                int status = rewardStatus.computeIfAbsent(config.getId(), k -> WelfareConst.SignRewardState.NOT_GET);
                if (status == WelfareConst.SignRewardState.NOT_GET) {
                    rewardStatus.put(config.getId(), WelfareConst.SignRewardState.CAN_GET);
                }
            }
        }

        roleEquipFirstGain.setRewardStatus(rewardStatus);
        DataCenter.updateData(roleEquipFirstGain);

        ResPersonalEquipFirstGainInfoMessage msg = new ResPersonalEquipFirstGainInfoMessage();
        msg.getPersonalEquipFirstGainBeanList().addAll((converterPersonalEquipFirstGainBeanList(roleEquipFirstGain.getRewardStatus())));
        MessageUtil.sendMsg(msg, role.getRoleId());
    }
}
