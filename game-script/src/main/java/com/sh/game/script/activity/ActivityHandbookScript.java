package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.handbook.ResHandbookInfoMessage;
import com.sh.game.common.communication.msg.system.handbook.ResHandbookRewardMessage;
import com.sh.game.common.communication.msg.system.handbook.ResUnlockHandbookMessage;
import com.sh.game.common.config.model.EquipHandbookConfig;
import com.sh.game.common.config.model.EquipHandbookGoalConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.handbook.entity.RoleHandbook;
import com.sh.game.system.handbook.script.IHandbookScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 灵根天命系统 活动 (类型1074) 转的 系统
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-12-27
 **/
@Slf4j
@Script
public class ActivityHandbookScript implements IHandbookScript{

    @Override
    public RoleHandbook find(long rid) {
        RoleHandbook roleHandbook = DataCenter.get(RoleHandbook.class, rid);
        if (roleHandbook == null) {
            roleHandbook = new RoleHandbook();
            roleHandbook.setId(rid);
            DataCenter.insertData(roleHandbook, true);
        }

        return roleHandbook;
    }

    @Override
    public void reqInfo(Role role) {
        RoleHandbook roleHandbook = find(role.getRoleId());

        ResHandbookInfoMessage msg = new ResHandbookInfoMessage();
        msg.getHandBookCidList().addAll(roleHandbook.getUnlockedHandbookList());
        msg.getGoalsCidList().addAll(roleHandbook.getRequiredHandbookRewardList());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void unlockHandbook(Role role, int cid) {
        // 修复策划配置表错误，非绑时扣除
        EquipHandbookConfig config = ConfigDataManager.getInstance().getById(EquipHandbookConfig.class, cid);
        if (config == null) {
            return;
        }
        RoleHandbook roleHandbook = find(role.getRoleId());
        if (roleHandbook.getUnlockedHandbookList().contains(config.getId())) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getMappingId());
        if (!stash.commit(role, LogAction.HANDBOOK_COST)) {
            return;
        }
        roleHandbook.getUnlockedHandbookList().add(config.getId());
        DataCenter.updateData(roleHandbook);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.HAND_BOOK);

        AttributeManager.getInstance().attributeCount(role);

        ResUnlockHandbookMessage msg = new ResUnlockHandbookMessage();
        msg.setEquipCid(config.getId());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void requireProgressReward(Role role, int cfgId) {
        EquipHandbookGoalConfig config = ConfigDataManager.getInstance().getById(EquipHandbookGoalConfig.class, cfgId);
        if (config == null) {
            return;
        }

        RoleHandbook roleHandbook = find(role.getRoleId());
        // 同组图鉴全部解锁
        List<Integer> unlockedHandbookList = roleHandbook.getUnlockedHandbookList();
        List<Integer> cfgIdList = ConfigDataManager.getInstance().getList(EquipHandbookConfig.class)
                .stream().filter(e -> e.getType() == config.getType()).map(EquipHandbookConfig::getId).collect(Collectors.toList());
        if (!unlockedHandbookList.containsAll(cfgIdList)) {
            return;
        }

        List<Integer> requiredHandbookRewardList = roleHandbook.getRequiredHandbookRewardList();
        if (requiredHandbookRewardList.contains(config.getType())) {
            // 重复领取
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.HANDBOOK_REWARD, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足发邮件);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getReward(), LogAction.HANDBOOK_REWARD));
        }
        requiredHandbookRewardList.add(config.getType());
        DataCenter.updateData(roleHandbook);

        ResHandbookRewardMessage msg = new ResHandbookRewardMessage();
        msg.setCid(config.getId());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }
}
