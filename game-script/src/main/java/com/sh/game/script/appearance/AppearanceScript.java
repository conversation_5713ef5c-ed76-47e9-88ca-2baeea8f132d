package com.sh.game.script.appearance;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.appearance.ResAppearanceExchangeMessage;
import com.sh.game.common.communication.msg.system.appearance.ResAppearanceInfoMessage;
import com.sh.game.common.communication.msg.system.appearance.ResAppearanceWearsMessage;
import com.sh.game.common.communication.msg.system.appearance.ResSelfAppearanceUnlockMessage;
import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceStatusBean;
import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.communication.notice.NpcAddNotice;
import com.sh.game.common.config.cache.AppearanceCache;
import com.sh.game.common.config.cache.MapConditionCache;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.*;
import com.sh.game.log.entity.RoleAppearanceLog;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.server.SessionManager;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.appearance.script.IAppearanceScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.rank.RankManager;
import com.sh.game.system.role.script.IRoleOnMinuteScript;
import com.sh.game.system.roleMount.RoleMountManager;
import com.sh.game.system.shabake.ShaBaKeManager;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.*;

@Script
@Slf4j
public class AppearanceScript implements IAppearanceScript,
        IEventOnRoleLoginScript,
        IRoleOnMinuteScript,
        IEventOnRoleLevelUpScript,
        IEventOnRoleCreate {

    @Override
    public void onRoleLogin(Role role) {
        //沙巴克称号
        ShaBaKeManager.getInstance().onLoginFixShaBakeTitle(role);

        sendMsg(role);
        dealCrossSBK(role);
    }

    public void sendMsg(Role role) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        int now = TimeUtil.getNowOfSeconds();
        ResAppearanceInfoMessage msg = new ResAppearanceInfoMessage();
        for (Map.Entry<Integer, Integer> entry : appearance.getStatus().entrySet()) {
            int expire = entry.getValue();
            if (expire >= 0 && expire < now) {
                continue;
            }
            AppearanceStatusBean builder = new AppearanceStatusBean();
            Integer fashionId = entry.getKey();
            builder.setId(fashionId);
            builder.setExpire(expire);
            builder.setLevel(appearance.getLevelMap().getOrDefault(fashionId, 0));
            msg.getAppearance().add(builder);
        }
        MessageUtil.sendMsg(msg, role.getId());

        ResAppearanceWearsMessage wearsMessage = new ResAppearanceWearsMessage();
        Map<Integer, Integer> temp = new HashMap<>(appearance.getWears());
        AppearanceConfig config;
        for (Map.Entry<Integer, Integer> entry : temp.entrySet()) {
            config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, entry.getValue());
            if (config == null) {
                continue;
            }
            if (entry.getKey() != config.getType()) {
                appearance.getWears().remove(entry.getKey());
                continue;
            }
            AppearanceWearBean b2 = new AppearanceWearBean();
            b2.setId(entry.getValue());
            b2.setType(entry.getKey());
            wearsMessage.getWares().add(b2);
        }
        MessageUtil.sendMsg(wearsMessage, role.getId());
        SummaryManager.getInstance().updateFacadeData(role.getId(), appearance.getWears());
    }

    //跨服沙巴克限时时装称号修复
    private void dealCrossSBK(Role role) {
        String fashionValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.FIX_CROSS_SBK_FASHIONID);
        List<Integer> fashionIds = new JinhaoIntegerListConverter().convert(fashionValue);
        RoleAdvance roleAdvance = role.getRoleAdvance();
        for (Integer fashionId : fashionIds) {
            Integer expire = role.getRoleAdvance().getAppearance().getStatus().get(fashionId);
            if (expire == null) {
                continue;
            }
            if (expire != -1) {
                continue;
            }
            modifyFashion(LogAction.FIX_SHABAKE_FASHION, role.getRoleId(), 0, fashionId);
            DataCenter.updateData(roleAdvance);
            log.info("沙巴克登录修复检查,角色:{},昵称:{},删除过期称号:{},原称号时长:{}", role.getRoleId(), role.getName(), fashionId, expire);
        }
    }

    @Override
    public void reqUnlock(Role role, long uid) {
        Item item = role.getBackpack().findItemByUniqueId(uid);
        if (item == null) {
            return;
        }
        AppearanceConfig config = null;
        for (AppearanceConfig appearanceConfig : ConfigDataManager.getInstance().getList(AppearanceConfig.class)) {
            if (appearanceConfig.getItemId() == item.getCfgId()) {
                config = appearanceConfig;
                break;
            }
        }
        if (config == null) {
            return;
        }
        //时装道具判断是否可用
        if (!checkFashionCanUse(role, config)) {
            return;
        }
        onUnlock(role, config, item);
    }

    /**
     * 检测时装是否能使用
     *
     * @return
     */
    private boolean checkFashionCanUse(Role role, AppearanceConfig config) {

        if (!ConditionUtil.validate(role, config.getTypeCondition())) {
            TipUtil.show(role, CommonTips.请激活坐骑后使用);
            return false;
        }

        return true;
    }

    @Override
    public void conditionUnlock(Role role, int fashionId) {
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
        if (config == null || config.getUnlock().isEmpty() || !ConditionUtil.validate(role, config.getUnlock())) {
            return;
        }
        onUnlock(role, config, null);
    }

    @Override
    public void buyAppearance(Role role, int fashionId) {
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
        if (config == null || config.getCost().isEmpty()) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.APPEARANCE_BUY_ULOCK)) {
            return;
        }
        onUnlock(role, config, null);
    }

    @Override
    public void onUnlock(Role role, AppearanceConfig config, Item item) {
        int time = config.getTime();
        int parent = config.getFather();
        if (parent > 0) {
            config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, parent);
            if (config == null) {
                log.error("parent appearance not found: {}", parent);
                return;
            }
        }


        //获取时装状态
        Appearance appearance = role.getRoleAdvance().getAppearance();
        int expire = appearance.getStatus().getOrDefault(config.getId(), 0);
        //永久激活的，不需要重新激活
        if (expire == -1) {
            // 侠侣皮肤重复激活不提示
//            if (config.getType() != RoleConst.AppearanceType.XIALV) {
//                TipUtil.show(role, CommonTips.永久时装不可在激活);
//            }
            log.warn("激活称号或时装-重复激活#玩家{}->{},configId:{}", role.getId(), role.getName(), config.getId());
            return;
        }

        //没有物品则不扣取
        if (item != null) {
            //扣除物品
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(item);
            if (!stash.commit(role, LogAction.APPEARANCE_ULOCK)) {
                log.error("装扮解锁扣除物品失败:name:{},{},cfgid:{}", role.getName(), role.getId(), item.getCfgId());
                return;
            }
        }


        //设置过期时间
        if (time == 0) {
            expire = -1;
        } else {
            expire = Math.max(TimeUtil.getNowOfSeconds(), expire);
            expire += time;
        }

        //更新数据
        appearance.getStatus().put(config.getId(), expire);
        if (expire == -1 && config.getUpGrades() == 1) {
            appearance.getLevelMap().put(config.getId(), 1);
            log.info("时装，玩家解锁时装，id {} name {} fashionId {}", role.getId(), role.getName(), config.getId());
        }
        DataCenter.updateData(role.getRoleAdvance());

        //坐骑有拥有技能
        if (config.getType() == FashionConst.AppearanceType.ROLE_MOUNT) {
            SkillManager.getInstance().updateRoleSkill(role);
        }

        //发送场景
        ProxyPlayer proxyPlayer = PlayerProxyManager.getInstance().getPlayer(role.getId());
        // 玩家在线
        if (proxyPlayer != null) {
            proxyPlayer.appearanceAdd(config.getId());
        }


        //发送消息
        ResAppearanceInfoMessage msg = new ResAppearanceInfoMessage();
        AppearanceStatusBean b = new AppearanceStatusBean();
        b.setId(config.getId());
        b.setExpire(expire);
        b.setLevel(appearance.getLevelMap().getOrDefault(config.getId(), 0));
        msg.getAppearance().add(b);
        MessageUtil.sendMsg(msg, role.getId());
//        autoWear(role, config.getType());

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.FASHION_ID);

        //称号领取日志添加
        RoleAppearanceLog ledLog = new RoleAppearanceLog(role);
        ledLog.setTime(TimeUtil.getNowOfSeconds());
        ledLog.setRoleId(role.getId());
        ledLog.setRoleName(role.getName());
        ledLog.setServerId(role.getSid());
        ledLog.setAppearanceId(config.getId());
        ledLog.setAppearanceName(config.getName());
        ledLog.setAccount(role.getAccount());
        ledLog.submit();

        log.info("ModifyFashion:unlock==>rid={},item={},now={},wear={}", role.getId(), item == null ? 0 : item.getCfgId(),
                appearance.getStatus().toString(), appearance.getWears().toString());

        if (config.getAnnounce() > 0) {
            AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role, config.getName());
        }

        // 刷npc
        CommonData data = SysDataProvider.get(CommonData.class);
        if (!data.getTitles().containsKey(config.getId())) {
            TitleStatueConfig titleStatueConfig = ConfigDataManager.getInstance().getById(TitleStatueConfig.class,
                    config.getId());
            if (titleStatueConfig == null) {
                return;
            }

            AnnounceManager.getInstance().post(titleStatueConfig.getAnnounce(), 0, role);
            data.getTitles().put(config.getId(), role.getId());
            DataCenter.updateData(data);

            long mapId = MapProxyManager.getInstance().getMap(titleStatueConfig.getStatuemap()[0]).getId();
            NpcAddNotice notice = new NpcAddNotice();
            notice.setMapId(mapId);
            int npcId = role.getSex() == RoleConst.Sex.MALE ? titleStatueConfig.getStatueid()[0] :
                    titleStatueConfig.getStatueid()[1];

            notice.getNpcs().add(new int[]{npcId, titleStatueConfig.getStatuemap()[1],
                    titleStatueConfig.getStatuemap()[2]});
            notice.getParams().add(role.getName());

            notice.getParams().add(role.getUnion().getName());

            GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, notice, mapId);

            // 给全服玩家发邮件
            for (Long rid : DataCenter.getRidMap()) {
                MailManager.getInstance().sendMail(rid, titleStatueConfig.getMail(), null, role.getName());
            }
        }

        // 全服唯一装扮激活，该目标为全服玩家公共任务目标
        if (config.getServerTarget() > 0) {
            // 全服玩家任务目标更新
            ScriptEngine.invoke1tn(IEventOnAllOnlineRoleGoalUpdateScript.class, script -> script.onAllOnlineRoleGoalUpdate(GoalConst.GoalType.OPEN_UNIQUE_STATUE));
        }


    }

    @Override
    public void reqWear(Role role, int id) {
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
        if (config == null || config.getFather() > 0) {
            return;
        }
        Appearance appearance = role.getRoleAdvance().getAppearance();
        int expire = appearance.getStatus().getOrDefault(config.getId(), 0);
        if (expire >= 0 && expire < TimeUtil.getNowOfSeconds()) {
            return;
        }
        if (appearance.getWears().getOrDefault(config.getType(), 0) == config.getId()) {
            return;
        }
        onWear(role, config.getType(), config.getId());
    }


    @Override
    public void reqUnWear(Role role, int id) {
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
        if (config == null || config.getFather() > 0) {
            return;
        }
        onWear(role, config.getType(), 0);
    }

    /**
     * 带上称号
     *
     * @param role
     * @param type
     * @param id
     */
    private void onWear(Role role, int type, int id) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Appearance appearance = roleAdvance.getAppearance();
        if (id > 0) {
            appearance.getWears().put(type, id);
            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (config != null && type == RoleConst.AppearanceType.XIALV) {
                role.proxyCall(proxy -> proxy.xialvUpdate(role));
            }
            if (config != null && type == FashionConst.AppearanceType.ROLE_MOUNT) {
                role.proxyCall(proxyPlayer -> proxyPlayer.mountUpdateNotice(config.getModel()));
                RoleMountManager.getInstance().reqDressOrDisrobeRoleMount(role);
            }
        } else {
            appearance.getWears().remove(type);
            role.proxyCall(proxyPlayer -> proxyPlayer.mountUpdateNotice(0));
        }
        DataCenter.updateData(roleAdvance);

        // 宠物更新场景
        ResAppearanceWearsMessage msg = new ResAppearanceWearsMessage();
        AppearanceWearBean bean = new AppearanceWearBean();
        bean.setType(type);
        bean.setId(id);
        msg.getWares().add(bean);
        MessageUtil.sendMsg(msg, role.getId());

        SummaryManager.getInstance().updateFacadeData(role.getId(), appearance.getWears());
        RankManager.getInstance().onWearFashionChange(role.getId(), type, id);
        Map<Integer, Integer> appearanceWears = roleAdvance.getAppearanceWears();
        appearanceWears.put(type, id);
        roleAdvance.setAppearanceWears(appearanceWears);

        if (type == FashionConst.AppearanceType.PIT) {
            role.proxyCall(proxyPlayer -> proxyPlayer.chongWuUpdate(role));
        }
    }

    @Override
    public void onRoleMinute(Role role) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        if (appearance.getWears().size() > 0) {
            List<Integer> temp = new ArrayList<>(appearance.getWears().values());
            for (int faction : temp) {
                if (faction == 0 || appearance.getStatus().containsKey(faction)) {
                    continue;
                }
                reqUnWear(role, faction);
            }
        }

        int now = TimeUtil.getNowOfSeconds();
        Set<Integer> expired = new HashSet<>();
        for (Map.Entry<Integer, Integer> entry : appearance.getStatus().entrySet()) {
            if (entry.getValue() < 0 || entry.getValue() >= now) {
                continue;
            }
            expired.add(entry.getKey());
        }
        //没有过期的,return
        if (expired.isEmpty()) {
            return;
        }
        expired.forEach(id -> {
            //过期了，删除
            appearance.getStatus().remove(id);
            //场景更新删除
            ProxyPlayer proxyPlayer = PlayerProxyManager.getInstance().getPlayer(role.getId());
            proxyPlayer.appearanceRemove(id);

            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (config == null) {
                return;
            }
            if (config.getType() == FashionConst.AppearanceType.ROLE_MOUNT) {
                SkillManager.getInstance().updateRoleSkill(role);
            }
            if (appearance.getWears().getOrDefault(config.getType(), 0).equals(id)) {
                autoWear(role, config.getType());
            }
        });
        DataCenter.updateData(role.getRoleAdvance());
        onTitleRemove(role, expired);
    }

    @Override
    public void modifyFashion(LogAction action, long rid, int removeFashion, int addFashion, int expire) {
        Role role = DataCenter.getRole(rid);
        if (role == null) {
            return;
        }
        if (role.getRoleAdvance() == null) {
            return;
        }
        Appearance appearance = role.getRoleAdvance().getAppearance();
        boolean isOnline = SessionManager.getInstance().isRoleOnline(rid);
        ResAppearanceInfoMessage msg = new ResAppearanceInfoMessage();

        Integer remove = appearance.getStatus().remove(removeFashion);
        AppearanceConfig config;
        if (remove != null) {
            AppearanceStatusBean bean = new AppearanceStatusBean();
            msg.getAppearance().add(bean);
            config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, removeFashion);
            if (config != null && appearance.getWears().getOrDefault(config.getType(), 0) == removeFashion) {
                autoWear(role, config.getType());
            }
            if (config != null && config.getType() == FashionConst.AppearanceType.ROLE_MOUNT) {
                SkillManager.getInstance().updateRoleSkill(role);
            }
        }

        config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, addFashion);
        if (config != null) {
            int end;
            if (expire < 1) {
                end = -1;
            } else {
                end = TimeUtil.getNowOfSeconds() + expire;
            }
            appearance.getStatus().put(addFashion, end);
            AppearanceStatusBean bean = new AppearanceStatusBean();
            bean.setId(addFashion);
            bean.setExpire(end);
            msg.getAppearance().add(bean);
            autoWear(role, config.getType());
        }
        log.info("ModifyFashion:rid={},action={},add={},remove={},now={},wear={}", rid, action.getCode(), addFashion, removeFashion,
                appearance.getStatus().toString(), appearance.getWears().toString());

        DataCenter.updateData(role.getRoleAdvance());
        if (isOnline) {
            MessageUtil.sendMsg(msg, role.getId());
        }
    }

    @Override
    public void modifyFashion(LogAction action, long rid, int expire, int... fashions) {
        Role role = DataCenter.getRole(rid);
        if (role == null) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance();
        if (roleAdvance == null) {
            return;
        }
        Appearance appearance = roleAdvance.getAppearance();
        ResAppearanceInfoMessage msg = new ResAppearanceInfoMessage();
        AppearanceConfig config;
        if (expire == 0) {
            Integer remove;
            for (int fashion : fashions) {
                remove = appearance.getStatus().remove(fashion);
                if (remove == null) {
                    continue;
                }
                AppearanceStatusBean bean = new AppearanceStatusBean();
                bean.setId(fashion);
                msg.getAppearance().add(bean);
                config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashion);
                if (config != null && appearance.getWears().getOrDefault(config.getType(), 0) == fashion) {
                    autoWear(role, config.getType());
                }
                if (config != null && config.getType() == FashionConst.AppearanceType.ROLE_MOUNT) {
                    SkillManager.getInstance().updateRoleSkill(role);
                }
            }
        } else {
            int end;
            for (int fashion : fashions) {
                config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashion);
                if (config == null) {
                    continue;
                }
                if (expire < 0) {
                    end = -1;
                } else {
                    end = TimeUtil.getNowOfSeconds() + expire;
                }
                appearance.getStatus().put(fashion, end);
                AppearanceStatusBean bean = new AppearanceStatusBean();
                bean.setId(fashion);
                bean.setExpire(end);
                msg.getAppearance().add(bean);
                autoWear(role, config.getType());
            }
        }
        log.info("ModifyFashion:rid={},action={},expire={},fashions={},now={},wear={}", rid, action.getCode(), expire, Arrays.toString(fashions),
                appearance.getStatus().toString(), appearance.getWears().toString());
        DataCenter.updateData(roleAdvance);
        if (SessionManager.getInstance().isRoleOnline(rid)) {
            MessageUtil.sendMsg(msg, rid);
        }
    }

    /**
     * 自动穿戴指定类型时装、称号
     *
     * @param role
     * @param fashionType
     */
    private void autoWear(Role role, int fashionType) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Appearance appearance = roleAdvance.getAppearance();
        Map<Integer, Integer> status = appearance.getStatus();
        if (status.isEmpty()) {
            if (appearance.getWears().getOrDefault(fashionType, 0) != 0) {
                onWear(role, fashionType, 0);
            }
            return;
        }
        AppearanceConfig config;
        List<AppearanceConfig> configList = new ArrayList<>();
        for (int id : status.keySet()) {
            config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (config.getType() != fashionType || config.getOrder() == 0) {
                continue;
            }
            configList.add(config);
        }
        if (configList.isEmpty()) {
            if (appearance.getWears().getOrDefault(fashionType, 0) != 0) {
                onWear(role, fashionType, 0);
            }
            return;
        }
        Collections.sort(configList, Comparator.comparingInt(AppearanceConfig::getOrder).reversed());
        reqWear(role, configList.get(0).getId());
    }

    @Override
    public void exchange(Role role, int id) {
        boolean check = hasTitleStatue(role, id);

        if (check) {
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            onUnlock(role, appearanceConfig, null);
        }
        ResAppearanceExchangeMessage msg = new ResAppearanceExchangeMessage();
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public boolean hasTitleStatue(Role role, int titleStatueId) {
        TitleStatueConfig titleStatueConfig = ConfigDataManager.getInstance().getById(TitleStatueConfig.class, titleStatueId);
        if (titleStatueConfig == null) {
            log.error("titel_statue config not found id:{}", titleStatueId);
            return false;
        }

        List<List<int[]>> items = titleStatueConfig.getItems();

        if (items == null) {
            log.error("title_statue配置文件，装备设置为null");
            return false;
        }

        Iterator<List<int[]>> iterator = items.iterator();

        //TODO 王杰波，重新整理逻辑
        while (iterator.hasNext()) {
            //默认条件不满足
            boolean hasItem = false;
            //判断条件，是否有一个满足
            for (int[] item : iterator.next()) {
                if (item.length < 2) {
                    log.error("titel_statue config文件，items 配置错误");
                    return false;
                }
                int count = item[1];
                int itemCfgId = item[0];
                if (role.getBackpack().findItemByCfgId(itemCfgId, BackpackConst.Place.values()).size() >= count) {
                    //条件满足，跳出小条件判断，判断下一个大条件
                    hasItem = true;
                    break;
                }
            }

            //条件有一个不满足，hasItem返回false
            if (!hasItem) {
                TipUtil.show(role, CommonTips.脚本_条件不满足);
                return false;
            }
        }

        return true;

    }


    @Override
    public void reqSelfAppearanceUnlock(Role role, int chenghaoId, int costIndex) {
        ResSelfAppearanceUnlockMessage msg = new ResSelfAppearanceUnlockMessage();
        //判断下有没有该称号
        Appearance appearance = role.getRoleAdvance().getAppearance();
        ChenghaoConfig config = ConfigDataManager.getInstance().getById(ChenghaoConfig.class, chenghaoId);
        ChenghaoConfig nextConfig = ConfigDataManager.getInstance().getById(ChenghaoConfig.class, chenghaoId + 1);
        if (config == null) {
            return;
        }
        if (nextConfig == null) {
            return;
        }
        //解锁下一级称号
        AppearanceConfig appConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, nextConfig.getFashionid());
        if (appConfig == null) {
            return;
        }
        if (appearance.getStatus().get(appConfig.getId()) != null) {
            return;
        }

        boolean check = ConditionUtil.validate(role, nextConfig.getCondition(), true);
        if (!check) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        int[] ints = config.getCostitem().get(costIndex);
        if (ints.length < 2) {
            return;
        }
        stash.decrease(ints[0], ints[1]);
        if (!stash.commit(role, LogAction.SELFAPPEARANCE_UNLOCK)) {
            return;
        }
        //扣完道具解锁称号
        onUnlock(role, appConfig, null);

        //默认创角称号不可被自动替换
        int defaultFashionId = GlobalUtil.getGlobalInt(GameConst.GlobalId.CREATE_DEFAULT_FASHION);
        AppearanceConfig defaultFashionConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, defaultFashionId);
        if (defaultFashionConfig.getType() == appConfig.getType()
                && appearance.getWears().getOrDefault(appConfig.getType(), 0) != defaultFashionId) {
            //带上称号
            onWear(role, appConfig.getType(), appConfig.getId());
        }
        if (nextConfig.getAnnounce() > 0) {
            AnnounceManager.getInstance().post(nextConfig.getAnnounce(), 0L, role, appConfig.getName());
        }
        msg.setSuccess(true);
        msg.setLevel(nextConfig.getFashionid());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public AppearanceConfig findSelfAppearance(Role role) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        List<AppearanceConfig> geRenChengHao = new ArrayList<>();
        for (Map.Entry<Integer, Integer> selfAppearance : appearance.getStatus().entrySet()) {
            Integer fashionId = selfAppearance.getKey();
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
            if (appearanceConfig == null) {
                continue;
            }
            if (appearanceConfig.getIsSys() != FashionConst.IS_SYS.PERSONAL_TITLE || appearanceConfig.getType() != FashionConst.AppearanceType.APPEARANCE_TYPE) {
                continue;
            }
            geRenChengHao.add(appearanceConfig);
        }
        return geRenChengHao.stream().max(Comparator.comparing(AppearanceConfig::getLevel)).orElse(null);
    }

    @Override
    public void reqSelfAppearanceUnlockInfo(Role role) {
        AppearanceConfig appearanceConfig = findSelfAppearance(role);
        ResSelfAppearanceUnlockMessage msg = new ResSelfAppearanceUnlockMessage();
        msg.setSuccess(true);
        msg.setLevel(appearanceConfig == null ? 0 : appearanceConfig.getId());
        MessageUtil.sendMsg(msg, role.getId());
    }

    private void onTitleRemove(Role role, Set<Integer> removedTitles) {
        MapProxy mapProxy = MapProxyManager.getInstance().getMap(role.getMapId());
        if (mapProxy == null || !mapProxy.isAvailable()) {
            return;
        }
        MapConditionCache cache = ConfigCacheManager.getInstance().getCache(MapConditionCache.class);
        List<MapConditionConfig> list = cache.getList(mapProxy.getCfgId());
        for (MapConditionConfig config : list) {
            // 当前地图是需要检查限时称号的
            if (config.getTitletimelimit() == 1 && !ConditionUtil.validate(role, config.getCondition())) {
                if (config.getFreecondition().size() <= 0 || !ConditionUtil.validate(role, config.getFreecondition(), false)) {
                    TeleportManager.getInstance().teleportLastMap(role);
                }
            }
        }

        for (Integer title : removedTitles) {
            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, title);
            if (config == null) {
                continue;
            }
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.FASHION_EXPIRED, null, config.getName());
        }
    }

    @Override
    public void onRoleLevelUp(Role role, long actorId, int oLv) {
        //超过等级后创角称号取消穿戴
        int level = GlobalUtil.getGlobalInt(GameConst.GlobalId.CREATE_DEFAULT_FASHION_VANISH);
        if (role.getLevel() < level) {
            return;
        }
        int defaultFashionId = GlobalUtil.getGlobalInt(GameConst.GlobalId.CREATE_DEFAULT_FASHION);
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, defaultFashionId);
        if (config == null) {
            return;
        }
        //当前穿戴称号
        int fashionId = role.getRoleAdvance().getAppearance().getWears().getOrDefault(config.getType(), 0);
        if (fashionId <= 0 || fashionId != defaultFashionId) {
            return;
        }

        autoWear(role, config.getType());
    }

    @Override
    public void appearanceLevelUp(Role role, int fashionId) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Appearance appearance = roleAdvance.getAppearance();
        Integer expireTime = appearance.getStatus().get(fashionId);
        if (expireTime == null || expireTime != -1) {
            log.error("时装，玩家请求时装升级，当前时装未解锁或非永久，id {} name {} fashionId {}", role.getId(), role.getName(), fashionId);
            return;
        }
        Map<Integer, Integer> levelMap = appearance.getLevelMap();
        Integer level = levelMap.get(fashionId);
        if (level == null) {
            log.error("时装，玩家请求时装升级，当前时装等级未初始化，id {} name {} fashionId {}", role.getId(), role.getName(), fashionId);
            return;
        }
        AppearanceCache cache = ConfigCacheManager.getInstance().getCache(AppearanceCache.class);
        AppearanceLevelConfig levelConfig = cache.findAppearanceLevelConfig(fashionId, level);
        if (levelConfig == null) {
            log.error("时装，玩家请求时装升级，当前时装不可升级，id {} name {} fashionId {} level {}", role.getId(), role.getName(), fashionId, level);
            return;
        }
        AppearanceLevelConfig nextConfig = ConfigDataManager.getInstance().getById(AppearanceLevelConfig.class, levelConfig.getNextId());
        if (nextConfig == null) {
            log.error("时装，玩家请求时装升级，当前时装没有下一级，id {} name {} fashionId {} level {}", role.getId(), role.getName(), fashionId, level);
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(levelConfig.getNeed());
        if (!stash.commit(role, LogAction.APPEARANCE_UPGRADE)) {
            return;
        }
        levelMap.put(fashionId, nextConfig.getLevel());
        log.info("时装，玩家请求时装升级，id {} name {} fashionId {} level {}", role.getId(), role.getName(), fashionId, nextConfig.getLevel());
        DataCenter.updateData(roleAdvance);
        int now = TimeUtil.getNowOfSeconds();
        ResAppearanceInfoMessage msg = new ResAppearanceInfoMessage();
        for (Map.Entry<Integer, Integer> entry : appearance.getStatus().entrySet()) {
            int expire = entry.getValue();
            if (expire >= 0 && expire < now) {
                continue;
            }
            AppearanceStatusBean builder = new AppearanceStatusBean();
            Integer fashion = entry.getKey();
            builder.setId(fashion);
            builder.setExpire(expire);
            builder.setLevel(appearance.getLevelMap().getOrDefault(fashion,0));
            msg.getAppearance().add(builder);
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onRoleCreate(Role role) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        int zhuanshengId = ZhuanShengConst.firstZhuanShengId;
        roleAdvance.setZhuanshengId(zhuanshengId);
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, zhuanshengId);
        if (zhuanShengConfig == null) {
            return;
        }
        List<Integer> list = zhuanShengConfig.getFashionId();
        if (!list.isEmpty()) {
            for (Integer fashionId : list) {
                conditionWear(role, fashionId, true);
            }
        }
        DataCenter.updateData(roleAdvance);
    }

    @Override
    public void careerChange(Role role) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        // 根据策划最新要求，转职后默认选择形象和境界头像最高的，伙伴和神魔除外
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, roleAdvance.getZhuanshengId());
        if (zhuanShengConfig == null) {
            return;
        }
        for (Integer fashionId : zhuanShengConfig.getFashionId()) {
            conditionWear(role, fashionId, false);
        }
        sendMsg(role);
    }

    @Override
    public void conditionWear(Role role, Integer fashionId, boolean isUnLock) {
        AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
        if (config == null) {
            return;
        }
        if (isUnLock) {
            onUnlock(role, config, null);
        }
        if (!ConditionUtil.validate(role, config.getUnlock())) {
            return;
        }
        reqWear(role, config.getId());
    }
}
