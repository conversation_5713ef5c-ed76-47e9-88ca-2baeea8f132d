package com.sh.game.script.daily.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.jiutian.ResJiuTianInfoMessage;
import com.sh.game.common.config.model.DailyScheduleConfig;
import com.sh.game.common.config.model.JiuTianConfig;
import com.sh.game.common.constant.DailyConst;
import com.sh.game.common.constant.ItemConst;
import com.sh.game.common.constant.JiuTianConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleJiuTian;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.script.daily.abc.AbstractDailyTimedScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.daily.DailyManager;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.game.system.jiutian.script.IJiuTianScript;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Script
public class DailyJiuTianZhiDianScript extends AbstractDailyTimedScript implements IJiuTianScript, IEventOnRoleMidnightScript {

    @Override
    public int getType() {
        return DailyConst.DailyType.JIU_TIAN_ZHI_DIAN;
    }

    @Override
    protected void onScheduleBegin(DailySchedule schedule) {
        DailyScheduleConfig conf = schedule.getConf();
        if (conf == null) {
            return;
        }
        //重新开启地图
        for (int mapConfigId : conf.getUseMap()) {
            MapProxyManager.getInstance().alloc(mapConfigId);
        }
    }

    @Override
    protected void onScheduleEnd(DailySchedule schedule) {
        DailyScheduleConfig conf = schedule.getConf();
        if (conf == null) {
            return;
        }
        for (int mapConfigId : conf.getUseMap()) {
            MapProxy mapProxy = MapProxyManager.getInstance().getMap(mapConfigId);
            if (mapProxy != null) {
                mapProxy.kickPlayer();
            }
        }

    }

    @Override
    public void reqEnterJiuTianMap(Role role, int cfgId) {
        if (!DailyManager.getInstance().isInTimedPeriod(getType())) {
            log.info("九天之巅，玩家请求进入九天之巅地图，九天之巅活动已结束，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }
        JiuTianConfig config = ConfigDataManager.getInstance().getById(JiuTianConfig.class, cfgId);
        if (config == null) {
            log.info("九天之巅，玩家请求进入九天之巅地图，配置不存在，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }
        if (config.getType() != JiuTianConst.JI_FEN) {
            log.info("九天之巅，玩家请求进入九天之巅地图，配置类型错误，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }

        RoleJiuTian jiuTian = findJiuTian(role);

        if (jiuTian.getJiuTianJiFen() < config.getParam()) {
            log.error("九天之巅，玩家请求进入九天之巅地图，玩家当前积分不够，玩家id {} name {} 配置id {} 玩家当前积分 {}", role.getId(), role.getName(), config.getId(), jiuTian.getJiuTianJiFen());
            return;
        }

        jiuTian.setJiuTianJiFen(jiuTian.getJiuTianJiFen() - config.getParam());
        DataCenter.updateData(jiuTian);

        if (!jiuTian.getReward().contains(config.getId())) {
            jiuTian.getReward().add(config.getId());
            DataCenter.updateData(jiuTian);
            List<Item> reward = config.getReward().stream().map(r -> ItemUtil.create(r[0], r[1], LogAction.JIU_TIAN_JI_FEN_REWARD)).collect(Collectors.toList());
            MailManager.getInstance().sendMail(role.getId(), config.getMail(), reward, config.getNum());
            log.info("九天之巅，玩家请求进入地图，玩家获取积分奖励，玩家id {} name {} 配置id {} 玩家当前积分 {}", role.getId(), role.getName(), config.getId(), config.getId());
        }

        TeleportManager.getInstance().teleport(role, config.getMapId(), 0L, config.getPos()[0], config.getPos()[1], config.getRange());
        log.info("九天之巅，玩家请求进入九天之巅地图，玩家进入地图成功，玩家id {} name {} 配置id {} 玩家当前积分 {}", role.getId(), role.getName(), config.getId(), jiuTian.getJiuTianJiFen());

        if (config.getAnnounce() > 0) {
            AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
        }

        reqJiuTianInfo(role);
    }

    @Override
    public void reqJiuTianKillPlayerReward(Role role, int cfgId) {
        JiuTianConfig config = ConfigDataManager.getInstance().getById(JiuTianConfig.class, cfgId);
        if (config == null) {
            log.error("九天之巅，玩家请求领取杀人数奖励，配置找不到，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }
        if (config.getType() != JiuTianConst.KILL_PLAYER) {
            log.error("九天之巅，玩家请求领取杀人数奖励，玩家传入配置type错误，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }
        RoleJiuTian jiuTian = findJiuTian(role);

        List<Integer> reward = jiuTian.getReward();
        if (reward.contains(config.getId())) {
            log.error("九天之巅，玩家请求领取杀人数奖励，玩家已领取当前配置，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);
            return;
        }

        if (config.getParam() > jiuTian.getJiuTianKillCount()) {
            log.error("九天之巅，玩家请求领取杀人数奖励，玩家当前杀人数不足，玩家id {} name {} 配置id {} 玩家当前杀人数 {}", role.getId(), role.getName(), cfgId, jiuTian.getJiuTianKillCount());
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.JIU_TIAN_KILL_COUNT_REWARD)) {
            return;
        }

        reward.add(config.getId());
        DataCenter.updateData(jiuTian);

        log.info("九天之巅，玩家请求杀人数奖励，玩家领取成功，玩家id {} name {} 配置id {}", role.getId(), role.getName(), cfgId);

        reqJiuTianInfo(role);
    }


    @Override
    public void onRoleMidnight(Role role) {
        RoleJiuTian jiuTian = findJiuTian(role);
        jiuTian.setJiuTianJiFen(0);
        jiuTian.setJiuTianKillCount(0);
        jiuTian.getReward().clear();
        DataCenter.updateData(jiuTian);
        log.info("九天之巅，玩家午夜事件充值数据，玩家id {} name {}", role.getId(), role.getName());
    }

    private RoleJiuTian findJiuTian(Role role) {
        RoleJiuTian jiuTian = DataCenter.get(RoleJiuTian.class, role.getId());
        if (jiuTian == null) {
            jiuTian = new RoleJiuTian();
            jiuTian.setId(role.getId());
            DataCenter.insertData(jiuTian, true);
            log.info("九天之巅，玩家初始化九天之巅数据，玩家id {} name {}", role.getId(), role.getName());
        }
        return jiuTian;
    }

    @Override
    public void addJiuTianJiFen(Role role, int jiFen) {
        RoleJiuTian jiuTian = findJiuTian(role);
        int jiuTianJiFen = jiuTian.getJiuTianJiFen();
        jiuTian.setJiuTianJiFen(jiuTianJiFen + jiFen);
        DataCenter.updateData(jiuTian);
        log.info("九天之巅，增加玩家积分，玩家id {} name {} 增加积分 {} 当前积分 {}", role.getId(), role.getName(), jiFen, jiuTian.getJiuTianJiFen());
        reqJiuTianInfo(role);

        // 添加无用道具，只是负责显示加了多少积分
        BackpackStash stash = new BackpackStash(role);
        stash.increase(ItemConst.ItemId.SCORE, jiFen);
        stash.commit(role, LogAction.JIU_TIAN_JI_FEN);
    }

    @Override
    public void setKillCount(Role role, int count) {
        RoleJiuTian jiuTian = findJiuTian(role);
        jiuTian.setJiuTianKillCount(count);
        DataCenter.updateData(jiuTian);
        log.info("九天之巅，设置玩家杀人数，玩家id {} name {} 杀人数 {}", role.getId(), role.getName(), count);
    }

    @Override
    public void reqJiuTianInfo(Role role) {
        RoleJiuTian jiuTian = findJiuTian(role);
        ResJiuTianInfoMessage msg = new ResJiuTianInfoMessage();
        msg.setKillCount(jiuTian.getJiuTianKillCount());
        msg.setJiFen(jiuTian.getJiuTianJiFen());
        msg.getReward().addAll(jiuTian.getReward());
        MessageUtil.sendMsg(msg, role.getId());
    }
}
