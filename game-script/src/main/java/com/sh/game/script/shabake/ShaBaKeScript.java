// package com.sh.game.script.shabake;
//
// import com.sh.concurrent.AbstractCommand;
// import com.sh.game.GameContext;
// import com.sh.game.common.communication.msg.system.shabake.*;
// import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
// import com.sh.game.common.communication.notice.ShaBaKeFixMonsterNotice;
// import com.sh.game.common.communication.notice.ShaBaKeFixProtectTypeNotice;
// import com.sh.game.common.communication.notice.ShaBaKeStartUpCreateNotice;
// import com.sh.game.common.constant.*;
// import com.sh.game.common.entity.Union;
// import com.sh.game.common.entity.backpack.BackpackStash;
// import com.sh.game.common.entity.backpack.item.Item;
// import com.sh.game.common.entity.daily.AbsShaBaKeData;
// import com.sh.game.common.entity.sys.IShaBakeDateUpdate;
// import com.sh.game.common.entity.sys.ShaBaKeCrossDataProxy;
// import com.sh.game.common.entity.sys.ShaBaKeData;
// import com.sh.game.common.entity.usr.Role;
// import com.sh.game.common.entity.usr.RoleSummary;
// import com.sh.game.common.tips.CommonTips;
// import com.sh.game.common.util.GlobalUtil;
// import com.sh.game.common.util.MessageUtil;
// import com.sh.game.common.util.TimeUtil;
// import com.sh.game.common.util.TipUtil;
// import com.sh.game.data.DataCenter;
// import com.sh.game.data.SysDataProvider;
// import com.sh.game.event.IEventOnRoleLoginScript;
// import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
// import com.sh.game.notice.NoticeCallback;
// import com.sh.game.protos.ShabakeProtos;
// import com.sh.game.scene.MapProxy;
// import com.sh.game.scene.MapProxyManager;
// import com.sh.game.system.announce.AnnounceManager;
// import com.sh.game.system.appearance.AppearanceManager;
// import com.sh.game.system.cross.CrossStorageManager;
// import com.sh.game.system.daily.DailyManager;
// import com.sh.game.system.mail.MailManager;
// import com.sh.game.system.redpack.RedPackManager;
// import com.sh.game.system.shabake.script.IShaBaKeScript;
// import com.sh.game.system.summary.SummaryManager;
// import com.sh.game.system.union.UnionManager;
// import com.sh.game.system.union.UnionUtil;
// import com.sh.game.system.union.entity.MemberInfo;
// import com.sh.game.system.user.NameManager;
// import com.sh.script.annotation.Script;
// import lombok.extern.slf4j.Slf4j;
//
// import java.util.ArrayList;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// import static com.sh.game.common.constant.ChatConst.AnnounceId.*;
//
// @Slf4j
// @Script
// public class ShaBaKeScript implements IShaBaKeScript, IEventOnRoleLoginScript, IEventScheduleUpdateOnMinuteScript {
//
//     private AbsShaBaKeData findShaBakeConst() {
//         // 查找进行中的沙巴克活动
//         if (DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.SHABAKE)) {
//             return ShaBaKeConst.getInstance();
//         }
//         if (DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.CROSS_SHABAKE)) {
//             return ShaBaKeCrossConst.getInstance();
//         }
//
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         ShaBaKeCrossDataProxy shaBaKeCrossData = CrossStorageManager.getInstance().findShaBaKeCrossData();
//         if (shaBaKeCrossData == null) {
//             return ShaBaKeConst.getInstance();
//         }
//
//         if (shaBaKeData.getLastEndTime() < shaBaKeCrossData.getLastEndTime()) {
//             return ShaBaKeCrossConst.getInstance();
//         }
//
//         return ShaBaKeConst.getInstance();
//     }
//
//     private IShaBakeDateUpdate findShaBakeData() {
//         if (DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.SHABAKE)) {
//             return SysDataProvider.get(ShaBaKeData.class);
//         }
//         if (DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.CROSS_SHABAKE)) {
//             return CrossStorageManager.getInstance().findShaBaKeCrossData();
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         ShaBaKeCrossDataProxy shaBaKeCrossData = CrossStorageManager.getInstance().findShaBaKeCrossData();
//         if (shaBaKeCrossData == null) {
//             return shaBaKeData;
//         }
//
//         if (shaBaKeData.getLastEndTime() < shaBaKeCrossData.getLastEndTime()) {
//             return shaBaKeCrossData;
//         }
//         return shaBaKeData;
//     }
//
//     @Override
//     public void reqCallProtect(Role role, int type, int useType) {
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         if (type < ShaBaKeConst.getInstance().PROTECY_TYPE_GJ || type > ShaBaKeConst.getInstance().PROTECY_TYPE_ALL) {
//             return;
//         }
//         if (useType < ShaBaKeConst.getInstance().SUCCESS_TYPE_IMME || useType > ShaBaKeConst.getInstance().SUCCESS_TYPE_ACTIVITY) {
//             return;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         int gjNum = shaBaKeData.getGjNum();
//         int protectNum = shaBaKeData.getProtectNum();
//         int shaBaKeCallGjNum = ShaBaKeConst.getInstance().shaBaKeCallGjNum;
//         int shaBaKeCallProtectNum = ShaBaKeConst.getInstance().shaBaKeCallProtectNum;
//         int needGj = shaBaKeCallGjNum - gjNum;
//         int needProtect = shaBaKeCallProtectNum - protectNum;
//         if (needGj <= 0 && needProtect <= 0) {
//             return;
//         }
//         //消耗
//         int shaBaKeGjCost = ShaBaKeConst.getInstance().shaBaKeGjCost;
//         int shaBaKeProtectCost = ShaBaKeConst.getInstance().shaBaKeProtectCost;
//         int totalNeed;
//         if (type == ShaBaKeConst.getInstance().PROTECY_TYPE_GJ) {
//             totalNeed = needGj * shaBaKeGjCost;
//             needProtect = 0;
//         } else if (type == ShaBaKeConst.getInstance().PROTECY_TYPE_HUWEI) {
//             totalNeed = shaBaKeProtectCost * needProtect;
//             needGj = 0;
//         } else {
//             totalNeed = needGj * shaBaKeGjCost + shaBaKeProtectCost * needProtect;
//         }
//         long money = shaBaKeData.getMoney();
//         if (money < totalNeed) {
//             return;
//         }
//         shaBaKeData.setMoney(money - totalNeed);
//
//         //处理怪物
//         if (useType == ShaBaKeConst.getInstance().SUCCESS_TYPE_IMME) {
//             if (needGj > 0 || needProtect > 0) {
//                 ShaBaKeFixMonsterNotice monsterNotice = new ShaBaKeFixMonsterNotice();
//                 monsterNotice.setGjNum(needGj);
//                 monsterNotice.setProtectNum(needProtect);
//                 monsterNotice.setProtectType(ShaBaKeConst.getInstance().NORMAL);
//                 monsterNotice.setServerType(ShaBaKeConst.getInstance().SERVER_TYPE);
//                 int shaBaKeFirstMap = ShaBaKeConst.getInstance().shaBaKeFirstMap;
//                 MapProxy mapProxy = MapProxyManager.getInstance().getMap(shaBaKeFirstMap);
//                 if (mapProxy != null) {
//                     GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, monsterNotice, mapProxy.getId());
//                 }
//             }
//         }
//         shaBaKeData.setGjNum(shaBaKeData.getGjNum() + needGj);
//         shaBaKeData.setProtectNum(shaBaKeData.getProtectNum() + needProtect);
//         DataCenter.updateData(shaBaKeData);
//
//         ResCallProtectMessage msg = new ResCallProtectMessage();
//         msg.setProto(ShabakeProtos.ResCallProtect.newBuilder()
//                 .setUseType(useType)
//                 .build());
//         MessageUtil.sendMsg(msg, role.getId());
//
//         sendMoneyMsg(role, shaBaKeData.getMoney());
//
//         reqMonsterHp(role);
//     }
//
//     @Override
//     public void reqControlDoor(Role role) {
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         int shaBaKeDoorCost = ShaBaKeConst.getInstance().shaBaKeDoorCost;
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         long money = shaBaKeData.getMoney();
//         if (money < shaBaKeDoorCost) {
//             return;
//         }
//         shaBaKeData.setMoney(money - shaBaKeDoorCost);
//
//         shaBaKeData.setDoorClose(shaBaKeData.isSetClose() && !shaBaKeData.isDoorClose());
//         shaBaKeData.setSetClose(true);
//         DataCenter.updateData(shaBaKeData);
//
//         sendMoneyMsg(role, shaBaKeData.getMoney());
//
//         ResControlDoorMessage msg = new ResControlDoorMessage();
//         msg.setProto(ShabakeProtos.ResControlDoor.newBuilder()
//                 .setClose(shaBaKeData.isDoorClose())
//                 .build());
//         MessageUtil.sendMsg(msg, role.getId());
//     }
//
//
//     @Override
//     public void reqPutMoney(Role role, long coin) {
//         if (coin <= 0) {
//             return;
//         }
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         BackpackStash stash = new BackpackStash(role);
//         stash.decrease(BagConst.ItemId.GOLD_BIND, coin);
//         stash.commit(role, LogAction.SHABAKE, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
//             @Override
//             public void callback(BackpackStashCommitRetNotice notice) {
//                 if (!notice.isSuccess()) {
//                     return;
//                 }
//                 shaBaKeData.setMoney(shaBaKeData.getMoney() + coin);
//                 DataCenter.updateData(shaBaKeData);
//
//                 sendMoneyMsg(role, shaBaKeData.getMoney());
//             }
//         });
//     }
//
//     /**
//      * 发送消息
//      *
//      * @param money
//      * @param role
//      */
//     private void sendMoneyMsg(Role role, long money) {
//         Union union = UnionManager.getInstance().getUnion(role);
//         if (union == null) {
//             return;
//         }
//         ResShaBaKeMoneyMessage msg = new ResShaBaKeMoneyMessage();
//         List<MemberInfo> collect = union.getMemberInfos().values().stream().filter(memberInfo ->
//                 UnionPositionConst.canOperate(memberInfo.getPosition(), UnionPositionConst.Operation.SHABAKE_OPERATION)).collect(Collectors.toList());
//         msg.setProto(ShabakeProtos.ResShaBaKeMoney.newBuilder()
//                 .setMoney(money)
//                 .build());
//         collect.forEach(memberInfo -> MessageUtil.sendMsg(msg, memberInfo.getMemberId()));
//     }
//
//     @Override
//     public void reqMonsterHp(Role role) {
//         Union union = UnionManager.getInstance().getUnion(role);
//         if (union == null) {
//             return;
//         }
//         IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//         ResMonsterHpMessage msg = new ResMonsterHpMessage();
//         ShabakeProtos.ResMonsterHp.Builder protoBuilder = ShabakeProtos.ResMonsterHp.newBuilder();
//         protoBuilder.setGjNum(shaBaKeData.getGjNum());
//         protoBuilder.setProtectNum(shaBaKeData.getProtectNum());
//         shaBaKeData.getNpcHp().values().forEach(protoBuilder::addWallHp);
//         msg.setProto(protoBuilder.build());
//         MessageUtil.sendMsg(msg, role.getId());
//     }
//
//     @Override
//     public void reqGetMoney(Role role, long coin) {
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         if (coin <= 0) {
//             return;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         long money = shaBaKeData.getMoney();
//         long result = money - coin;
//         if (result < 0) {
//             result = 0;
//             coin = money;
//         }
//
//         long finalMoney = result;
//
//         BackpackStash stash = new BackpackStash(role);
//         stash.increase(BagConst.ItemId.GOLD_BIND, coin);
//         stash.commit(role, LogAction.SHABAKE, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
//             @Override
//             public void callback(BackpackStashCommitRetNotice notice) {
//                 if (!notice.isSuccess()) {
//                     return;
//                 }
//                 shaBaKeData.setMoney(finalMoney);
//                 DataCenter.updateData(shaBaKeData);
//
//                 sendMoneyMsg(role, finalMoney);
//             }
//         });
//     }
//
//     @Override
//     public void reqFixWall(Role role) {
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         Map<Integer, Integer> npcHp = shaBaKeData.getNpcHp();
//         long count = npcHp.values().stream().filter(percent -> percent < 100).count();
//         if (count <= 0) {
//             return;
//         }
//         //计算消耗
//         int shaBaKeDoorCost = findShaBakeConst().shaBaKeWallCost;
//         int totalRatio = 0;
//         for (int value : npcHp.values()) {
//             totalRatio += value;
//         }
//         int needFix = npcHp.size() * 100 - totalRatio;
//         int needNum = shaBaKeDoorCost * needFix;
//
//         long money = shaBaKeData.getMoney();
//         if (money < needNum) {
//             return;
//         }
//         shaBaKeData.setMoney(money - needNum);
//         npcHp.clear();
//         List<Integer> shaBaKeWall = findShaBakeConst().shaBaKeWall;
//         for (int i : shaBaKeWall) {
//             npcHp.put(i, 100);
//         }
//         DataCenter.updateData(shaBaKeData);
//
//         ResFixWallMessage msg = new ResFixWallMessage();
//         msg.setProto(ShabakeProtos.ResFixWall.newBuilder().build());
//         MessageUtil.sendMsg(msg, role.getId());
//
//         sendMoneyMsg(role, shaBaKeData.getMoney());
//
//         reqMonsterHp(role);
//     }
//
//     @Override
//     public void reqFixProtect(Role role, int type) {
//         if (type < ShaBaKeConst.getInstance().ACTIVE || type > ShaBaKeConst.getInstance().NORMAL) {
//             return;
//         }
//         if (!checkCanOperation(role)) {
//             return;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         int shaBaKeProtectTypeCost = ShaBaKeConst.getInstance().shaBaKeProtectTypeCost;
//         long money = shaBaKeData.getMoney();
//         if (money < shaBaKeProtectTypeCost) {
//             return;
//         }
//         shaBaKeData.setMoney(money - shaBaKeProtectTypeCost);
//         shaBaKeData.setProtectType(type);
//         DataCenter.updateData(shaBaKeData);
//         //地图处理
//         ShaBaKeFixProtectTypeNotice typeNotice = new ShaBaKeFixProtectTypeNotice();
//         typeNotice.setServerType(ShaBaKeConst.getInstance().SERVER_TYPE);
//         typeNotice.setFixType(type);
//         int shaBaKeFirstMap = ShaBaKeConst.getInstance().shaBaKeFirstMap;
//         MapProxy mapProxy = MapProxyManager.getInstance().getMap(shaBaKeFirstMap);
//         if (mapProxy != null) {
//             GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, typeNotice, mapProxy.getId());
//         }
//
//         ResFixProtectMessage msg = new ResFixProtectMessage();
//         msg.setProto(ShabakeProtos.ResFixProtect.newBuilder()
//                 .setType(type)
//                 .build());
//         MessageUtil.sendMsg(msg, role.getId());
//
//         sendMoneyMsg(role, shaBaKeData.getMoney());
//         reqMonsterHp(role);
//     }
//
//     /**
//      * 能否操作
//      *
//      * @return
//      */
//     private boolean checkCanOperation(Role role) {
//         Union union = UnionManager.getInstance().getUnion(role);
//         if (union == null) {
//             TipUtil.show(role.getId(), CommonTips.脚本_没有行会);
//             return false;
//         }
//         if (!UnionPositionConst.canOperate(union.memberPosition(role), UnionPositionConst.Operation.SHABAKE_OPERATION)) {
//             TipUtil.show(role.getId(), CommonTips.脚本_没有权限);
//             return false;
//         }
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         long lastWinUnionId = shaBaKeData.getLastWinUnionId();
//         if (union.getId() != lastWinUnionId) {
//             TipUtil.show(role.getId(), CommonTips.脚本_没有权限);
//             return false;
//         }
//         //TODO 错误依赖场景代码
//         /*ShaBaKeController controller = (ShaBaKeController) DailyManager.getInstance().getController(DailyConst.DailyType.SHABAKE);
//         if (controller.isInPeriod()) {
//             return false;
//         }*/
//         return true;
//     }
//
//     @Override
//     public void onMapReady() {
//         MapProxy mapProxy = MapProxyManager.getInstance().getMap(ShaBaKeConst.getInstance().shaBaKeFirstMap);
//         if (mapProxy != null) {
//             ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//             if (shaBaKeData.getLastWinUnionId() <= 0) {
//                 shaBaKeData.initData();
//             }
//             //创建怪物
//             log.error("=================================================================================== 沙巴克启动正式开始");
//             ShaBaKeStartUpCreateNotice createNotice = new ShaBaKeStartUpCreateNotice();
//             createNotice.setLastWinUnionId(shaBaKeData.getLastWinUnionId());
//             createNotice.setGjNum(shaBaKeData.getGjNum());
//             createNotice.setProtectNum(shaBaKeData.getProtectNum());
//             createNotice.setNpcHp(shaBaKeData.getNpcHp());
//             createNotice.setProtectType(shaBaKeData.getProtectType());
//             GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, createNotice, mapProxy.getId());
//         }
//     }
//
//     @Override
//     public void onLoginFixShaBakeTitle(Role role) {
//         Union myUnion = UnionManager.getInstance().getUnion(role);
//         if (myUnion == null) {
//             return;
//         }
//
//         ShaBaKeData shaBaKeData = SysDataProvider.get(ShaBaKeData.class);
//         ShaBaKeCrossDataProxy shaBaKeCrossData = CrossStorageManager.getInstance().findShaBaKeCrossData();
//
//         long lastWinUnionId = shaBaKeData.getLastWinUnionId();
//         long lastCrossWinUnionId = shaBaKeCrossData.getLastWinUnionId();
//
//         reqShaBaKeInfo(role);
//
//         // 修复沙巴克称号
//         int fashion = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHABAKE_WIN_FASHION);
//         if (lastWinUnionId <= 0 || myUnion.getId() != lastWinUnionId) {
//             AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getRoleId(), 0, fashion);
//             List<Integer> shaBaKeTitle = ShaBaKeConst.getInstance().shaBaKeTitle;
//             AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getRoleId(), 0, shaBaKeTitle);        }
//         if (myUnion.getId() == lastWinUnionId) {
//             long leaderId = UnionUtil.getUnionLeaderId(myUnion.getId());
//             //通知城主上线 处理时装
//             if (leaderId == role.getId()) {
//                 AnnounceManager.getInstance().post(SHABAKE_LEADER_LOGIN, 0, role);
//                 if (!AppearanceManager.getInstance().checkFashion(role, fashion)) {
//                     AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), -1, fashion);
//                 }
//             } else {
//                 if (AppearanceManager.getInstance().checkFashion(role, fashion)) {
//                     AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), 0, fashion);
//                 }
//             }
//             int shaBaKeTitle = UnionUtil.getShaBaKeTitle(myUnion, myUnion.getMemberInfos().get(role.getId()).getPosition());
//             if (!AppearanceManager.getInstance().checkFashion(role, shaBaKeTitle)) {
//                 AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), -1, shaBaKeTitle);
//             }
//         }
//
//         // 修复跨服沙巴克称号
// //        boolean isCrossShaBaKe = DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.CROSS_SHABAKE);
// //        int crossFashion = GlobalUtil.getGlobalInt(GameConst.GlobalId.CROSS_SHABAKE_WIN_FASHION);
// //        if (lastCrossWinUnionId <= 0 || myUnion.getId() != lastCrossWinUnionId) {
// //            AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getRoleId(), 0, crossFashion);
// //            List<Integer> shaBaKeTitle = ShaBaKeCrossConst.getInstance().shaBaKeTitle;
// //            AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getRoleId(), 0, shaBaKeTitle);        }
// //        if (myUnion.getId() == lastCrossWinUnionId) {
// //            long leaderId = UnionUtil.getUnionLeaderId(myUnion.getId());
//             //通知城主上线 处理时装
// //            if (leaderId == role.getId()) {
// //                AnnounceManager.getInstance().post(CROSS_SHABAKE_LEADER_LOGIN, 0, role);
// //                if (!AppearanceManager.getInstance().checkFashion(role, crossFashion) && !isCrossShaBaKe) {
// //                    AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), -1, crossFashion);
// //                }
// //            } else {
// //                if (AppearanceManager.getInstance().checkFashion(role, crossFashion)) {
// //                    AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), 0, crossFashion);
// //                }
// //            }
// //            int shaBaKeTitle = getShaBaKeTitle(myUnion, myUnion.getMemberInfos().get(role.getId()).getPosition());
// //            if (!AppearanceManager.getInstance().checkFashion(role, shaBaKeTitle) && !isCrossShaBaKe) {
// //                AppearanceManager.getInstance().modifyFashion(LogAction.SHABAKE, role.getId(), -1, shaBaKeTitle);
// //            }
// //        }
//     }
//
//     private int getShaBaKeTitle(Union union, int position) {
//         if (union == null) {
//             return 0;
//         }
//         ShaBaKeCrossDataProxy shaBaKeData = CrossStorageManager.getInstance().findShaBaKeCrossData();
//         if (shaBaKeData.getLastWinUnionId() > 0 && shaBaKeData.getLastWinUnionId() == union.getId()) {
//             List<Integer> shaBaKeTitle = ShaBaKeCrossConst.getInstance().shaBaKeTitle;
//             return shaBaKeTitle.get(position - 1);
//         }
//         return 0;
//     }
//
//     @Override
//     public void reqShaBaKeInfo(Role role) {
//         IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//         ResShaBaKeSetInfoMessage msg = new ResShaBaKeSetInfoMessage();
//         ShabakeProtos.ResShaBaKeSetInfo.Builder protoBuilder = ShabakeProtos.ResShaBaKeSetInfo.newBuilder();
//         long lastWinUnionId = shaBaKeData.getLastWinUnionId();
//         Union union = UnionManager.getInstance().getUnion(lastWinUnionId);
//         if (union != null) {
//             protoBuilder.setUnionId(union.getId());
//             protoBuilder.setUnionName(union.getName());
//             protoBuilder.setLeaderName(NameManager.getInstance().getNameByRid(UnionUtil.getUnionLeaderId(union.getId())));
//         }
//         protoBuilder.setClose(!shaBaKeData.isSetClose() || shaBaKeData.isDoorClose());
//         protoBuilder.setCoin(shaBaKeData.getMoney());
//         List<Integer> shaBaKeWall = findShaBakeConst().shaBaKeWall;
//         for (int npcId : shaBaKeWall) {
//             if (shaBaKeData.getNpcHp().get(npcId) != null) {
//                 if (shaBaKeData.getNpcHp().get(npcId) <= 0) {
//                     protoBuilder.addDieMonster(npcId);
//                 }
//             }
//         }
//         protoBuilder.setUnionChairmanUid(shaBaKeData.findWinUnionChairmanUid());
//         protoBuilder.setIsCross(shaBaKeData instanceof ShaBaKeCrossDataProxy);
//         protoBuilder.setType(shaBaKeData.getProtectType());
//         msg.setProto(protoBuilder.build());
//         MessageUtil.sendMsg(msg, role.getId());
//     }
//
//     @Override
//     public void reqShaBaKeRedPack(Role role, int count) {
//         Union union = UnionManager.getInstance().getUnion(role);
//         if (union == null || UnionUtil.getUnionLeaderId(union.getId()) != role.getId()) {
//             return;
//         }
//         IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//         if (shaBaKeData.getLastWinUnionId() != union.getId()) {
//             return;
//         }
//         //时间超时
//         int globalInt = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHABAKE_ALLOC_TIME);
//         if (TimeUtil.getNowOfSeconds() > shaBaKeData.getLastEndTime() + globalInt) {
//             return;
//         }
//         if (shaBaKeData.isMoneyGet() || shaBaKeData.isBackToLeader()) {
//             return;
//         }
//         int shaBaKeMoney = shaBaKeData.getShaBaKeMoney();
//         int minNum = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHABAKE_REDPACK_MIN);
//
//         if (count < minNum || count > shaBaKeMoney) {
//             log.error("{}沙巴克发红包参数有问题，{}-{}", role.getName(), shaBaKeMoney, count);
//             return;
//         }
//
//         RedPackManager.getInstance().sendRedPack(union.getId(), role.getId(), BagConst.ItemId.MONEY, shaBaKeMoney, count);
//         shaBaKeData.setMoneyGet(true);
//         if (shaBaKeData instanceof ShaBaKeData) {
//             DataCenter.updateData((ShaBaKeData) shaBaKeData);
//         } else if (shaBaKeData instanceof ShaBaKeCrossDataProxy) {
//             CrossStorageManager.getInstance().updateShaBaKeCrossData((ShaBaKeCrossDataProxy) shaBaKeData);
//         }
//         int announce = findShaBakeConst() instanceof ShaBaKeConst ? SHABAKE_REDPACK : CROSS_SHABAKE_REDPACK;
//         AnnounceManager.getInstance().post(announce, 0, role, union, shaBaKeMoney);
//
//         ResShaBaKeAllocMessage msg = new ResShaBaKeAllocMessage();
//         msg.setProto(ShabakeProtos.ResShaBaKeAlloc.newBuilder()
//                 .setOwn("已发红包")
//                 .build());
//         MessageUtil.sendToUnion(union, msg);
//     }
//
//     @Override
//     public void reqShaBaKeAlloc(Role role, long uid, long itemId) {
//         Union union = UnionManager.getInstance().getUnion(role);
//         IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//         if (union == null || shaBaKeData.getLastWinUnionId() != union.getId()) {
//             TipUtil.show(role.getId(), CommonTips.脚本_无法分配);
//             return;
//         }
//         if (UnionUtil.getUnionLeaderId(union.getId()) != role.getId()) {
//             TipUtil.show(role.getId(), CommonTips.脚本_非会长无法进行分配);
//             return;
//         }
//         if (!union.getMemberInfos().containsKey(uid)) {
//             TipUtil.show(role.getId(), CommonTips.脚本_分配对象已退会);
//             return;
//         }
//         RoleSummary summary = SummaryManager.getInstance().getSummary(uid);
//         if (summary == null) {
//             TipUtil.show(role.getId(), CommonTips.脚本_分配对象已退会);
//             return;
//         }
//         //时间超时
//         if (TimeUtil.getNowOfSeconds() > shaBaKeData.getLastEndTime() + GlobalUtil.getGlobalInt(GameConst.GlobalId.SHABAKE_ALLOC_TIME)) {
//             TipUtil.show(role.getId(), CommonTips.脚本_已过分配期);
//             return;
//         }
//         Map<Long, Long> shaBaKeAlloc = shaBaKeData.getShaBaKeAlloc();
//         //已经分配过
//         if (shaBaKeAlloc.containsKey(itemId)) {
//             TipUtil.show(role.getId(), CommonTips.脚本_当前道具已分配);
//             return;
//         }
//         //不存在的道具
//         Map<Long, Item> shaBaKeReward = shaBaKeData.getShaBaKeReward();
//         Item item = shaBaKeReward.getOrDefault(itemId, null);
//         if (item == null) {
//             return;
//         }
//
//         shaBaKeAlloc.put(itemId, uid);
//
//         int mail = 0;
//         if (shaBaKeData instanceof ShaBaKeData) {
//             DataCenter.updateData((ShaBaKeData) shaBaKeData);
//             mail = EmailConst.MailId.SHABAKE_ALLOC_REWARD;
//         } else if (shaBaKeData instanceof ShaBaKeCrossDataProxy) {
//             CrossStorageManager.getInstance().updateShaBaKeCrossData((ShaBaKeCrossDataProxy) shaBaKeData);
//             mail = EmailConst.MailId.CROSS_SHABAKE_ALLOC_REWARD;
//         }
//         BackpackStash stash = new BackpackStash(role);
//         stash.increase(item.getCfgId(), item.findCount());
//         stash.commitToMail(uid, mail);
//
//         ResShaBaKeAllocMessage msg = new ResShaBaKeAllocMessage();
//         msg.setProto(ShabakeProtos.ResShaBaKeAlloc.newBuilder()
//                 .setUid(uid)
//                 .setItemId(itemId)
//                 .setOwn(summary.getName())
//                 .build());
//         MessageUtil.sendToUnion(union, msg);
//     }
//
//     @Override
//     public void reqShaBaKeAllocInfo(long roleId) {
//         Role role = DataCenter.getRole(roleId);
//         if (role == null) {
//             return;
//         }
//         Union union = UnionManager.getInstance().getUnion(role);
//         if (union == null) {
//             return;
//         }
//         IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//         if (shaBaKeData.getLastWinUnionId() != union.getId()) {
//             return;
//         }
//
//         Map<Long, Item> shaBaKeReward = shaBaKeData.getShaBaKeReward();
//         if (shaBaKeReward.isEmpty()) {
//             return;
//         }
//
//         Map<Long, Long> shaBaKeAlloc = shaBaKeData.getShaBaKeAlloc();
//
//         ResShaBaKeAllocInfoMessage msg = new ResShaBaKeAllocInfoMessage();
//         ShabakeProtos.ResShaBaKeAllocInfo.Builder protoBuilder = ShabakeProtos.ResShaBaKeAllocInfo.newBuilder();
//
//         List<ShabakeProtos.AllocItemBean> itemList = new ArrayList<>();
//         for (Item value : shaBaKeReward.values()) {
//             ShabakeProtos.AllocItemBean.Builder itemBean = ShabakeProtos.AllocItemBean.newBuilder();
//             itemBean.setCfgId(value.getCfgId());
//             itemBean.setCount(value.findCount());
//             itemBean.setItemId(value.getId());
//             if (shaBaKeAlloc.containsKey(value.getId())) {
//                 RoleSummary summary = SummaryManager.getInstance().getSummary(shaBaKeAlloc.get(value.getId()));
//                 if (summary != null) {
//                     itemBean.setUid(summary.getId());
//                     itemBean.setOwn(summary.getName());
//                 }
//             }
//             itemList.add(itemBean.build());
//         }
//         //钻石特殊处理下
//         long unionLeaderId = UnionUtil.getUnionLeaderId(union.getId());
//         ShabakeProtos.AllocItemBean.Builder itemBean = ShabakeProtos.AllocItemBean.newBuilder();
//         itemBean.setCfgId(BagConst.ItemId.MONEY);
//         itemBean.setCount(shaBaKeData.getShaBaKeMoney());
//         if (shaBaKeData.isMoneyGet()) {
//             itemBean.setOwn("已发红包");
//         } else if (shaBaKeData.isBackToLeader()) {
//             RoleSummary summary = SummaryManager.getInstance().getSummary(unionLeaderId);
//             if (summary != null) {
//                 itemBean.setUid(summary.getId());
//                 itemBean.setOwn(summary.getName());
//             }
//         }
//         itemList.add(itemBean.build());
//         protoBuilder.addAllItemList(itemList);
//
//         //会长才能看到的数据
//         if (role.getId() == unionLeaderId) {
//             List<ShabakeProtos.AllocPlayerBean> playerList = new ArrayList<>();
//             Map<Long, Integer> playerScores = shaBaKeData.getPlayerScores();
//             for (long rid : union.getMemberInfos().keySet()) {
//                 playerList.add(ShabakeProtos.AllocPlayerBean.newBuilder()
//                         .setUid(rid)
//                         .setScore(playerScores.getOrDefault(rid, 0))
//                         .build());
//             }
//             protoBuilder.addAllPlayerList(playerList);
//         }
//         msg.setProto(protoBuilder.build());
//         MessageUtil.sendMsg(msg, role.getId());
//     }
//
//     @Override
//     public void onRoleLogin(Role role) {
//         reqShaBaKeAllocInfo(role.getId());
//     }
//
//     @Override
//     public void scheduleUpdateOnMinute() {
//         GameContext.getGameServer().getRouter().getProcessor(ProcessorId.SERVER_COMMON).process(new AbstractCommand() {
//             @Override
//             public void doAction() {
//                 int allocTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHABAKE_ALLOC_TIME);
//                 IShaBakeDateUpdate shaBaKeData = findShaBakeData();
//                 boolean isCrossShaBaKe = false;
//                 if (shaBaKeData instanceof ShaBaKeCrossDataProxy) {
//                     isCrossShaBaKe = true;
//                 }
//                 int lastEndTime = shaBaKeData.getLastEndTime();
//                 int nowOfSeconds = TimeUtil.getNowOfSeconds();
//                 if (shaBaKeData.getLastWinUnionId() > 0 && nowOfSeconds > lastEndTime + allocTime && !shaBaKeData.isBackToLeader()) {
//                     //道具返还
//                     Map<Long, Item> shaBaKeReward = shaBaKeData.getShaBaKeReward();
//                     Map<Long, Long> shaBaKeAlloc = shaBaKeData.getShaBaKeAlloc();
//                     List<Item> backReward = new ArrayList<>();
//                     for (Map.Entry<Long, Item> longItemEntry : shaBaKeReward.entrySet()) {
//                         if (!shaBaKeAlloc.containsKey(longItemEntry.getKey())) {
//                             backReward.add(longItemEntry.getValue());
//                         }
//                     }
//                     long unionLeaderId = UnionUtil.getUnionLeaderId(shaBaKeData.getLastWinUnionId());
//                     RoleSummary summary = SummaryManager.getInstance().getSummary(unionLeaderId);
//                     if (summary == null) {
//                         return;
//                     }
//                     Union union = UnionManager.getInstance().getUnion(shaBaKeData.getLastWinUnionId());
//                     if (union == null) {
//                         return;
//                     }
//                     int curTime = GameContext.isCombined() ? shaBaKeData.getMergedTime() : shaBaKeData.getOpenTime();
//                     if (!backReward.isEmpty()) {
//                         //剩下得返还给会长
//                         int mailId = GameContext.isCombined() ? 1015 : 1013;
//                         if (isCrossShaBaKe) {
//                             mailId = 18005;
//                         }
//                         MailManager.getInstance().sendMail(unionLeaderId, mailId, backReward, curTime);
//
//                         backReward.forEach(item -> {
//                             shaBaKeAlloc.put(item.getId(), unionLeaderId);
//                             //通知客户端
//                             ResShaBaKeAllocMessage msg = new ResShaBaKeAllocMessage();
//                             msg.setProto(ShabakeProtos.ResShaBaKeAlloc.newBuilder()
//                                     .setUid(unionLeaderId)
//                                     .setItemId(item.getId())
//                                     .setOwn(summary.getName())
//                                     .build());
//                             MessageUtil.sendToUnion(union, msg);
//                         });
//                     }
//                     //钻石处理
//                     if (!shaBaKeData.isMoneyGet()) {
//                         int shaBaKeMoney = shaBaKeData.getShaBaKeMoney();
//                         if (shaBaKeMoney > 0) {
//                             Item item = new Item();
//                             item.setCfgId(BagConst.ItemId.MONEY_BIND);
//                             item.setCount(shaBaKeMoney);
//                             int mailId = GameContext.isCombined() ? 3011 : 3010;
//                             if (isCrossShaBaKe) {
//                                 mailId = 18004;
//                             }
//                             List<Item> reward = new ArrayList<>();
//                             reward.add(item);
//                             MailManager.getInstance().sendMail(unionLeaderId, mailId, reward, curTime);
//                             //通知客户端
//                             ResShaBaKeAllocMessage msg = new ResShaBaKeAllocMessage();
//                             msg.setProto(ShabakeProtos.ResShaBaKeAlloc.newBuilder()
//                                     .setOwn(summary.getName())
//                                     .build());
//                             MessageUtil.sendToUnion(union, msg);
//                         }
//                     }
//                     shaBaKeData.setBackToLeader(true);
//                     if (shaBaKeData instanceof ShaBaKeData) {
//                         DataCenter.updateData((ShaBaKeData) shaBaKeData);
//                     } else if (shaBaKeData instanceof ShaBaKeCrossDataProxy) {
//                         CrossStorageManager.getInstance().updateShaBaKeCrossData((ShaBaKeCrossDataProxy) shaBaKeData);
//                     }
//                 }
//             }
//         }, 0L);
//     }
// }