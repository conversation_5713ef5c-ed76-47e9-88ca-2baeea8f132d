package com.sh.game.script.yuanjingxiaopu;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.yuanjing.ResYuanJingStoreInfoMessage;
import com.sh.game.common.config.model.YuanJingStoreConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.BackPackStashUtil;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.yuanjingxiaopu.entity.RoleYuanJingStore;
import com.sh.game.system.yuanjingxiaopu.script.IYuanJingXiaoPuScript;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Script
public class YuanJingXiaoPuScript implements IYuanJingXiaoPuScript {

    /**
     * 抽卡
     */
    @Override
    public void drawCard(Role role, int cid, int num) {
        if (num != 1 && num != 10) {
            log.error("源晶小铺-抽卡#玩家:{}-{},配置id:{},参数错误:{}", role.getId(), role.getName(), cid, num);
            return;
        }
        YuanJingStoreConfig config = ConfigDataManager.getInstance().getById(YuanJingStoreConfig.class, cid);
        if (config == null) {
            log.error("源晶小铺-抽卡#玩家:{}-{},配置id:{}，配置不存在", role.getId(), role.getName(), cid);
            return;
        }
        //扣除消耗
        boolean result = BackPackStashUtil.decrease(role, config.getCost(), num, LogAction.YUAN_JING_STORE_DRAW_CARD_COST);
        if (!result) {
            log.error("源晶小铺-抽卡#玩家:{}-{},配置id:{},count:{},消耗道具失败", role.getId(), role.getName(), cid, num);
            return;
        }
        List<Item> items = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            List<Item> list = drawCard(role, config);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            items.addAll(list);
        }
        boolean add = BackPackStashUtil.increaseItemList(role, items, LogAction.YUAN_JING_STORE_DRAW_CARD_REWARD,
                TurnBasedConst.BattleType.YU_JING_XIAO_PU);
        if (!add) {
            log.error("源晶小铺-抽卡#玩家:{}-{},配置id:{},count:{},添加道具失败", role.getId(), role.getName(), cid, num);
            return;
        }
        DataCenter.updateData(role.findYuanJingStore());
        sendMsg(role);
        log.info("源晶小铺-抽卡#玩家:{}-{},配置id:{},count:{},成功", role.getId(), role.getName(), cid, num);
    }

    private List<Item> drawCard(Role role, YuanJingStoreConfig config) {
        RoleYuanJingStore store = role.findYuanJingStore();
        //保底道具
        int minBox = config.getMinBox();
        List<Item> items = new ArrayList<>();
        if (store.getDrawCount() + 1 == config.getCount()) {//到达保底次数
            Item item = ItemUtil.create(minBox, 1, LogAction.YUAN_JING_STORE_DRAW_CARD_REWARD);
            if (item == null) {
                return Collections.emptyList();
            }
            items.add(item);
            store.setDrawCount(0);
        } else {
            List<Item> list = BoxUtil.openBox(config.getBox());
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            items.addAll(list);
            store.setDrawCount(store.getDrawCount() + 1);

            //抽到保底奖励，直接重置抽卡次数
            list.forEach(item -> {
                if (item.getCfgId() == minBox) {
                    store.setDrawCount(0);
                }
            });
        }
        return items;
    }

    @Override
    public void sendMsg(Role role) {
        RoleYuanJingStore store = role.findYuanJingStore();

        ResYuanJingStoreInfoMessage msg = new ResYuanJingStoreInfoMessage();
        msg.setCount(store.getDrawCount());
        MessageUtil.sendMsg(msg, role.getId());
    }
}
