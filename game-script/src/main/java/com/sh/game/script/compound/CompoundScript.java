package com.sh.game.script.compound;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.Tuple;
import com.sh.commons.tuple.TwoTuple;
import com.sh.commons.util.Cast;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.abc.bean.ItemBriefBean;
import com.sh.game.common.communication.msg.system.compound.*;
import com.sh.game.common.communication.msg.system.compound.bean.SpecialItemCompoundInfoBean;
import com.sh.game.common.config.cache.ChaoticPowerCache;
import com.sh.game.common.config.cache.CompoundCache;
import com.sh.game.common.config.cache.SpecialRingCache;
import com.sh.game.common.config.cache.ZodiacCache;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleBackpackUpdateScript;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.system.activity.script.IActivityTianXieScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.compound.entity.CompoundSpecialItemRecord;
import com.sh.game.system.compound.entity.CompoundZodiacWeight;
import com.sh.game.system.compound.entity.RoleCompoundExtraProbability;
import com.sh.game.system.compound.script.ICompoundScript;
import com.sh.game.system.daily.DailyManager;
import com.sh.game.system.function.FunctionManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.record.RecordManager;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.system.treasureHunt.TreasureHuntManager;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 道具合成
 */

@Slf4j
@Script
@EqualsAndHashCode
public class CompoundScript implements ICompoundScript, IEventOnRoleBackpackUpdateScript {

    @Override
    public void reqBagCompoundItem(Role role, int configId, int compoundCount, int career, boolean hero, int materialCid, boolean quick, boolean isPerfect) {
        if (compoundCount <= 0) {
            log.error("背包合成消息错误！合成数量为0");
            return;
        }
        CompoundConfig config = ConfigDataManager.getInstance().getById(CompoundConfig.class, configId);
        if (config == null) {
            return;
        }
        //如果当前合成需要消耗对应道具转为100%成功,但又没有配置的,那置此isPerfect为false
        if (isPerfect && (config.getBaifenbai() == null || config.getBaifenbai().size() <= 0)){
            isPerfect = false;  //没有配置消耗,不能100%成功
        }

        SpecialRingCache cache = ConfigCacheManager.getInstance().getCache(SpecialRingCache.class);
        if (cache != null && cache.isCompoundRing(configId)) {
            //检查是否在活动中
            if (!DailyManager.getInstance().isInTimedPeriod(TreasureHuntConst.TREASURE_HUNT_ACTIVITY)) {
                TipUtil.show(role, CommonTips.脚本_活动中特殊特戒);
                return;
            }
            if (role.getRingsMap().getOrDefault(config.getPage3(), 0) < 1) {
                return;
            }
        }

        //额外成功率    todo 暂时不支持快捷合成
        RoleCompoundExtraProbability extraProbability = findExtraProbability(role.getRoleId());
        Map<Integer, Integer> extraProbabilityMap = extraProbability.getExtraProbabilityMap();
        int testExtraProbability = extraProbabilityMap.getOrDefault(configId, 0);
        int currentExtraProbability = extraProbabilityMap.getOrDefault(configId, 0);

        int succeedCount = 0;
        //总消耗
        List<int[]> Allcosts = new ArrayList<>();
        List<int[]> verifyCosts = new ArrayList<>();
        if (quick) {
            // 快捷合成消耗
            Map<Integer,Long> costMap = new HashMap<>();
            List<int[]> quick_compound = config.getQuick_compound();
            for (int[] cost : quick_compound) {
                costMap.put(cost[0], (long) cost[1] * compoundCount);
            }
            // 找出同组的中间材料配置
            List<CompoundConfig> compoundConfigList = ConfigDataManager.getInstance().getList(CompoundConfig.class);
            List<CompoundConfig> list = new ArrayList<>();
            for (CompoundConfig compoundConfig : compoundConfigList) {
                if (config.getQuick_group() == compoundConfig.getQuick_group() && config.getId() > compoundConfig.getId()) {
                    list.add(compoundConfig);
                }
            }
            // 检查是否拥有中间材料
            for (int j = list.size() - 1; j >= 0; j--) {
                CompoundConfig compoundConfig = list.get(j);
                List<int[]> product = compoundConfig.getProduct();
                int intermediate = 0;
                long itemCount = 0;
                for (int[] ints : product) {
                    itemCount = role.getBackpack().fetchCountLByCfgId(ints[0], BackpackConst.Browse.BACKPACK_AND_EQUIP);
                    if (itemCount > 0) {
                        intermediate = ints[0];
                        break;
                    }
                }
                if (intermediate == 0) {
                    continue;
                }
                // 此处计算最小消耗量
                List<int[]> costList = compoundConfig.getQuick_compound();
                for (int[] cost : costList) {
                    long costCount = costMap.getOrDefault(cost[0], 0L);
                    long multiple = costCount / cost[1];
                    itemCount = Math.min(multiple, itemCount);
                }
                for (int[] cost : costList) {
                    // 消耗中间材料并在消耗中减去中间材料的快捷消耗
                    long costCount = costMap.getOrDefault(cost[0], 0L);
                    costMap.put(cost[0], costCount - cost[1] * itemCount);
                }
                costMap.put(intermediate, itemCount);
            }

            for (Map.Entry<Integer, Long> entry : costMap.entrySet()) {
                Allcosts.add(new int[]{entry.getKey(), Math.toIntExact(entry.getValue())});
            }
            // 快捷合成一次只合成一件,不会批量合成,每次都只是消耗一次最终目标的合成材料
            if (RandomUtil.isGenerate(10000, config.getProbability())) {
                succeedCount++;
            }
        } else {
            for (int i = 0; i < compoundCount; i++) {
                Allcosts.addAll(config.getItemid1());
                List<int[]> item_career = config.getItem_career();

                // 必需要拥有但只有合成成功才参与消耗
                List<int[]> itemSave = config.getItemSave();
                if (RandomUtil.isGenerate(10000, isPerfect ? 10000 : config.getProbability() + currentExtraProbability)) {
                    succeedCount++;
                    Allcosts.addAll(itemSave);
                    if (isPerfect){
                        Allcosts.addAll(config.getBaifenbai());
                    }
                    currentExtraProbability = 0;
                } else {
                    currentExtraProbability += config.getProbabilityUp();
                }

                verifyCosts.addAll(itemSave);
                if (!role.getBackpack().verifyItemCount(verifyCosts, BackpackConst.Browse.BACKPACK_AND_EQUIP)) {
                    TipUtil.show(role, CommonTips.脚本_道具不足);
                    return;
                }

                // 是否消耗可选材料
                boolean isSelectCost = false;
                for (int[] ints : item_career) {
                    if (ints.length < 1) {
                        continue;
                    }
                    if (ints[0] == materialCid) {
                        Allcosts.add(ints);
                        isSelectCost = true;
                    }
                }

                // 有配置需要消耗可选材料但未传可选材料cid
                if (!item_career.isEmpty() && !isSelectCost) {
                    return;
                }
            }
        }
        /**
         * 装备和item消耗独立分开
         */
        Map<Integer, Integer> equitCostsMap = new HashMap<>();
        Map<Integer, Integer> itemCostsMap = new HashMap<>();
        Map<Integer, Long> moneyCostsMap = new HashMap<>();
        for (int i = 0; i < Allcosts.size(); i++) {
            int[] ints = Allcosts.get(i);

            int itemId = ints[0];
            //检验道具是否为配置表
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            if (itemConfig == null) {
                continue;
            }

            if (itemConfig.getItemtype() == BagConst.ItemType.EQUIP) {
                Integer count = equitCostsMap.get(itemId);
                if (count == null) {
                    count = 0;
                }
                equitCostsMap.put(itemId, count + ints[1]);
            } else if (itemConfig.getItemtype() == BagConst.ItemType.COIN) {
                Long count = moneyCostsMap.get(itemId);
                if (count == null) {
                    count = 0L;
                }
                moneyCostsMap.put(itemId, count + ints[1]);
            } else {
                Integer count = itemCostsMap.get(itemId);
                if (count == null) {
                    count = 0;
                }
                itemCostsMap.put(itemId, count+ints[1]);
            }
        }

        /**
         * 货币有专属属性，降低消耗
         */
        for (Map.Entry<Integer, Long> entry : moneyCostsMap.entrySet()) {
            if (entry.getKey() == BagConst.ItemId.GOLD || entry.getKey() == BagConst.ItemId.GOLD_BIND) {
                long goldRate = 10000 - role.getFinalAttribute().get(AttributeEnum.COMPOUND_GOLD_DECLINE);

                int[] faqijianmian = config.getFaqijianmian();
                if (faqijianmian != null && faqijianmian.length >= 2) {
                    if (role.findFaQiHuiYuanConfig().getVipLevel() >= faqijianmian[0]) {
                        goldRate *= (10000D - faqijianmian[1]) / 10000;
                    }
                }
                // 向上取整
                long gold = (goldRate  * entry.getValue() + 9999) /10000;
                moneyCostsMap.put(entry.getKey(), gold);
            }
        }


        int dressing = config.getDressing();
        BackpackConst.Place[] costBack;
        //获得道具到哪个背包
        BackpackConst.Place targetPos = null;
        //根据不同配置扣道具的背包范围不同
        if (dressing == 3) {
            if (hero) {
                costBack = BackpackConst.Browse.BACKPACK_HERO_EQUIP;
                targetPos = BackpackConst.Place.BACKPACK_HERO;
            } else {
                costBack = BackpackConst.Browse.BACKPACK_AND_EQUIP;
            }
        } else {
            costBack = dressing <= 0 ? BackpackConst.Browse.BACKPACK :
                    (dressing == 1 ? BackpackConst.Browse.BACKPACK_AND_EQUIP : BackpackConst.Browse.BACKPACK_HERO_EQUIP);
        }

        /**
         * 计算消耗 begin
         */
        BackpackStash stash = new BackpackStash(role);

        List<Item> touBaoItem = new ArrayList<>();
        List<Integer> gemIds = new ArrayList<>();
        List<Integer> tiTanGemIds = new ArrayList<>();
        Map<Integer, Integer> longhunGem = new HashMap<>();
        List<Integer> longhunGemList = new ArrayList<>();
        Map<Integer, Integer> xiahunGem = new HashMap<>();
        List<Integer> xiahunGemList = new ArrayList<>();
        //特戒淬炼
        TwoTuple<Integer, Integer> refinementTuple = Tuple.tuple(0, 0);
        List<Integer> chaoticGemIdList = new ArrayList<>();
        //附灵分解返还
        List<int[]> fuLingFenJies = new ArrayList<>();
        //混沌赋灵最高等级
        int chaoticPowerLevel = 0;

        int reserveGemId = 0;
        int reserveChaoticGemId = 0;
//        EquipAttribute identifyAttribute = null;
        //装备类消耗

        if (config.getInherit() == 1 && equitCostsMap.entrySet().size() != 1) {
            log.error("合成-龙魂宝石继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), configId, equitCostsMap.entrySet().size());
            return;
        }

        if (config.getTejieInherit() == 1 && equitCostsMap.entrySet().size() != 1) {
            log.error("合成-特戒继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), configId, equitCostsMap.entrySet().size());
            return;
        }

        for (Map.Entry<Integer, Integer> entry : equitCostsMap.entrySet()) {

            int itemId = entry.getKey();
            int count = entry.getValue();
            List<Item> myItems = role.getBackpack().findItemByCfgId(itemId, costBack);//获取拥有的道具
            if (myItems.size() < count) {
                TipUtil.show(role, CommonTips.脚本_道具不足);
                return;
            }
            List<Item> list = myItems.subList(0, count);
            if (config.getMenu_type() == 10 && config.getNext() == itemId) {
                reserveGemId = list.get(0).getEquipData().getGemId();
                reserveChaoticGemId = list.get(0).getEquipData().getChaoticGemId();
            } else {
                for (Item myItem : list) {
                    EquipData equipData = myItem.getEquipData();
                    if (equipData == null) {
                        continue;
                    }
                    int gemId = equipData.getGemId();
                    if (gemId > 0) {
                        gemIds.add(gemId);
                    }

                    int chaoticGemId = equipData.getChaoticGemId();
                    if (chaoticGemId > 0) {
                        chaoticGemIdList.add(chaoticGemId);
                    }
                }
            }

            //泰坦神石
            for (Item myItem : list) {
                EquipData equipData = myItem.getEquipData();
                if (equipData == null) {
                    continue;
                }
                int gemId = equipData.getTiTanGemId();
                if (gemId > 0) {
                    tiTanGemIds.add(gemId);
                }
            }

            for (Item item : list) {
                if (item.getEquipData() != null && item.getEquipData().getToubao() == 1) {
                    touBaoItem.add(item);
                }
            }

            //龙魂宝石
            if (config.getInherit() == 1) {
                //继承
                longhunGem = longHunInherit(list, role, configId);
            } else {
                //返还
                longhunGemList = longHunRefund(list);
            }

            //侠魂宝石
            if (config.getXiahunInherit() == 1) {
                //继承
                xiahunGem = xiaHunInherit(list, role, configId);
            } else {
                //返还
                xiahunGemList = xiaHunRefund(list);
            }

            if (config.getTejieInherit() == 1) {
                refinementTuple = refinementInherit(role, configId, list);
            }

            //混沌赋灵
            if (config.getFulingInherit() == 1) {
                chaoticPowerLevel = chaoticPowerInherit(role, configId, list);
            }

            //混沌附灵分解
            if (config.getFulingfenjie() == 1) {
                ChaoticPowerCache chaoticPowerCache = ConfigCacheManager.getInstance().getCache(ChaoticPowerCache.class);
                for (Item item : list) {
                    ItemConfig itemConfig = item.findItemConfig();
                    if (itemConfig == null) {
                        continue;
                    }
                    ChaoticPowerConfig powerConfig = chaoticPowerCache.getPowerConfig(itemConfig.getType(), item.getEquipData().getChaoticPowerLevel());
                    if (powerConfig == null) {
                        continue;
                    }
                    fuLingFenJies.addAll(powerConfig.getRecost());
                }
            }

            stash.decrease(list);
        }

        // 货币类消耗
        for (Map.Entry<Integer, Long> entry : moneyCostsMap.entrySet()) {
            Long count = entry.getValue();
            if (count < 0) {
                log.error("合成功能，货币消耗，玩家消耗货币数量小于0，itemId {} count {} ，compoundId {} compoundCount {}，玩家id {} name {}", entry.getKey(), count, config.getId(), compoundCount, role.getId(), role.getName());
                return;
            }
            stash.decrease(entry.getKey(), entry.getValue());
        }

        //物品类消耗
        for (Map.Entry<Integer, Integer> entry : itemCostsMap.entrySet()) {
            List<Item> itemListsFromBack = role.getBackpack().findItemByCfgId(entry.getKey(), BackpackConst.Place.BACKPACK, BackpackConst.Place.MIJI_GEM);
            List<Item> itemListsFromDocker = role.getBackpack().findItemByCfgId(entry.getKey(), BackpackConst.Place.DOCKER);

            int backCount = 0;
            int dockerCount = 0;
            for (Item item : itemListsFromBack) {
                backCount += item.findCount();
            }
            for (Item item : itemListsFromDocker) {
                dockerCount += item.findCount();
            }

            if (entry.getValue() < 0) {
                log.error("合成功能，材料消耗，玩家消耗材料数量小于0，itemId {} count {} ，compoundId {} compoundCount {}，玩家id {} name {}", entry.getKey(), entry.getValue(), config.getId(), compoundCount, role.getId(), role.getName());
                return;
            }

            if (backCount + dockerCount < entry.getValue()) {
                TipUtil.show(role, CommonTips.脚本_道具不足);
                return;
            }

            if (backCount >= entry.getValue()) {
                stash.decrease(entry.getKey(), entry.getValue());
            } else {
                // 道具足够先消耗背包，再消耗快捷栏
                stash.decrease(entry.getKey(), backCount);
                Item item = new Item();
                item.setCfgId(entry.getKey());
                item.setCount(entry.getValue() - backCount);
                item.setWhere(BackpackConst.Place.DOCKER.getWhere());
                stash.decrease(item, entry.getValue() - backCount);
            }
        }


        if(targetPos == null){
            targetPos = BackpackConst.Place.BACKPACK;
        }

        // 最终合成结果
        Item item = new Item();
        List<Item> result = new ArrayList<>(compoundCount);
        for (int i = 0; i < succeedCount; i++) {

            // 合成产出（包含box）
            List<int[]> product = config.getProduct();

            // 合成产出转换为实际道具(包含开box)
            List<Item> items = EmailConst.toMailAttach(product, LogAction.BAG_COMPOUND);

            if (items == null) {
                log.error("玩家：{},背包合成/分解失败，未配置产出物，合成表id：{}", role.getRoleId(), config.getId());
                return;
            }

            // 合成单个道具处理
            item.setCfgId(items.get(0).getCfgId());
            item.setCount(items.get(0).findCountLong());
            item.setWhere(targetPos.getWhere());
            if (config.getMenu_type() == 10 && reserveGemId > 0) {
                item.setEquipData(new EquipData());
                item.getEquipData().setGemId(reserveGemId);
            }
            if (config.getInherit() == 1 && longhunGem != null) {
                if (item.getEquipData() == null) {
                    item.setEquipData(new EquipData());
                }
                item.getEquipData().setLonghunGem(longhunGem);
                log.info("合成-龙魂宝石继承, roleId: {} , roleName: {} , itemConfigId: {} , gemIdList: {}", role.getRoleId(), role.getName(), item.getCfgId(), longhunGem.values());
            }

            if (config.getXiahunInherit() == 1 && xiahunGem != null) {
                if (item.getEquipData() == null) {
                    item.setEquipData(new EquipData());
                }
                item.getEquipData().setXiahunGem(xiahunGem);
                log.info("合成-侠魂宝石继承, roleId: {} , roleName: {} , itemConfigId: {} , gemIdList: {}", role.getRoleId(), role.getName(), item.getCfgId(), xiahunGem.values());
            }

            if (config.getTejieInherit() == 1 && refinementTuple != null) {
                if (item.getEquipData() == null) {
                    item.setEquipData(new EquipData());
                }
                item.getEquipData().setRefinementLevel(refinementTuple.getFirst());
                item.getEquipData().setRefinementRate(refinementTuple.getSecond());
                log.info("合成-特戒淬炼继承, roleId: {} , roleName: {} , itemConfigId: {} , level: {} , rate: {}", role.getRoleId(), role.getName(), item.getCfgId(), refinementTuple.getFirst(), refinementTuple.getSecond());
            }
            if (config.getMenu_type() == 10 && reserveChaoticGemId > 0) {
                if (item.getEquipData() == null) {
                    item.setEquipData(new EquipData());
                }
                item.getEquipData().setChaoticGemId(reserveChaoticGemId);
            }

            //混沌赋灵继承
            if (config.getFulingInherit() == 1) {
                if (item.getEquipData() == null) {
                    item.setEquipData(new EquipData());
                }
                item.getEquipData().setChaoticPowerLevel(chaoticPowerLevel);
                log.info("合成-混沌赋灵继承, role:{} {},itemConfigId:{},compoundConfigId:{},powerConfigLevel:{}", role.getRoleId(), role.getName(), item.getCfgId(), configId, chaoticPowerLevel);
            }

            result.add(item);
            stash.increase(item);

            // 分解获得多个道具
            for (int j = 1; j < items.size(); j++) {
                Item partItem = items.get(j);
                partItem.setWhere(targetPos.getWhere());

                result.add(partItem);
                stash.increase(partItem);
            }
            FunctionManager.getInstance().count(role, item.getCfgId());

        }

        if (config.getInherit() == 1 && result.size() != 1) {
            log.error("合成-龙魂宝石继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), configId, result.size());
            return;
        }

        // 返还武魂石
        for (int gemId : gemIds) {
            stash.increase(gemId, 1);
        }

        //返还泰坦神石
        for (Integer tanGemId : tiTanGemIds) {
            stash.increase(tanGemId, 1);
        }

        // 返还混沌宝石
        for (int chaoticGemId : chaoticGemIdList) {
            stash.increase(chaoticGemId, 1);
        }

        //返还龙魂宝石
        if (config.getInherit() != 1) {
            for (Integer longHunGemConfigId : longhunGemList) {
                stash.increase(longHunGemConfigId, 1);
            }
        }

        //返还侠魂宝石
        if (config.getXiahunInherit() != 1) {
            for (Integer xiaHunGemConfigId : xiahunGemList) {
                stash.increase(xiaHunGemConfigId, 1);
            }
        }

        //附灵分解返还
        stash.increase(fuLingFenJies);

        if (!stash.commit(role, LogAction.BAG_COMPOUND, true, null)) {
            log.error("合成道具消耗失败:name:{},id:{},configid:{},count:{}", role.getName(), role.getId(), configId, compoundCount);
            return;
        }

        for (Item toubao : touBaoItem) {
            // 邮件返还保金
            EquipTouBaoConfig touBaoConfig = ConfigDataManager.getInstance().getById(EquipTouBaoConfig.class, toubao.getCfgId());
            List<Item> backList = new ArrayList<>();
            for (int[] cost : touBaoConfig.getCost()) {
                Item costItem = ItemUtil.create(cost[0], cost[1], LogAction.QIBAO);
                backList.add(costItem);
            }
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.TOUBAO_RETURN, backList, toubao.findItemConfig().getName());
        }

        extraProbabilityMap.put(configId, currentExtraProbability);
        DataCenter.updateData(extraProbability);
        reqExtraProbabilityInfo(role);

        ScriptEngine.invoke1t1(IActivityTianXieScript.class, script -> script.onCompound(role, result));

        ResBagComPoundResultMessage msg = new ResBagComPoundResultMessage();
        msg.setCfgId(configId);
        msg.setSuccedCound(succeedCount);
        List<Integer> showList = new ArrayList<>();
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.COMPOUNT_SHOW);
        if (globalConfig != null) {
            showList = new JinhaoIntegerListConverter().convert(globalConfig.getValue());
        }
        if (showList.contains(config.getMenu_type())) {
            ItemBriefBean itemBriefBean = new ItemBriefBean();
            itemBriefBean.setItemId(item.getCfgId());
            itemBriefBean.setItemCount(item.findCount());
            msg.setItems(itemBriefBean);
        }
        MessageUtil.sendMsg(msg, role.getId());

        // 合成特戒，更新合成次数
        if (cache != null && cache.isCompoundRing(configId)) {
            if (role.getRingsMap().containsKey(config.getPage3())) {
                int less = role.getRingsMap().get(config.getPage3()) - succeedCount;
                role.getRingsMap().put(config.getPage3(), less);
                DataCenter.updateData(role);
                TreasureHuntManager.getInstance().reqThemeInfo(role);
            }
        }

        List<Item> generates = stash.findGenerates();
        if (config.getAnnounce() > 0 && !generates.isEmpty()) {
            generates = generates.stream().filter(value -> value.getCfgId() == item.getCfgId()).collect(Collectors.toList());
            if (!generates.isEmpty()) {
                AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role, generates.get(0));
            }
        }

        // 添加合成记录
        CompoundSpecialItemRecord record;

        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (config.getRecord() > 0 && !generates.isEmpty()) {
            record = new CompoundSpecialItemRecord();
            record.setCompoundTime(TimeUtil.getNowOfSeconds());
            record.setItemId(generates.get(0).getCfgId());
            record.setRid(role.getId());
            record.setRoleName(role.getName());
            record.setMapCfgId(player == null ? 0 : player.getMapCfgId());
            RecordManager.getInstance().addCompoundRecord(record);
        }


        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.EQUIP_SYNTHESIS_TIMES);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.COMPOUND_ITEM, configId);
        log.info("玩家【{}】->【{}】成功合成/分解道具itemId：【{}】->count:【{}】 {}:额外成功率{}->{}", role.getId(), role.getName(), generates != null && generates.size() > 0 ? generates.get(0).getCfgId() : 0, succeedCount, configId, testExtraProbability, currentExtraProbability);
    }

    /**
     * 混沌赋灵 继承
     *
     * @param role      角色
     * @param configId  合成配置id
     * @param list      功能list
     * @return int 混沌赋灵最高等级
     */
    private int chaoticPowerInherit(Role role, int configId, List<Item> list) {
        int chaoticPowerLevel = 0;
        for (Item item : list) {
            EquipData equipData = item.getEquipData();
            if (equipData == null) {
                continue;
            }
            if (equipData.getChaoticPowerLevel() > chaoticPowerLevel) {
                chaoticPowerLevel = equipData.getChaoticPowerLevel();
            }
        }
        return chaoticPowerLevel;
    }

    /**
     * 特戒淬炼继承
     *
     * @param role              角色
     * @param compoundConfigId  合成表id
     * @param equipList         消耗的装备道具列表
     * @return TwoTuple<Integer,Integer> 特戒淬炼 first: 淬炼等级   second: 淬炼额外成功率
     */
    private TwoTuple<Integer, Integer> refinementInherit(Role role, int compoundConfigId, List<Item> equipList) {
        if (equipList.size() != 1) {
            log.error("合成-特戒淬炼继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), compoundConfigId, equipList.size());
            return null;
        }
        Item item = equipList.get(0);
        EquipData equipData = item.getEquipData();
        if (equipData == null) {
            return null;
        }
        return Tuple.tuple(equipData.getRefinementLevel(), equipData.getRefinementRate());
    }

    /**
     * 龙魂道具继承
     *
     * @param equipList             消耗的装备道具列表
     * @param role                  角色
     * @param compoundConfigId      合成表id
     * @return Map<Integer, Integer> 龙魂融合 key: 龙魂类型   value: 龙魂宝石id
     */
    private Map<Integer, Integer> longHunInherit(List<Item> equipList, Role role, int compoundConfigId) {
        if (equipList.size() != 1) {
            log.error("合成-龙魂宝石继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), compoundConfigId, equipList.size());
            return null;
        }
        Item item = equipList.get(0);
        int itemCfgId = item.getCfgId();
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        if (itemConfig.getLonghunType() != 1) {
            log.error("合成-无法被龙魂融合的装备, roleId: {} , roleName: {} , configId: {} , itemConfigId: {} , longHunType: {}", role.getRoleId(), role.getName(), compoundConfigId, itemCfgId, itemConfig.getLonghunType());
            return null;
        }
        EquipData equipData = item.getEquipData();
        if (equipData == null) {
            return null;
        }
        return equipData.getLonghunGem();
    }

    /**
     * 侠魂道具继承
     *
     * @param equipList             消耗的装备道具列表
     * @param role                  角色
     * @param compoundConfigId      合成表id
     * @return Map<Integer, Integer> 侠魂融合 key: 侠魂类型   value: 侠魂宝石id
     */
    private Map<Integer, Integer> xiaHunInherit(List<Item> equipList, Role role, int compoundConfigId) {
        if (equipList.size() != 1) {
            log.error("合成-侠魂宝石继承装备必须要 1 to 1 的关系!!!, roleId: {} , roleName: {} , configId: {} , equipSize: {}", role.getRoleId(), role.getName(), compoundConfigId, equipList.size());
            return null;
        }
        Item item = equipList.get(0);
        int itemCfgId = item.getCfgId();
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        if (itemConfig.getXiahunType() != 1) {
            log.error("合成-无法被侠魂融合的装备, roleId: {} , roleName: {} , configId: {} , itemConfigId: {} , longHunType: {}", role.getRoleId(), role.getName(), compoundConfigId, itemCfgId, itemConfig.getLonghunType());
            return null;
        }
        EquipData equipData = item.getEquipData();
        if (equipData == null) {
            return null;
        }
        return equipData.getXiahunGem();
    }

    /**
     * 龙魂道具返还
     *
     * @param equipList         消耗的装备道具列表
     * @return List<Integer> 需要返还的龙魂道具列表
     */
    private List<Integer> longHunRefund(List<Item> equipList) {
        List<Integer> longHunGemList = new ArrayList<>();
        for (Item item : equipList) {
            EquipData equipData = item.getEquipData();
            if (equipData == null) {
                continue;
            }
            int itemCfgId = item.getCfgId();
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
            if (itemConfig.getLonghunType() != 1) {
                continue;
            }
            longHunGemList.addAll(equipData.getLonghunGem().values());
        }
        return longHunGemList;
    }

    /**
     * 侠魂道具返还
     *
     * @param equipList         消耗的装备道具列表
     * @return List<Integer> 需要返还的侠魂道具列表
     */
    private List<Integer> xiaHunRefund(List<Item> equipList) {
        List<Integer> xiaHunGemList = new ArrayList<>();
        for (Item item : equipList) {
            EquipData equipData = item.getEquipData();
            if (equipData == null) {
                continue;
            }
            int itemCfgId = item.getCfgId();
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
            if (itemConfig.getXiahunType() != 1) {
                continue;
            }
            xiaHunGemList.addAll(equipData.getXiahunGem().values());
        }
        return xiaHunGemList;
    }

    @Override
    public void reqGossipCompound(Role role, int configId, List<Long> itemIdList) {
        ComposeConfig config = ConfigDataManager.getInstance().getById(ComposeConfig.class, configId);
        if (config == null) {
            return;
        }

        Backpack backpack = role.getBackpack();
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getNeedItem());
        int[] extArray = config.getNeeditem2();
        int extCount = config.getNeednum2();
        while (extArray != null && extCount > 0 && itemIdList != null && !itemIdList.isEmpty()) {
            long uid = itemIdList.remove(0);
            Item item = backpack.findItemByUniqueId(uid);
            if (item == null) {
                continue;
            }
            if (item.findCountLong() <= 0) {
                return;
            }
            if (!ArrayUtils.contains(extArray, item.getCfgId())) {
                continue;
            }

            extCount--;
            stash.decrease(item);
        }
        stash.increase(BoxUtil.openBox(role, config.getTargetId()));
        if (stash.commit(role, LogAction.GOSSIP_COMPOUND)) {
            return;
        }

        ResGossipCompoundResultMessage msg = new ResGossipCompoundResultMessage();
        msg.setItemIdList(config.getTargetId());
        MessageUtil.sendMsg(msg, role.getId());
        log.info("玩家{} -> {} 合成了高级八卦装备:{}", role.getId(), role.getName(), config.getTargetId());
    }

    @Override
    public void sendCompoundSpecialItemInfos(Role role) {
        List<CompoundSpecialItemRecord> compoundSpecialItemRecordList = RecordManager.getInstance().findCompoundRecords();
        ResCompoundSpecialItemInfosMessage msg = new ResCompoundSpecialItemInfosMessage();
        List<SpecialItemCompoundInfoBean> infoBean = new ArrayList<>();

        for (int i = compoundSpecialItemRecordList.size() - 1; i >= 0; i--) {
            CompoundSpecialItemRecord itemRecord = compoundSpecialItemRecordList.get(i);
            SpecialItemCompoundInfoBean bean = new SpecialItemCompoundInfoBean();
            bean.setItemId(itemRecord.getItemId());
            bean.setRoleName(itemRecord.getRoleName());
            bean.setTime(itemRecord.getCompoundTime());
            bean.setMapCfgId(itemRecord.getMapCfgId());
            infoBean.add(bean);
        }
        msg.getInfoBean().addAll(infoBean);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 获取生肖装备合成成功率
     *
     * @param role    role
     * @param lidList 道具唯一id列表
     */
    @Override
    public void reqZodiacSuccessRate(Role role, List<Long> lidList) {
        ResZodiacSuccessRateMessage msg = new ResZodiacSuccessRateMessage();
        msg.setSuccessRate(getZodiacRate(role, lidList));
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求生肖装备合成
     *
     * @param role    role
     * @param lidList 道具唯一id列表
     */
    @Override
    public void reqCompoundZodiac(Role role, List<Long> lidList) {
        if (lidList.isEmpty()) {
            return;
        }
        int rate = getZodiacRate(role, lidList);
        // 合成消耗
        List<Item> cost = new ArrayList<>();
        for (long lid : lidList) {
            Item item = role.getBackpack().findItemByItemId(lid, BackpackConst.Browse.BACKPACK);
            if (item != null) {
                cost.add(item);
            }
        }
        ResCompoundZodiacMessage msg = new ResCompoundZodiacMessage();
        BackpackStash stash = new BackpackStash(role);
        // 合成失败
        if (RandomUtil.randomNumber(ZodiacConst.COMPOUND_PERCENTAGE) >= rate) {
            stash.decrease(cost);
            if (!stash.commit(role, LogAction.ZODIAC_COMPOUND_COST)) {
                return;
            }
            msg.setResult(ZodiacConst.COMPOUND_FAIL);
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }

        int minLevel = ZodiacConst.DEFAULT_MIN_LEVEL;
        int maxLevel = 0;
        int boxId;
        boolean flag = false;
        for (long lid : lidList) {
            // 套装级别
            int level = getEquipLevelByLid(role, lid);
            if (level == 0) {
                continue;
            }
            if (level > maxLevel) {
                maxLevel = level;
            }
            if (minLevel > level) {
                minLevel = level;
            }
            if (level == ZodiacConst.LEVEL_FIVE) {
                flag = true;
            }
        }
        ZodiacCache cache = ConfigCacheManager.getInstance().getCache(ZodiacCache.class);
        if (cache == null) {
            return;
        }
        if (flag) {
            boxId = cache.getBoxId(ZodiacConst.LEVEL_FIVE);
        } else {
            ZodiacCompoundConfig currentMax = ConfigDataManager.getInstance().getById(ZodiacCompoundConfig.class, maxLevel);
            ZodiacCompoundConfig newMax = ConfigDataManager.getInstance().getById(ZodiacCompoundConfig.class, maxLevel + 1);
            if (currentMax == null || newMax == null) {
                return;
            }
            // 合成新等级装备
            if (RandomUtil.randomNumber(newMax.getPoint()) < currentMax.getSuccessPoint()) {
                boxId = cache.getBoxId(maxLevel + 1);
            } else {
                List<CompoundZodiacWeight> list = new ArrayList<>();
                for (int i = minLevel; i <= maxLevel; i++) {
                    ZodiacCompoundConfig config = ConfigDataManager.getInstance().getById(ZodiacCompoundConfig.class, i);
                    if (config == null) {
                        continue;
                    }
                    CompoundZodiacWeight weight = new CompoundZodiacWeight();
                    weight.setLevel(i);
                    weight.setWeight(config.getPoint());
                    list.add(weight);
                }
                if (list.isEmpty()) {
                    return;
                }
                boxId = cache.getBoxId(getCompound(list).getLevel());
            }
        }
        List<Item> items = BoxUtil.openBox(boxId);
        stash.decrease(cost);
        stash.increase(items.get(0).getCfgId());
        if (!stash.commit(role, LogAction.ZODIAC_COMPOUND)) {
            return;
        }
        AnnounceManager.getInstance().post(ZodiacConst.COMPOUND_ANNO, 0L, role, items.get(0));
        msg.setResult(ZodiacConst.COMPOUND_SUCCESS);
        msg.setItemId(items.get(0).getCfgId());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求合成额外成功率信息
     *
     * @param role 角色
     */
    @Override
    public void reqExtraProbabilityInfo(Role role) {
        RoleCompoundExtraProbability roleCompoundExtraProbability = findExtraProbability(role.getRoleId());
        Map<Integer, Integer> extraProbabilityMap = roleCompoundExtraProbability.getExtraProbabilityMap();
        ResCompoundExtraProbabilityMessage msg = new ResCompoundExtraProbabilityMessage();
        for (Map.Entry<Integer, Integer> entry : extraProbabilityMap.entrySet()) {
            CommonKeyValueBean bean = new CommonKeyValueBean();
            bean.setKey(entry.getKey());
            bean.setValue(entry.getValue());
            msg.getExtraProbability().add(bean);
        }
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 请求合成
     *
     * @param role     角色
     * @param configId 合成配置id
     * @param aCostId  消耗的异火配置id 1
     * @param bCostId  消耗的异火配置id 2
     */
    @Override
    public void reqStrangeFireCompound(Role role, int configId, int aCostId, int bCostId) {
        StrangeFireCompoundConfig config = ConfigDataManager.getInstance().getById(StrangeFireCompoundConfig.class, configId);
        if (config == null) {
            log.error("异火-合成-配置不存在,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
            return;
        }

        if (!ConditionUtil.validate(role, config.getCondition())) {
            log.error("异火-合成-条件不满足,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
            return;
        }

        CompoundCache cache = ConfigCacheManager.getInstance().getCache(CompoundCache.class);
        Integer aCostCount = cache.getCompoundACostCount(configId, aCostId);
        if (aCostCount == null) {
            log.error("异火-合成-选择原料1不存在,role:{} {},configId:{},costId:{}", role.getRoleId(), role.getName(), configId, aCostId);
            return;
        }

        Integer bCostCount = cache.getCompoundBCostCount(configId, bCostId);
        if (bCostCount == null) {
            log.error("异火-合成-选择原料2不存在,role:{} {},configId:{},costId:{}", role.getRoleId(), role.getName(), configId, bCostId);
            return;
        }

        int[] reward = cache.getCompoundReward(configId);
        if (reward == null) {
            log.error("异火-合成-无产出,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
            return;
        }

        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(config.getItemid());
        costStash.decrease(aCostId, aCostCount);
        costStash.decrease(bCostId, bCostCount);
        if (!costStash.commit(role, LogAction.STRANGE_FIRE_COMPOUND_COST)) {
            log.error("异火-合成-消耗道具失败,role:{} {},configId:{},a:{} {},b:{} {}", role.getRoleId(), role.getName(), configId, aCostId, aCostCount, bCostId, bCostCount);
            return;
        }

        BackpackStash rewardStash = new BackpackStash(role);
        rewardStash.increase(reward[0], reward[1]);
        if (!rewardStash.commit(role, LogAction.STRANGE_FIRE_COMPOUND_REWARD)) {
            rewardStash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("异火-合成-成功,role:{} {},configId:{},a:{} {},b:{} {},reward:{}", role.getRoleId(), role.getName(), configId, aCostId, aCostCount, bCostId, bCostCount, reward);

        ResStrangeFireCompoundMessage msg = new ResStrangeFireCompoundMessage();
        msg.setConfigId(configId);
        msg.setRewardId(reward[0]);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 根据角色id获取合成额外成功率信息
     *
     * @param roleId 角色id
     * @return RoleCompoundExtraProbability 合成额外成功率信息
     */
    private RoleCompoundExtraProbability findExtraProbability(long roleId) {
        RoleCompoundExtraProbability roleCompoundExtraProbability = DataCenter.get(RoleCompoundExtraProbability.class, roleId);
        if (roleCompoundExtraProbability == null) {
            roleCompoundExtraProbability = new RoleCompoundExtraProbability();
            roleCompoundExtraProbability.setId(roleId);
            DataCenter.insertData(roleCompoundExtraProbability, true);
        }
        return roleCompoundExtraProbability;
    }

    @Override
    public void onRoleBackpackUpdate(Role role, List<ItemChange> changes) {
        if (changes.isEmpty()) {
            return;
        }
        List<Item> costItem = new ArrayList<>();
        List<Item> addItem = new ArrayList<>();
        for (ItemChange change : changes) {
            if (change.getLogAction() != LogAction.BAG_COMPOUND.getCode()) {
                continue;
            }
            Item oItem = change.getOItem();
            Item nItem = change.getNItem();
            if (oItem != null && nItem != null) {
                continue;
            }
            if (oItem != null && oItem.findItemConfig().getItemtype() != BagConst.ItemType.EQUIP) {
                continue;
            }
            if (nItem != null && nItem.findItemConfig().getItemtype() != BagConst.ItemType.EQUIP) {
                continue;
            }
            if (oItem != null && oItem.getEquipData() == null) {
                continue;
            }
            if (oItem != null) {
                costItem.add(oItem);
            }
            if (nItem != null) {
                addItem.add(nItem);
            }
        }
        if (costItem.isEmpty() || addItem.isEmpty()) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        for (int i = 0; i < addItem.size(); i++) {
            Item oItem = costItem.get(i);
            Item item = addItem.get(i);
            if (oItem != null && item != null) {
                EquipData equipData = oItem.getEquipData();
                stash.update(item, newItem -> {
                    newItem.eData().setStarLevel(equipData.getStarLevel());
                    newItem.eData().setStarLocked(equipData.getStarLocked());
                    newItem.eData().setStarExp(equipData.getStarExp());
                    newItem.eData().setZhufu(equipData.getZhufu());
                    newItem.eData().setZhufuAttribute(equipData.getZhufuAttribute());
                    newItem.eData().setXilianAttr(equipData.getXilianAttr());
                });
            }
        }
        stash.commit(role, LogAction.BAG_COMPOUND_INHERIT);
    }

    /**
     * 获取生肖合成成功率
     *
     * @param role    role
     * @param lidList 道具唯一id列表
     * @return
     */
    private int getZodiacRate(Role role, List<Long> lidList) {
        if (lidList.isEmpty()) {
            return 0;
        }
        int totalPoint = 0;
        int successPoint = 0;
        int rate = 0;
        for (long lid : lidList) {
            Item item = role.getBackpack().findItemByItemId(lid, BackpackConst.Browse.BACKPACK);
            if (item == null) {
                continue;
            }
            ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
            // 判断道具是否为生肖装备
            if (config == null || config.getType() < ZodiacConst.ZODIAC_TYPE_MOUSE || config.getType() > ZodiacConst.ZODIAC_TYPE_PIG) {
                continue;
            }
            // 套装级别
            int level = getEquipLevel(config.getSuitID());
            if (level == 0) {
                continue;
            }
            ZodiacCompoundConfig zodiacConfig = ConfigDataManager.getInstance().getById(ZodiacCompoundConfig.class, level);
            if (zodiacConfig == null) {
                continue;
            }
            if (isSpecialEquip(config.getType())) {
                totalPoint += zodiacConfig.getSpecialPoint();
            } else {
                totalPoint += zodiacConfig.getPoint();
            }
            if (zodiacConfig.getSuccessPoint() > successPoint) {
                successPoint = zodiacConfig.getSuccessPoint();
            }
        }
        if (successPoint != 0) {
            rate = (int) Math.ceil((double) (totalPoint * ZodiacConst.COMPOUND_PERCENTAGE) / successPoint);
        }
        rate = rate >= ZodiacConst.COMPOUND_PERCENTAGE ? ZodiacConst.COMPOUND_PERCENTAGE : rate;
        return rate;
    }

    @Override
    public void reqDiBingDecompose(Role role, int configId, int compoundCount, int career, boolean hero, int materialCid, boolean isPerfect, List<Long> uids) {
        if (compoundCount <= 0) {
            log.error("背包合成消息错误！合成数量为0");
            return;
        }
        //此为装备唯一id，不能重复
        Set<Long> uidSet = new HashSet<>(uids);
        if (uidSet.size() != uids.size()) {
            return;
        }
        CompoundConfig config = ConfigDataManager.getInstance().getById(CompoundConfig.class, configId);
        if (config == null) {
            return;
        }
        //只有帝兵分解才能走
        if (config.getFulingfenjie() != 1) {
            return;
        }
        //如果当前合成需要消耗对应道具转为100%成功,但又没有配置的,那置此isPerfect为false
        if (isPerfect && (config.getBaifenbai() == null || config.getBaifenbai().size() <= 0)) {
            isPerfect = false;  //没有配置消耗,不能100%成功
        }

        //额外成功率    todo 暂时不支持快捷合成
        RoleCompoundExtraProbability extraProbability = findExtraProbability(role.getRoleId());
        Map<Integer, Integer> extraProbabilityMap = extraProbability.getExtraProbabilityMap();
        int testExtraProbability = extraProbabilityMap.getOrDefault(configId, 0);
        int currentExtraProbability = extraProbabilityMap.getOrDefault(configId, 0);

        int succeedCount = 0;
        //总消耗
        List<int[]> Allcosts = new ArrayList<>();
        List<int[]> verifyCosts = new ArrayList<>();
        for (int i = 0; i < compoundCount; i++) {
            Allcosts.addAll(config.getItemid1());
            List<int[]> item_career = config.getItem_career();

            // 必需要拥有但只有合成成功才参与消耗
            List<int[]> itemSave = config.getItemSave();
            if (RandomUtil.isGenerate(10000, isPerfect ? 10000 : config.getProbability() + currentExtraProbability)) {
                succeedCount++;
                Allcosts.addAll(itemSave);
                if (isPerfect) {
                    Allcosts.addAll(config.getBaifenbai());
                }
                currentExtraProbability = 0;
            } else {
                currentExtraProbability += config.getProbabilityUp();
            }

            verifyCosts.addAll(itemSave);
            if (!role.getBackpack().verifyItemCount(verifyCosts, BackpackConst.Browse.BACKPACK_AND_EQUIP)) {
                TipUtil.show(role, CommonTips.脚本_道具不足);
                return;
            }

            // 是否消耗可选材料
            boolean isSelectCost = false;
            for (int[] ints : item_career) {
                if (ints.length < 1) {
                    continue;
                }
                if (ints[0] == materialCid) {
                    Allcosts.add(ints);
                    isSelectCost = true;
                }
            }

            // 有配置需要消耗可选材料但未传可选材料cid
            if (!item_career.isEmpty() && !isSelectCost) {
                return;
            }
        }
        /**
         * 装备和item消耗独立分开
         */
        Map<Integer, Integer> equitCostsMap = new HashMap<>();
        Map<Integer, Integer> itemCostsMap = new HashMap<>();
        Map<Integer, Long> moneyCostsMap = new HashMap<>();
        for (int i = 0; i < Allcosts.size(); i++) {
            int[] ints = Allcosts.get(i);

            int itemId = ints[0];
            //检验道具是否为配置表
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            if (itemConfig == null) {
                continue;
            }

            if (itemConfig.getItemtype() == BagConst.ItemType.EQUIP) {
                Integer count = equitCostsMap.get(itemId);
                if (count == null) {
                    count = 0;
                }
                equitCostsMap.put(itemId, count + ints[1]);
            } else if (itemConfig.getItemtype() == BagConst.ItemType.COIN) {
                Long count = moneyCostsMap.get(itemId);
                if (count == null) {
                    count = 0L;
                }
                moneyCostsMap.put(itemId, count + ints[1]);
            } else {
                Integer count = itemCostsMap.get(itemId);
                if (count == null) {
                    count = 0;
                }
                itemCostsMap.put(itemId, count + ints[1]);
            }
        }

        /**
         * 货币有专属属性，降低消耗
         */
        for (Map.Entry<Integer, Long> entry : moneyCostsMap.entrySet()) {
            if (entry.getKey() == BagConst.ItemId.GOLD || entry.getKey() == BagConst.ItemId.GOLD_BIND) {
                long goldRate = 10000 - role.getFinalAttribute().get(AttributeEnum.COMPOUND_GOLD_DECLINE);

                int[] faqijianmian = config.getFaqijianmian();
                if (faqijianmian != null && faqijianmian.length >= 2) {
                    if (role.findFaQiHuiYuanConfig().getVipLevel() >= faqijianmian[0]) {
                        goldRate *= (10000D - faqijianmian[1]) / 10000;
                    }
                }
                // 向上取整
                long gold = (goldRate * entry.getValue() + 9999) / 10000;
                moneyCostsMap.put(entry.getKey(), gold);
            }
        }


        int dressing = config.getDressing();
        BackpackConst.Place[] costBack;
        //获得道具到哪个背包
        BackpackConst.Place targetPos = null;
        //根据不同配置扣道具的背包范围不同
        if (dressing == 3) {
            if (hero) {
                costBack = BackpackConst.Browse.BACKPACK_HERO_EQUIP;
                targetPos = BackpackConst.Place.BACKPACK_HERO;
            } else {
                costBack = BackpackConst.Browse.BACKPACK_AND_EQUIP;
            }
        } else {
            costBack = dressing <= 0 ? BackpackConst.Browse.BACKPACK :
                    (dressing == 1 ? BackpackConst.Browse.BACKPACK_AND_EQUIP : BackpackConst.Browse.BACKPACK_HERO_EQUIP);
        }

        /**
         * 计算消耗 begin
         */
        BackpackStash stash = new BackpackStash(role);

        List<Item> touBaoItem = new ArrayList<>();
        List<Integer> gemIds = new ArrayList<>();
        List<Integer> tiTanGemIds = new ArrayList<>();
        //附灵分解返还
        List<int[]> fuLingFenJies = new ArrayList<>();

        //装备类消耗
        for (Map.Entry<Integer, Integer> entry : equitCostsMap.entrySet()) {

            int itemId = entry.getKey();
            int count = entry.getValue();
            //要扣的装备
            List<Item> list = new ArrayList<>();

            //此处新逻辑为同cfgId装备需要扣除的数量

            //预扣除的装备
            List<Item> uidList = new ArrayList<>();
            for (Long uid : uids) {
                if (uid == null) {
                    continue;
                }
                //从指定背包查询指定唯一id的装备
                Item equip = role.getBackpack().findItemByItemId(uid, costBack);
                if (equip == null) {
                    continue;
                }

                if (equip.getCfgId() != entry.getKey()) {
                    continue;
                }
                uidList.add(equip);
            }
            //预扣除的装备列表校验等符合数量，使用唯一id的装备扣除
            if (uidList.size() == count) {
                list.addAll(uidList);
            } else {
                //预扣除参数出问题,使用原合成逻辑
                List<Item> myItems = role.getBackpack().findItemByCfgId(itemId, costBack);//获取拥有的道具
                if (myItems.size() < count) {
                    TipUtil.show(role, CommonTips.脚本_道具不足);
                    return;
                }
                list = myItems.subList(0, count);
            }

            for (Item myItem : list) {
                EquipData equipData = myItem.getEquipData();
                if (equipData == null) {
                    continue;
                }
                int gemId = equipData.getGemId();
                if (gemId > 0) {
                    gemIds.add(gemId);
                }
            }

            //泰坦神石
            for (Item myItem : list) {
                EquipData equipData = myItem.getEquipData();
                if (equipData == null) {
                    continue;
                }
                int gemId = equipData.getTiTanGemId();
                if (gemId > 0) {
                    tiTanGemIds.add(gemId);
                }
            }

            for (Item item : list) {
                if (item.getEquipData() != null && item.getEquipData().getToubao() == 1) {
                    touBaoItem.add(item);
                }
            }

            //混沌附灵分解
            if (config.getFulingfenjie() == 1) {
                ChaoticPowerCache chaoticPowerCache = ConfigCacheManager.getInstance().getCache(ChaoticPowerCache.class);
                for (Item item : list) {
                    ItemConfig itemConfig = item.findItemConfig();
                    if (itemConfig == null) {
                        continue;
                    }
                    ChaoticPowerConfig powerConfig = chaoticPowerCache.getPowerConfig(itemConfig.getType(), item.getEquipData().getChaoticPowerLevel());
                    if (powerConfig == null) {
                        continue;
                    }
                    fuLingFenJies.addAll(powerConfig.getRecost());
                }
            }

            stash.decrease(list);
        }

        // 货币类消耗
        for (Map.Entry<Integer, Long> entry : moneyCostsMap.entrySet()) {
            Long count = entry.getValue();
            if (count < 0) {
                log.error("合成功能，货币消耗，玩家消耗货币数量小于0，itemId {} count {} ，compoundId {} compoundCount {}，玩家id {} name {}", entry.getKey(), count, config.getId(), compoundCount, role.getId(), role.getName());
                return;
            }
            stash.decrease(entry.getKey(), entry.getValue());
        }

        //物品类消耗
        for (Map.Entry<Integer, Integer> entry : itemCostsMap.entrySet()) {
            List<Item> itemListsFromBack = role.getBackpack().findItemByCfgId(entry.getKey(), BackpackConst.Place.BACKPACK, BackpackConst.Place.MIJI_GEM);
            List<Item> itemListsFromDocker = role.getBackpack().findItemByCfgId(entry.getKey(), BackpackConst.Place.DOCKER);

            int backCount = 0;
            int dockerCount = 0;
            for (Item item : itemListsFromBack) {
                backCount += item.findCount();
            }
            for (Item item : itemListsFromDocker) {
                dockerCount += item.findCount();
            }

            if (entry.getValue() < 0) {
                log.error("合成功能，材料消耗，玩家消耗材料数量小于0，itemId {} count {} ，compoundId {} compoundCount {}，玩家id {} name {}", entry.getKey(), entry.getValue(), config.getId(), compoundCount, role.getId(), role.getName());
                return;
            }

            if (backCount + dockerCount < entry.getValue()) {
                TipUtil.show(role, CommonTips.脚本_道具不足);
                return;
            }

            if (backCount >= entry.getValue()) {
                stash.decrease(entry.getKey(), entry.getValue());
            } else {
                // 道具足够先消耗背包，再消耗快捷栏
                stash.decrease(entry.getKey(), backCount);
                Item item = new Item();
                item.setCfgId(entry.getKey());
                item.setCount(entry.getValue() - backCount);
                item.setWhere(BackpackConst.Place.DOCKER.getWhere());
                stash.decrease(item, entry.getValue() - backCount);
            }
        }


        if (targetPos == null) {
            targetPos = BackpackConst.Place.BACKPACK;
        }

        // 最终合成结果
        Item item = new Item();
        List<Item> result = new ArrayList<>(compoundCount);
        for (int i = 0; i < succeedCount; i++) {

            // 合成产出（包含box）
            List<int[]> product = config.getProduct();

            // 合成产出转换为实际道具(包含开box)
            List<Item> items = EmailConst.toMailAttach(product, LogAction.BAG_COMPOUND);

            if (items == null) {
                log.error("玩家：{},背包合成/分解失败，未配置产出物，合成表id：{}", role.getRoleId(), config.getId());
                return;
            }

            // 合成单个道具处理
            item.setCfgId(items.get(0).getCfgId());
            item.setCount(items.get(0).findCountLong());
            item.setWhere(targetPos.getWhere());

            result.add(item);
            stash.increase(item);

            // 分解获得多个道具
            for (int j = 1; j < items.size(); j++) {
                Item partItem = items.get(j);
                partItem.setWhere(targetPos.getWhere());

                result.add(partItem);
                stash.increase(partItem);
            }
            FunctionManager.getInstance().count(role, item.getCfgId());

        }

        // 返还武魂石
        for (int gemId : gemIds) {
            stash.increase(gemId, 1);
        }

        //返还泰坦神石
        for (Integer tanGemId : tiTanGemIds) {
            stash.increase(tanGemId, 1);
        }

        //附灵分解返还
        stash.increase(fuLingFenJies);

        if (!stash.commit(role, LogAction.BAG_COMPOUND, true, null)) {
            log.error("合成道具消耗失败:name:{},id:{},configid:{},count:{}", role.getName(), role.getId(), configId, compoundCount);
            return;
        }

        for (Item toubao : touBaoItem) {
            // 邮件返还保金
            EquipTouBaoConfig touBaoConfig = ConfigDataManager.getInstance().getById(EquipTouBaoConfig.class, toubao.getCfgId());
            List<Item> backList = new ArrayList<>();
            for (int[] cost : touBaoConfig.getCost()) {
                Item costItem = ItemUtil.create(cost[0], cost[1], LogAction.QIBAO);
                backList.add(costItem);
            }
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.TOUBAO_RETURN, backList, toubao.findItemConfig().getName());
        }

        extraProbabilityMap.put(configId, currentExtraProbability);
        DataCenter.updateData(extraProbability);
        reqExtraProbabilityInfo(role);

        ScriptEngine.invoke1t1(IActivityTianXieScript.class, script -> script.onCompound(role, result));

        ResBagComPoundResultMessage msg = new ResBagComPoundResultMessage();
        msg.setCfgId(configId);
        msg.setSuccedCound(succeedCount);
        List<Integer> showList = new ArrayList<>();
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.COMPOUNT_SHOW);
        if (globalConfig != null) {
            showList = new JinhaoIntegerListConverter().convert(globalConfig.getValue());
        }
        if (showList.contains(config.getMenu_type())) {
            ItemBriefBean itemBriefBean = new ItemBriefBean();
            itemBriefBean.setItemId(item.getCfgId());
            itemBriefBean.setItemCount(item.findCount());
            msg.setItems(itemBriefBean);
        }
        MessageUtil.sendMsg(msg, role.getId());

        List<Item> generates = stash.findGenerates();

        if (config.getAnnounce() > 0 && !generates.isEmpty()) {
            generates = generates.stream().filter(value -> value.getCfgId() == item.getCfgId()).collect(Collectors.toList());
            if (!generates.isEmpty()) {
                AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role, generates.get(0));
            }
        }

        // 添加合成记录
        CompoundSpecialItemRecord record;

        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (config.getRecord() > 0 && !generates.isEmpty()) {
            record = new CompoundSpecialItemRecord();
            record.setCompoundTime(TimeUtil.getNowOfSeconds());
            record.setItemId(generates.get(0).getCfgId());
            record.setRid(role.getId());
            record.setRoleName(role.getName());
            record.setMapCfgId(player == null ? 0 : player.getMapCfgId());
            RecordManager.getInstance().addCompoundRecord(record);
        }
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.EQUIP_SYNTHESIS_TIMES);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.COMPOUND_ITEM, configId);
        log.info("玩家【{}】->【{}】成功合成/分解道具itemId：【{}】->count:【{}】 {}:额外成功率{}->{}", role.getId(), role.getName(), generates != null && generates.size() > 0 ? generates.get(0).getCfgId() : 0, succeedCount, configId, testExtraProbability, currentExtraProbability);
    }

    @Override
    public void reqCompound(Role role, int configId, int mergeCount) {
        if (mergeCount <= 0) {
            log.error("道具合成消息错误！合成数量为0");
            return;
        }
        ItemMergeConfig itemMergeConfig = ConfigDataManager.getInstance().getById(ItemMergeConfig.class, configId);
        if (itemMergeConfig == null) {
            log.error("道具合成消息错误！找不到合成配置【{}】", configId);
            return;
        }
        List<Item> items = role.getBackpack().findItemByCfgId(itemMergeConfig.getNeedItem1()[0], BackpackConst.Browse.EQUIP_ALL);
        if (items.isEmpty()) {
            log.error("道具合成消息错误！没有找到源道具【{}】", itemMergeConfig.getNeedItem1()[0]);
            return;
        }
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemMergeConfig.getTargetId()[0]);
        if (itemConfig == null) {
            log.error("道具合成消息错误！找不到目标道具配置【{}】", itemMergeConfig.getTargetId()[0]);
            return;
        }
        List<int[]> needItem = new ArrayList<>();
        needItem.add(new int[]{itemMergeConfig.getNeedItem1()[0], itemMergeConfig.getNeedItem1()[1] * mergeCount});
        itemMergeConfig.getNeedItem2().forEach(value -> needItem.add(new int[]{value[0], value[1] * mergeCount}));
        itemMergeConfig.getNeedItem3().forEach(value -> needItem.add(new int[]{value[0], value[1] * mergeCount}));

        if (!BackPackStashUtil.decrease(role, needItem, LogAction.ITEM_COMPOUND)) {
            log.error("道具-合成-材料不足,roleId:{},roleName:{},configId:{},mergeCount:{}", role.getId(), role.getName(), configId, mergeCount);
            return;
        }
        int[] targetItem = {itemMergeConfig.getTargetId()[0], itemMergeConfig.getTargetId()[1]};
        List<int[]> targetList = Collections.singletonList(targetItem);
        if (!BackPackStashUtil.increase(role, targetList, mergeCount, LogAction.BAG_MERGE_ITEM, true)) {
            log.error("道具-合成-放入背包失败,roleId:{},roleName:{},targetItem:{},mergeCount:{}", role.getId(), role.getName(), itemMergeConfig.getTargetId(), mergeCount);
        }
        // 发送公告
        if (itemMergeConfig.getAnnounce() > 0) {
            AnnounceManager.getInstance().post(itemMergeConfig.getAnnounce(), 0L, role, itemMergeConfig.getTargetId());
        }
        // 发送前端奖励消息
        RewardInfoManager.getInstance().resRewardInfo(role, TurnBasedConst.BattleType.NORMAL, targetList);

        ResCompoundMessage msg = new ResCompoundMessage();
        msg.setConfigId(configId);
        msg.setItemIdList(Collections.singletonList(itemMergeConfig.getTargetId()[0]));
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 获取套装级别
     *
     * @param suitId 套装id
     * @return
     */
    private int getEquipLevel(int[] suitId) {
        if (suitId.length < 2) {
            return 0;
        }
        for (int suit : suitId) {
            switch (suit) {
                case ZodiacConst.ZODIAC_SUIT_ONE:
                    return ZodiacConst.LEVEL_ONE;
                case ZodiacConst.ZODIAC_SUIT_TWO:
                    return ZodiacConst.LEVEL_TWO;
                case ZodiacConst.ZODIAC_SUIT_THREE:
                    return ZodiacConst.LEVEL_THREE;
                case ZodiacConst.ZODIAC_SUIT_FOUR:
                    return ZodiacConst.LEVEL_FOUR;
                case ZodiacConst.ZODIAC_SUIT_FIVE:
                    return ZodiacConst.LEVEL_FIVE;
            }
        }
        return 0;
    }

    /**
     * 获取装备级别
     *
     * @param role role
     * @param lid  道具唯一id
     * @return
     */
    private int getEquipLevelByLid(Role role, long lid) {
        Item item = role.getBackpack().findItemByItemId(lid, BackpackConst.Browse.BACKPACK);
        if (item == null) {
            return 0;
        }
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        // 判断道具是否为生肖装备
        if (config == null || config.getType() < ZodiacConst.ZODIAC_TYPE_MOUSE || config.getType() > ZodiacConst.ZODIAC_TYPE_PIG) {
            return 0;
        }
        return getEquipLevel(config.getSuitID());
    }

    /**
     * 判断是否为特殊部位
     *
     * @param type 装备类型
     * @return
     */
    private boolean isSpecialEquip(int type) {
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.ZODIAC_SPECIAL_TYPE);
        if (StringUtils.isBlank(globalConfig.getValue())) {
            return false;
        }
        int[] ints = Cast.stringToInts(globalConfig.getValue(), Symbol.JINHAO);
        for (int val : ints) {
            if (type == val) {
                return true;
            }
        }
        return false;
    }

    private CompoundZodiacWeight getCompound(List<CompoundZodiacWeight> list) {
        // 总权重
        int total = 0;
        // 以权重区间段的后面的值作为key存当前信息
        TreeMap<Integer, CompoundZodiacWeight> map = new TreeMap<>();
        for (CompoundZodiacWeight cw : list) {
            total += cw.getWeight();
            map.put(total, cw);
        }
        int random = new Random().nextInt(total) + 1;
        Integer key = map.ceilingKey(random);
        return map.get(key);
    }
}