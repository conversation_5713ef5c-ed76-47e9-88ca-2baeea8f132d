package com.sh.game.script.equipCollect;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.equipCollect.ResEquipCollectInfoMessage;
import com.sh.game.common.communication.msg.system.equipCollect.bean.EquipCollectBean;
import com.sh.game.common.config.cache.EquipCollectConfigCache;
import com.sh.game.common.config.model.EquipCollectConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleDropMonsterScript;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.equipCollect.entity.RoleEquipCollect;
import com.sh.game.system.equipCollect.script.IActivityEquipCollectScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 装备收集
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-04-01
 **/
@Slf4j
@Script
public class AcitityActivityEquipCollectScript extends AbstractActivityScript implements IActivityEquipCollectScript, IEventOnRoleDropMonsterScript {

    @Override
    public int getType() {
        return ActivityConst.EQUIP_COLLECT;
    }

    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        RoleEquipCollect roleEquipCollect = find(role.getId());
        roleEquipCollect.getCollectedEquip().clear();
        roleEquipCollect.getRequiredCidList().clear();
        DataCenter.updateData(roleEquipCollect);
    }

    @Override
    public RoleEquipCollect find(long rid) {
        RoleEquipCollect roleEquipCollect = DataCenter.get(RoleEquipCollect.class, rid);
        if (roleEquipCollect == null) {
            roleEquipCollect = new RoleEquipCollect();
            roleEquipCollect.setId(rid);
            DataCenter.insertData(roleEquipCollect, true);
        }

        return roleEquipCollect;
    }

    @Override
    public void reqInfo(Role role) {
        RoleEquipCollect roleEquipCollect = find(role.getId());
        List<Integer> requiredCidList = roleEquipCollect.getRequiredCidList();
        List<EquipCollectBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Set<Integer>> entry : roleEquipCollect.getCollectedEquip().entrySet()) {
            int cid = entry.getKey();
            Set<Integer> equipCidList = entry.getValue();

            EquipCollectBean bean = new EquipCollectBean();
            bean.setCid(cid);
            bean.setRequiredReward(requiredCidList.contains(cid));
            bean.getEquipCid().addAll(new ArrayList<>(equipCidList));
        }

        ResEquipCollectInfoMessage msg = new ResEquipCollectInfoMessage();
        msg.getBean().addAll(beans);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void gainReward(Role role, int cid) {
        EquipCollectConfig config = ConfigDataManager.getInstance().getById(EquipCollectConfig.class, cid);
        if (config == null) {
            return;
        }
        RoleEquipCollect roleEquipCollect = find(role.getRoleId());
        Map<Integer, Set<Integer>> collectedEquip = roleEquipCollect.getCollectedEquip();
        List<Integer> requiredCidList = roleEquipCollect.getRequiredCidList();
        if (requiredCidList.contains(cid)) {
            return;
        }
        Set<Integer> requiredEquip = collectedEquip.getOrDefault(cid, new HashSet<>());
        if (!requiredEquip.containsAll(config.getItemsid())) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.EQUIP_COLLECT_REWARD, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足发邮件);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getReward(), LogAction.EQUIP_COLLECT_REWARD));
        }
        requiredCidList.add(config.getId());
        DataCenter.updateData(roleEquipCollect);

        reqInfo(role);
    }

    @Override
    public void onRoleDropMonster(Role role, List<Item> drops) {
        if (drops == null || drops.isEmpty()) {
            return;
        }
        EquipCollectConfigCache cache = ConfigCacheManager.getInstance().getCache(EquipCollectConfigCache.class);
        Set<Integer> allEquipCfgId = cache.findAllEquipCfgId();
        List<Integer> configList = drops.stream().map(Item::getCfgId)
                .filter(allEquipCfgId::contains).collect(Collectors.toList());
        if (configList.isEmpty()) {
            return;
        }
        RoleEquipCollect roleEquipCollect = find(role.getRoleId());
        Map<Integer, Set<Integer>> collectedEquip = roleEquipCollect.getCollectedEquip();
        for (Integer equipCid : configList) {
            for (Integer cid : cache.findCidByEquipCfgId(equipCid)) {
                collectedEquip.putIfAbsent(cid, new HashSet<>());
                collectedEquip.get(cid).add(equipCid);
            }
        }
        DataCenter.updateData(roleEquipCollect);

        reqInfo(role);
    }
}
