package com.sh.game.script.train;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.train.ResTrainListMessage;
import com.sh.game.common.communication.msg.system.train.bean.TrainBean;
import com.sh.game.common.config.model.TrainConfig;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.train.TrainManager;
import com.sh.game.system.train.script.ITrainScript;
import com.sh.script.annotation.Script;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/8 11:18<br>;
 * 版本：1.0<br>;
 * 描述：
 */
@Script
public class TrainScript implements ITrainScript, IEventOnRoleLoginScript {

    @Override
    public void onRoleLogin(Role role) {
        long rid = role.getId();
        sendTrainList(rid, TrainManager.TARGET_ROLE, role.getRoleAdvance().getTrainLevel());
        Hero hero = role.getHero();
        if (hero != null) {
            sendTrainList(rid, TrainManager.TARGET_HERO, hero.getRoleAdvance().getTrainLevel());
        }
    }

    @Override
    public void reqElevateTrain(Role role, int targetType, int trainType) {
        Map<Integer, Integer> trains;
        Hero hero = null;
        RoleAdvance advance;
        if (targetType == TrainManager.TARGET_HERO) {
            hero = role.getHero();
            if (hero == null) {
                return;
            }
            advance = hero.getRoleAdvance();
            trains = advance.getTrainLevel();
        } else {
            advance = role.getRoleAdvance();
            trains = advance.getTrainLevel();
        }
        int current = trains.getOrDefault(trainType, -1);
        int nextID = 0;
        TrainConfig config = ConfigDataManager.getInstance().getById(TrainConfig.class, current);
        if (config != null) {
            if (!BackpackManager.getInstance().costItem(role, config.getCost(), LogAction.TRADE,
                    null, BackpackConst.Browse.BACKPACK_AND_HERO)) {
                return;
            }
            nextID = config.getNextId();
        } else {
            List<TrainConfig> list = ConfigDataManager.getInstance().getList(TrainConfig.class);
            if (list == null || list.size() == 0) {
                return;
            }
            Optional<TrainConfig> first = list.stream().filter(c -> c.getType() == trainType).min(Comparator.comparingInt(TrainConfig::getId));
            if (first.isPresent()) {
                nextID = first.get().getId();
            }
        }
        if (nextID == 0) {
            return;
        }
        trains.put(trainType, nextID);
        DataCenter.updateData(advance);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.TRAIN_LEVEL);
        sendTrainList(role.getId(), targetType, trains);
    }

    /**
     * 发送培养信息
     *
     * @param rid
     * @param targetType 0：主角，1：英雄
     * @param trains
     */
    private void sendTrainList(long rid, int targetType, Map<Integer, Integer> trains) {
        ResTrainListMessage msg = new ResTrainListMessage();
        msg.setTargetType(targetType);
        for (Map.Entry<Integer, Integer> entry : trains.entrySet()) {
            TrainBean bean = new TrainBean();
            bean.setTrainType(entry.getKey());
            bean.setLevel(entry.getValue());
            msg.getTrainList().add(bean);
        }
        MessageUtil.sendMsg(msg, rid);
    }
}
