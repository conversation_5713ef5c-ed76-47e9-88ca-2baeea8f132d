package com.sh.game.script.fenghao;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.fenghao.ResFengHaoInfoMessage;
import com.sh.game.common.communication.msg.system.fenghao.ResFengHaoUpgradeMessage;
import com.sh.game.common.config.model.FengHaoConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnFengHaoLvUp;
import com.sh.game.log.entity.FengHaoLog;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.fenghao.script.IFengHaoScript;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.log4j.Log4j2;

import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2021/6/16.
 */
@Log4j2
@Script
public class FengHaoScript implements IFengHaoScript {

    @Override
    public void sendCurrentId(Role role) {

        int fenghaoId = role.getFengHaoId();
        //发送消息
        ResFengHaoInfoMessage infoMessage = new ResFengHaoInfoMessage();
        infoMessage.setFid(fenghaoId);
        MessageUtil.sendMsg(infoMessage, role.getId());

    }

    @Override
    public void upgrade(Role role) {
        FengHaoConfig fengHaoConfig = ConfigDataManager.getInstance().getById(FengHaoConfig.class, role.getFengHaoId());
        if (fengHaoConfig == null) {
            return;
        }


        FengHaoConfig nextFengHao = ConfigDataManager.getInstance().getById(FengHaoConfig.class, fengHaoConfig.getNextId());
        if (nextFengHao == null) {
            //下一级为null  不能升级
            log.info("玩家id:[{}]，name:[{}],当前封号：{}， 下一级为null,不能升级", role.getId(), role.getName(), fengHaoConfig.getId());
            return;
        }


        /**
         * 升级扣除和奖励（主用于封号）道具
         */
        if (!fengHaoConfig.getCostitem().isEmpty() || !fengHaoConfig.getRewardItem().isEmpty()) {
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(fengHaoConfig.getCostitem());
            stash.increase(fengHaoConfig.getRewardItem());
            boolean commit = stash.commit(role, LogAction.ROLE_FENGHAO_UP);
            if (!commit) {
                log.debug("玩家:[{}],id:[{},封号升级,道具使用失败cfgId:[{}]", role.getName(), role.getId(), fengHaoConfig.getId());
                return;
            }
        }

        int tupo=0;

        if (nextFengHao.getRemoveTitle() > 0) {
            //移除以前封号
            AppearanceManager.getInstance().modifyFashion(LogAction.FengHao_REMOVE_TITLE, role.getId(), 0, nextFengHao.getRemoveTitle());
            log.info("删除玩家封号称号name:{},roleId:{},外观id:{},封号id:{}", role.getName(), role.getId(), nextFengHao.getRemoveTitle(), nextFengHao.getId());

            tupo=1;

        }

        changeLevel(role, nextFengHao, tupo);

    }

    /**
     * 封号等级改变
     * @param role
     * @param nextFengHao
     * @param tupo
     */
    @Override
    public void changeLevel(Role role, FengHaoConfig nextFengHao, int tupo) {

        if (role.getFengHaoId() >= nextFengHao.getId()){
            log.error("封号升级不能低于当前封号等级,name:{},roleId:{},升级的id:{}", role.getName(), role.getId(), nextFengHao.getId());
            return;
        }

        //升级
        role.setFengHaoId(nextFengHao.getId());
        DataCenter.updateData(role);
        AnnounceManager.getInstance().post(nextFengHao.getAnnounce(), 0, role, nextFengHao.getName());

        //修改属性

        //修改属性
        ResFengHaoUpgradeMessage message = new ResFengHaoUpgradeMessage();
        message.setFid(nextFengHao.getId());
        MessageUtil.sendMsg(message, role.getId());

        pushFengHaoUpEvent(role, role.getFengHaoId());

        //封号升级日志添加
        FengHaoConfig config = ConfigDataManager.getInstance().getById(FengHaoConfig.class, role.getFengHaoId());
        FengHaoLog log = new FengHaoLog(role);
        log.setRoleName(role.getName());
        log.setUid(role.getUid());
        log.setFengHao(config.getName());
        log.setTupo(tupo);
        log.setAccount(role.getAccount());
        log.submit();
    }

    @Override
    public void deleteAllTitle(Role role) {
        List<FengHaoConfig> fengHaoConfigs = ConfigDataManager.getInstance().getList(FengHaoConfig.class);
        for (FengHaoConfig haoConfig : fengHaoConfigs) {
            if (haoConfig.getRemoveTitle() == 0) {
                continue;
            }

            AppearanceManager.getInstance().modifyFashion(LogAction.FengHao_REMOVE_TITLE, role.getId(), 0, haoConfig.getRemoveTitle());
            log.info("删除玩家封号称号name:{},roleId:{},外观id:{}", role.getName(), role.getId(), haoConfig.getRemoveTitle());
        }
     }

    /**
     * 发送封号升级
     */
    public void pushFengHaoUpEvent(Role role, int fenghaoId) {
        ScriptEngine.invoke1tn(IEventOnFengHaoLvUp.class, script -> script.fenghaoUp(role, fenghaoId));
    }
}
