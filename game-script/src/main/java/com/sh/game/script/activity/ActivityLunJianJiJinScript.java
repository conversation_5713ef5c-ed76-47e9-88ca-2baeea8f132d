package com.sh.game.script.activity;


import com.sh.common.config.ConfigCacheManager;
import com.sh.game.common.config.cache.ChengZhangJiJinConfigCache;
import com.sh.game.common.config.model.ChengZhangJiJinConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.script.activity.abc.AbstractJiJinActivityScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

import java.util.List;

@Script
public class ActivityLunJianJiJinScript extends AbstractJiJinActivityScript {
    @Override
    public int getType() {
        return ActivityConst.LUNJIAN_JIJIN;
    }

    @Override
    protected int getMaxLimitVal() {
        ChengZhangJiJinConfigCache cache = ConfigCacheManager.getInstance().getCache(ChengZhangJiJinConfigCache.class);
        return cache.findMaxLimit(getType());
    }

    @Override
    protected int getRewardCount() {
        ChengZhangJiJinConfigCache cache = ConfigCacheManager.getInstance().getCache(ChengZhangJiJinConfigCache.class);
        List<ChengZhangJiJinConfig> configs = cache.findConfigsByActivityId(getType());
        return configs.size();
    }

    @Override
    protected int findRechargeGoal() {
        return GlobalUtil.getGlobalInt(GameConst.GlobalId.LJJJ_RECHARGE_SET);
    }

    /**
     * 发送公告
     *
     * @param role   角色
     * @param config 成长基金配置
     */
    @Override
    protected void sendAnnounce(Role role, ChengZhangJiJinConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
