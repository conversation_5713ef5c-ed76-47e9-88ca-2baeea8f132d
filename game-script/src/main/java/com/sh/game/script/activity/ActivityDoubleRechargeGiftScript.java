package com.sh.game.script.activity;

import com.sh.game.common.config.model.GiftPackConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Script(order = 1)
@Slf4j
public class ActivityDoubleRechargeGiftScript extends AbstractGiftPackScript {
    @Override
    public int getType() {
        return ActivityConst.SHUANGBEI_GIFTPACK;
    }

    /**
     * 发送公告
     *
     * @param role      角色
     * @param config    礼包配置
     */
    @Override
    protected void sendAnnounce(Role role, GiftPackConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
