package com.sh.game.script.ztpet;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.ztPet.ResZTPetInfoMessage;
import com.sh.game.common.communication.msg.system.ztPet.bean.ZTPetBean;
import com.sh.game.common.config.model.ZTPetLevelConfig;
import com.sh.game.common.config.model.ZTPetRankConfig;
import com.sh.game.common.config.model.ZTPetTypeConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.BackPackStashUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.ztpet.entity.RoleZTPet;
import com.sh.game.system.ztpet.entity.ZTPetData;
import com.sh.game.system.ztpet.script.IZTPetScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Script
@Slf4j
public class ZTPetScript implements IZTPetScript, IEventOnRoleAttributeCountScript {
    /**
     * 宠物购买
     */
    @Override
    public void reqZTPetBuy(Role role, int cid) {
        ZTPetTypeConfig petConfig = ConfigDataManager.getInstance().getById(ZTPetTypeConfig.class, cid);
        if (petConfig == null) {
            log.error("购买宠物#玩家:{}-{},配置id:{},未找到对应宠物配置", role.getId(), role.getName(), cid);
            return;
        }
        RoleZTPet roleZTPet = role.findZTPet();
        //宠物已购买
        if (roleZTPet.getPetData().containsKey(cid)) {
            log.error("购买宠物#玩家:{}-{},配置id:{},已购买过该宠物", role.getId(), role.getName(), cid);
            return;
        }
        //消耗
        boolean result = BackPackStashUtil.decrease(role, petConfig.getCost(), 1, LogAction.ZT_PET_BUY_COST);
        if (!result) {
            log.error("购买宠物#玩家:{}-{},配置id:{},消耗不足", role.getId(), role.getName(), cid);
            return;
        }
        ZTPetData petData = new ZTPetData();
        petData.setId(cid);
        petData.setLevel(1);
        petData.setRank(0);
        roleZTPet.getPetData().put(cid, petData);
        DataCenter.updateData(roleZTPet);

        sendMsg(role);
        log.info("购买宠物#玩家:{}-{},配置id:{},购买成功", role.getId(), role.getName(), cid);
    }

    /**
     * 选择跟随宠物
     * 如果petId == 0 代表取消跟随
     */
    public void reqZTPetSelect(Role role, int petId) {
        RoleZTPet roleZTPet = role.findZTPet();
        if (petId > 0 && !roleZTPet.getPetData().containsKey(petId)) {
            log.error("选择跟随宠物#玩家:{}-{},宠物id:{},玩家还未购买该宠物", role.getId(), role.getName(), petId);
            return;
        }
        roleZTPet.setSelectPetId(petId);
        DataCenter.updateData(roleZTPet);

        onRoleAttributeChange(role);
        ZTPetData petData = roleZTPet.getPetData().getOrDefault(petId, null);
        int level = petData == null ? 0 : petData.getLevel();
        int rank = petData == null ? 0 : petData.getRank();
        role.proxyCall(proxyPlayer -> proxyPlayer.ztPetUpdateNotice(petId, level, rank));
        SkillManager.getInstance().updateRoleSkill(role);
        sendMsg(role);
    }

    /**
     * 宠物升级
     */
    @Override
    public void reqZTPetLevelUp(Role role, int petId) {
        RoleZTPet roleZTPet = role.findZTPet();
        if (!roleZTPet.getPetData().containsKey(petId)) {
            log.error("升级宠物#玩家:{}-{},宠物id:{},玩家还未购买宠物", role.getId(), role.getName(), petId);
            return;
        }
        ZTPetData petData = roleZTPet.getPetData().get(petId);
        ZTPetLevelConfig config = ConfigDataManager.getInstance().getById(ZTPetLevelConfig.class, petId + "#" + petData.getLevel());
        if (config == null) {
            log.error("升级宠物#玩家:{}-{},宠物id:{},等级:{},未找到当前等级对应的配置", role.getId(), role.getName(), petId, petData.getLevel());
            return;
        }
        int nextLevel = config.getNextLevel();
        if (nextLevel == 0) { //已到最大等级
            log.error("升级宠物#玩家:{}-{},宠物id:{},等级:{},已到最大等级", role.getId(), role.getName(), petId, petData.getLevel());
            return;
        }
        ZTPetLevelConfig nextConfig = ConfigDataManager.getInstance().getById(ZTPetLevelConfig.class, petId + "#" + nextLevel);
        if (nextConfig == null) {
            log.error("升级宠物#玩家:{}-{},宠物id:{},等级:{},未找到下一等级对应的配置", role.getId(), role.getName(), petId, petData.getLevel());
            return;
        }
        boolean result = BackPackStashUtil.decrease(role, config.getCost(), 1, LogAction.ZT_PET_LEVEL_UP_COST);
        if (!result) {
            log.error("升级宠物#玩家:{}-{},宠物id:{},等级:{},消耗不足", role.getId(), role.getName(), petId, petData.getLevel());
            return;
        }
        petData.setLevel(nextLevel);
        DataCenter.updateData(roleZTPet);

        if (roleZTPet.getSelectPetId() == petId) {
            onRoleAttributeChange(role);
            role.proxyCall(proxyPlayer -> proxyPlayer.ztPetUpdateNotice(petId, petData.getLevel(), petData.getRank()));
        }

        sendMsg(role);
        log.info("升级宠物#玩家:{}-{},宠物id:{},等级:{},升级成功", role.getId(), role.getName(), petId, petData.getLevel());
    }

    /**
     * 宠物升阶
     */
    @Override
    public void reqZTPetUpgrade(Role role, int petId) {
        RoleZTPet roleZTPet = role.findZTPet();
        if (!roleZTPet.getPetData().containsKey(petId)) {
            log.error("升阶宠物#玩家:{}-{},宠物id:{},玩家还未购买宠物", role.getId(), role.getName(), petId);
            return;
        }
        ZTPetData petData = roleZTPet.getPetData().get(petId);
        ZTPetRankConfig config = ConfigDataManager.getInstance().getById(ZTPetRankConfig.class, petId + "#" + petData.getRank());
        if (config == null) {
            log.error("升阶宠物#玩家:{}-{},宠物id:{},阶级:{},未找到当前阶级对应的配置", role.getId(), role.getName(), petId, petData.getRank());
            return;
        }
        int nextRank = config.getNextRank();
        if (nextRank == 0) { //已到最大阶级
            log.error("升阶宠物#玩家:{}-{},宠物id:{},阶级:{},已到最大阶级", role.getId(), role.getName(), petId, petData.getRank());
            return;
        }
        ZTPetRankConfig nextConfig = ConfigDataManager.getInstance().getById(ZTPetRankConfig.class, petId + "#" + nextRank);
        if (nextConfig == null) {
            log.error("升阶宠物#玩家:{}-{},宠物id:{},阶级:{},未找到下一阶级对应的配置", role.getId(), role.getName(), petId, petData.getRank());
            return;
        }
        boolean result = BackPackStashUtil.decrease(role, config.getCost(), 1, LogAction.ZT_PET_RANK_UP_COST);
        if (!result) {
            log.error("升阶宠物#玩家:{}-{},宠物id:{},阶级:{},消耗不足", role.getId(), role.getName(), petId, petData.getRank());
            return;
        }
        petData.setRank(nextRank);
        DataCenter.updateData(roleZTPet);

        if (roleZTPet.getSelectPetId() == petId) {
            onRoleAttributeChange(role);
            role.proxyCall(proxyPlayer -> proxyPlayer.ztPetUpdateNotice(petId, petData.getLevel(), petData.getRank()));
            SkillManager.getInstance().updateRoleSkill(role);
        }
        sendMsg(role);
        log.info("升阶宠物#玩家:{}-{},宠物id:{},阶级:{},升阶成功", role.getId(), role.getName(), petId, petData.getRank());
    }

    @Override
    public void sendMsg(Role role) {
        RoleZTPet roleZTPet = role.findZTPet();
        ResZTPetInfoMessage msg = new ResZTPetInfoMessage();
        msg.setSelectPet(roleZTPet.getSelectPetId());
        for (ZTPetData data : roleZTPet.getPetData().values()) {
            ZTPetBean bean = new ZTPetBean();
            bean.setId(data.getId());
            bean.setLevel(data.getLevel());
            bean.setRank(data.getRank());
            msg.getZTPetBean().add(bean);
        }
        MessageUtil.sendMsg(msg, role);
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.ZT_PET;
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        Attribute attribute = new Attribute();
        RoleZTPet roleZTPet = role.findZTPet();
        if (roleZTPet.getSelectPetId() == 0) {
            return;
        }
        ZTPetData petData = roleZTPet.getPetData().get(roleZTPet.getSelectPetId());
        //等级属性
        ZTPetLevelConfig levelConfig = ConfigDataManager.getInstance().getById(ZTPetLevelConfig.class, petData.getId() + "#" + petData.getLevel());
        if (levelConfig != null) {
            attribute.fixAdd(levelConfig.getAttribute(), role.getCareer());
        }

        //阶级属性
        ZTPetRankConfig rankConfig = ConfigDataManager.getInstance().getById(ZTPetRankConfig.class, petData.getId() + "#" + petData.getRank());
        if (rankConfig != null) {
            attribute.fixAdd(rankConfig.getAttribute(), role.getCareer());
        }

        role.getAttributes().put(getAttributeType(), attribute);
    }

}
