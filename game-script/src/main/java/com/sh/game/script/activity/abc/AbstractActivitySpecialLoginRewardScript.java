package com.sh.game.script.activity.abc;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ActivityScheduleConfig;
import com.sh.game.common.config.model.ActivitySpecialLoginConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActionConst;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.*;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityRechargeData;
import com.sh.game.system.activity.entity.role.ActivitySpecialLoginRewardData;
import com.sh.game.system.activity.script.IActivitySpecialLoginRewardScript;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sh.game.common.constant.ActivityConst.RuleType.*;

/**
 * 特殊登录礼包
 *
 * <AUTHOR> Chen
 * @date 2022/6/28/028 17:17
 */
@Slf4j
public abstract class AbstractActivitySpecialLoginRewardScript extends AbstractActivityScript implements
        IActivitySpecialLoginRewardScript,
        IEventOnActivityStatistic,
        IEventOnRoleMidnightScript,
        IEventOnRoleLoginScript,
        IEventOnRoleRechargedScript,
        IEventOnRoleCoinChangedScript,
        IEventOnRoleLogoutScript {

    /**
     * 领取奖励
     *
     * @param role     角色
     * @param configId cfg_special_login配置表id
     */
    @Override
    public void reqReward(Role role, int configId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int activityId = schedule.getActivityID();
        RoleActivity activity = role.getRoleActivity();
        ActivitySpecialLoginRewardData specialLoginRewardData = activity.getSpecialLoginRewardMap().getOrDefault(activityId, new ActivitySpecialLoginRewardData());
        List<Integer> rewardIdList = specialLoginRewardData.getRewardIdList();
        //已领取
        if (rewardIdList.contains(configId)) {
            log.error("特殊登录豪礼-领奖-重复领取, roleId: {} roleName: {} , configId: {}", role.getRoleId(), role.getName(), configId);
            return;
        }

        if (role.verifyMidnight()) {
            log.info("特殊登录豪礼-领奖-今日午夜事件未执行, roleId: {} roleName: {} , configId: {} , now: {}, 创角时间:{} , 上次午夜事件时间:{}", role.getRoleId(), role.getName(), configId, TimeUtil.getNowOfSeconds(), role.getRoleCreationTime(), role.getRoleLogin().getResetTime());
            return;
        }

        ActivitySpecialLoginConfig config = ConfigDataManager.getInstance().getById(ActivitySpecialLoginConfig.class, configId);
        if (config == null || config.getActivityId() != activityId) {
            return;
        }
        if (!ConditionUtil.validate(role, config.getCondition())) {
            return;
        }

        //天数条件不符合
        if (findProceedDays(role) != config.getDay()) {
            log.error("特殊登录豪礼-领奖-领取天数错误, roleId: {} roleName: {} , configId: {}, proceedDays(): {}, configDay: {}", role.getRoleId(), role.getName(), configId, findProceedDays(role), config.getDay());
            return;
        }

        if (config.getType() == RECHARGE && specialLoginRewardData.getRecharge() < config.getGolas()) {
            log.error("特殊登录豪礼-领奖-充值额度不足, roleId: {} roleName: {} , configId: {}, recharge: {}", role.getRoleId(), role.getName(), configId, specialLoginRewardData.getRecharge());
            return;
        }
        if (config.getType() == CONSUME && specialLoginRewardData.getConsume() < config.getGolas()) {
            log.error("特殊登录豪礼-领奖-消费额度不足, roleId: {} roleName: {} , configId: {}, consume: {}", role.getRoleId(), role.getName(), configId, specialLoginRewardData.getConsume());
            return;
        }

        //计算在线时长
        calculRoleOnlineTime(role);
        ActivitySpecialLoginRewardData loginRewardData = role.getRoleActivity().getSpecialLoginRewardMap().getOrDefault(activityId, new ActivitySpecialLoginRewardData());
        if (config.getType() == ONLINE_TIME && loginRewardData.getOnlineTime() < config.getGolas()) {
            log.error("特殊登录豪礼-领奖-在线时长不足, roleId: {} roleName: {} , configId: {}, 在线时长(分钟): {}", role.getRoleId(), role.getName(), configId, specialLoginRewardData.getOnlineTime());
            return;
        }

        if (findProceedDays(role) != schedule.findProceedDays()) {
            log.error("特殊登录豪礼-领奖-活动天数大于奖励配置天数, 默认领取第一天奖励, roleId: {} roleName: {} , configId: {}, proceedDays(): {} -> {} , configDay: {}", role.getRoleId(), role.getName(),  configId, schedule.findProceedDays(), findProceedDays(role), config.getDay());
        }

        specialLoginRewardData.getRewardIdList().add(configId);
        specialLoginRewardData.setActivityId(activityId);
        activity.getSpecialLoginRewardMap().put(activityId, specialLoginRewardData);
        DataCenter.updateData(activity);

        log.info("特殊登录豪礼-领奖, roleId: {} roleName: {} , configId: {}", role.getRoleId(), role.getName(), configId);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.SPECIAL_LOGIN_REWARD)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }
        sendAnnounce(role, config);
        reqInfo(role);
    }

    /**
     * 发送信息
     *
     * @param role 角色
     */
    @Override
    public void reqInfo(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        int proceedDays = findProceedDays(role);

        calculRoleOnlineTime(role);

        ActivitySpecialLoginRewardData specialLoginRewardData = role.getRoleActivity().getSpecialLoginRewardMap().getOrDefault(schedule.getActivityID(), new ActivitySpecialLoginRewardData());

    }

    /**
     * 计算玩家活动期间在线时长(分钟)
     * @param role
     */
    private void calculRoleOnlineTime(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity roleActivity = role.getRoleActivity();
        ActivitySpecialLoginRewardData loginRewardData = roleActivity.getSpecialLoginRewardMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivitySpecialLoginRewardData());
        int loginTime = loginRewardData.getCalculTime();
        loginTime = loginTime <= 0 ? TimeUtil.getNowOfSeconds() : loginTime;
        //累计计算开始时间
        int beginTime = loginTime > schedule.getBeginAt() ? loginTime : schedule.getBeginAt();
        long addMinutes = (TimeUtil.getNowOfSeconds() - beginTime) / TimeUtil.ONE_MINUTE_IN_SECONDS;

        loginRewardData.setOnlineTime(loginRewardData.getOnlineTime() + addMinutes);
        if (addMinutes != 0) {
            loginRewardData.setCalculTime(TimeUtil.getNowOfSeconds());
            log.info("周末大礼活动id:{},重新累计在线时长,角色:{},昵称:{},在线时长:{},本次累加:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), loginRewardData.getOnlineTime(), addMinutes);
        }
        DataCenter.updateData(roleActivity);
    }

    /**
     * 获取特殊登录豪礼信息
     *
     * @param role 角色
     * @return ActivitySpecialLoginRewardData
     */
    @Override
    public ActivitySpecialLoginRewardData find(Role role) {
        RoleActivity activity = role.getRoleActivity();
        int activityId = getCurrentActivityID(role);
        ActivitySpecialLoginRewardData specialLoginRewardData = activity.getSpecialLoginRewardMap().get(activityId);
        if (specialLoginRewardData == null) {
            specialLoginRewardData = new ActivitySpecialLoginRewardData();
            specialLoginRewardData.setActivityId(activityId);
        }
        DataCenter.updateData(activity);
        return specialLoginRewardData;
    }

    /**
     * 发送公告
     *
     * @param role      角色
     * @param config    节日登录配置
     */
    protected void sendAnnounce(Role role, ActivitySpecialLoginConfig config) {
    }

    @Override
    public void onRoleMidnight(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        //清空当日消费和充值数据
        RoleActivity activity = role.getRoleActivity();
        ActivitySpecialLoginRewardData specialLoginRewardData = activity.getSpecialLoginRewardMap().getOrDefault(schedule.getActivityID(), new ActivitySpecialLoginRewardData());
        specialLoginRewardData.setRecharge(0);
        specialLoginRewardData.setConsume(0);
        specialLoginRewardData.setActivityId(schedule.getActivityID());
        specialLoginRewardData.setMidnightTime(TimeUtil.getNowOfSeconds());
        activity.getSpecialLoginRewardMap().put(schedule.getActivityID(), specialLoginRewardData);
        reqInfo(role);
    }

    @Override
    public void onRoleLogin(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity roleActivity = role.getRoleActivity();
        ActivitySpecialLoginRewardData loginRewardData = roleActivity.getSpecialLoginRewardMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivitySpecialLoginRewardData());
        loginRewardData.setCalculTime(TimeUtil.getNowOfSeconds());
        DataCenter.updateData(roleActivity);
        reqInfo(role);
    }

    @Override
    public void onRoleLogout(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        //计算累计时间
        calculRoleOnlineTime(role);
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        //记录活动期间内充值
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity activity = role.getRoleActivity();
        ActivitySpecialLoginRewardData specialLoginRewardData = activity.getSpecialLoginRewardMap().get(schedule.getActivityID());
        if (specialLoginRewardData == null) {
            specialLoginRewardData = new ActivitySpecialLoginRewardData();
            specialLoginRewardData.setActivityId(schedule.getActivityID());
        }
        specialLoginRewardData.setRecharge(specialLoginRewardData.getRecharge() + rechargeConfig.getCount());
        activity.getSpecialLoginRewardMap().put(schedule.getActivityID(), specialLoginRewardData);
        DataCenter.updateData(activity);
        reqInfo(role);
    }

    @Override
    public void onRoleCoinChanged(Role role, Map<Integer, Long> changes, int action) {
        //活动期间内记录消耗灵符
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        //不算消费的行为
        if (ActionConst.CONSUME_IGNORE_ACTIONS.contains(action)) {
            return;
        }

        long lingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY, 0L);
        long bindLingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY_BIND, 0L);
        int xiaofeiNum = 0;
        int bindXiaofeiNum = 0;
        //lingfuCost<0为消耗灵符
        if (lingfuCost < 0) {
            xiaofeiNum = (int) -lingfuCost;
        }
        //bindLingfuCost<0为消耗绑定灵符
        if (bindLingfuCost < 0) {
            bindXiaofeiNum = (int) -bindLingfuCost;
        }

        //本次消费的灵符数量
        int lingfuNum = xiaofeiNum + bindXiaofeiNum;
        if (lingfuNum <= 0) {
            return;
        }

        RoleActivity activity = role.getRoleActivity();
        ActivitySpecialLoginRewardData specialLoginRewardData = activity.getSpecialLoginRewardMap().get(schedule.getActivityID());
        if (specialLoginRewardData == null) {
            specialLoginRewardData = new ActivitySpecialLoginRewardData();
            specialLoginRewardData.setActivityId(schedule.getActivityID());
        }
        specialLoginRewardData.setConsume(specialLoginRewardData.getConsume() + lingfuNum);
        activity.getSpecialLoginRewardMap().put(schedule.getActivityID(), specialLoginRewardData);
        DataCenter.updateData(activity);
        reqInfo(role);
    }

    /**
     * 活动开始清空数据
     *
     * @param schedule  活动
     * @param role      角色
     */
    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        RoleActivity activity = findRoleActitiy(role);
        //新活动开启时清空
        int activityId = getCurrentActivityID(role);

        ActivitySpecialLoginRewardData specialLoginRewardData = new ActivitySpecialLoginRewardData();
        specialLoginRewardData.setActivityId(activityId);
        specialLoginRewardData.setStartTime(schedule.getBeginAt());
        if (role.isOnline()) {
            specialLoginRewardData.setCalculTime(TimeUtil.getNowOfSeconds());
        }
        specialLoginRewardData.setMidnightTime(TimeUtil.getNowOfSeconds());
        Map<Integer, ActivitySpecialLoginRewardData> specialLoginRewardMap = activity.getSpecialLoginRewardMap();
        if (clearPersonalData()) {
            specialLoginRewardMap.put(activityId, specialLoginRewardData);
        } else {
            specialLoginRewardMap.putIfAbsent(activityId, specialLoginRewardData);
        }
        DataCenter.updateData(activity);
    }

    /**
     * 获取活动第几天(如果配置表里没配当天的奖励则默认为第一天)
     *
     * @return boolean
     */
    protected int findProceedDays(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return 0;
        }
        int proceedDays = schedule.findProceedDays();
        //当天配置
        List<ActivitySpecialLoginConfig> configList = ConfigDataManager.getInstance().getList(ActivitySpecialLoginConfig.class).stream()
                .filter(config -> config.getDay() == schedule.findProceedDays()
                        && schedule.getActivityID() == config.getActivityId())
                .collect(Collectors.toList());
        if (configList.isEmpty()) {
            proceedDays = 1;
        }
        return proceedDays;
    }
}
