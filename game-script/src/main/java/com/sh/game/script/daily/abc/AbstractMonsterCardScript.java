package com.sh.game.script.daily.abc;

import com.sh.game.common.config.converter.map.SemicolonAndJingHaoIntMapConverter;
import com.sh.game.common.constant.CountConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.event.IEventOnRemoteModuleState;
import com.sh.game.event.IEventOnRoleBeforeDropMonsterScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.game.system.daily.script.IDailyTimedScript;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/07/14 16:35
 */
public abstract class AbstractMonsterCardScript extends AbstractDailyTimedScript implements IDailyTimedScript, IEventOnRoleBeforeDropMonsterScript, IEventOnRemoteModuleState {

    @Override
    protected void onScheduleBegin(DailySchedule schedule) {
//        DailyScheduleConfig conf = schedule.getConf();
//        if (conf == null) {
//            return;
//        }
//        //重新开启地图
//        for (int mapConfigId : conf.getUseMap()) {
//            MapProxyManager.getInstance().alloc(mapConfigId);
//        }
    }

    @Override
    protected void onScheduleEnd(DailySchedule schedule) {
//        DailyScheduleConfig conf = schedule.getConf();
//        if (conf == null) {
//            return;
//        }
//        for (int mapConfigId : conf.getUseMap()) {
//            MapProxy mapProxy = MapProxyManager.getInstance().getMap(mapConfigId);
//            if (mapProxy != null) {
//                mapProxy.kickPlayer();
//                MapProxyManager.getInstance().destroy(mapConfigId);
//            }
//        }
    }

    /**
     * Boss掉落前处理,可以对数量等进行修改等操作
     *
     * @param role          角色
     * @param monsterCfgId  怪物配置id
     * @param dropType      掉落类型
     * @param magCfgID      地图配置id
     * @param x             x坐标
     * @param y             y坐标
     * @param dropIds       掉落物品列表
     * @param hung
     */
    @Override
    public void onRoleBeforeDropMonster(Role role, int monsterCfgId, int dropType, int magCfgID, int x, int y, List<Item> dropIds, int hung, int dailyType) {
        if (dailyType != getType()) {
            return;
        }

        //掉落次数限制,不配置则无限制
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.ROLE_MONSTER_CARD_DROP, getType());
        Map<Integer, Integer> limitMap = GlobalUtil.find(GameConst.GlobalId.MONSTER_CARD_DROP_LIMIT, SemicolonAndJingHaoIntMapConverter.class);
        if (limitMap == null) {
            return;
        }
        Integer limit = limitMap.get(getType());

        if (limit == null) {
            return;
        }

        if (count >= limit) {
            dropIds.clear();
            return;
        }

        CountManager.getInstance().count(role, CountConst.CountType.ROLE_MONSTER_CARD_DROP, getType());
    }
}
