package com.sh.game.script.skill;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.communication.msg.system.skill.ResSkillChangeMessage;
import com.sh.game.common.communication.msg.system.skill.ResSkillInfoMessage;
import com.sh.game.common.communication.msg.system.skill.bean.SkillBean;
import com.sh.game.common.communication.msg.system.skill.bean.SkillListBean;
import com.sh.game.common.config.cache.HuoBanCache;
import com.sh.game.common.config.cache.ItemSkillCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.map.SkillDTO;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.entity.usr.RoleHuoBan;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLevelUpScript;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.skill.entity.RoleSkill;
import com.sh.game.system.skill.script.ISkillScript;
import com.sh.game.system.ztpet.entity.RoleZTPet;
import com.sh.game.system.ztpet.entity.ZTPetData;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Script
public class SkillScript implements ISkillScript, IEventOnRoleLevelUpScript {

    @Override
    public void createRoleSkill(Role role, long actorId) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = roleAdvance.getRoleSkill();
        CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class,
                role.getCareer() + Symbol.JINHAO + role.getSex());

        for (Integer skillCfgId : characterConfig.getInitSkill()) {
            SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillCfgId);
            Skill skill = new Skill();
            skill.setSkillId(skillBattleConfig.getId());
            skill.setLevel(skillBattleConfig.getLevel());
            roleSkill.getSkillMap().put(skill.getSkillId(), skill);
        }
        roleAdvance.setRoleSkill(roleSkill);
        DataCenter.updateData(roleAdvance);
    }

    @Override
    public void sendSkillInfo(Role role) {
        ResSkillInfoMessage msg = new ResSkillInfoMessage();
        buildResSkillInfoMessage(msg, role.getRoleAdvance(), role.getAttribute(), role);
        Hero hero = role.getHero();
        if (hero != null) {
            buildResSkillInfoMessage(msg, hero.getRoleAdvance(), hero.getAttribute(), null);
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    private void buildResSkillInfoMessage(ResSkillInfoMessage msg, RoleAdvance advance, Attribute attribute, Role role) {
        Map<Integer, Skill> roleSkillMap = advance.getRoleSkill().getSkillMap();
        SkillListBean listBean = new SkillListBean();
        listBean.setActorId(advance.getId());
        roleSkillMap.values().forEach(skill -> {
            SkillBean bean = new SkillBean();
            bean.setLevel(skill.getLevel());
            bean.setSkillId(skill.getSkillId());
            bean.setPractice(skill.getPractice());
            bean.setAutoUse(skill.isAutoUse());
            if (role != null) {
                bean.setSkillCd(getSkillCd(role, skill.getSkillId()));
            }
            listBean.getSkillInfo().add(bean);
        });


        Map<Integer, Integer> sysSkillMap = attribute.getSkillMap();
        sysSkillMap.forEach((k, v) -> {
            SkillBean bean = new SkillBean();
            bean.setSkillId(k);
            bean.setLevel(v);
            bean.setPractice(100);
            listBean.getSkillInfo().add(bean);
        });
        msg.getListInfo().add(listBean);
    }

    private void sendSkillChangeInfo(Role role, long actorId, int changeSkill) {
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = roleAdvance.getRoleSkill().getSkillMap();
        Skill skill = skillMap.get(changeSkill);

        ResSkillChangeMessage msg = new ResSkillChangeMessage();
        msg.setActorId(actorId);
        SkillBean bean = new SkillBean();
        bean.setSkillId(skill.getSkillId());
        bean.setLevel(skill.getLevel());
        bean.setPractice(skill.getPractice());
        bean.setAutoUse(skill.isAutoUse());

        long cd = getSkillCd(role, skill.getSkillId());
        bean.setSkillCd(cd);
        msg.setSkillBean(bean);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void levelUpSkill(Role role, long actorId, int skillId) {
        if (!role.isOwner(actorId)) {
            return;
        }
        int level = 0;
        IAvatar avatar = role.getId() == actorId ? role : role.getHero();
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Skill skill = advance.getRoleSkill().getSkillMap().get(skillId);
        if (skill != null) {
            level = skill.getLevel();
        }
        level++;

        if (!canLevelUpSkill(avatar, advance, skillId, level)) {
            return;
        }

        levelUpSkill(role, actorId, skillId, level);
    }

    @Override
    public void levelUpSkill(Role role, long actorId, int skillId, int level) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = roleAdvance.getRoleSkill();

        Map<Integer, Skill> roleSkillMap = roleSkill.getSkillMap();
        Skill skill = roleSkillMap.computeIfAbsent(skillId, k -> {
            Skill newSkill = new Skill();
            newSkill.setSkillId(skillId);
            return newSkill;
        });

        skill.setLevel(level);
        skill.setPractice(0);
        //超过遗忘技能里的等级
        Skill forgetSkill = roleSkill.getForgetSkillMap().get(skillId);
        if (forgetSkill != null && forgetSkill.getLevel() >= skill.getLevel()) {
            if (forgetSkill.getLevel() == skill.getLevel()) {
                skill.setPractice(forgetSkill.getPractice());
            }
            roleSkill.getForgetSkillMap().remove(skillId);
        }

        DataCenter.updateData(roleAdvance);
        SkillManager.getInstance().onSkillLevelUp(role, actorId, skillId, level);

        sendSkillChangeInfo(role, actorId, skillId);

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + level);
        // TODO 无配置
        if (skillConditionConfig == null) {
            return;
        }
        if (skillConditionConfig.getAnnounce() > 0) {
//            AnnounceManager.getInstance().announce(skillConditionConfig.getAnnounce(), role.getId(), role.getName(), skillConditionConfig.getName(), skillConditionConfig.getAnparam());
        }
        SkillConditionConfig prevConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() - 1));
        if (prevConditionConfig != null && skillConditionConfig.getSpPiaozi() != 1) {
            TipUtil.show(role.getId(), CommonTips.服务_技能已经提升, prevConditionConfig.getName(), skillConditionConfig.getName());
        }
        if (skillConditionConfig.getSpPiaozi() == 1) {
            TipUtil.show(role.getId(), CommonTips.学习特殊技能飘字, skillConditionConfig.getName());
        }

        if (!skill.isAutoUse()) {
            return;
        }
        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skill.getSkillId());
        skillDTO.setLevel(skill.getLevel());
        skillDTO.setPractice(skill.getPractice());
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(actorId, list));
        log.info("玩家: 【{}】->【{}】 学习技能：【{}-{}】", role.getId(), role.getName(), skillId, level);

        // 角色等级达到条件技能连升
        skillLevelUpByRoleLevel(role, actorId, skillId);
    }

    @Override
    public boolean canLevelUpSkill(IAvatar avatar, RoleAdvance advance, int skillId, int level) {
        int curLevel = 0;
        int curPractice = 0;
        Skill skill = advance.getRoleSkill().getSkillMap().get(skillId);
        if (skill != null) {
            curLevel = skill.getLevel();
            curPractice = skill.getPractice();
        }

        if (level <= curLevel) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_已学习对应技能);
            return false;
        }

        if (level >= curLevel + 2) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_技能等级不满足条件);
            return false;
        }

        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig == null) {
            return false;
        }

        SkillConditionConfig curSkillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + curLevel);
        if (curSkillConditionConfig != null && !ConditionUtil.validate(avatar, curSkillConditionConfig.getCondition(), true)) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_未满足学习条件);
            return false;
        }
        //职业限制
        if (skillConfig.getCls() != SkillConst.SkillCls.COMB_SKILL.getCls()) {
            if (avatar.getCareer() == RoleConst.Career.ZHAN) {
                if (skillConfig.getCls() != SkillConst.SkillCls.ZS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            } else if (avatar.getCareer() == RoleConst.Career.FA) {
                if (skillConfig.getCls() != SkillConst.SkillCls.FS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            } else if (avatar.getCareer() == RoleConst.Career.DAO) {
                if (skillConfig.getCls() != SkillConst.SkillCls.DS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            }
        }

        SkillConditionConfig targetConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + level);
        if (targetConditionConfig == null) {
            return false;
        }
        //遗忘技能重新学 不用判断之前低等级的熟练度
//        Skill forgetSkill = advance.getRoleSkill().getForgetSkillMap().get(skillId);
//        if (forgetSkill == null || forgetSkill.getLevel() < curLevel) {
//            if (curPractice < targetConditionConfig.getPracticeneed()) {
//                TipUtil.show(avatar.getId(), CommonTips.脚本_当前技能熟练度不足);
//                return false;
//            }
//        }

        return true;
    }

    @Override
    public void forgetSkill(Role role, long actorId, int skillId) {
        if (!role.isOwner(actorId)) {
            return;
        }

        RoleAdvance advance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = advance.getRoleSkill();
        Map<Integer, Skill> skillMap = roleSkill.getSkillMap();

        Skill skill = skillMap.remove(skillId);
        if (skill == null) {
            return;
        }
        roleSkill.getForgetSkillMap().put(skillId, skill);
        DataCenter.updateData(advance);

        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig != null && skillConfig.getBupiaozi() != 1) {
            TipUtil.error(role.getId(), skillConfig.getName() + CommonTips.脚本_已被遗忘);
        }

        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skillId);
        skillDTO.setLevel(0);
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(role.getId(), list));
        sendSkillInfo(role);
    }

    @Override
    public void addSkillPractice(Role role, long actorId, Collection<Integer> skillIds, int add) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = roleAdvance.getRoleSkill().getSkillMap();

        List<SkillDTO> skillDTOList = new ArrayList<>();
        boolean needNotice = false;
        for (Integer skillId : skillIds) {
            Skill skill = skillMap.get(skillId);
            if (skill == null) {
                continue;
            }

            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                continue;
            }

            SkillConditionConfig nextConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() + 1));
            if (nextConditionConfig == null) {
                continue;
            }

            int levelUpNeed = nextConditionConfig.getPracticeneed();

            //不是自动升级的，满了不用再加 需要技能书升级
            boolean canAutoLevelUp = skillConditionConfig.getPracticeup() == 1;
            if (!canAutoLevelUp && skill.getPractice() >= levelUpNeed) {
                continue;
            }

            int addValue = add;
            if (add == 0) {
                int[] practiceAdd = skillConditionConfig.getPracticeadd();
                if (practiceAdd.length < 2) {
                    log.error("当前技能熟练度增加配置错误->{}", skillConditionConfig.getId());
                    addValue = 1;
                } else {
                    addValue = RandomUtil.random(practiceAdd[0], practiceAdd[1]);
                }
                Attribute attribute = role.getId() == actorId ? role.getFinalAttribute() : role.getHero().getFinalAttribute();
                addValue = (int) (addValue * (1 + AttributeEnum.MASTERY.getAttrValue(attribute) / 10000D));
            }

            int oldValue = skill.getPractice(), oldLevel = skill.getLevel();
            IAvatar avatar = role.getId() == actorId ? role : role.getHero();
            upPractice(avatar, skill, addValue);
            int newValue = skill.getPractice(), newLevel = skill.getLevel();

            if (newLevel != oldLevel) {
                TipUtil.show(role.getId(), CommonTips.服务_技能已经提升, skillConditionConfig.getName(), nextConditionConfig.getName());
            } else {
                int piece = levelUpNeed / 5;
                int oldStage = oldValue / piece;
                int newStage = newValue / piece;
                if (newStage != oldStage) {
                    TipUtil.show(role.getId(), CommonTips.服务_技能熟练度已提升, skillConditionConfig.getName(), newValue);
                }
            }

            //通知场景
            if (newLevel != oldLevel || skill.getPractice() >= levelUpNeed) {
                SkillDTO skillDTO = new SkillDTO();
                skillDTO.setSkillId(skill.getSkillId());
                skillDTO.setLevel(skill.getLevel());
                skillDTO.setPractice(skill.getPractice());
                skillDTOList.add(skillDTO);
            }

            needNotice = newLevel != oldLevel || newValue != oldValue;
            sendSkillChangeInfo(role, actorId, skillId);
        }

        if (needNotice) {
            DataCenter.updateData(roleAdvance);
        }

        role.proxyCall(proxy -> proxy.skillUpdate(actorId, skillDTOList));
    }

    @Override
    public void selectHeroSkill(Role role, long actorId, int skillId) {
        if (role.getId() == actorId) {
            return;
        }
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Skill skill = roleAdvance.getRoleSkill().getSkillMap().get(skillId);
        if (skill == null) {
            return;
        }
        SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skill.getSkillId());
        if (config.getCls() == SkillConst.SkillCls.COMB_SKILL.getCls()) {
            return;
        }
        skill.setAutoUse(!skill.isAutoUse());
        DataCenter.updateData(roleAdvance);
        sendSkillChangeInfo(role, actorId, skillId);
        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skill.getSkillId());
        skillDTO.setLevel(skill.getLevel());
        skillDTO.setPractice(skill.getPractice());
        skillDTO.setAutoUse(skill.isAutoUse());
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(actorId, list));
    }

    @Override
    public void onRoleLevelUp(Role role, long actorId, int oLv) {
        // 角色升级时自动升级技能
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = advance.getRoleSkill().getSkillMap();

        SkillConditionConfig skillConditionConfig;
        for (Skill skill : skillMap.values()) {
            skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                    skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                log.warn("玩家：{}，的技能：{}，等级：{}在配置表中未找到", role.getId(), skill.getLevel(), skill.getLevel());
                continue;
            }
            int needRoleLevel = skillConditionConfig.getPersonLevel();
            if (needRoleLevel <= 1) {
                continue;
            }
            if (oLv < needRoleLevel && role.getLevel() >= needRoleLevel) {
                // 满足技能升级条件
                ScriptEngine.invoke1t1(ISkillScript.class, script -> script.levelUpSkill(role, actorId, skill.getSkillId()));
            }

        }
    }

    private void upPractice(IAvatar avatar, Skill skill, int addValue) {
        while (addValue > 0) {
            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                break;
            }
            SkillConditionConfig nextConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() + 1));
            if (nextConditionConfig == null) {
                break;
            }
            int levelUpNeed = nextConditionConfig.getPracticeneed();

            if (skillConditionConfig.getPracticeup() == 1 && ConditionUtil.validate(avatar, skillConditionConfig.getCondition(), false)) {
                int need = Math.max(0, levelUpNeed - skill.getPractice());
                if (addValue >= need) {
                    skill.setLevel(skill.getLevel() + 1);
                    skill.setPractice(0);
                    addValue -= need;
                } else {
                    skill.setPractice(skill.getPractice() + addValue);
                    addValue = 0;
                }
            } else {
                skill.setPractice(Math.min(skill.getPractice() + addValue, levelUpNeed));
                break;
            }
        }
    }

    /**
     * @param role
     * @param actorId
     * @param skillId
     */
    private void skillLevelUpByRoleLevel(Role role, long actorId, int skillId) {
        // 角色升级时自动升级技能
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = advance.getRoleSkill().getSkillMap();
        Skill skill = skillMap.get(skillId);

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (skillConditionConfig == null) {
            log.warn("玩家：{}，的技能：{}，等级：{}在配置表中未找到", role.getId(), skill.getLevel(), skill.getLevel());
            return;
        }
        int needRoleLevel = skillConditionConfig.getPersonLevel();
        if (needRoleLevel == 0 || role.getLevel() < needRoleLevel) {
            return;
        }
        log.warn("玩家:{}满足等级:{}条件，技能:{}，等级:{}将自动升级", role.getId(), role.getLevel(), skill.getSkillId(), skill.getLevel());
        ScriptEngine.invoke1t1(ISkillScript.class, script -> script.levelUpSkill(role, actorId, skill.getSkillId()));
    }

    @Override
    public List<SkillBattleConfig> findRoleSkill(Role role) {
        List<SkillBattleConfig> skillList = new ArrayList<>(6);
        //宠物
        addZTPetSkill(role, skillList);

        //坐骑
        addRoleMountSkill(role, skillList);

        // 上阵的伙伴
        HuoBanCache cache = ConfigCacheManager.getInstance().getCache(HuoBanCache.class);
        Map<Integer, Integer> shangZhenMap = role.findHuoBan().findInUseFormation().getSlots();

        RoleHuoBan huoBan = role.findHuoBan();
        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();

        for (Integer index : shangZhenMap.keySet().stream().sorted().collect(Collectors.toList())) {
            Integer cfgId = shangZhenMap.getOrDefault(index, -1);
            HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, cfgId);
            if (config == null) {
                continue;
            }
            HuoBan ban = huoBanBag.get(cfgId);
            if (ban == null) {
               continue;
            }
            HuoBanSkillConfig skillConfig = cache.findSkillConfig(config.getId(), ban.getSkillLevel());
            if (skillConfig == null) {
                continue;
            }
            SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillConfig.getSkillID());
            if (skillBattleConfig != null) {
                skillList.add(skillBattleConfig);
            }
            // 变身技能
            SkillBattleConfig henshinSkill = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillConfig.getHenshin());
            if (henshinSkill != null) {
                skillList.add(henshinSkill);
            }
        }
        // 装备携带的技能
        role.getBackpack().fetchStorage(BackpackConst.Place.EQUIP).getData().forEach((index, item) -> {
            ItemSkillCache itemSkillCache = ConfigCacheManager.getInstance().getCache(ItemSkillCache.class);
            skillList.addAll(itemSkillCache.getItemSkills(item.getCfgId()));
        });
        // 初始技能
        CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class,
                role.getCareer() + Symbol.JINHAO + role.getSex());
        if (characterConfig != null) {
            addSkillBattleConfig(characterConfig.getInitSkill(), skillList);
        }

        return skillList;
    }

    private void addSkillBattleConfig(List<Integer> skillIds, List<SkillBattleConfig> skillList) {
        for (Integer skillCfgId : skillIds) {
            SkillBattleConfig config = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillCfgId);
            if (config != null) {
                skillList.add(config);
            }
        }
    }

    private void addZTPetSkill(Role role, List<SkillBattleConfig> skillList) {
        RoleZTPet pet = role.findZTPet();
        int selectPetId = pet.getSelectPetId();
        if (selectPetId == 0) { //没有跟随宠物
            return;
        }
        ZTPetData petData = pet.getPetData().getOrDefault(selectPetId, null);
        ZTPetRankConfig config = ConfigDataManager.getInstance().getById(ZTPetRankConfig.class, selectPetId + "#" + petData.getRank());
        if (config == null) { //找不到对应配置
            return;
        }
        addSkillBattleConfig(config.getSkill(), skillList);
    }

    private void addRoleMountSkill(Role role, List<SkillBattleConfig> skillList) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        int fashionId = appearance.getWears().getOrDefault(FashionConst.AppearanceType.ROLE_MOUNT, 0);

        //拥有技能
        for (int id : appearance.getStatus().keySet()) {
            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (config == null) {
                continue;
            }
            if (config.getType() != FashionConst.AppearanceType.ROLE_MOUNT) {
                continue;
            }
            //拥有技能
            addSkillBattleConfig(config.getHaveBuff(), skillList);

            //穿戴技能
            if (fashionId > 0 && id == fashionId) {
                addSkillBattleConfig(config.getBuff(), skillList);
            }
        }
    }

    @Override
    public void updateRoleSkill(Role role) {
        List<SkillBattleConfig> roleSkill = findRoleSkill(role);
        role.proxyCall(proxy -> proxy.battleSkillUpdate(role.getId(), roleSkill));
    }

    private long getSkillCd(Role role, int skillId) {
        long cd = CDUtil.getCd(role, CdConst.CdType.SKILL, skillId);
        long value = AttributeEnum.ZTXY_COOLDOWN_REDUCTION.getAttrValue(role.getFinalAttribute());
        if (value > 0) {
            cd = (long) (cd * (10000 - value) / 10000.0);
        }
        return cd;
    }

}
