package com.sh.game.script.skill;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.communication.msg.system.roleSkillTree.ResRoleSkillTreeInfoMessage;
import com.sh.game.common.communication.msg.system.roleSkillTree.bean.SkillTreeBean;
import com.sh.game.common.communication.msg.system.skill.ResSkillChangeMessage;
import com.sh.game.common.communication.msg.system.skill.ResSkillInfoMessage;
import com.sh.game.common.communication.msg.system.skill.bean.SkillBean;
import com.sh.game.common.communication.msg.system.skill.bean.SkillListBean;
import com.sh.game.common.config.cache.HuoBanCache;
import com.sh.game.common.config.cache.ItemSkillCache;
import com.sh.game.common.config.cache.SkillBattleConfigCache;
import com.sh.game.common.config.cache.SkillsTreeConfigCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.map.SkillDTO;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.entity.usr.RoleHuoBan;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLevelUpScript;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.skill.entity.RoleSkill;
import com.sh.game.system.skill.entity.RoleSkillTree;
import com.sh.game.system.skill.script.ISkillScript;
import com.sh.game.system.ztpet.entity.RoleZTPet;
import com.sh.game.system.ztpet.entity.ZTPetData;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Script
public class SkillScript implements ISkillScript, IEventOnRoleLevelUpScript {

    @Override
    public void createRoleSkill(Role role, long actorId) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = roleAdvance.getRoleSkill();
        CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class,
                role.getCareer() + Symbol.JINHAO + role.getSex());

        for (Integer skillCfgId : characterConfig.getInitSkill()) {
            SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillCfgId);
            Skill skill = new Skill();
            skill.setSkillId(skillBattleConfig.getId());
            skill.setLevel(skillBattleConfig.getLevel());
            roleSkill.getSkillMap().put(skill.getSkillId(), skill);
        }
        roleAdvance.setRoleSkill(roleSkill);
        DataCenter.updateData(roleAdvance);
    }

    @Override
    public void sendSkillInfo(Role role) {
        ResSkillInfoMessage msg = new ResSkillInfoMessage();
        buildResSkillInfoMessage(msg, role.getRoleAdvance(), role.getAttribute(), role);
        Hero hero = role.getHero();
        if (hero != null) {
            buildResSkillInfoMessage(msg, hero.getRoleAdvance(), hero.getAttribute(), null);
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    private void buildResSkillInfoMessage(ResSkillInfoMessage msg, RoleAdvance advance, Attribute attribute, Role role) {
        Map<Integer, Skill> roleSkillMap = advance.getRoleSkill().getSkillMap();
        SkillListBean listBean = new SkillListBean();
        listBean.setActorId(advance.getId());
        roleSkillMap.values().forEach(skill -> {
            SkillBean bean = new SkillBean();
            bean.setLevel(skill.getLevel());
            bean.setSkillId(skill.getSkillId());
            bean.setPractice(skill.getPractice());
            bean.setAutoUse(skill.isAutoUse());
            if (role != null) {
                bean.setSkillCd(getSkillCd(role, skill.getSkillId()));
            }
            listBean.getSkillInfo().add(bean);
        });


        Map<Integer, Integer> sysSkillMap = attribute.getSkillMap();
        sysSkillMap.forEach((k, v) -> {
            SkillBean bean = new SkillBean();
            bean.setSkillId(k);
            bean.setLevel(v);
            bean.setPractice(100);
            listBean.getSkillInfo().add(bean);
        });
        msg.getListInfo().add(listBean);
    }

    private void sendSkillChangeInfo(Role role, long actorId, int changeSkill) {
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = roleAdvance.getRoleSkill().getSkillMap();
        Skill skill = skillMap.get(changeSkill);

        ResSkillChangeMessage msg = new ResSkillChangeMessage();
        msg.setActorId(actorId);
        SkillBean bean = new SkillBean();
        bean.setSkillId(skill.getSkillId());
        bean.setLevel(skill.getLevel());
        bean.setPractice(skill.getPractice());
        bean.setAutoUse(skill.isAutoUse());

        long cd = getSkillCd(role, skill.getSkillId());
        bean.setSkillCd(cd);
        msg.setSkillBean(bean);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void levelUpSkill(Role role, long actorId, int skillId) {
        if (!role.isOwner(actorId)) {
            return;
        }
        int level = 0;
        IAvatar avatar = role.getId() == actorId ? role : role.getHero();
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Skill skill = advance.getRoleSkill().getSkillMap().get(skillId);
        if (skill != null) {
            level = skill.getLevel();
        }
        level++;

        if (!canLevelUpSkill(avatar, advance, skillId, level)) {
            return;
        }

        levelUpSkill(role, actorId, skillId, level);
    }

    @Override
    public void levelUpSkill(Role role, long actorId, int skillId, int level) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = roleAdvance.getRoleSkill();

        Map<Integer, Skill> roleSkillMap = roleSkill.getSkillMap();
        Skill skill = roleSkillMap.computeIfAbsent(skillId, k -> {
            Skill newSkill = new Skill();
            newSkill.setSkillId(skillId);
            return newSkill;
        });

        skill.setLevel(level);
        skill.setPractice(0);
        //超过遗忘技能里的等级
        Skill forgetSkill = roleSkill.getForgetSkillMap().get(skillId);
        if (forgetSkill != null && forgetSkill.getLevel() >= skill.getLevel()) {
            if (forgetSkill.getLevel() == skill.getLevel()) {
                skill.setPractice(forgetSkill.getPractice());
            }
            roleSkill.getForgetSkillMap().remove(skillId);
        }

        DataCenter.updateData(roleAdvance);
        SkillManager.getInstance().onSkillLevelUp(role, actorId, skillId, level);

        sendSkillChangeInfo(role, actorId, skillId);

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + level);
        // TODO 无配置
        if (skillConditionConfig == null) {
            return;
        }
        if (skillConditionConfig.getAnnounce() > 0) {
//            AnnounceManager.getInstance().announce(skillConditionConfig.getAnnounce(), role.getId(), role.getName(), skillConditionConfig.getName(), skillConditionConfig.getAnparam());
        }
        SkillConditionConfig prevConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() - 1));
        if (prevConditionConfig != null && skillConditionConfig.getSpPiaozi() != 1) {
            TipUtil.show(role.getId(), CommonTips.服务_技能已经提升, prevConditionConfig.getName(), skillConditionConfig.getName());
        }
        if (skillConditionConfig.getSpPiaozi() == 1) {
            TipUtil.show(role.getId(), CommonTips.学习特殊技能飘字, skillConditionConfig.getName());
        }

        if (!skill.isAutoUse()) {
            return;
        }
        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skill.getSkillId());
        skillDTO.setLevel(skill.getLevel());
        skillDTO.setPractice(skill.getPractice());
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(actorId, list));
        log.info("玩家: 【{}】->【{}】 学习技能：【{}-{}】", role.getId(), role.getName(), skillId, level);

        // 角色等级达到条件技能连升
        skillLevelUpByRoleLevel(role, actorId, skillId);
    }

    @Override
    public boolean canLevelUpSkill(IAvatar avatar, RoleAdvance advance, int skillId, int level) {
        int curLevel = 0;
        int curPractice = 0;
        Skill skill = advance.getRoleSkill().getSkillMap().get(skillId);
        if (skill != null) {
            curLevel = skill.getLevel();
            curPractice = skill.getPractice();
        }

        if (level <= curLevel) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_已学习对应技能);
            return false;
        }

        if (level >= curLevel + 2) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_技能等级不满足条件);
            return false;
        }

        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig == null) {
            return false;
        }

        SkillConditionConfig curSkillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + curLevel);
        if (curSkillConditionConfig != null && !ConditionUtil.validate(avatar, curSkillConditionConfig.getCondition(), true)) {
            TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_未满足学习条件);
            return false;
        }
        //职业限制
        if (skillConfig.getCls() != SkillConst.SkillCls.COMB_SKILL.getCls()) {
            if (avatar.getCareer() == RoleConst.Career.ZHAN) {
                if (skillConfig.getCls() != SkillConst.SkillCls.ZS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            } else if (avatar.getCareer() == RoleConst.Career.FA) {
                if (skillConfig.getCls() != SkillConst.SkillCls.FS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            } else if (avatar.getCareer() == RoleConst.Career.DAO) {
                if (skillConfig.getCls() != SkillConst.SkillCls.DS_BASIC.getCls()) {
                    TipUtil.show(avatar.getOwnRId(), CommonTips.脚本_无法学习其他职业技能);
                    return false;
                }
            }
        }

        SkillConditionConfig targetConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skillId + Symbol.JINHAO + level);
        if (targetConditionConfig == null) {
            return false;
        }
        //遗忘技能重新学 不用判断之前低等级的熟练度
//        Skill forgetSkill = advance.getRoleSkill().getForgetSkillMap().get(skillId);
//        if (forgetSkill == null || forgetSkill.getLevel() < curLevel) {
//            if (curPractice < targetConditionConfig.getPracticeneed()) {
//                TipUtil.show(avatar.getId(), CommonTips.脚本_当前技能熟练度不足);
//                return false;
//            }
//        }

        return true;
    }

    @Override
    public void forgetSkill(Role role, long actorId, int skillId) {
        if (!role.isOwner(actorId)) {
            return;
        }

        RoleAdvance advance = role.getRoleAdvance(actorId);
        RoleSkill roleSkill = advance.getRoleSkill();
        Map<Integer, Skill> skillMap = roleSkill.getSkillMap();

        Skill skill = skillMap.remove(skillId);
        if (skill == null) {
            return;
        }
        roleSkill.getForgetSkillMap().put(skillId, skill);
        DataCenter.updateData(advance);

        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig != null && skillConfig.getBupiaozi() != 1) {
            TipUtil.error(role.getId(), skillConfig.getName() + CommonTips.脚本_已被遗忘);
        }

        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skillId);
        skillDTO.setLevel(0);
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(role.getId(), list));
        sendSkillInfo(role);
    }

    @Override
    public void addSkillPractice(Role role, long actorId, Collection<Integer> skillIds, int add) {
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = roleAdvance.getRoleSkill().getSkillMap();

        List<SkillDTO> skillDTOList = new ArrayList<>();
        boolean needNotice = false;
        for (Integer skillId : skillIds) {
            Skill skill = skillMap.get(skillId);
            if (skill == null) {
                continue;
            }

            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                continue;
            }

            SkillConditionConfig nextConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() + 1));
            if (nextConditionConfig == null) {
                continue;
            }

            int levelUpNeed = nextConditionConfig.getPracticeneed();

            //不是自动升级的，满了不用再加 需要技能书升级
            boolean canAutoLevelUp = skillConditionConfig.getPracticeup() == 1;
            if (!canAutoLevelUp && skill.getPractice() >= levelUpNeed) {
                continue;
            }

            int addValue = add;
            if (add == 0) {
                int[] practiceAdd = skillConditionConfig.getPracticeadd();
                if (practiceAdd.length < 2) {
                    log.error("当前技能熟练度增加配置错误->{}", skillConditionConfig.getId());
                    addValue = 1;
                } else {
                    addValue = RandomUtil.random(practiceAdd[0], practiceAdd[1]);
                }
                Attribute attribute = role.getId() == actorId ? role.getFinalAttribute() : role.getHero().getFinalAttribute();
                addValue = (int) (addValue * (1 + AttributeEnum.MASTERY.getAttrValue(attribute) / 10000D));
            }

            int oldValue = skill.getPractice(), oldLevel = skill.getLevel();
            IAvatar avatar = role.getId() == actorId ? role : role.getHero();
            upPractice(avatar, skill, addValue);
            int newValue = skill.getPractice(), newLevel = skill.getLevel();

            if (newLevel != oldLevel) {
                TipUtil.show(role.getId(), CommonTips.服务_技能已经提升, skillConditionConfig.getName(), nextConditionConfig.getName());
            } else {
                int piece = levelUpNeed / 5;
                int oldStage = oldValue / piece;
                int newStage = newValue / piece;
                if (newStage != oldStage) {
                    TipUtil.show(role.getId(), CommonTips.服务_技能熟练度已提升, skillConditionConfig.getName(), newValue);
                }
            }

            //通知场景
            if (newLevel != oldLevel || skill.getPractice() >= levelUpNeed) {
                SkillDTO skillDTO = new SkillDTO();
                skillDTO.setSkillId(skill.getSkillId());
                skillDTO.setLevel(skill.getLevel());
                skillDTO.setPractice(skill.getPractice());
                skillDTOList.add(skillDTO);
            }

            needNotice = newLevel != oldLevel || newValue != oldValue;
            sendSkillChangeInfo(role, actorId, skillId);
        }

        if (needNotice) {
            DataCenter.updateData(roleAdvance);
        }

        role.proxyCall(proxy -> proxy.skillUpdate(actorId, skillDTOList));
    }

    @Override
    public void selectHeroSkill(Role role, long actorId, int skillId) {
        if (role.getId() == actorId) {
            return;
        }
        if (!role.isOwner(actorId)) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance(actorId);
        Skill skill = roleAdvance.getRoleSkill().getSkillMap().get(skillId);
        if (skill == null) {
            return;
        }
        SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skill.getSkillId());
        if (config.getCls() == SkillConst.SkillCls.COMB_SKILL.getCls()) {
            return;
        }
        skill.setAutoUse(!skill.isAutoUse());
        DataCenter.updateData(roleAdvance);
        sendSkillChangeInfo(role, actorId, skillId);
        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(skill.getSkillId());
        skillDTO.setLevel(skill.getLevel());
        skillDTO.setPractice(skill.getPractice());
        skillDTO.setAutoUse(skill.isAutoUse());
        List<SkillDTO> list = new ArrayList<>();
        list.add(skillDTO);
        role.proxyCall(proxy -> proxy.skillUpdate(actorId, list));
    }

    @Override
    public void onRoleLevelUp(Role role, long actorId, int oLv) {
        //玩家每升一级，技能树的技能点增加一点
        addSkillTreePoint(role, oLv);

        // 角色升级时自动升级技能
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = advance.getRoleSkill().getSkillMap();

        SkillConditionConfig skillConditionConfig;
        for (Skill skill : skillMap.values()) {
            skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                    skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                log.warn("玩家：{}，的技能：{}，等级：{}在配置表中未找到", role.getId(), skill.getLevel(), skill.getLevel());
                continue;
            }
            int needRoleLevel = skillConditionConfig.getPersonLevel();
            if (needRoleLevel <= 1) {
                continue;
            }
            if (oLv < needRoleLevel && role.getLevel() >= needRoleLevel) {
                // 满足技能升级条件
                ScriptEngine.invoke1t1(ISkillScript.class, script -> script.levelUpSkill(role, actorId, skill.getSkillId()));
            }

        }
    }

    private void addSkillTreePoint(Role role, int oLv) {
        int curLevel = role.getLevel();
        int point = curLevel - oLv;
        if (point <= 0) {
            return;
        }
        RoleSkillTree tree = role.findRoleSkillTree();
        tree.setSkillPoint(tree.getSkillPoint() + point);
        DataCenter.updateData(tree);

        sendSkillTreeInfo(role);
    }

    private void upPractice(IAvatar avatar, Skill skill, int addValue) {
        while (addValue > 0) {
            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
            if (skillConditionConfig == null) {
                break;
            }
            SkillConditionConfig nextConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + (skill.getLevel() + 1));
            if (nextConditionConfig == null) {
                break;
            }
            int levelUpNeed = nextConditionConfig.getPracticeneed();

            if (skillConditionConfig.getPracticeup() == 1 && ConditionUtil.validate(avatar, skillConditionConfig.getCondition(), false)) {
                int need = Math.max(0, levelUpNeed - skill.getPractice());
                if (addValue >= need) {
                    skill.setLevel(skill.getLevel() + 1);
                    skill.setPractice(0);
                    addValue -= need;
                } else {
                    skill.setPractice(skill.getPractice() + addValue);
                    addValue = 0;
                }
            } else {
                skill.setPractice(Math.min(skill.getPractice() + addValue, levelUpNeed));
                break;
            }
        }
    }

    /**
     * @param role
     * @param actorId
     * @param skillId
     */
    private void skillLevelUpByRoleLevel(Role role, long actorId, int skillId) {
        // 角色升级时自动升级技能
        RoleAdvance advance = role.getRoleAdvance(actorId);
        Map<Integer, Skill> skillMap = advance.getRoleSkill().getSkillMap();
        Skill skill = skillMap.get(skillId);

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class,
                skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (skillConditionConfig == null) {
            log.warn("玩家：{}，的技能：{}，等级：{}在配置表中未找到", role.getId(), skill.getLevel(), skill.getLevel());
            return;
        }
        int needRoleLevel = skillConditionConfig.getPersonLevel();
        if (needRoleLevel == 0 || role.getLevel() < needRoleLevel) {
            return;
        }
        log.warn("玩家:{}满足等级:{}条件，技能:{}，等级:{}将自动升级", role.getId(), role.getLevel(), skill.getSkillId(), skill.getLevel());
        ScriptEngine.invoke1t1(ISkillScript.class, script -> script.levelUpSkill(role, actorId, skill.getSkillId()));
    }

    /**
     * 技能树-选择技能
     * @param role role
     * @param cid 技能树配置id
     * @param skillId 技能配置id
     */
    @Override
    public void selectSkillTreeSkill(Role role, int cid, int skillId) {
        SkillsTreeConfig config = ConfigDataManager.getInstance().getById(SkillsTreeConfig.class, cid);
        if (config == null) {
            log.error("技能树-选择技能#玩家:{}-{},技能树配置不存在,cid:{}", role.getId(), role.getName(), cid);
            return;
        }
        SkillBattleConfig skillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillId);
        if (skillConfig == null) {
            log.error("技能树-选择技能#玩家:{}-{},技能配置不存在,skillId:{}", role.getId(), role.getName(), skillId);
            return;
        }
        boolean check = checkSkillTreeSkill(role, config, skillConfig);
        if (!check) {
            return;
        }
        if (skillConfig.getLevel() != 1) {
            log.error("技能树-选择技能#玩家:{}-{},技能树配置:{},技能配置id:{},技能等级非1级", role.getId(), role.getName(), cid, skillId);
            return;
        }

        RoleSkillTree skillTree = role.findRoleSkillTree();
        Map<Integer, Integer> skillTreeIdMap = skillTree.getSkillTreeIdMap();
        //是否有互斥技能已选择,包括重复选择情况
        SkillsTreeConfigCache cache = ConfigCacheManager.getInstance().getCache(SkillsTreeConfigCache.class);
        List<Integer> ids = cache.getIdsByFloor(config.getLayer());
        for (int treeId : ids) {
            if (skillTreeIdMap.containsKey(treeId)) {
                log.error("技能树-选择技能#玩家:{}-{},技能已选择,cid:{}, skillId:{}", role.getId(), role.getName(), cid, skillId);
                return;
            }
        }

        //前置技能是否已选择
        int fatherId = config.getFrontLinkID();
        if (fatherId > 0 && !skillTreeIdMap.containsKey(fatherId)) {
            log.error("技能树-选择技能#玩家:{}-{},前置技能未选择,cid:{}, skillId:{}", role.getId(), role.getName(), fatherId, skillId);
            return;
        }

        int needPoint = skillConfig.getSkillPoint();
        if (skillTree.getSkillPoint() < needPoint) { //技能点不足
            log.error("技能树-选择技能#玩家:{}-{},技能点不足,cid:{}, skillId:{}", role.getId(), role.getName(), cid, skillId);
            return;
        }
        //  扣除剩余点数
        skillTree.setSkillPoint(skillTree.getSkillPoint() - needPoint);
        skillTreeIdMap.put(cid, skillId);
        DataCenter.updateData(skillTree);
        sendSkillTreeInfo(role);

        updateRoleSkill(role);
        log.info("技能树-选择技能#玩家:{}-{},技能选择成功,cid:{}, skillId:{}", role.getId(), role.getName(), cid, skillId);
    }

    public boolean checkSkillTreeSkill(Role role, SkillsTreeConfig config, SkillBattleConfig skillConfig) {
        int cid = config.getPart();
        int skillId = skillConfig.getId();
        if (config.getJobID() > 0 && config.getJobID() != role.getCareer()) {
            log.error("技能树-选择技能#玩家:{}-{},职业:{},技能对应职业:{},职业不匹配,cid:{}, skillId:{}", role.getId(), role.getName(), role.getCareer(), config.getJobID(), cid, skillId);
            return false;
        }

        if (skillConfig.getGroup() != config.getGroup()) {
            log.error("技能树-选择技能#玩家:{}-{},技能树group:{},技能配置group:{},group不匹配,cid:{}, skillId:{}", role.getId(), role.getName(), config.getGroup(), skillConfig.getGroup(), cid, skillId);
            return false;
        }
        return true;
    }

    /**
     * 升级技能树
     * @param role   role
     * @param cid    技能树配置id
     * @param skillId 技能配置id
     */
    @Override
    public void levelUpSkillTreeSkill(Role role, int cid, int skillId) {
        SkillsTreeConfig config = ConfigDataManager.getInstance().getById(SkillsTreeConfig.class, cid);
        if (config == null) {
            log.error("技能树-技能升级#玩家:{}-{},技能树配置不存在,cid:{}", role.getId(), role.getName(), cid);
            return;
        }
        SkillBattleConfig skillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillId);
        if (skillConfig == null) {
            log.error("技能树-技能升级#玩家:{}-{},技能配置不存在,skillId:{}", role.getId(), role.getName(), skillId);
            return;
        }
        boolean check = checkSkillTreeSkill(role, config, skillConfig);
        if (!check) {
            return;
        }

        RoleSkillTree skillTree = role.findRoleSkillTree();
        Map<Integer, Integer> skillTreeIdMap = skillTree.getSkillTreeIdMap();
        int oldSkillId = skillTreeIdMap.getOrDefault(cid, 0);
        SkillBattleConfig oldSkillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, oldSkillId);
        if (oldSkillConfig == null) {
            log.error("技能树-技能升级#玩家:{}-{},技能树配置:{},技能配置id:{},未找到上一级技能配置", role.getId(), role.getName(), cid, skillId);
            return;
        }
        if (oldSkillConfig.getLevel() + 1 != skillConfig.getLevel()) {
            log.error("技能树-技能升级#玩家:{}-{},技能树配置:{},技能配置id:{},技能等级不匹配", role.getId(), role.getName(), config.getGroup(), skillConfig.getGroup());
            return;
        }

        int needPoint = skillConfig.getSkillPoint();
        if (skillTree.getSkillPoint() < needPoint) { //技能点不足
            log.error("技能树-技能升级#玩家:{}-{},技能点不足,cid:{}, skillId:{}", role.getId(), role.getName(), cid, skillId);
            return;
        }
        //  扣除剩余点数
        skillTree.setSkillPoint(skillTree.getSkillPoint() - needPoint);
        skillTreeIdMap.put(cid, skillId);
        DataCenter.updateData(skillTree);
        sendSkillTreeInfo(role);

        log.info("技能树-技能升级#玩家:{}-{},技能升级成功,cid:{}, skillId:{}", role.getId(), role.getName(), cid, skillId);
        updateRoleSkill(role);
    }

    /**
     * 技能树-技能降级
     * @param role  role
     * @param cid  技能树配置id
     *
     */
    @Override
    public void levelDownSkillTreeSkill(Role role, int cid) {
        SkillsTreeConfig config = ConfigDataManager.getInstance().getById(SkillsTreeConfig.class, cid);
        if (config == null) {
            log.error("技能树-技能降级#玩家:{}-{},技能树配置不存在,cid:{}", role.getId(), role.getName(), cid);
            return;
        }
        RoleSkillTree skillTree = role.findRoleSkillTree();
        Map<Integer, Integer> skillTreeIdMap = skillTree.getSkillTreeIdMap();
        int skillId = skillTreeIdMap.getOrDefault(cid, 0);
        SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillId);
        if (skillBattleConfig == null) {
            log.error("技能树-技能降级#玩家:{}-{},技能树配置:{},技能配置id:{}，当前未选择技能", role.getId(), role.getName(), cid, skillId);
            return;
        }
        int returnPoint = skillBattleConfig.getSkillPoint();
        SkillBattleConfigCache cache = ConfigCacheManager.getInstance().getCache(SkillBattleConfigCache.class);
        SkillBattleConfig lastSkillBattleConfig = cache.getConfigByGroup(skillBattleConfig.getGroup(), skillBattleConfig.getLevel() - 1);
        if (lastSkillBattleConfig != null) {
            skillTreeIdMap.put(cid, lastSkillBattleConfig.getId());
        } else {
            Set<Integer> removeSet = new HashSet<>();
            removeSet.add(cid);
            while (config != null && CollectionUtils.isNotEmpty(config.getLinkID())) {//看下子节点是否存在，如果子节点存在，也要将子节点技能点一并返还
                int nextId = 0;
                for (int treeId : config.getLinkID()) {
                    int skillID = skillTreeIdMap.getOrDefault(treeId, 0);
                    if (skillID == 0) {
                        continue;
                    }
                    SkillBattleConfig cfg = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillID);
                    if (cfg == null) {
                        continue;
                    }

                    nextId = treeId;
                    removeSet.add(treeId);

                    returnPoint += cfg.getSkillPoint();
                    for (int i = 1; i < cfg.getLevel(); i++) {
                        SkillBattleConfig levelConfig = cache.getConfigByGroup(cfg.getGroup(), i);
                        if (levelConfig != null) {
                            returnPoint += levelConfig.getSkillPoint();
                        }
                    }
                }
                config = nextId == 0 ? null : ConfigDataManager.getInstance().getById(SkillsTreeConfig.class, nextId);
            }
            for (int treeId : removeSet) {
                skillTreeIdMap.remove(treeId);
            }
        }

        skillTree.setSkillPoint(skillTree.getSkillPoint() + returnPoint); //返还点数
        DataCenter.updateData(skillTree);
        sendSkillTreeInfo(role);

        log.info("技能树-技能降级#玩家:{}-{},技能降级成功,cid:{},返还点数:{},最新点数:{}", role.getId(), role.getName(), cid, returnPoint, skillTree.getSkillPoint());
        updateRoleSkill(role);
    }

    /**
     * 重置玩家技能树
     * @param role role
     */
    @Override
    public void resetSkillTreeSkill(Role role) {
        RoleSkillTree skillTree = role.findRoleSkillTree();
        skillTree.getSkillTreeIdMap().clear();
        skillTree.setSkillPoint(role.getLevel());
        DataCenter.updateData(skillTree);

        sendSkillTreeInfo(role);
        updateRoleSkill(role);
        log.info("技能树-重置技能树#玩家:{}-{},技能树重置成功", role.getId(), role.getName());
    }

    @Override
    public void sendSkillTreeInfo(Role role) {
        RoleSkillTree skillTree = role.findRoleSkillTree();
        ResRoleSkillTreeInfoMessage msg = new ResRoleSkillTreeInfoMessage();
        msg.setSkillPoint(skillTree.getSkillPoint());
        for (Map.Entry<Integer, Integer> entry:skillTree.getSkillTreeIdMap().entrySet()) {
            SkillTreeBean bean = new SkillTreeBean();
            bean.setTreeCid(entry.getKey());
            bean.setSkillCid(entry.getValue());
            msg.getSkillBean().add(bean);
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public List<SkillBattleConfig> findRoleSkill(Role role) {
        List<SkillBattleConfig> skillList = new ArrayList<>(6);
        //宠物
        addZTPetSkill(role, skillList);

        //坐骑
        addRoleMountSkill(role, skillList);

        // 上阵的伙伴
        addRoleHuoBanSkill(role, skillList);

        // 装备携带的技能
        addRoleEquipSkill(role, skillList);

        // 初始技能
        addBasicSkill(role, skillList);

        //技能树
        addSkillTreeSkill(role, skillList);

        return skillList;
    }

    private void addSkillBattleConfig(Collection<Integer> skillIds, List<SkillBattleConfig> skillList) {
        for (Integer skillCfgId : skillIds) {
            SkillBattleConfig config = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skillCfgId);
            if (config != null) {
                skillList.add(config);
            }
        }
    }

    private void addZTPetSkill(Role role, List<SkillBattleConfig> skillList) {
        RoleZTPet pet = role.findZTPet();
        int selectPetId = pet.getSelectPetId();
        if (selectPetId == 0) { //没有跟随宠物
            return;
        }
        ZTPetData petData = pet.getPetData().getOrDefault(selectPetId, null);
        ZTPetRankConfig config = ConfigDataManager.getInstance().getById(ZTPetRankConfig.class, selectPetId + "#" + petData.getRank());
        if (config == null) { //找不到对应配置
            return;
        }
        addSkillBattleConfig(config.getSkill(), skillList);
    }

    private void addRoleMountSkill(Role role, List<SkillBattleConfig> skillList) {
        Appearance appearance = role.getRoleAdvance().getAppearance();
        int fashionId = appearance.getWears().getOrDefault(FashionConst.AppearanceType.ROLE_MOUNT, 0);

        //拥有技能
        for (int id : appearance.getStatus().keySet()) {
            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (config == null) {
                continue;
            }
            if (config.getType() != FashionConst.AppearanceType.ROLE_MOUNT) {
                continue;
            }
            //拥有技能
            addSkillBattleConfig(config.getHaveBuff(), skillList);

            //穿戴技能
            if (fashionId > 0 && id == fashionId) {
                addSkillBattleConfig(config.getBuff(), skillList);
            }
        }
    }

    private void addRoleHuoBanSkill(Role role, List<SkillBattleConfig> skillList) {
        HuoBanCache cache = ConfigCacheManager.getInstance().getCache(HuoBanCache.class);
        Map<Integer, Integer> shangZhenMap = role.findHuoBan().findInUseFormation().getSlots();

        RoleHuoBan huoBan = role.findHuoBan();
        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();

        for (Integer index : shangZhenMap.keySet().stream().sorted().collect(Collectors.toList())) {
            Integer cfgId = shangZhenMap.getOrDefault(index, -1);
            HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, cfgId);
            if (config == null) {
                continue;
            }
            HuoBan ban = huoBanBag.get(cfgId);
            if (ban == null) {
                continue;
            }
            HuoBanSkillConfig skillConfig = cache.findSkillConfig(config.getId(), ban.getSkillLevel());
            if (skillConfig == null) {
                continue;
            }
            SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillConfig.getSkillID());
            if (skillBattleConfig != null) {
                skillList.add(skillBattleConfig);
            }
            // 变身技能
            SkillBattleConfig henshinSkill = ConfigDataManager.getInstance()
                    .getById(SkillBattleConfig.class, skillConfig.getHenshin());
            if (henshinSkill != null) {
                skillList.add(henshinSkill);
            }
        }
    }

    public void addRoleEquipSkill(Role role, List<SkillBattleConfig> skillList) {
        ItemSkillCache itemSkillCache = ConfigCacheManager.getInstance().getCache(ItemSkillCache.class);
        role.getBackpack().fetchStorage(BackpackConst.Place.EQUIP).getData().forEach((index, item) -> {
            if (item == null) {
                return;
            }
            skillList.addAll(itemSkillCache.getItemSkills(item.getCfgId()));
        });
    }

    public void addBasicSkill(Role role, List<SkillBattleConfig> skillList) {
        CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class,
                role.getCareer() + Symbol.JINHAO + role.getSex());
        if (characterConfig != null) {
            addSkillBattleConfig(characterConfig.getInitSkill(), skillList);
        }
    }

    /**
     * 技能树
     *
     * @param role      role
     * @param skillList 技能集合
     */
    public void addSkillTreeSkill(Role role, List<SkillBattleConfig> skillList) {
        RoleSkillTree tree = role.findRoleSkillTree();
        Set<Integer> relationIdSet = new HashSet<>();
        for (Map.Entry<Integer, Integer> entry : tree.getSkillTreeIdMap().entrySet()) {
            SkillBattleConfig skillBattleConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, entry.getValue());
            if (skillBattleConfig != null) {
                skillList.add(skillBattleConfig);
            }

            SkillsTreeConfig config = ConfigDataManager.getInstance().getById(SkillsTreeConfig.class, entry.getKey());
            if (config != null && CollectionUtils.isNotEmpty(config.getRelationSkill())) {
                relationIdSet.addAll(config.getRelationSkill());
            }
        }
        addSkillBattleConfig(relationIdSet, skillList);
    }

    @Override
    public void updateRoleSkill(Role role) {
        List<SkillBattleConfig> roleSkill = findRoleSkill(role);
        role.proxyCall(proxy -> proxy.battleSkillUpdate(role.getId(), roleSkill));
    }

    private long getSkillCd(Role role, int skillId) {
        long cd = CDUtil.getCd(role, CdConst.CdType.SKILL, skillId);
        long value = AttributeEnum.ZTXY_COOLDOWN_REDUCTION.getAttrValue(role.getFinalAttribute());
        if (value > 0) {
            cd = (long) (cd * (10000 - value) / 10000.0);
        }
        return cd;
    }

}
