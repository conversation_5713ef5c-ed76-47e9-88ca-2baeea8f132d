package com.sh.game.script.injectsoul;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.injectsoul.ResInjectSoulInfoMessage;
import com.sh.game.common.communication.msg.system.injectsoul.ResInjectSoulResultMessage;
import com.sh.game.common.communication.msg.system.injectsoul.bean.InjectSoulBean;
import com.sh.game.common.config.cache.InjectSoulCache;
import com.sh.game.common.config.model.InjectSoulConfig;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnEquipChangedScript;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.injectsoul.entity.RoleInjectSoul;
import com.sh.game.system.injectsoul.script.IInjectSoulScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 注灵
 *
 * <AUTHOR> Chen
 * @since 2022-04-21 10:31
 **/
@Slf4j
@Script
public class InjectSoulScript implements IInjectSoulScript, IEventOnEquipChangedScript {

    /**
     * 根据角色id获取注灵信息
     *
     * @param roleId 角色id
     * @return {@link RoleInjectSoul} 注灵信息
     */
    @Override
    public RoleInjectSoul find(long roleId) {
        RoleInjectSoul roleInjectSoul = DataCenter.get(RoleInjectSoul.class, roleId);
        if (Objects.isNull(roleInjectSoul)) {
            roleInjectSoul = new RoleInjectSoul();
            roleInjectSoul.setId(roleId);
            DataCenter.insertData(roleInjectSoul, true);
        }
        return roleInjectSoul;
    }

    /**
     * 请求注灵升级
     *
     * @param role 角色
     * @param pos  部位
     */
    @Override
    public void reqInjectSoul(Role role, int pos) {
        RoleInjectSoul roleInjectSoul = find(role.getRoleId());
        Map<Integer, Integer> soulMap = roleInjectSoul.getSoulMap();
        int level = soulMap.getOrDefault(pos, 0);
        InjectSoulCache cache = ConfigCacheManager.getInstance().getCache(InjectSoulCache.class);

        InjectSoulConfig currentConfig = cache.getConfig(pos, level);
        if (currentConfig == null) {
            log.error("注灵升级-配置不存在, roleId: {} , roleName: {} , pos: {} , level: {}", role.getRoleId(), role.getName(), pos, level);
            return;
        }

        if (!ConditionUtil.validate(role, currentConfig.getMapCondition())) {
            log.error("注灵升级-条件不通过, roleId: {} , roleName: {} , configId: {}", role.getRoleId(), role.getName(), currentConfig.getId());
            return;
        }

        InjectSoulConfig nextConfig = ConfigDataManager.getInstance().getById(InjectSoulConfig.class, currentConfig.getNextId());
        if (nextConfig == null) {
            log.error("注灵升级-下一级配置不存在, roleId: {} , roleName: {} , pos: {} , level: {}", role.getRoleId(), role.getName(), pos, level);
            return;
        }

        //升级消耗
        BackpackStash stash = new BackpackStash(role);
        if (currentConfig.getItemsId() != null && !currentConfig.getItemsId().isEmpty()) {
            stash.decrease(currentConfig.getItemsId());
        }
        if (!stash.commit(role, LogAction.INJECT_SOUL_COST)) {
            log.error("注灵升级-消耗道具失败, roleId: {} , roleName: {} , configId: {}", role.getRoleId(), role.getName(), currentConfig.getId());
            return;
        }

        boolean flag = RandomUtil.isGenerate(currentConfig.getProbability() + role.findFaQiHuiYuanConfig().getZhuling());
        //是否成功
        if (flag) {
            soulMap.put(pos, nextConfig.getLevel());

            DataCenter.updateData(roleInjectSoul);

            AttributeManager.getInstance().attributeCount(role);

            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.INJECT_SOUL_LEVEL);

            reqInjectSoulInfo(role);
        }

        log.info("注灵升级, roleId: {} , roleName: {} , 是否成功: {} , pos: {} , level: {} -> {}" , role.getRoleId(), role.getName(), flag, pos, level, soulMap.getOrDefault(pos, 0));

        ResInjectSoulResultMessage msg = new ResInjectSoulResultMessage();
        msg.setResult(flag);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 请求获取注灵信息
     *
     * @param role 角色
     */
    @Override
    public void reqInjectSoulInfo(Role role) {
        ResInjectSoulInfoMessage msg = new ResInjectSoulInfoMessage();
        RoleInjectSoul roleInjectSoul = find(role.getRoleId());
        InjectSoulCache cache = ConfigCacheManager.getInstance().getCache(InjectSoulCache.class);
        for (Map.Entry<Integer, Integer> entry : roleInjectSoul.getSoulMap().entrySet()) {
            InjectSoulBean bean = new InjectSoulBean();
            bean.setPos(entry.getKey());
            InjectSoulConfig config = cache.getConfig(entry.getKey(), entry.getValue());
            if (config == null) {
                continue;
            }
            bean.setConfigId(config.getId());
            msg.getInjectSoulBeanList().add(bean);
        }
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 主角装备变化事件
     *
     * @param role      角色
     * @param changes   变化的装备
     */
    @Override
    public void onRoleEquipChanged(Role role, List<ItemChange> changes) {

    }

    /**
     * 英雄装备变化事件
     *
     * @param hero      英雄
     * @param changes   变化的装备
     */
    @Override
    public void onHeroEquipChanged(Hero hero, List<ItemChange> changes) {
    }

//    /**
//     * 登录结束
//     * 由于需求变更，已上线的项链注灵数据需迁移至新的数据存储中
//     *
//     * @param role 角色
//     */
//    @Override
//    public void onRoleLogin(Role role) {
//        RoleInjectSoul injectSoul = find(role.getRoleId());
//        if (injectSoul.getCfgId() > 0) {
//            InjectSoulConfig config = ConfigDataManager.getInstance().getById(InjectSoulConfig.class, injectSoul.getCfgId());
//            if (config == null) {
//                return;
//            }
//            Map<Integer, Integer> soulMap = injectSoul.getSoulMap();
//            soulMap.put(EquipConst.EquipIndex.POS_NECKLACE.getCls(), config.getLevel());
//            injectSoul.setCfgId(0);
//            DataCenter.updateData(injectSoul);
//            log.info("注灵-项链注灵数据迁移, roleId: {} , roleName: {} , cfgId: {} -> {}", role.getRoleId(), role.getName(), config.getId(), injectSoul.getCfgId());
//            onRoleAttributeChange(role);
//        }
//    }
}
