package com.sh.game.script.activity.giftpack;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Script(order = 1)
public class ActivityZhouKanPackScript extends AbstractGiftPackScript {
    @Override
    public int getType() {
        return ActivityConst.ZHOU_KAN_PACK;
    }
}
