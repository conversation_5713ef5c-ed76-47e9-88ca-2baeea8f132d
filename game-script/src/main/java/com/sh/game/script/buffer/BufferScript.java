package com.sh.game.script.buffer;

import com.sh.game.GameContext;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.entity.buff.Buffs;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleDieScript;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleStoreBuyScript;
import com.sh.game.log.entity.RoleDieLog;
import com.sh.game.scene.MapProxy;
import com.sh.game.system.buffer.script.IBufferScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Map;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR> zhaoliyang
 * @date : 2018-05-30 16:08
 */
@Script
@Slf4j
public class BufferScript implements IBufferScript,
        IEventOnRoleDieScript,
        IEventOnRoleLoginScript,
        IEventOnRoleStoreBuyScript {


    @Override
    public void onRoleDie(Role role, long killerId, String killerName, MapProxy mapProxy, int x, int y, int dropAdd, int dropResist) {
        //死亡日志
        RoleDieLog dieLog = new RoleDieLog(role);
        if (mapProxy != null) {
            dieLog.setMapID(mapProxy.getCfgId());
        }
        RoleSummary summary = SummaryManager.getInstance().getSummary(killerId);
        if (summary != null) {
            dieLog.setKillerID(killerId);
            dieLog.setKillerName(summary.getName());
            dieLog.setKillerLevel(summary.getLevel());
        }
        dieLog.submit();

        //副本不移除狂暴之力
        String globalValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.VIOLENT_POWER_NOT_VANISH);
        if (ArrayUtils.contains(globalValue.split(Symbol.JINHAO), Integer.toString(mapProxy.getCfgId()))) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Buffs buffs = roleAdvance.getBuffs();
        if (!buffs.isViolentPower()) {
            return;
        }
        if (!DataCenter.hasRole(killerId)) {
            return;
        }
        AppContext.getContext().sendMail(killerId, EmailConst.MailId.VIOLENT_POWER_REWARD, null);
    }

    @Override
    public void onRoleLogin(Role role) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Buffs buffs = roleAdvance.getBuffs();
        boolean violentPower = false;
        for (Map.Entry<Integer, Buff> entry : buffs.getBuffMap().entrySet()) {
            if (entry.getKey() == BuffConst.BuffId.VIOLENT_POWER) {
                Buff buff = entry.getValue();
                if (buff.getExpire() > TimeUtil.getNowOfMills()) {
                    violentPower = true;
                }
            }
        }
        buffs.setCanFly(true);
        buffs.setViolentPower(violentPower);
        DataCenter.updateData(roleAdvance);
    }

    @Override
    public void onRoleStoreBuy(Role role, int id, int count) {
        if (id != StoreConst.StoreId.VIOLENT_POWER) {
            return;
        }
        RoleAdvance roleAdvance = role.getRoleAdvance();
        Buffs buffs = roleAdvance.getBuffs();
        buffs.setViolentPower(true);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.VIOLENT_POWER);
        DataCenter.updateData(roleAdvance);
    }
}
