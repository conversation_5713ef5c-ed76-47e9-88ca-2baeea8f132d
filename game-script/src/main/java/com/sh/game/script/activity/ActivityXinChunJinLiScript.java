package com.sh.game.script.activity;

import com.alibaba.fastjson.JSON;
import com.sh.game.common.communication.msg.abc.bean.CommonRankingWithRoleSimpleBean;
import com.sh.game.common.communication.msg.abc.bean.RoleSimpleBean;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.event.IEventScheduleUpdateOnHourScript;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.XinChunJinLiData;
import com.sh.game.system.activity.script.IActivityXinChunJinLiScript;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.rank.RankManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2022/12/27 14:58
 */
@Slf4j
@Script
public class ActivityXinChunJinLiScript extends AbstractActivityScript implements IEventScheduleUpdateOnHourScript, IActivityXinChunJinLiScript, IEventOnRoleRechargedScript {
    /**
     * 活动类型
     *
     * @return
     */
    @Override
    public int getType() {
        return ActivityConst.XIN_CHUN_JIN_LI;
    }

    @Override
    public void scheduleUpdateOnHour() {
        ActivitySchedule schedule = getAvailableSchedule(null);
        if (schedule == null) {
            return;
        }
        int time = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIN_CHUN_JIN_LI_REWARD_TIME);
        int nowHour = TimeUtil.getNowHour();
        if (nowHour != time) {
            return;
        }
        AtomicBoolean flag = new AtomicBoolean(false);
        CommonData data = SysDataProvider.get(CommonData.class);
        data.getJinLiDataMap().compute(schedule.getActivityID(), (k, v) -> {
            if (v == null) {
                v = new XinChunJinLiData();
            }

            if (TimeUtil.isSameDay(v.getLastChouJiangTime(), TimeUtil.getNowOfMills())) {
                return v;
            }
            flag.set(true);
            v.setLastChouJiangTime(TimeUtil.getNowOfMills());
            v.getRewardMap().clear();

            Set<Long> idSet = v.getIdSet();
            log.info("新春锦鲤，开始抽奖，活动id {} 参与玩家数量 {} 参与详情 {}", schedule.getActivityID(), idSet.size(), JSON.toJSONString(idSet));
            LinkedList<Long> list = new LinkedList<>(idSet);
            Collections.shuffle(list);

            int i = 1;
            Long rid;
            while (i < 4 && (rid = list.poll()) != null) {
                Role role = DataCenter.get(Role.class, rid);
                if (role == null) {
                    continue;
                }
                v.getRewardMap().put(i, rid);
                List<int[]> reward;
                switch (i) {
                    case 1:
                        reward = GlobalUtil.findItemCost(GameConst.GlobalId.XIN_CHUN_JIN_LI_FIRST);
                        break;
                    case 2:
                        reward = GlobalUtil.findItemCost(GameConst.GlobalId.XIN_CHUN_JIN_LI_SECOND);
                        break;
                    case 3:
                        reward = GlobalUtil.findItemCost(GameConst.GlobalId.XIN_CHUN_JIN_LI_THIRD);
                        break;
                    default:
                        return v;
                }
                MailManager.getInstance().sendMail(rid, EmailConst.MailId.XIN_CHUN_JIN_LI_REWARD_MAIL, EmailConst.toMailAttach(reward, LogAction.XIN_CHUN_JIN_LI_REWARD));
                log.info("新春锦鲤，玩家获奖，活动id {} 玩家id {} name {} 名次 {}", schedule.getActivityID(), role.getId(), role.getName(), i);
                i++;
            }


            v.getIdSet().clear();
            return v;
        });
        if (flag.get()) {
            DataCenter.updateData(data);
        }
    }


    @Override
    public void info(Role role) {
//        ActivitySchedule schedule = getAvailableSchedule(role);
//        if (schedule == null) {
//            return;
//        }
//        CommonData data = SysDataProvider.get(CommonData.class);
//        XinChunJinLiData jinLiData = data.getJinLiDataMap().computeIfAbsent(schedule.getActivityID(), k -> new XinChunJinLiData());
//        Map<Integer, Long> rewardMap = jinLiData.getRewardMap();
//
//        ResXinChunJinLiInfoMessage msg = new ResXinChunJinLiInfoMessage();
//        ActivityAppendProtos.ResXinChunJinLiInfo.Builder protoBuilder = ActivityAppendProtos.ResXinChunJinLiInfo.newBuilder();
//        for (Map.Entry<Integer, Long> entry : rewardMap.entrySet()) {
//            Integer num = entry.getKey();
//            Long rid = entry.getValue();
//            RoleSummary summary = SummaryManager.getInstance().getSummary(rid);
//            if (summary == null) {
//                continue;
//            }
//            AbcProtos.CommonRankingWithRoleSimpleBean.Builder bean = AbcProtos.CommonRankingWithRoleSimpleBean.newBuilder();
//            bean.setRanking(num);
//            bean.setUid(summary.getId());
//            bean.setName(summary.getName());
//
//            AbcProtos.RoleSimpleBean roleSimpleBean = RankManager.getInstance().roleSummaryToSimpleBean(summary);
//            bean.setPlayer(roleSimpleBean);
//            protoBuilder.addBeans(bean);
//        }
//        msg.setProto(protoBuilder.build());
//        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int time = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIN_CHUN_JIN_LI_REWARD_TIME);
        int nowHour = TimeUtil.getNowHour();
        if (nowHour >= time) {
            return;
        }

        AtomicBoolean flag = new AtomicBoolean(false);

        CommonData data = SysDataProvider.get(CommonData.class);
        data.getJinLiDataMap().compute(schedule.getActivityID(), (k, v) -> {
            if (v == null) {
                v = new XinChunJinLiData();
            }
            if (!v.getIdSet().contains(role.getId())) {
                v.getIdSet().add(role.getId());
                log.info("新春锦鲤，玩家充值，玩家获得锦鲤抽取资格，活动id {} 玩家id {} name {}", schedule.getActivityID(), role.getId(), role.getName());
                flag.set(true);
            }
            return v;
        });
        if (flag.get()) {
            DataCenter.updateData(data);
        }
    }
}
