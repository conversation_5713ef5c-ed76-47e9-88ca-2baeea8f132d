package com.sh.game.script.lixianReward;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.lixianReward.ResLiXianRewardInfoMessage;
import com.sh.game.common.config.model.LiXianRewardConfig;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.ValueCardConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleBarrier;
import com.sh.game.common.entity.usr.RoleLiXian;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.system.activity.impl.ActivityValueCardManager;
import com.sh.game.system.lixianReward.script.ILiXianRewardScript;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Script
public class LiXianRewardScript implements ILiXianRewardScript, IEventOnRoleLoginScript {

    @Override
    public void sendMsg(Role role) {
        ResLiXianRewardInfoMessage msg = new ResLiXianRewardInfoMessage();
        msg.setOfflineTime(role.findRoleLiXian().getTime());
        LiXianRewardConfig config = getConfigByBarrier(role);
        if (config != null) {
            msg.setCid(config.getId());
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void receiveReward(Role role) {
        LiXianRewardConfig config = getConfigByBarrier(role);
        if (config == null) {
            log.warn("玩家领取离线奖励，找不到离线奖励配置。玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        if (!ConditionUtil.validate(role, config.getCondition())) {
            log.warn("玩家领取离线奖励，条件不满足。玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        RoleLiXian roleLiXian = role.findRoleLiXian();
        int offLineTime = roleLiXian.getTime();
        //校验离线时长
        if (offLineTime < 60) {
            log.warn("玩家领取离线奖励，离线时长小于配置的离线时长。玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        int maxTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.LI_XIAN_TIME_LIMIT);
        if (maxTime <= 0) {
            log.warn("玩家领取离线奖励，未配置离线时长上限。玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }

        int tqCount = ActivityValueCardManager.getInstance()
                .getTeQuanCardList(role, ValueCardConst.ValueCardEquity.LI_XIAN_REWARD_DOUBLE).size(); //特权月卡
        tqCount = tqCount > 0 ? tqCount * 2 : 1;
        int realOffLineTime = Math.min(offLineTime / 60, maxTime);
        List<int[]> reward = new ArrayList<>();
        for (int[] r : config.getReward()) {
            if (r == null || r.length < 2) {
                continue;
            }
            int value = (int) ((long) r[1] * realOffLineTime);
            reward.add(new int[]{r[0], value * tqCount});
        }
        //特殊奖励
        List<List<int[]>> specialReward = config.getSpecialReward();
        List<Integer> probList = config.getSpecialProb();
        for (int i = 0; i < specialReward.size(); i++) {
            List<int[]> special = specialReward.get(i);
            if (CollectionUtils.isEmpty(special)) {
                continue;
            }
            if (!RandomUtil.isGenerate(probList.get(i))) {
                continue;
            }
            for (int[] r : special) {
                if (r == null || r.length < 2) {
                    continue;
                }
                int value = (int) ((long) r[1] * realOffLineTime);
                reward.add(new int[]{r[0], value * tqCount});
            }
        }
        roleLiXian.setTime(0);
        DataCenter.updateData(roleLiXian);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(reward);
        stash.commitOrToMail(role, LogAction.BARRIER_LI_XIAN_REWARD, false);

        sendMsg(role);
        RewardInfoManager.getInstance().resRewardInfo(role, TurnBasedConst.BattleType.LI_XIAN_REWARD, reward);
        log.info("玩家领取离线奖励#领取成功,玩家:{}->{}", role.getId(), role.getName());
    }

    private LiXianRewardConfig getConfigByBarrier(Role role) {
        List<LiXianRewardConfig> list = ConfigDataManager.getInstance().getList(LiXianRewardConfig.class);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        RoleBarrier barrier = role.findBarrier();
        int barrierId = barrier.getMaxBarrierNo();
        if (barrierId == -1) {
            return null;
        }
        // 找到小于 barrierId 的最大配置项
        LiXianRewardConfig config = null;
        for (LiXianRewardConfig cfg : list) {
            if (cfg.getMapLevelId() <= barrierId) {
                if (config == null || cfg.getMapLevelId() >= config.getMapLevelId()) {
                    config = cfg;
                }
            }
        }

        return config;
    }

    @Override
    public void onRoleLogin(Role role) {
        LiXianRewardConfig config = getConfigByBarrier(role);
        if (config == null) {
            return;
        }
        int offLineTime = role.getRoleLogin().getLoginTime() - role.getRoleLogin().getLogoutTime();
        RoleLiXian roleLiXian = role.findRoleLiXian();
        roleLiXian.setTime(roleLiXian.getTime() + offLineTime);
        DataCenter.updateData(roleLiXian);
        sendMsg(role);
    }
}
