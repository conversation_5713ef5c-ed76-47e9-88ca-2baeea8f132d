package com.sh.game.script.pets;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.pets.ResChongWuInfoMessage;
import com.sh.game.common.config.model.ChongWuConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.pets.script.IRoleChongWuScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Script
@Slf4j
public class RoleChongWuScript implements IRoleChongWuScript {

    @Override
    public void reqRoleChongWuUp(Role role) {
        ChongWuConfig config = ConfigDataManager.getInstance().getById(ChongWuConfig.class, role.getRoleChongWuCfgId());
        if (config == null) {
            log.error("宠物系统-升级-找不到配置表,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
            return;
        }
        ChongWuConfig nextConfig = ConfigDataManager.getInstance().getById(ChongWuConfig.class, config.getNextId());
        if (nextConfig == null) {
            log.error("宠物系统-升级-找不到下一级配置表,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCostitem());
        if (!stash.commit(role, LogAction.CHONGWU_UP)) {
            log.error("宠物系统-升级-提交失败,扣除道具失败,角色:{},昵称:{},扣除道具:{}", role.getId(), role.getName(), config.getCostitem());
            return;
        }
        role.setRoleChongWuCfgId(nextConfig.getId());
        DataCenter.updateData(role);

        sendMsg(role);
        role.proxyCall(proxyPlayer -> proxyPlayer.chongWuUpdate(role));

        log.info("宠物系统-升级-宠物升级成功,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());

    }

    @Override
    public void unlockRoleChongWu(Role role, int cfgId) {
        if (role.getRoleChongWuCfgId() != 0) {
            log.error("宠物系统-解锁-已解锁不可重复解锁,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
            return;
        }
        ChongWuConfig inItConfig = ConfigDataManager.getInstance().getById(ChongWuConfig.class, cfgId);
        if (inItConfig == null) {
            return;
        }
        if (inItConfig.getFirstId() != cfgId) {
            log.error("宠物系统-解锁-宠物id不是初始化id,角色:{},昵称:{},配置id:{}", role.getRoleId(), role.getName(), cfgId);
            return;
        }
        role.setRoleChongWuCfgId(cfgId);
        DataCenter.updateData(role);

        sendMsg(role);
        role.proxyCall(proxyPlayer -> proxyPlayer.chongWuUpdate(role));

        log.info("宠物系统-解锁-解锁宠物成功,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
    }

    @Override
    public void reqReselectRoleChongWu(Role role, int cfgId) {
        ChongWuConfig roleConfig = ConfigDataManager.getInstance().getById(ChongWuConfig.class, role.getRoleChongWuCfgId());
        if (roleConfig == null) {
            log.error("宠物系统-重新选择宠物-找不到配置表,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
            return;
        }
        ChongWuConfig reSelectConfig = ConfigDataManager.getInstance().getById(ChongWuConfig.class, cfgId);
        if (reSelectConfig == null) {
            log.error("宠物系统-重新选择宠物-找不到配置表,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
            return;
        }
        if (roleConfig.getType() == reSelectConfig.getType()) {
            log.error("宠物系统-重新选择宠物-类型一致,角色:{},昵称:{},玩家宠物配置id:{},类型:{},传入类型:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId(), roleConfig.getType(), reSelectConfig.getType());
            return;
        }
        if (roleConfig.getLevel() != reSelectConfig.getLevel()) {
            log.error("宠物系统-重新选择宠物-等级不一致,角色:{},昵称:{},玩家宠物id:{},等级:{}传入参数id:{},等级:{}", role.getRoleId(), role.getName(),
                    role.getRoleChongWuCfgId(), roleConfig.getLevel(), reSelectConfig.getId(), reSelectConfig.getLevel());
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(reSelectConfig.getCost());
        if (!stash.commit(role, LogAction.CHONGWU_RESELECT)) {
            log.error("宠物系统-重新选择宠物-提交失败,扣除道具失败,角色:{},昵称:{},扣除道具:{}", role.getId(), role.getName(), reSelectConfig.getCost());
            return;
        }

        role.setRoleChongWuCfgId(cfgId);
        DataCenter.updateData(role);
        role.proxyCall(proxyPlayer -> proxyPlayer.chongWuUpdate(role));

        sendMsg(role);

        log.info("宠物系统-重新选择宠物-重新选择宠物成功,角色:{},昵称:{},玩家宠物配置id:{}", role.getRoleId(), role.getName(), role.getRoleChongWuCfgId());
    }

    @Override
    public void reqRoleChongWuInfo(Role role) {
        sendMsg(role);
    }

    /**
     * 返回宠物信息
     * @param role
     */
    private void sendMsg(Role role){
        int rolePetsCfgId = role.getRoleChongWuCfgId();
        ResChongWuInfoMessage msg = new ResChongWuInfoMessage();
        msg.setCfgId(rolePetsCfgId);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void chongWuAttributeChange(Role role) {

    }
}
