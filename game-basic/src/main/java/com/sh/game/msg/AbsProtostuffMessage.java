package com.sh.game.msg;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import java.util.HashSet;
import java.util.Set;

public abstract class AbsProtostuffMessage extends AbstractMessage {

    public static final Set<String> EXCLUSIONS = new HashSet<String>() {
        {
            add("length");
            add("sequence");
            add("processorId");
            add("session");
            add("hostId");
            add("content");
        }
    };

    @Override
    public void decode(byte[] bytes) {
        if (bytes.length > 0) {
            SerializationUtil.deserialize(bytes, this, EXCLUSIONS);
        }
    }

    @Override
    public byte[] encode() {
        return SerializationUtil.serialize(this, EXCLUSIONS);
    }


    @Override
    public boolean write(KryoOutput kryoOutput) {
        return true;
    }

    @Override
    public boolean read(KryoInput kryoInput) {
        return true;
    }

}

