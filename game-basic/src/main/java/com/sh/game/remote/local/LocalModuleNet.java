package com.sh.game.remote.local;

import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.server.ModuleNet;
import com.sh.game.remote.rpc.server.RPCServerConsumer;
import lombok.extern.slf4j.Slf4j;

/**
 * LocalModuleNet
 *
 * <AUTHOR>
 * @date 2020/9/7 16:44
 */
@Slf4j
public class LocalModuleNet extends ModuleNet {

    private RPCServerConsumer consumer = new RPCServerConsumer(this);


    public RPCConnection createLocalConnection(RPCConnection clientConnection) {
        LocalRPCConnection serverConnection = new LocalRPCConnection();
        serverConnection.setHostId(this.getHostId());
        serverConnection.setPeerConsumer(this.consumer);
        serverConnection.setHost("localhost");
        serverConnection.setPort(this.port);
        return serverConnection;
    }

    public void register(RPCConnection connection) {
        clientMap.put(connection.getHostId(), connection);
    }

}
