package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.msg.DestroyMessage;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.game.server.AbstractHandlerPool;
import com.sh.server.MessageHandler;
import com.sh.server.AbstractMessage;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;

/**
 * ClientHandlerPool
 *
 * <AUTHOR>
 * @date 2020/8/27 14:28
 */
@Slf4j
public class ClientHandlerPool extends AbstractHandlerPool {

    private ClientMessageHandler clientMessageHandler;

    private Map<Integer, Constructor<? extends MessageHandler<? extends AbstractMessage>>>
                constructorMap = new HashMap<>();



    public ClientHandlerPool(ClientMessageHandler clientMessageHandler) {

        this.clientMessageHandler = clientMessageHandler;
        //notice传输
        register(new NoticeTransform(), ClientMessageHandler.NoticeTransformHandler.class, (byte)21);

        //message传输
        register(new MessageTransform(), ClientMessageHandler.MessageTransformHandler.class, (byte)21);

        //心跳
        register(new PingMessage(), ClientMessageHandler.PingHandler.class, (byte)22);

        //请求注册
        register(new RegisterMessage(),ClientMessageHandler.RegisterHandler.class, (byte)22);

        //对端销毁
        register(new DestroyMessage(), ClientMessageHandler.DestroyHandler.class, (byte)22);
    }

    @Override
    public MessageHandler<? extends AbstractMessage> getHandler(int messageId) {

        try {
            Constructor<? extends MessageHandler<? extends AbstractMessage>> constructor
                    = constructorMap.get(messageId);
            MessageHandler<? extends AbstractMessage> handler
                    = constructor.newInstance(clientMessageHandler);
            return handler;
        } catch (Exception e) {
            log.error("获取handler实例发生错误, msgId:" + messageId, e);
        }
        return null;
    }

    public <T extends AbstractMessage> void register(T message, Class<? extends MessageHandler<T>> handlerClazz, byte queueId) {
        super.register(message, handlerClazz, queueId);
        try {
            Constructor<? extends MessageHandler<? extends AbstractMessage>> constructor
                    = handlerClazz.getDeclaredConstructor(clientMessageHandler.getClass());
            constructorMap.put(message.getId(), constructor);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException("handler注册发生错误", e);
        }

    }

}
