package com.sh.game.server;

import com.sh.server.MessageHandler;
import com.sh.server.AbstractMessage;
import com.sh.server.HandlerPool;
import com.sh.server.MessageHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/7 15:14
 */
@Slf4j
public class AbstractHandlerPool implements IHandlerPool {


    protected Map<Integer, Byte> queueIdMap = new HashMap<>();

    protected Map<Integer, Class<? extends MessageHandler<? extends AbstractMessage>>> handlerPool = new HashMap<>();


    @Override
    public MessageHandler<? extends AbstractMessage> getHandler(int messageId) {
        Class<? extends MessageHandler<? extends AbstractMessage>> clazz = handlerPool.get(messageId);
        if (clazz != null) {
            try {
                MessageHandler<? extends AbstractMessage> handler = clazz.getDeclaredConstructor().newInstance();
                return handler;
            } catch (Exception e) {
                log.error("获取handler实例发生错误, msgId:" + messageId, e);
                return null;
            }
        }
        return null;
    }


    public byte getProcessorId(int messageId) {
        Byte processorId = queueIdMap.get(messageId);
        if (processorId == null) {
            return 0;
        }
        return processorId;
    }


    public void merge(AbstractHandlerPool pool) {

        pool.handlerPool.forEach((k, v) -> {
            Class<? extends MessageHandler<? extends AbstractMessage>> handlerClazz = pool.handlerPool.get(k);
            byte queueId = pool.queueIdMap.get(k);

            if (this.handlerPool.containsKey(k)) {
                Class<? extends MessageHandler<? extends AbstractMessage>> existHandler = this.handlerPool.get(k);
                throw new RuntimeException("消息号[{"+k+"}]注册重复,已注册：{"+existHandler.getName()+"},准备注册：{"+v.getName()+"}");
            }

            this.handlerPool.put(k, handlerClazz);
            this.queueIdMap.put(k, queueId);
        });

    }


    public <T extends AbstractMessage> void register(T message, Class<? extends MessageHandler<T>> handlerClazz, byte queueId) {

        if (this.handlerPool.containsKey(message.getId())) {
            Class<? extends MessageHandler<? extends AbstractMessage>>  existHandler = this.handlerPool.get(message.getId());
            log.warn("消息号[{"+message.getId()+"}]注册重复,已注册：{"+existHandler.getName()+"},准备注册：{"+existHandler.getClass().getName()+"}");
            return;
        }

        queueIdMap.put(message.getId(), queueId);
        handlerPool.put(message.getId(), handlerClazz);
    }




    protected <T extends AbstractMessage> void register(T message, Class<? extends MessageHandler<T>> handlerClazz) {
        register(message, handlerClazz, (byte)0);
    }
}
