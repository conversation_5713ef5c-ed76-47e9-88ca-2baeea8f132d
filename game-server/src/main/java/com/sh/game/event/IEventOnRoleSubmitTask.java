package com.sh.game.event;

import com.sh.game.common.config.model.TaskConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.script.IScript;

import java.util.List;

/**
 * 玩家玩成任务事件( 用于处理完成特定任务时增加某计数,)
 */
public interface IEventOnRoleSubmitTask  extends  IScript {

    void OnRoleSubmitTaskEvent(Role role, TaskConfig taskConfig, TaskRecord record );
}
