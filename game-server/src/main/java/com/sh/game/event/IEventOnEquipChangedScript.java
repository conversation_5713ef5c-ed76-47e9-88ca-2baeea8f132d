package com.sh.game.event;

import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.role.entity.Hero;
import com.sh.script.IScript;

import java.util.List;

/**
 * 装备变化计算
 */
public interface IEventOnEquipChangedScript extends IScript {

    /**
     * 主角装备变化
     *
     * @param role
     * @param changes
     */
    void onRoleEquipChanged(Role role, List<ItemChange> changes);


    /**
     * 英雄装备变化
     *
     * @param hero
     * @param changes
     */
    @Deprecated
    default void onHeroEquipChanged(Hero hero, List<ItemChange> changes){

    }

    /**
     * 俠侶装备变化
     *
     * @param role
     * @param changes
     */
    default void onXiaLvEquipChanged(Role role, List<ItemChange> changes) {

    }

    /**
     * 兽决装备变化
     */
    default void onRoleShouJueEquipChanged(Role role, List<ItemChange> changes) {

    }

    /**
     * 命格装备变化
     */
    default void onRoleBirthChartEquipChanged(Role role, List<ItemChange> changes) {

    }

}
