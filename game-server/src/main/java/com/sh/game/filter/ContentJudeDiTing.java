package com.sh.game.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chat.ChatManager;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
/**
 * 贪玩sdk接口
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/10/12.
 */
@Slf4j
public class ContentJudeDiTing implements ContentJude {
    /**
     * api验证线程
     */
    private ExecutorService executor = Executors.newFixedThreadPool(4, r -> new Thread(r, "contentJude"));



    @Override
    public void replaceBadWord(ChatContent chatContent) {

        executor.execute(() -> {
            Role role = DataCenter.get(Role.class, chatContent.getSenderId());
            String content = chatApi(role, chatContent.getChannel(), chatContent.getTargetId(), chatContent.getContent());
            if (content ==null){
                return;
            }
            GameContext.getGameServer().getRouter().process(ProcessorId.SERVER_PLAYER, new AbstractCommand() {
                @Override
                public void doAction() {
                    ChatManager.getInstance().sendChatMessage(role, chatContent.getChannel(), chatContent.getTargetId(), content, chatContent.getReplaces(), chatContent.getBanFlag());
                }
            }, role.getId());

        });
    }


    private String chatApi(Role role, int channel, long target, String content) {
        Map<String, Object> params = new HashMap<>();
        params.put("data_id", IDUtil.getId(IDConst.ATOMIC));
        params.put("context", content);
        params.put("context_type", "chat");
        params.put("token", "4DB96A6BD7270FF6");

        User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
        if (user != null) {
            params.put("user_id", user.getAccount());
            params.put("ip", user.getIp());
        }
        params.put("role_id", role.getId());
        params.put("area", GameContext.getServerId());

        params.put("room_id", channel);
        params.put("channel_id", channel);
        params.put("game_version", "2.1.4");

      //  http://gtf.ai.xingzhezi.cn/v2.0/game_chat_ban/detect_text
        String ret = HttpUtil.post("http://gtf.ai-abroad.xingzheai.cn/v2.0/game_chat_ban/detect_text", params, 3000);
        if (ret != null) {
            try {
                JSONObject retJson = JSON.parseObject(ret);
                if (retJson.getInteger("code") != 0) {
                    log.error("聊天发送失败：{}", ret);
                    TipUtil.show(role, CommonTips.服务_聊天内容包含敏感信息);
                    return null;
                }
            } catch (Exception e) {
                log.error("访问运营方聊天接口失败: {}", e.getMessage());
            }
        }

//        params.put("platform_id", 1);
//        params.put("game_type_id", 11);
//        params.put("base_game_id", 868);
//        params.put("game_id", 6414);
//        params.put("game_server_id", GameContext.getServerId());
//        params.put("game_server_name", GameContext.getServerId());
//        params.put("game_server_open", GameContext.getOpenTime().toEpochSecond(ZoneOffset.of("+8")));
//
//        User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
//        if (user != null) {
//            params.put("uid", user.getAccount());
//            params.put("username", user.getUsername());
//            params.put("ip", user.getIp());
//        }
//        params.put("role_id", role.getId());
//        params.put("role_name", role.getName());
//        params.put("role_occupation", RoleConst.CAREER_NAMES[role.getCareer()]);
//        params.put("role_level", role.getLevel());
//        params.put("role_create_time", role.getRoleLogin().getCreateTime());
//        params.put("role_pay", role.getRecharge().getRechargedTotal() / 100.0);
//
//        if (channel == ChatConst.ChatType.PERSON) {
//            Role receiver = SessionManager.getInstance().getRole(target);
//            if (receiver != null) {
//                User receiverUser = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(receiver.getId()));
//                if (receiverUser != null) {
//                    params.put("to_uid", receiverUser.getAccount());
//                    params.put("to_username", receiverUser.getUsername());
//                }
//                params.put("to_role_id", receiver.getId());
//                params.put("to_role_name", receiver.getName());
//                params.put("to_role_pay", receiver.getRecharge().getRechargedTotal());
//            }
//        }
//        int content_type = 9;
//        switch (channel) {
//            case ChatConst.ChatType.PERSON:
//                content_type = 1;
//                break;
//            case ChatConst.ChatType.WORLD:
//                content_type = 4;
//                break;
//            case ChatConst.ChatType.UNION:
//                content_type = 6;
//                break;
//            case ChatConst.ChatType.GROUP:
//                content_type = 7;
//                break;
//            case ChatConst.ChatType.NEAR:
//                content_type = 8;
//                break;
//        }
//        params.put("content_type", content_type);
//        params.put("content", content);
//        int now = TimeUtil.getNowOfSeconds();
//        params.put("send_time", now);
//        params.put("time", now);

//        List<String> keys = new ArrayList<>(params.keySet());
//        Collections.sort(keys);
//        StringBuilder builder = new StringBuilder();
//        for (String key : keys) {
//            builder.append(key);
//            builder.append("=");
//            builder.append(params.get(key));
//            builder.append("&");
//        }
//        String sign = Md5Util.md5(builder.toString().substring(0, builder.length() - 1) + "-" + "FKLAJSDfdksaj78ASFjJFADVSDFS");
//        params.put("sign", sign);
//
//        String ret = HttpUtil.post("http://gtf.ai.xingzhezi.cn/v2.0/game_chat_ban/detect_text", params, 3000);
//        if (ret != null) {
//            try {
//                JSONObject retJson = JSON.parseObject(ret);
//                if (retJson.getInteger("code") != 10009) {
//                    log.error("聊天发送失败：{}", ret);
//                    TipUtil.show(role, CommonTips.服务_聊天内容包含敏感信息);
//                    return null;
//                }
//            } catch (Exception e) {
//                log.error("访问运营方聊天接口失败: {}", e.getMessage());
//            }
//        }

        return content;
    }

}
