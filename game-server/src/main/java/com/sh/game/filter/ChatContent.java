package com.sh.game.filter;

import com.sh.commons.tuple.TwoTuple;
import com.sh.concurrent.AbstractCommand;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/10/12.
 */
@Getter
@Setter
public class ChatContent {

    private long senderId;
    
    private String senderName;

    private int channel;

    private long targetId;

    private List<TwoTuple<String, String>> replaces;

    private int banFlag;

    private String content;

    private AbstractCommand callBack;

}
