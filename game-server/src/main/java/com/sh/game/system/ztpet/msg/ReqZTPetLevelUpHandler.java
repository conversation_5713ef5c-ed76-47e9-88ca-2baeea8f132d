package com.sh.game.system.ztpet.msg;

import com.sh.game.common.communication.msg.system.ztPet.ReqZTPetLevelUpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.ztpet.ZTPetManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求宠物升级
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqZTPetLevelUpHandler extends MessageHandler<ReqZTPetLevelUpMessage> {
    @Override
    public void doAction(ReqZTPetLevelUpMessage msg) {
        ZTPetManager.getInstance().reqZTPetLevelUp(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }
}
