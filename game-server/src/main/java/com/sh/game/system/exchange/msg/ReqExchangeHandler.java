package com.sh.game.system.exchange.msg;

import com.sh.game.common.communication.msg.system.exchange.ReqExchangeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.exchange.ExchangeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求兑换</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqExchangeHandler extends AbstractHandler<ReqExchangeMessage> {

    @Override
    public void doAction(ReqExchangeMessage msg) {
        ExchangeManager.getInstance().exchange(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }
}
