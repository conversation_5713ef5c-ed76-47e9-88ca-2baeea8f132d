package com.sh.game.system.user.msg;

import com.sh.game.system.user.UserManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.user.ReqHeartMessage;

/**
 * <p>心跳请求</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-07-31 14:48:10
 */
public class ReqHeartHandler extends AbstractHandler<ReqHeartMessage> {

    @Override
    public void doAction(ReqHeartMessage msg) {
        UserManager.getInstance().clientHeartbeat(msg.getSession());
    }

}
