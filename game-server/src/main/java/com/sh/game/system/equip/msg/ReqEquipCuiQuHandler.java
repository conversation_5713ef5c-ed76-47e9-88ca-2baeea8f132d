package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqEquipCuiQu;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.MessageHandler;

/**
 * 请求装备萃取
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEquipCuiQuHandler extends MessageHandler<ReqEquipCuiQu> {
    @Override
    public void doAction(ReqEquipCuiQu msg) {
        EquipManager.getInstance().reqEquipCuiQu(SessionUtil.getRole(msg.getSession()), msg.getEquipId());
    }
}
