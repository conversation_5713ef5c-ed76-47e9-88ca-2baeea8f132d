package com.sh.game.system.trade;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.trade.script.ITradeScript;
import com.sh.script.ScriptEngine;

/**
 * 交易模块
 */
public class TradeManager {

    private static final TradeManager INSTANCE = new TradeManager();

    private TradeManager() {
    }

    public static TradeManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求交易
     *
     * @param role  发起者
     * @param oppId 接收者
     */
    public void reqTradeTrading(Role role, long oppId) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeTrading(role, oppId));
    }

    /**
     * 同意交易
     *
     * @param role role
     */
    public void reqTradeAccept(Role role) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeAccept(role));
    }

    /**
     * 请求拒绝交易
     *
     * @param role   role
     * @param roleId 被拒绝者id
     * @param type   1主动拒绝 2未响应 3不在附近
     */
    public void reqTradeRefuse(Role role, long roleId, int type) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeRefuse(role, roleId, type));
    }

    /**
     * 请求添加交易道具
     *
     * @param role  role
     * @param where 道具来源
     * @param index 来源中的位置
     * @param count 数量
     * @param grid  交易列表位置
     */
    public void reqTradeAppend(Role role, int where, int index, int count, int grid) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeAppend(role, where, index, count, grid));
    }

    /**
     * 请求移除交易道具
     *
     * @param role role
     * @param grid 交易列表位置
     */
    public void reqTradeRemove(Role role, int grid) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeRemove(role, grid));
    }

    /**
     * 请求交易上锁
     *
     * @param role role
     */
    public void reqTradeLock(Role role) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeLock(role));
    }

    /**
     * 请求交易解锁
     *
     * @param role role
     */
    public void reqTradeUnLock(Role role) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeUnLock(role));
    }

    /**
     * 请求取消交易
     *
     * @param rid 玩家id
     */
    public void reqTradeCancel(long rid) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeCancel(rid));
    }

    /**
     * 请求完成交易
     *
     * @param role role
     */
    public void reqTradeDeal(Role role) {
        ScriptEngine.invoke1t1(ITradeScript.class, script -> script.reqTradeDeal(role));
    }

}
