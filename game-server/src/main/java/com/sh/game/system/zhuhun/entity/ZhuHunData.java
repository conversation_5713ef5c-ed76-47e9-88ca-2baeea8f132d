package com.sh.game.system.zhuhun.entity;

import com.sh.commons.tuple.ThreeTuple;
import com.sh.commons.tuple.TwoTuple;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 铸魂数据
 *
 * <AUTHOR>
 * @date 2022/09/22 20:12
 */
@Getter
@Setter
public class ZhuHunData {

    /**
     * 铸魂次数
     */
    @Tag(1)
    private int count;

    /**
     * 当前铸魂id列表
     * first:   铸魂id
     * second:  是否加锁
     */
    @Tag(2)
    private List<TwoTuple<Integer, Boolean>> idList = new ArrayList<>();

    /**
     * 替换铸魂id列表
     * 为空数组则未铸魂无法替
     */
    @Tag(3)
    private List<Integer> replaceIdList = new ArrayList<>();
}
