package com.sh.game.system.union.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2023/11/27 14:40
 */
public interface IUnionYaoXieScript extends IScript {

    void info(Role role);

    void tia<PERSON><PERSON><PERSON>(Role role);

    void union<PERSON><PERSON>ard(Role role);

    void personalReward(Role role);

    void bi<PERSON>ie(Role role);
}
