package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleMysteryStore;
import com.sh.game.system.activity.entity.role.ActivityMySteryStoreData;
import com.sh.game.system.activity.script.IActivityMysteryStoresScript;
import com.sh.script.ScriptEngine;

public class ActivityMysteryStoresManager {
    private static final ActivityMysteryStoresManager INSTANCE = new ActivityMysteryStoresManager();

    private ActivityMysteryStoresManager() {

    }

    public static ActivityMysteryStoresManager getInstance() {
        return INSTANCE;
    }

    /**
     * 获取玩家神秘商店数据
     *
     * @param rid 角色uid
     * @return 玩家神秘商店数据
     */
    public ActivityMySteryStoreData find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityMysteryStoresScript.class, script -> script.find(rid));
    }

    /**
     * 神秘商店商品信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityMysteryStoresScript.class, script -> script.reqInfo(role));
    }


    /**
     * 刷新神秘商店商品信息
     *
     * @param role 角色
     */
    public void reqFlushGoods(Role role, int activityId) {
        ScriptEngine.invoke1tn(IActivityMysteryStoresScript.class, script -> script.reqFlushGoods(role, activityId));
    }

    /**
     * 请求购买神秘商店物品
     *
     * @param role 角色
     * @param cid  神秘商店配置表cid
     */
    public void reqBuyGoods(Role role, int cid) {
        ScriptEngine.invoke1tn(IActivityMysteryStoresScript.class, script -> script.reqBuyGoods(role, cid));
    }

    /**
     * 登陆时刷新玩家神秘商店
     *
     * @param role               角色
     * @param lastLoginTimestamp 上次登陆时间戳（秒）
     */
    public void loginFlushMysteryStore(Role role, long lastLoginTimestamp) {
        ScriptEngine.invoke1tn(IActivityMysteryStoresScript.class, script -> script.loginFlushMysteryStore(role, lastLoginTimestamp));
    }
}
