package com.sh.game.system.hinder;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.hinder.script.IHinderScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @data 2020/9/24 19:44
 */
public class HinderManager {
    private static HinderManager ourInstance = new HinderManager();

    public static HinderManager getInstance() {
        return ourInstance;
    }

    private HinderManager() {
    }

    public void hinderEnd(Role role, int rank) {
        ScriptEngine.invoke1t1(IHinderScript.class, script -> script.hinderEnd(role, rank));
    }

    public void hinderEnter(Role role) {
        ScriptEngine.invoke1t1(IHinderScript.class, script -> script.hinderEnter(role));
    }
}
