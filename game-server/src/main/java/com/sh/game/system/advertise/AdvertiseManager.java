package com.sh.game.system.advertise;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.advertise.script.IAdvertiseScript;
import com.sh.script.ScriptEngine;

public class AdvertiseManager {
    private static final AdvertiseManager INSTANCE = new AdvertiseManager();

    private AdvertiseManager() {

    }

    public static AdvertiseManager getInstance() {
        return INSTANCE;
    }

    public void handle(Role role, int cid) {
        ScriptEngine.invoke1t1(IAdvertiseScript.class, script -> script.handle(role, cid));
    }
}
