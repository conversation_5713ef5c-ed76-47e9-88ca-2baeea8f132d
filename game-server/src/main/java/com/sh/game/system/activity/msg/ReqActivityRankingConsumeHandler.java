package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityRankingConsumeMessage;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityRankingConsumeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求消费排行活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityRankingConsumeHandler extends AbstractHandler<ReqActivityRankingConsumeMessage> {

    @Override
    public void doAction(ReqActivityRankingConsumeMessage msg) {
        ActivityRankingConsumeManager.getInstance().reqRanking(SessionUtil.getRole(msg.getSession()));
    }

}
