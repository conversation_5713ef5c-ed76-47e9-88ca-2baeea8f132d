package com.sh.game.system.door;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.door.entity.RoleDoor;
import com.sh.game.system.door.script.IDoorScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * 玄界之门
 *
 * <AUTHOR>
 * @date 2022/11/03 21:29
 */
public class DoorManager {


    private static final DoorManager INSTANCE = new DoorManager();

    private DoorManager() {
    }

    public static DoorManager getInstance() {
        return INSTANCE;
    }

    /**
     * 根据角色id获取玄界之门信息
     *
     * @param roleId 角色id
     * @return RoleDoor 玄界之门信息
     */
    public RoleDoor find(long roleId) {
        return ScriptEngine.invoke1t1WithRet(IDoorScript.class, script -> script.find(roleId));
    }

    /**
     * 请求角色玄界之门信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IDoorScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求玄界之门升级
     *
     * @param role      角色
     * @param itemList  消耗道具唯一id列表
     */
    public void reqUpgrade(Role role, List<Long> itemList) {
        ScriptEngine.invoke1t1(IDoorScript.class, script -> script.reqUpgrade(role, itemList));
    }
}
