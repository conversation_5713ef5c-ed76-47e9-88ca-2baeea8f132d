package com.sh.game.system.season;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.season.script.ISeasonScript;
import com.sh.script.ScriptEngine;

public class SeasonManager {
    private static final SeasonManager INSTANCE = new SeasonManager();

    private SeasonManager() {
    }

    public static SeasonManager getInstance() {
        return INSTANCE;
    }

    public void reqSeasonPreReward(Role role) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.reqSeasonPreReward(role));
    }

    public void reqEnterDuplicate(Role role, int duplicateId) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.reqEnterDuplicate(role, duplicateId));
    }

    public void updateRankScore(Role role, int chapter, int time) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.updateRankScore(role, chapter, time));
    }

    public void claimedExtReward(Role role, int no) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.claimedExtReward(role, no));
    }

    public void sendRankMsg(Role role, int start, int end) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.sendRankMsg(role, start, end));
    }

    public void sendSeasonDuplicateMsg(Role role) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.sendSeasonDuplicateMsg(role));
    }

    public void sendPreRewardState(Role role) {
        ScriptEngine.invoke1t1(ISeasonScript.class, s -> s.sendPreRewardState(role));
    }
}
