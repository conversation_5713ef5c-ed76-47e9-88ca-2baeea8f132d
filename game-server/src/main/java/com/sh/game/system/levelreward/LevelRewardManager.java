package com.sh.game.system.levelreward;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.levelreward.script.ILevelRewardScript;
import com.sh.script.ScriptEngine;

/**
 * 等级奖励
 *
 * <AUTHOR>
 * @since 2022-04-18 17:08
 **/
public class LevelRewardManager {

    private static final LevelRewardManager INSTANCE = new LevelRewardManager();

    private LevelRewardManager() {
    }

    public static LevelRewardManager getInstance() {
        return INSTANCE;
    }

    /**
     * 根据角色ID获取用户领取等级奖励信息
     *
     * @param roleId 角色ID
     **/
    public void find(long roleId) {
        ScriptEngine.invoke1t1WithRet(ILevelRewardScript.class, script -> script.find(roleId));
    }

    /**
     * 请求获取等级奖励
     *
     * @param role 角色
     * @param cid cfg_grade_reward等级奖励表id
     */
    public void gainLevelReward(Role role, int cid) {
        ScriptEngine.invoke1t1(ILevelRewardScript.class, script -> script.gainLevelReward(role, cid));
    }

    /**
     * 请求等级奖励领取信息
     *
     * @param role 角色
     */
    public void sendLevelRewardInfo(Role role) {
        ScriptEngine.invoke1t1(ILevelRewardScript.class, script -> script.sendLevelRewardInfo(role));
    }
}
