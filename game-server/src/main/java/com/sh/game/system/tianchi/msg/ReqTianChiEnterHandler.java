package com.sh.game.system.tianchi.msg;

import com.sh.game.common.communication.msg.system.tianchi.ReqTianChiEnterMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.tianchi.TianChiManager;
import com.sh.server.AbstractHandler;

/**
 * <p>进入副本</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqTianChiEnterHandler extends AbstractHandler<ReqTianChiEnterMessage> {

    @Override
    public void doAction(ReqTianChiEnterMessage msg) {
        TianChiManager.getInstance().enter(SessionUtil.getRole(msg.getSession()), msg.getDalu());
    }

}
