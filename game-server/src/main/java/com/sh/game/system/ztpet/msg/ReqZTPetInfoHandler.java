package com.sh.game.system.ztpet.msg;

import com.sh.game.common.communication.msg.system.ztPet.ReqZTPetInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.ztpet.ZTPetManager;
import com.sh.server.MessageHandler;
import java.lang.Override;

/**
 * 请求宠物系统信息
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqZTPetInfoHandler extends MessageHandler<ReqZTPetInfoMessage> {
  @Override
  public void doAction(ReqZTPetInfoMessage msg) {
    ZTPetManager.getInstance().sendMsg(SessionUtil.getRole(msg.getSession()));
  }
}
