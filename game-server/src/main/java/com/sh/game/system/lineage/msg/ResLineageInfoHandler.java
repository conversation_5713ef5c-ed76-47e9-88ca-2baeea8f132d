package com.sh.game.system.lineage.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.lineage.LineageManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.lineage.ResLineageInfoMessage;

/**
 * <p>返回四大传承信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ResLineageInfoHandler extends AbstractHandler<ResLineageInfoMessage> {

    @Override
    public void doAction(ResLineageInfoMessage msg) {
        LineageManager.getInstance().sendLineageInfo(SessionUtil.getRole(msg.getSession()));
    }

}
