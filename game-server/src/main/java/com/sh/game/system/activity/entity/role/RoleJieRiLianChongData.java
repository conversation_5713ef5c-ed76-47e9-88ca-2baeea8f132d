package com.sh.game.system.activity.entity.role;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/5 16:50
 */
@Getter
@Setter
public class RoleJieRiLianChongData {

    /**
     * 已领取的每日充值奖励
     */
    @Tag(1)
    private List<Integer> dayRechargeReward = new ArrayList<>();

    /**
     * 已领取的连充奖励
     */
    @Tag(2)
    private List<Integer> activityRechargeReward = new ArrayList<>();

    /**
     * 每日充值信息，key 活动天数，value 当天充值奖励
     */
    @Tag(3)
    private Map<Integer, Integer> rechargeData = new HashMap<>();

    /**
     * 活动期间总充值
     */
    @Tag(4)
    private int totalRecharge;

    /**
     * 终极大奖
     */
    @Tag(5)
    private List<Integer> totalReward = new ArrayList<>();
}
