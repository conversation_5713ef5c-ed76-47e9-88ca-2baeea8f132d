package com.sh.game.system.ztpet.msg;

import com.sh.game.common.communication.msg.system.ztPet.ReqZTPetUpgradeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.ztpet.ZTPetManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求宠物升阶
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqZTPetUpgradeHandler extends MessageHandler<ReqZTPetUpgradeMessage> {
    @Override
    public void doAction(ReqZTPetUpgradeMessage msg) {
        ZTPetManager.getInstance().reqZTPetUpgrade(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }
}
