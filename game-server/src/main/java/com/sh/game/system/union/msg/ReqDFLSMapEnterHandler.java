package com.sh.game.system.union.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionDianFenLianSaiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqDFLSMapEnterMessage;

/**
 * <p>请求进图</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDFLSMapEnterHandler extends AbstractHandler<ReqDFLSMapEnterMessage> {

    @Override
    public void doAction(ReqDFLSMapEnterMessage msg) {
        UnionDianFenLianSaiManager.getInstance().reqEnterDFLSMap(SessionUtil.getRole(msg));
    }

}
