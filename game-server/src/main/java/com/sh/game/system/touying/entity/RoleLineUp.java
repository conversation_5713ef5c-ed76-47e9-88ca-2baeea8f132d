package com.sh.game.system.touying.entity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.HoardConfig;
import com.sh.game.system.hoard.entity.Hoard;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Slf4j
@Deprecated
public class RoleLineUp {
    @Tag(1)
    private long id;

    /**
     * 是否解锁
     */
    @Tag(2)
    private boolean unLock;

    /**
     * 神魔
     */
    @Tag(3)
    private List<Integer> shenMoList = new ArrayList<>();

    @Tag(5)
    private Map<Integer, Hoard> hoardMap = new HashMap<>();

    /**
     * 座驾
     */
    @Tag(6)
    private int useMount;

    public Map<Integer, Integer> findHoardTypeCount() {
        Map<Integer, Integer> map = new HashMap<>(hoardMap.size());
        for (Hoard hoard : hoardMap.values()) {
            HoardConfig config = ConfigDataManager.getInstance().getById(HoardConfig.class, hoard.getCid());
            if (config == null) {
                continue;
            }
            map.compute(config.getType(), (k, v) -> {
                if (v == null) {
                    v = 0;
                }
                v++;
                return v;
            });
        }
        return map;
    }
}
