package com.sh.game.system.activity.entity;

import com.sh.game.common.util.TimeUtil;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivitySchedule {

    /**
     * 活动编号
     */
    @Tag(1)
    private int activityID;

    /**
     * 活动类型
     */
    @Tag(2)
    private int activityType;

    /**
     * 活动开始时间，秒
     */
    @Tag(3)
    private int beginAt;

    /**
     * 活动截止时间秒
     */
    @Tag(4)
    private int endAt;

    @Exclude
    private int priority;

    @Exclude
    private int proceedDays = 0;

    /**
     * 获取活动开始天数
     *
     * @return 当前时间距活动开启时间的天数
     */
    public int findProceedDays() {
        proceedDays = (int) (TimeUtil.getDay(System.currentTimeMillis()) - TimeUtil.getDay(beginAt * 1000L) + 1);
        return proceedDays;
    }

    /**
     * 获取活动开始周数
     *
     * @return
     */
    public int findProceedWeeKs() {
        int dayCount = TimeUtil.getWeekCountFromMillisTime(beginAt * 1000L);
        return dayCount / 7 + 1;
    }

    /**
     * 获取活动开始小时数
     *
     * @return 当前时间距活动开启时间的天数
     */
    public int findProceedHours() {
        int now = TimeUtil.getNowOfSeconds();
        if (beginAt >= now) {
            return 0;
        }
        return TimeUtil.betweenHour(beginAt, now);
    }

}
