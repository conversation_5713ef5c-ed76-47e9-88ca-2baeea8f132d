package com.sh.game.system.wudao;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.wudao.script.IWuDaoScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/28
 */
public class WuDaoManager {
    private static final WuDaoManager INSTANCE = new WuDaoManager();

    public static WuDaoManager getInstance() {
        return INSTANCE;
    }

    private WuDaoManager() {

    }

    /**
     * 升级
     *
     * @param role
     * @param type
     */
    public void levelUp(Role role, int type) {
        ScriptEngine.invoke1t1(IWuDaoScript.class, script -> script.levelUp(role, type));
    }

    /**
     * 请求信息
     *
     * @param role
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IWuDaoScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求信息
     *
     * @param role
     */
    public void reqReward(Role role) {
        ScriptEngine.invoke1t1(IWuDaoScript.class, script -> script.reward(role));
    }

    /**
     * 查找套装ID
     *
     * @param role
     */
    public int findSuitId(Role role) {
       return ScriptEngine.invoke1t1WithRet(IWuDaoScript.class, script -> script.findSuitId(role));
    }
}
