package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityTouZiLiCaiScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/3/28 11:53
 */
public class ActivityTouZiManager {

    private static final ActivityTouZiManager INSTANCE = new ActivityTouZiManager();

    public static ActivityTouZiManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IActivityTouZiLiCaiScript.class, s -> s.info(role));
    }

    public void reward(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityTouZiLiCaiScript.class, s -> s.reward(role, cid));
    }

    public void touZi(Role role) {
        ScriptEngine.invoke1t1(IActivityTouZiLiCaiScript.class, s -> s.tou<PERSON>i(role));
    }
}
