package com.sh.game.system.transformSuperMan.msg;

import com.sh.game.common.communication.msg.system.transformSuperMan.ReqSuperManProtectMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.transformSuperMan.TransformSuperManManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求狂暴保护状态变更</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqSuperManProtectHandler extends AbstractHandler<ReqSuperManProtectMessage> {

    @Override
    public void doAction(ReqSuperManProtectMessage msg) {
        TransformSuperManManager.getInstance().reqOpenProtect(SessionUtil.getRole(msg.getSession()), msg.getIsOpen());
    }

}
