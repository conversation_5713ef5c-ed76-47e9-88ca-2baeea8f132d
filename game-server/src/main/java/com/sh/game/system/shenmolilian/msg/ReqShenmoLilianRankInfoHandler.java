package com.sh.game.system.shenmolilian.msg;

import com.sh.game.common.communication.msg.system.shenmolilian.ReqShenmoLilianRankInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shenmolilian.ShenmoLilianManager;
import com.sh.server.AbstractHandler;

@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShenmoLilianRankInfoHandler extends AbstractHandler<ReqShenmoLilianRankInfoMessage> {

    @Override
    public void doAction(ReqShenmoLilianRankInfoMessage msg) {
        ShenmoLilianManager.getInstance().reqShenmoLilianRankInfo(SessionUtil.getRole(msg), msg.getActivityId());
    }

}
