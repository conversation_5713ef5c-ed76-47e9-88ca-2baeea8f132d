package com.sh.game.system.zhenYan;


import com.sh.game.common.communication.msg.system.zhenYan.bean.ZhenYanCompoundBean;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.zhenYan.script.IZhenYanScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * 九字真言系统
 */
public class ZhenYanManager {

    private static final ZhenYanManager INSTANCE = new ZhenYanManager();

    private ZhenYanManager() {
    }

    public static ZhenYanManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求合成
     *
     * @param role
     * @param beanList
     */
    public void reqZhenYanCompound(Role role, List<ZhenYanCompoundBean> beanList, int cfgId) {
        ScriptEngine.invoke1t1(IZhenYanScript.class, s -> s.reqZhenYanCompound(role, beanList, cfgId));
    }

    public void reqZhenYanInfo(Role role) {
        ScriptEngine.invoke1t1(IZhenYanScript.class, s -> s.reqZhenYanInfo(role));
    }

    /**
     * 请求真言塔信息
     *
     * @param role
     */
    public void reqZhenYanTowerInfo(Role role) {
        ScriptEngine.invoke1t1(IZhenYanScript.class, s -> s.reqZhenYanTowerInfo(role));
    }

    public void reqIntoZhenYanTower(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IZhenYanScript.class, s -> s.reqIntoZhenYanTower(role, cfgId));
    }

}
