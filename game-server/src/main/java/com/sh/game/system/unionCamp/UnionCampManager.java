package com.sh.game.system.unionCamp;

import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.buff.Buffs;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.unionCamp.script.IUnionCampScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-05-07
 **/
public class UnionCampManager {
    private static final UnionCampManager INSTANCE = new UnionCampManager();


    private UnionCampManager() {
    }

    public static UnionCampManager getInstance() {
        return INSTANCE;
    }


    /**
     * 请求阵营信息
     *
     * @param role 角色
     */
    public void sendUnionCampInfo(Role role) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.sendUnionCampInfo(role));
    }

    /**
     * 加入阵营
     *
     * @param role      角色
     * @param campType  阵营类型
     * @param isClearCd 是否清除cd
     */
    public void joinUnionCamp(Role role, int campType, boolean isClearCd) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.joinUnionCamp(role, campType, isClearCd));
    }

    /**
     * 退出阵营
     *
     * @param role 角色
     */
    public void exitUnionCamp(Role role) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.exitUnionCamp(role));
    }

    /**
     * 请求阵营盟友列表信息
     *
     * @param role 角色
     */
    public void sendCampAllyInfo(Role role) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.sendCampAllyInfo(role));
    }


    /**
     * 查找玩家阵营
     *
     * @param role 角色
     * @return 阵营类型
     */
    public int findUnionCampType(Role role) {
        return ScriptEngine.invoke1t1WithRet(IUnionCampScript.class, script -> script.findUnionCampType(role));
    }


    /**
     * 发送玩家阵营改变消息
     *
     * @param updateUnionId 有改变的行会id列表
     **/
    public void unionCampChange(List<Long> updateUnionId) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.unionCampChange(updateUnionId));
    }

    /**
     * 是否过强势阵营的行会
     *
     * @param union 行会
     * @return 是否过强势行会
     */
    public boolean isPowerCampUnion(Union union) {
        return ScriptEngine.invoke1t1WithRet(IUnionCampScript.class, script -> script.isPowerCampUnion(union));
    }


    public void checkUnionCampBuff(Role role, Buffs buffs) {
        ScriptEngine.invoke1t1(IUnionCampScript.class, script -> script.checkUnionCampBuff(role, buffs));
    }


}
