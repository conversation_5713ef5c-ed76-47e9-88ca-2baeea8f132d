package com.sh.game.system.roleMount;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.roleMount.script.IRoleMountScript;
import com.sh.script.ScriptEngine;

public class RoleMountManager {
    private static final RoleMountManager INSTANCE = new RoleMountManager();

    private RoleMountManager() {
    }

    public static RoleMountManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求穿脱坐骑
     * @param role
     */
    public void reqDressOrDisrobeRoleMount(Role role){
        ScriptEngine.invoke1t1(IRoleMountScript.class, script->script.reqDressOrDisrobeRoleMount(role));
    }

    /**
     * 请求个人坐骑信息
     *
     * @param role
     */
    public void reqRoleMountInfo(Role role) {
        ScriptEngine.invoke1t1(IRoleMountScript.class, script->script.reqRoleMountInfo(role));
    }


}
