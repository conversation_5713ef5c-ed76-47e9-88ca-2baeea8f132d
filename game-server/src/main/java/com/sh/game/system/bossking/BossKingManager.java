package com.sh.game.system.bossking;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.bossking.script.IBossKingScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/11/1 13:03
 */
public class BossKingManager {

    private static final BossKingManager INSTANCE = new BossKingManager();

    public static BossKingManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IBossKingScript.class, s-> s.info(role));
    }

    public void tiao<PERSON>han(Role role) {
        ScriptEngine.invoke1t1(IBossKingScript.class, s-> s.tia<PERSON><PERSON><PERSON>(role));
    }

    public void saoDang(Role role) {
        ScriptEngine.invoke1t1(IBossKingScript.class, s-> s.saoDang(role));
    }
}
