package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

/**
 * 多倍回收卡
 *
 * <AUTHOR>
 * @since 2022-04-12 17:43
 **/
public class ItemUsageForRecoveryCard extends ItemUsage {

    /**
     * 是否需要到场景服验证
     *
     * @param role      用户角色
     * @param item      道具
     * @param config    道具配置
     * @return
     */
    @Override
    public boolean needMapVerify(Role role, Item item, ItemConfig config) {
        return true;
    }

    /**
     * 道具使用类型
     *
     * @return {@link int}
     */
    @Override
    public int getUsedType() {
        return 422;
    }

    /**
     * 使用
     *
     * @param role      角色
     * @param item      道具
     * @param config    道具配置
     * @param count     使用数量
     * @param params    使用参数
     * @param stash     道具暂存区
     * @return 返回空null，折通知场景服使用
     */
    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        return null;
    }
}
