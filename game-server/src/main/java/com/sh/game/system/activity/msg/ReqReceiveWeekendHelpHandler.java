package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqReceiveWeekendHelpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.script.IActivityWeekendRechargeScript;
import com.sh.script.ScriptEngine;
import com.sh.server.AbstractHandler;

/**
 * <p>领取周末助力奖励请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqReceiveWeekendHelpHandler extends AbstractHandler<ReqReceiveWeekendHelpMessage> {

    @Override
    public void doAction(ReqReceiveWeekendHelpMessage msg) {
        ScriptEngine.invoke1t1(IActivityWeekendRechargeScript.class,
                script -> script.reqReceiveHelpReward(SessionUtil.getRole(msg), msg.getCid()));
    }

}
