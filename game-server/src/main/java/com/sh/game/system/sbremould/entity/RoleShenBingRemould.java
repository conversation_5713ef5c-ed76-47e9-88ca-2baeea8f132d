package com.sh.game.system.sbremould.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 神兵改造
 *
 * <AUTHOR>
 * @date 2022/09/20 19:54
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleShenBingRemould extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 神兵改造信息列表
     * key:     pos             部位
     * value:   remouldData     神兵改造信息
     */
    @Tag(2)
    private Map<Integer, RemouldData> remouldMap = new HashMap<>();
}
