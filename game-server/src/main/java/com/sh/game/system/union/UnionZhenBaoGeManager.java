package com.sh.game.system.union;

import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.union.script.IUnionZhenBaoGeScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/11/28 15:16
 */
public class UnionZhenBaoGeManager {

    private static final UnionZhenBaoGeManager INSTANCE = new UnionZhenBaoGeManager();

    public static UnionZhenBaoGeManager getInstance() {
        return INSTANCE;
    }


    public void info(Role role) {
        ScriptEngine.invoke1t1(IUnionZhenBaoGeScript.class, s -> s.info(role));
    }

    public void yiJia(Role role) {
        ScriptEngine.invoke1t1(IUnionZhenBaoGeScript.class, s -> s.yiJia(role));
    }

    public void buy(Role role) {
        ScriptEngine.invoke1t1(IUnionZhenBaoGeScript.class, s -> s.buy(role));
    }

    public void initZhenBaoGe(Union union) {
        ScriptEngine.invoke1t1(IUnionZhenBaoGeScript.class, s -> s.initZhenBaoGe(union));
    }
}
