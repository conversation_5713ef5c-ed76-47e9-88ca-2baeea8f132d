package com.sh.game.system.daily.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.daily.DailyManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.daily.ReqDailyTimedNextTimeMessage;

/**
 * <p>请求日常下次开放时间</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyTimedNextTimeHandler extends AbstractHandler<ReqDailyTimedNextTimeMessage> {

    @Override
    public void doAction(ReqDailyTimedNextTimeMessage msg) {
        DailyManager.getInstance().reqDailyTimedNext(SessionUtil.getRole(msg.getSession()), msg.getDailyType());
    }

}
