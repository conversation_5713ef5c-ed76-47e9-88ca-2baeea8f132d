package com.sh.game.system.babelroad;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.babelroad.entity.RoleBabelRoadData;
import com.sh.game.system.babelroad.script.IBabelRoadScript;
import com.sh.game.system.lunhuita.LunHuiTaManager;
import com.sh.script.ScriptEngine;

/**
 * 通天之路
 *
 * <AUTHOR>
 * @date 2022/6/6 21:12
 */
public class BabelRoadManager {

    private static final BabelRoadManager INSTANCE = new BabelRoadManager();

    public static BabelRoadManager getInstance() {
        return INSTANCE;
    }

    private BabelRoadManager() {
    }

    /**
     * 获取角色通天之路数据
     *
     * @param role 角色
     * @return RoleBabelRoadData 通天之路数据
     */
    public RoleBabelRoadData find(Role role) {
        return ScriptEngine.invoke1t1WithRet(IBabelRoadScript.class, script -> script.find(role));
    }

    /**
     * 发送通天之路信息
     *
     * @param role 角色
     */
    public void sendBabelRoadInfo(Role role){
        ScriptEngine.invoke1t1(IBabelRoadScript.class, script -> script.sendBabelRoadInfo(role));
    }

    /**
     * 进入通天之路副本地图
     *
     * @param role 角色
     */
    public void enterDuplicate(Role role){
        ScriptEngine.invoke1t1(IBabelRoadScript.class, script -> script.enterDuplicate(role));
    }

    /**
     * 领取通天之路每日奖励
     *
     * @param role 角色
     */
    public void gainBabelRoadDailyReward(Role role){
        ScriptEngine.invoke1t1(IBabelRoadScript.class, script -> script.gainBabelRoadDailyReward(role));
    }

}
