package com.sh.game.system.ztpet;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.ztpet.script.IZTPetScript;
import com.sh.script.ScriptEngine;
import lombok.Getter;

public class ZTPetManager {
    @Getter
    private static final ZTPetManager instance = new ZTPetManager();

    private ZTPetManager() {
    }

    public void reqZTPetBuy(Role role, int cid) {
        ScriptEngine.invoke1t1(IZTPetScript.class, script -> script.reqZTPetBuy(role, cid));
    }

    public void reqZTPetSelect(Role role, int petId) {
        ScriptEngine.invoke1t1(IZTPetScript.class, script -> script.reqZTPetSelect(role, petId));
    }

    public void reqZTPetLevelUp(Role role, int petId) {
        ScriptEngine.invoke1t1(IZTPetScript.class, script -> script.reqZTPetLevelUp(role, petId));
    }

    public void reqZTPetUpgrade(Role role, int petId) {
        ScriptEngine.invoke1t1(IZTPetScript.class, script -> script.reqZTPetUpgrade(role, petId));
    }

    public void sendMsg(Role role) {
        ScriptEngine.invoke1t1(IZTPetScript.class, script -> script.sendMsg(role));
    }
}
