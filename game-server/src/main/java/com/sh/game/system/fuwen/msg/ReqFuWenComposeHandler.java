package com.sh.game.system.fuwen.msg;

import com.sh.game.common.communication.msg.system.fuwen.ReqFuWenComposeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fuwen.FuWenManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求合成符文</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqFuWenComposeHandler extends AbstractHandler<ReqFuWenComposeMessage> {

    @Override
    public void doAction(ReqFuWenComposeMessage msg) {
        FuWenManager.getInstance().reqFuWenCompose(SessionUtil.getRole(msg.getSession()), msg.getItemId());
    }

}
