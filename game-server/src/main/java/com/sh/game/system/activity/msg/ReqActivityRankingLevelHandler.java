package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityRankingLevelMessage;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityRankingLevelManager;
import com.sh.server.AbstractHandler;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityRankingLevelHandler extends AbstractHandler<ReqActivityRankingLevelMessage> {

    @Override
    public void doAction(ReqActivityRankingLevelMessage msg) {
        ActivityRankingLevelManager.getInstance().reqRanking(SessionUtil.getRole(msg.getSession()), msg.getType());
    }

}
