package com.sh.game.system.chongWuEquip.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chongWuEquip.ChongWuEquipManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.chongWuEquip.ReqChongWuEquipJianDingMessage;

/**
 * <p>请求宠物装备鉴定</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqChongWuEquipJianDingHandler extends AbstractHandler<ReqChongWuEquipJianDingMessage> {

    @Override
    public void doAction(ReqChongWuEquipJianDingMessage msg) {
        ChongWuEquipManager.getInstance().reqChongWuEquipJianDing(SessionUtil.getRole(msg), msg.getEquipId());
    }

}
