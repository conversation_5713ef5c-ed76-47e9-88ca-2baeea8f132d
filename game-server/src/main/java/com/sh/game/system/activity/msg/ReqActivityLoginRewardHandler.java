package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityLoginRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityLoginRewardManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求节日领奖</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqActivityLoginRewardHandler extends AbstractHandler<ReqActivityLoginRewardMessage> {

    @Override
    public void doAction(ReqActivityLoginRewardMessage msg) {
        ActivityLoginRewardManager.getInstance().reqActivityReward(SessionUtil.getRole(msg), msg.getPlayType());
    }

}
