package com.sh.game.system.appearance.msg;

import com.sh.game.common.communication.msg.system.appearance.ReqAppearanceExchangeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.server.AbstractHandler;

/**
 * <p>兑换</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqAppearanceExchangeHandler extends AbstractHandler<ReqAppearanceExchangeMessage> {

    @Override
    public void doAction(ReqAppearanceExchangeMessage msg) {
        AppearanceManager.getInstance().exchange(SessionUtil.getRole(msg.getSession()), msg.getAppearanceId());
    }

}
