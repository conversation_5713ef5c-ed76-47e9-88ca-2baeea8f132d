package com.sh.game.system.function;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.function.script.IFunctionScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @data 2020/10/27 14:09
 */
public class FunctionManager {

    private static final FunctionManager INSTANCE = new FunctionManager();

    private FunctionManager() {
    }

    public static FunctionManager getInstance() {
        return INSTANCE;
    }

    public void getReward(Role role, int type, int count) {
        ScriptEngine.invoke1t1(IFunctionScript.class, script -> script.getReward(role, type, count));
    }

    public void sendInfo(Role role) {
        ScriptEngine.invoke1t1(IFunctionScript.class, script -> script.sendInfo(role));
    }

    public void count(Role role, int itemId) {
        ScriptEngine.invoke1t1(IFunctionScript.class, script -> script.count(role, itemId));
    }
}
