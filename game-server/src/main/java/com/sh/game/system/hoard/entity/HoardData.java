package com.sh.game.system.hoard.entity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.HoardConfig;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/4 16:46
 */
@Getter
@Setter
public class HoardData {

    @Tag(1)
    private int levelId = 1;

    @Tag(2)
    private Hoard hoard;

    @Tag(3)
    private int exp;
}
