package com.sh.game.system.lineage.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 当前角色四大传承基本数据
 *
 * <AUTHOR>
 * @since 2022-04-13 13:52
 **/
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleLineageItem extends AbstractRoleEntity {

    /**
     * 角色ID
     */
    @Tag(1)
    private long id;

    /**
     * 当前传承
     * key:     传承类型type
     * value:   传承等级level
     */
    @Tag(2)
    private Map<Integer, Integer> lineageMap = new HashMap<>();

}
