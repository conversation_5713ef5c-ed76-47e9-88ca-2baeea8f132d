package com.sh.game.system.yuanjingxiaopu.msg;

import com.sh.game.common.communication.msg.system.yuanjing.ReqYuanJingStoreDrawMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.yuanjingxiaopu.YuanJingXiaoPuManager;
import com.sh.server.MessageHandler;
import java.lang.Override;

/**
 * 请求源晶小铺抽卡
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqYuanJingStoreDrawHandler extends MessageHandler<ReqYuanJingStoreDrawMessage> {
  @Override
  public void doAction(ReqYuanJingStoreDrawMessage msg) {
    YuanJingXiaoPuManager.getInstance().drawCard(SessionUtil.getRole(msg.getSession()), msg.getCid(), msg.getCount());
  }
}
