package com.sh.game.system.ztpet.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleZTPet extends AbstractRoleEntity {
    @Tag(1)
    private long id;

    /**
     * 玩家购买的宠物
     */
    @Tag(2)
    private Map<Integer, ZTPetData> petData = new HashMap<>();

    /**
     * 玩家当前所选宠物
     */
    @Tag(3)
    private int selectPetId;

}
