package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.RechargeConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.recharge.RechargeManager;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ItemUsageForRechargeCard extends ItemUsage {

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[] rechargeParams = config.getUseParam()[0];
        if (rechargeParams == null || rechargeParams.length == 0) {
            return false;
        }

        RechargeConfig cfg = getRechargeConfig(rechargeParams[0]);
        if (cfg == null) {
            log.error("找不到 id = {} 的RechargeConfig", rechargeParams[0]);
            return false;
        }

        // UseParam长度等于1的话使用默认参数
        int rechargeSourceId = RechargeConst.source.card;
        if (rechargeParams.length > 1) {
            rechargeSourceId = rechargeParams[1];
        }

        RechargeManager.getInstance().recharge(role, cfg, rechargeSourceId, 0);
        log.info("rid = {} , 使用了充值卡道具, itemId = {} , rechargeSourceId = {} , count = {}", role.getId(), config.getId(), rechargeSourceId, count);

        return true;
    }

    private RechargeConfig getRechargeConfig(int recChargeId) {
        for (RechargeConfig rechargeConfig : ConfigDataManager.getInstance().getList(RechargeConfig.class)) {
            if (rechargeConfig.getId() == recChargeId) {
                return rechargeConfig;
            }
        }
        return null;
    }

    @Override
    public int getUsedType() {
        return 400;
    }
}
