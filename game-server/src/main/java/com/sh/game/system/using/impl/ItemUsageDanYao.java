package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigCacheManager;
import com.sh.game.common.communication.msg.system.count.ResCountDataMessage;
import com.sh.game.common.communication.msg.system.count.bean.CountBean;
import com.sh.game.common.communication.msg.system.danYao.ResDanYoInfoMessage;
import com.sh.game.common.communication.msg.system.danYao.bean.DanYaoTalentBean;
import com.sh.game.common.config.cache.DrugtalentConfigCache;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.DanYaoConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.danYao.entity.RoleDanYao;
import com.sh.game.system.danYao.script.IDanYaoScript;
import com.sh.game.system.using.ItemUsage;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sh.game.common.constant.CountConst.CountType.ITEM_USE_NUM;

@Slf4j
public class ItemUsageDanYao extends ItemUsage {
    @Override
    public int getUsedType() {
        return 220;
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        if (config.getUseParam().length < 1 || config.getUseParam()[0].length < 1) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        RoleDanYao roleDanYao = role.getRoleDanYao();
        int remainingPoints = roleDanYao.getRemainingPoints();
        roleDanYao.setRemainingPoints(remainingPoints + config.getUseParam()[0][0] * count);
        //指定itemId使用不计入个人使用次数
        String itemListValue = GlobalUtil.getGlobalValue(GameConst.GlobalId.DANYAO_NORECORD_QUANTITY);
        List<Integer> itemIdList = new JinhaoIntegerListConverter().convert(itemListValue);
        if (!itemIdList.contains(config.getId())) {
            roleDanYao.setUsageCount(roleDanYao.getUsageCount() + count);
        }
        DataCenter.updateData(role);
        TipUtil.show(role, CommonTips.获取丹药天赋点, config.getUseParam()[0][0] * count);
        log.info("丹药系统-使用丹药成功,角色:{},昵称:{},丹药id:{},今日使用次数:{},增加天赋点:{},角色剩余天赋点:{}",
                role.getId(), role.getName(), config.getId(), roleDanYao.getUsageCount(), config.getUseParam()[0][0], roleDanYao.getRemainingPoints());
        ScriptEngine.invoke1t1(IDanYaoScript.class, script -> script.calDanYaoShuXing(role));
        ResDanYoInfoMessage msg = new ResDanYoInfoMessage();
        msg.setUsageCount(roleDanYao.getUsageCount());
        msg.setRemainingPoints(roleDanYao.getRemainingPoints());
        Map<Integer, Integer> talentDataMap = roleDanYao.getTalentDataMap();
        List<DanYaoTalentBean> listBean = new ArrayList<>();
        DrugtalentConfigCache cache = ConfigCacheManager.getInstance().getCache(DrugtalentConfigCache.class);
        List<Integer> danYaoTypes = cache.findDanYaoTypes(DanYaoConst.Type.DANYAO_TYPE);
        for (Integer danYaoType : danYaoTypes) {
            Integer point = talentDataMap.getOrDefault(danYaoType, 0);
            DanYaoTalentBean bean = new DanYaoTalentBean();
            bean.setCount(point);
            bean.setType(danYaoType);
            listBean.add(bean);
        }
        msg.getBean().addAll(listBean);

        //丹药精炼
        Map<Integer, Integer> talentMasterDataMap = roleDanYao.getTalentMasterDataMap();
        List<DanYaoTalentBean> jingLianListBean = new ArrayList<>();
        List<Integer> jingLiandanYaoTypes = cache.findDanYaoTypes(DanYaoConst.Type.DANYAO_JINGLIAN_TYPE);
        for (Integer danYaoType : jingLiandanYaoTypes) {
            Integer point = talentMasterDataMap.getOrDefault(danYaoType, 0);
            DanYaoTalentBean bean = new DanYaoTalentBean();
            bean.setCount(point);
            bean.setType(danYaoType);
            jingLianListBean.add(bean);
        }
        msg.getJingLianBean().addAll(jingLianListBean);
        MessageUtil.sendMsg(msg, role.getId());
        return true;
    }

    /**
     * 验证是否超出使用次数上限
     *
     * @param role   角色
     * @param config 道具配置表
     * @param count  使用数量
     * @return 是否超出使用次数上限
     */
    public boolean verifyUseCountLimit(Role role, ItemConfig config, int count) {
        // 使用次数上限
        int useLimit = config.getUseLimit();

        // 无次数限制
        if (useLimit <= 0) {
            return false;
        }

        // 神兵会员有额外使用次数
        int targetId = config.getBind() > 0 ? config.getBind() : config.getId();

        int useCount = CountManager.getInstance().getCount(role, ITEM_USE_NUM, config.getType());

        int extraNum = shenBingHuiYuanUsageCountUp(role, targetId);
        useLimit += extraNum;
        // 已使用数量加批量使用数量超出上限
        if (useCount + count > useLimit) {
            TipUtil.show(role, CommonTips.服务_道具达到使用上限);
            return true;
        }
        return false;
    }

    /**
     * 增加使用次数上限
     *
     * @param role   角色
     * @param config 道具配置表
     * @param count  使用数量
     */
    public void addUseCount(Role role, ItemConfig config, int count) {
        // 使用次数上限
        int useLimit = config.getUseLimit();

        //有使用限制，增加次数
        if (useLimit <= 0) {
            return;
        }

        //记录使用数量
        int useConfigId = config.getBind() > 0 ? config.getBind() : config.getId();

        CountManager.getInstance().count(role, ITEM_USE_NUM, config.getType(), count);
        //发送次数更新消息
        ResCountDataMessage countDataMessage = new ResCountDataMessage();
        CountBean bean = new CountBean();
        bean.setGroupId(config.getType());
        bean.setCountType(ITEM_USE_NUM.getType());
        bean.setCountNum(CountManager.getInstance().getCount(role, ITEM_USE_NUM, config.getType()));
        countDataMessage.getCountList().add(bean);
        MessageUtil.sendMsg(countDataMessage, role.getId());
    }


}
