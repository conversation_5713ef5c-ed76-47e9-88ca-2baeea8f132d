package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.TipUtil;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 回城卷轴--随机点
 */
@Slf4j
public class ItemUsageForScrollDungeon extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        MapConfig mapConfig = getMapConfig(role);
        if (mapConfig == null) {
            return false;
        }
        if (mapConfig.getHome() == 1) {
            TipUtil.show(role, CommonTips.服务_当前地图无法使用);
            return false;
        }
        int[] homeMap = mapConfig.getHomeMap();
        if (homeMap == null || homeMap.length < 3) {
            log.error("找不到地图配置或者地图回城点配置错误: {}", mapConfig.getMapCode());
            return false;
        }

        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        MapConfig mapConfig = getMapConfig(role);
        TeleportManager.getInstance().teleport(role, mapConfig.getHomeMap()[0], 0, 0, 0);
        return true;
    }

    @Override
    public int getUsedType() {
        return 111;
    }
}
