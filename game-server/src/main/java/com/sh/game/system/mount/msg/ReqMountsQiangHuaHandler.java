package com.sh.game.system.mount.msg;

import com.sh.game.common.communication.msg.system.mounts.ReqMountsQiangHuaMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.mount.MountManager;
import com.sh.server.AbstractHandler;

/**
 * <p>强化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqMountsQiangHuaHandler extends AbstractHandler<ReqMountsQiangHuaMessage> {

    @Override
    public void doAction(ReqMountsQiangHuaMessage msg) {
        MountManager.getInstance().reqQiangHua(SessionUtil.getRole(msg.getSession()));
    }

}
