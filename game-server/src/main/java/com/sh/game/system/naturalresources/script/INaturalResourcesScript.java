package com.sh.game.system.naturalresources.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/22 11:26
 */
public interface INaturalResourcesScript extends IScript {

    void reqNaturalResourcesInfo(Role role);

    /** 采集到头顶 */
    void reqNaturalResourcesHarvest(Role role, int resourcesId);

    void naturalResourcesHarvest(Role role, int monsterCfgId);

    /** 资源采集场-刷新池-刷新消耗 */
    boolean collectMonsterRebornPoolCost(Role role, int rebornItemCfgId, int rebornPoolCost);

    /** 资源采集场-刷新池-刷新消耗返还 */
    void collectMonsterRebornPoolBack(Role role, int rebornItemCfgId, int rebornPoolBack);
}
