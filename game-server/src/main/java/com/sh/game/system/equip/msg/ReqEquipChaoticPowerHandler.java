package com.sh.game.system.equip.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.equip.ReqEquipChaoticPowerMessage;

/**
 * <p>请求混沌赋灵</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEquipChaoticPowerHandler extends AbstractHandler<ReqEquipChaoticPowerMessage> {

    @Override
    public void doAction(ReqEquipChaoticPowerMessage msg) {
        EquipManager.getInstance().reqChaoticPower(SessionUtil.getRole(msg), msg.getEquipId());
    }

}
