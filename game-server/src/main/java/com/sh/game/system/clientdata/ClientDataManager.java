package com.sh.game.system.clientdata;

import com.sh.game.common.communication.msg.system.clientdata.bean.ClientDataBean;
import com.sh.game.common.communication.msg.system.clientdata.bean.ClientStringDataBean;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.clientdata.script.IClientDataScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
public class ClientDataManager {

    private static final ClientDataManager INSTANCE = new ClientDataManager();

    public static ClientDataManager getInstance() {
        return INSTANCE;
    }

    public void reqClientData(Role role) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientData(role));
    }

    public void reqClientDataUpdate(Role role, List<ClientDataBean> clientDataBeanList) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientDataUpdate(role, clientDataBeanList));
    }

    public void reqClientDataRemove(Role role, List<Integer> keyList) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientDataRemove(role, keyList));
    }

    public void reqClientStringData(Role role) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientStringData(role));
    }

    public void reqClientStringDataUpdate(Role role, List<ClientStringDataBean> clientStringDataBeanList) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientStringDataUpdate(role, clientStringDataBeanList));
    }

    public void reqClientStringDataRemove(Role role, List<String> keyList) {
        ScriptEngine.invoke1t1(IClientDataScript.class, s -> s.reqClientStringDataRemove(role, keyList));
    }
}
