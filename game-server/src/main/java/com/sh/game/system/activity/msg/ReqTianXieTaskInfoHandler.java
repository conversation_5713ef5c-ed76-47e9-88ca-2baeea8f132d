package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqTianXieTaskInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityTianXieManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求天邪战鉴的任务信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqTianXieTaskInfoHandler extends AbstractHandler<ReqTianXieTaskInfoMessage> {

    @Override
    public void doAction(ReqTianXieTaskInfoMessage msg) {
        ActivityTianXieManager.getInstance().reqTaskInfo(SessionUtil.getRole(msg));
    }

}
