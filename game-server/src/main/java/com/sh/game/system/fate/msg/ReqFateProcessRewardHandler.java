package com.sh.game.system.fate.msg;

import com.sh.game.common.communication.msg.system.fate.ReqFateProcessRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fate.script.IFateProcessScript;
import com.sh.script.ScriptEngine;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求领取天命历程奖励
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqFateProcessRewardHandler extends MessageHandler<ReqFateProcessRewardMessage> {
    @Override
    public void doAction(ReqFateProcessRewardMessage msg) {
        ScriptEngine.invoke1t1(IFateProcessScript.class, script -> script.claimFateProcessReward(SessionUtil.getRole(msg), msg.getTaskId()));
    }
}
