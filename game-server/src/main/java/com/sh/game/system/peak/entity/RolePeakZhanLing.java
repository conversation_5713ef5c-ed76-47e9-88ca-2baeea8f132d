package com.sh.game.system.peak.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.task.entity.TaskRecord;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巅峰联赛战令
 *
 * <AUTHOR>
 * @date 2022/11/18 16:05
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RolePeakZhanLing extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 总经验
     */
    @Tag(2)
    private int exp;

    /**
     * 宝箱大奖次数
     * key:     届数
     * value:   次数
     */
    @Tag(3)
    private Map<Integer, Integer> bigPrize = new HashMap<>();

    /**
     * 已领取的奖励id列表
     */
    @Tag(4)
    private List<Integer> rewardList = new ArrayList<>();

    /**
     * 任务
     * key:     任务类型(1:日常、2:周常)
     * value:   任务记录
     */
    @Tag(5)
    private Map<Integer, List<TaskRecord>> taskMap = new HashMap<>();

    /**
     * 开启时间(每月1日)
     */
    @Tag(6)
    private int startTime;

    /**
     * 每日购买经验次数
     */
    @Tag(7)
    private int buyExpCount;

    /**
     * 开宝箱次数
     * key:     届数
     * value:   次数
     */
    @Tag(8)
    private Map<Integer, Integer> boxMap = new HashMap<>();

    /**
     * 角色当前届数
     */
    @Tag(9)
    private int season;
}
