package com.sh.game.system.role.msg;

import com.sh.game.common.communication.msg.system.guajisystem.ReqLiXianGuaJiGetTaskRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.role.RoleManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求领取离线挂机的任务奖励, 领到背包满时会停止, 清理背包后才能继续领</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqLiXianGuaJiGetTaskRewardHandler extends AbstractHandler<ReqLiXianGuaJiGetTaskRewardMessage> {

    @Override
    public void doAction(ReqLiXianGuaJiGetTaskRewardMessage msg) {
        RoleManager.getInstance().ReqLiXianGuaJiGetTaskReward(SessionUtil.getRole(msg.getSession()));
    }

}
