package com.sh.game.system.task.msg;

import com.sh.game.common.communication.msg.system.task.ReqUpgradeAchieveMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.task.TaskManager;
import com.sh.server.AbstractHandler;

/**
 * <p>升级成就等级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqUpgradeAchieveHandler extends AbstractHandler<ReqUpgradeAchieveMessage> {

    @Override
    public void doAction(ReqUpgradeAchieveMessage msg) {
        TaskManager.getInstance().reqUpgradeAchieve(SessionUtil.getRole(msg));
    }

}
