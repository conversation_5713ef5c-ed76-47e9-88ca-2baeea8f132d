package com.sh.game.system.build.entity;

import com.sh.commons.tuple.TwoTuple;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/26 16:58
 */
@Getter
@Setter
public class BuildData {
    @Tag(41)
    private Map<Integer, Integer> buildExploit = new HashMap<>();
    /** buildId-<level,exp>/> */
    @Tag(42)
    private Map<Integer, TwoTuple<Integer, Integer>> buildLevel = new HashMap<>();
}
