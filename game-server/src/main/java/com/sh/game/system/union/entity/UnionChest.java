package com.sh.game.system.union.entity;

import com.sh.game.common.entity.backpack.item.Item;
import io.netty.util.internal.ConcurrentSet;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/1/22 14:52
 */
@Getter
@Setter
public class UnionChest {

    /**
     * 对应boxid
     */
    @Tag(1)
    private int boxId;

    /**
     * 获取时间，用于过期删除用
     */
    @Tag(2)
    private int gainTime;

    /**
     * 已经获取的人
     */
    @Tag(3)
    private Set<Long> gainRid = new ConcurrentSet<>();

    /**
     * 选择隐藏宝箱的人
     */
    @Tag(4)
    private Set<Long> deleteRid = new ConcurrentSet<>();

    @Tag(5)
    private int kill;

    @Tag(6)
    private int monsterId;

    @Tag(7)
    private String name;

    @Tag(8)
    private Map<Long, List<Item>> rewardLog = new ConcurrentHashMap<>();
}
