package com.sh.game.system.identify;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.item.EquipAttribute;
import com.sh.game.system.identify.script.IIdentifyScript;
import com.sh.script.ScriptEngine;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class IdentifyManager {
    private static final IdentifyManager INSTANCE = new IdentifyManager();

    public static final Map<Long, TwoTuple<Long, EquipAttribute>> identifyResult = new HashMap<>();

    private IdentifyManager() {

    }

    public static IdentifyManager getInstance() {
        return INSTANCE;
    }


    public void reqIdentify(Role role, long uid, List<Integer> lockAttrPos, List<Integer> lockBuffPos, int type) {
        Optional.ofNullable(ScriptEngine.get1t1(IIdentifyScript.class)).ifPresent(script -> script.reqIdentify(role,
                uid, lockAttrPos, lockBuffPos, type));
    }

    public void reqSave(Role role, long uid) {
        Optional.ofNullable(ScriptEngine.get1t1(IIdentifyScript.class)).ifPresent(script -> script.reqSave(role, uid));
    }

    public void unlockFengyin(Role role, long lid) {
        Optional.ofNullable(ScriptEngine.get1t1(IIdentifyScript.class)).ifPresent(script -> script.unlockFengyin(role, lid));
    }

    public boolean checkTrade(Item item) {
        return ScriptEngine.invoke1t1WithRet(IIdentifyScript.class, script -> script.checkTrade(item));
    }

}
