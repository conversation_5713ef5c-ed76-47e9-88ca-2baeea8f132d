package com.sh.game.system.chat.chattype.impl;

import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.communication.notice.RemoteChatNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.UnionCampConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.server.ConnectionConst;
import com.sh.game.system.chat.chattype.IChat;
import com.sh.game.system.unionCamp.UnionCampManager;

import java.util.List;

/**
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-05-12
 **/
public class CampChat implements IChat {

    /**
     * 阵营聊天
     *
     * @param role   发送者
     * @param target 接收者
     * @param msg    消息内容
     */
    @Override
    public void chat(Role role, long target, ResChatMessage msg, int banFlag) {
        int campType = UnionCampManager.getInstance().findUnionCampType(role);
        if (!UnionCampConst.ALL_CAMP_TYPE.contains(campType)) {
            return;
        }
        msg.getChat().setHostId(role.getSid());
        ModuleClient client = GameContext.getGameServer().getSceneModule().getClient();
        List<RPCConnection> connList = client.getConnectionListByType(ConnectionConst.TYPE.CROSS);
        RemoteChatNotice notice = new RemoteChatNotice();
        notice.setMsg(msg);
        notice.setCampType(campType);
        connList.forEach(connection -> {
            notice.addHost(connection.getHostId());
        });
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_COMMON, notice, 0);
    }

}
