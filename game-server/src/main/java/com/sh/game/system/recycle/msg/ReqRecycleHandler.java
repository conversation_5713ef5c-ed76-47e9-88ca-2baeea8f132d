package com.sh.game.system.recycle.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.recycle.RecycleManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.recycle.ReqRecycleMessage;

/**
 * <p>请求回收</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqRecycleHandler extends AbstractHandler<ReqRecycleMessage> {

    @Override
    public void doAction(ReqRecycleMessage msg) {
        RecycleManager.getInstance().reqRecycle(SessionUtil.getRole(msg.getSession()), msg.getItems());
    }

}
