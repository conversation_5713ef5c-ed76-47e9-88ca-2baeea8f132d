package com.sh.game.system.yuanjingxiaopu.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleYuanJingStore extends AbstractRoleEntity {
    @Tag(1)
    private long id;

    /**
     * 抽卡次数
     */
    @Tag(2)
    private int drawCount;
}
