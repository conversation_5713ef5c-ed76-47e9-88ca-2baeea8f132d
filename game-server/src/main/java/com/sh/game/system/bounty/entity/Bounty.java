package com.sh.game.system.bounty.entity;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020/9/14 15:29
 */
@Getter
@Setter
public class Bounty {
    /**
     * id
     */
    @Tag(1)
    private int id;

    /**
     * 是否已领奖
     */
    @Tag(2)
    private boolean reward;

    /**
     * 击杀怪物数 配置id -> 击杀数
     */
    @Tag(3)
    private Map<Integer, Integer> monster = new HashMap<>();
}
