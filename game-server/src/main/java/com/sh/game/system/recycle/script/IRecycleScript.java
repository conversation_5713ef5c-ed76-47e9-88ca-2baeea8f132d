package com.sh.game.system.recycle.script;

import com.sh.game.common.communication.msg.system.recycle.bean.ItemDescBean;
import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

/**
 * 回收相关接口
 */
public interface IRecycleScript extends IScript {

    void reqRecycle(Role role, List<ItemDescBean> items);

    /**
     * 人民币回收
     * @param role
     * @param recycleId
     */
    void rmbRecycle(Role role, int recycleId);


    /**
     * 发送人民币回收限制信息
     */
    void sendRMBRecyleLimitInfo(Role role);

    /**
     * 请求查询人民币回收信息
     */
    void reqRMBRecycleInfo(Role role);

    /**
     *  请求人民币兑换
     */
    void reqRMBExchange(Role role, int val);

    /**
     *  一键回收
     */
    void reqRMBRecyleAll(Role role, List<Integer> recycleIds);

    /**
     * 获取当前玩家人民币未兑换金额
     *
     * @param role 角色
     * @return int 人民币已兑换金额
     */
    int getUnUseRMBVal(Role role);

    /**
     * 元宝兑换
     *
     * @param role  role
     * @param count 兑换次数
     */
    void reqYuanBaoDuiHuan(Role role, int count);

}
