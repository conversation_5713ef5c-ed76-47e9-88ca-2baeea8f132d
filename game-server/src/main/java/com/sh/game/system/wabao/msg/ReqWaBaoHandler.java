package com.sh.game.system.wabao.msg;

import com.sh.game.common.communication.msg.system.wabao.ReqWaBaoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWaBaoManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求寻宝</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWaBaoHandler extends AbstractHandler<ReqWaBaoMessage> {

    @Override
    public void doAction(ReqWaBaoMessage msg) {
        ActivityWaBaoManager.getInstance().reqWaBao(SessionUtil.getRole(msg.getSession()), msg.getWabaoId());
    }

}
