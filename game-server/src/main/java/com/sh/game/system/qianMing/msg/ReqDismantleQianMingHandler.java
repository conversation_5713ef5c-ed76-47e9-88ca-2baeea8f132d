package com.sh.game.system.qianMing.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.qianMing.QianMingManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.qianMing.ReqDismantleQianMingMessage;

/**
 * <p>请求拆卸签名</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDismantleQianMingHandler extends AbstractHandler<ReqDismantleQianMingMessage> {

    @Override
    public void doAction(ReqDismantleQianMingMessage msg) {
        QianMingManager.getInstance().reqDismantleQianMing(SessionUtil.getRole(msg), msg.getIndex());
    }

}
