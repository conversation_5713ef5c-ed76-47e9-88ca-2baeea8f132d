package com.sh.game.system.store.entity;

import io.protostuff.Tag;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *<AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/10/13 17:40
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 */
@lombok.Getter
@lombok.Setter
public class StoreItemInfo {

    @Tag(1)
    private int storeId;

    @Tag(2)
    private int count;

    @Tag(3)
    private int boxItemId;

    @Tag(4)
    private int boxItemCount;

    @Tag(5)
    private boolean isCanBuy;

}
