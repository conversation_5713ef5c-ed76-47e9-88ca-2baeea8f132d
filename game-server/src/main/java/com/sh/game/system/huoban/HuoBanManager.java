package com.sh.game.system.huoban;

import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.huoban.script.IHuoBanScript;
import com.sh.game.system.touying.entity.RoleLineUp;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/10/10 15:39
 */
public class HuoBanManager {

    public static final HuoBanManager INSTANCE = new HuoBanManager();

    public static HuoBanManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.info(role));
    }

    public void refreshHuoBan(Role role, int type) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.refreshHuoBan(role, type));
    }

    public void refreshHuoBan(Role role) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.refresh<PERSON>uo<PERSON><PERSON>(role));
    }

    public void buyHuoBan(Role role, int index) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.buyHuoBan(role, index));
    }

    public void huoBanLevelUp(Role role, int configId) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanLevelUp(role, configId));
    }

    public void buyHuoBanBagSize(Role role) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.buyHuoBanBagSize(role));
    }

    /**
     * 变更伙伴状态
     */
    public void huoBanState(Role role, int configId, int index,int lineupIndex) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanState(role, configId, index,lineupIndex));
    }

    public void huoBanQiangHua(Role role, int configId, int type) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanQiangHua(role, configId, type));
    }

    public void calHuoBanAttr(Role role, Attribute attribute) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.calHuoBanAttr(role, attribute));
    }

    public void huoBanRest(Role role, int configId) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanRest(role, configId));
    }

    public void huoBanItemJiHuo(Role role, int configId) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanItemJiHuo(role, configId));
    }

    public void huoBanJiHuoByCount(Role role, int configId, int count) {
        ScriptEngine.invoke1t1(IHuoBanScript.class, s -> s.huoBanJiHuoByCount(role, configId, count));
    }

    public int updateShangZhenTask(Role role) {
        return ScriptEngine.invoke1t1WithRet(IHuoBanScript.class, script -> script.updateShangZhenTask(role));
    }
}
