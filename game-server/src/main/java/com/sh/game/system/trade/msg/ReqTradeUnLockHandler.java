package com.sh.game.system.trade.msg;

import com.sh.game.common.communication.msg.system.trade.ReqTradeUnLockMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.trade.TradeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求取消锁定</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_SOCIAL)
public class ReqTradeUnLockHandler extends AbstractHandler<ReqTradeUnLockMessage> {

    @Override
    public void doAction(ReqTradeUnLockMessage msg) {
        TradeManager.getInstance().reqTradeUnLock(SessionUtil.getRole(msg.getSession()));
    }

}
