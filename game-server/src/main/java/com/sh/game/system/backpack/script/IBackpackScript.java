package com.sh.game.system.backpack.script;

import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.notice.NoticeCallback;
import com.sh.script.IScript;

import java.util.List;
import java.util.Map;

public interface IBackpackScript extends IScript {
    boolean costItem(Role role, List<int[]> costItems, LogAction logAction, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place... places);

    boolean doCostItem(Role role, List<int[]> costItems, LogAction logAction, BackpackConst.Place... places);

    Boolean addItem(Role role, List<int[]> addItems, LogAction logAction, boolean tip, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place place);

    void reqBuyJiTan(Role role);

    void itemLog(Role role, List<ItemChange> changes, LogAction logAction);

    void coinLog(Role role, Map<Integer, Long> changes, Map<Integer, Long> oldValues, int action);

    /**
     * 请求领取每日体力
     *
     * @param role 角色
     */
    void reqDailyPhysicalPower(Role role);

    /**
     * 请求每日体力信息
     *
     * @param role 角色
     */
    void reqPhysicalPowerInfo(Role role);

    /**
     * 穿脱校验
     *
     * @param role         角色
     * @param sourceConfig 源道具配置
     * @param targetWhere  目标背包
     * @param targetIndex  目标位置
     * @param sourceItem   源道具
     * @return boolean true: 可穿脱    false: 不可穿脱
     */
    boolean mergeVerify(Role role, ItemConfig sourceConfig, int targetWhere, int targetIndex, Item sourceItem);

    /**
     * 通过唯一id查找道具，此方法会检查背包种是否又被复制的道具
     * @param role
     * @param uniqueId
     * @return
     */
    Item findItemByUniqueId(Role role,long uniqueId);

    void reqCoin(Role role);

    void reqGrid(Role role, int where);

    void reqUnlock(Role role, int where, int count);

    void reqSort(Role role, int where);

    void reqMerge(Role role, int sourceWhere, int sourceIndex, int targetWhere, int targetIndex, int count, int logAction);

    boolean executeMerge(Role role, Backpack backpack, Storage sourceStorage, ItemConfig sourceConfig, Item sourceItem, int sourceWhere, int sourceIndex, Storage targetStorage, Item targetItem, int targetWhere , int targetIndex, int count, int logAction);

    void reqDiscard(Role role, int where, int index, int count);

    void onUpdate(Role role, List<ItemChange> changes);

    void fetchTreasure(Role role, List<Long> lidList);

    void reqExchangeExp(Role role, int itemId, long actorId);

    void reqExchangeExpInfo(Role role);

    /**
     * 缴械
     *
     * @param role  角色
     * @param pos   部位
     * @param time  时间
     */
    void disarmEquip(Role role, int pos, long time);
}
