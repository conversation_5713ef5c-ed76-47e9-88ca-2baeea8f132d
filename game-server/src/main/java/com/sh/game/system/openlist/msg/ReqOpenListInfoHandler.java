package com.sh.game.system.openlist.msg;

import com.sh.game.common.communication.msg.system.openlist.ReqOpenListInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.openlist.OpenListManager;
import com.sh.server.AbstractHandler;

@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqOpenListInfoHandler extends AbstractHandler<ReqOpenListInfoMessage> {

    @Override
    public void doAction(ReqOpenListInfoMessage msg) {
        OpenListManager.getInstance().info(SessionUtil.getRole(msg));
    }

}