package com.sh.game.system.itemUpgrade.msg;

import com.sh.game.common.communication.msg.system.itemUpgrade.ReqItemUpgradeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.itemUpgrade.ItemUpgradeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求道具升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqItemUpgradeHandler extends AbstractHandler<ReqItemUpgradeMessage> {

    @Override
    public void doAction(ReqItemUpgradeMessage msg) {
        ItemUpgradeManager.getInstance().itemUpgrade(SessionUtil.getRole(msg), msg.getCfgId(), msg.getIndex());
    }

}
