package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.XiaLvConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.system.using.ItemUsage;
import com.sh.game.system.xialv.XiaLvManager;

import java.util.List;

public class ItemUsageForXiaLv extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        if (useParam.length <= 0 || useParam[0].length <= 0) {
            return false;
        }
        int xiaLvId = useParam[0][0];
        XiaLvConfig xiaLvConfig = ConfigDataManager.getInstance().getById(XiaLvConfig.class, xiaLvId);
        boolean check = ConditionUtil.validate(role, xiaLvConfig.getCondition(), true);
        if (!check) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        int xiaLvId = useParam[0][0];
        XiaLvManager.getInstance().unlockWaiguan(role, xiaLvId);
        return true;
    }

    @Override
    public int getUsedType() {
        return 301;
    }
}
