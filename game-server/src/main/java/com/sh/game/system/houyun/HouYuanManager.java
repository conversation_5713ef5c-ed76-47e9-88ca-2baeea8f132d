package com.sh.game.system.houyun;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.houyun.sctipt.IHouYuanScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:14
 */
public class HouYuanManager {

    private static final HouYuanManager INSTANCE = new HouYuanManager();

    public static HouYuanManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.info(role));
    }

    public void buyRat(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.buyRat(role));
    }

    public void takeItem(Role role, long rid, int index, int count) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.takeItem(role, rid, index, count));
    }

    public void recallRat(Role role, long rid, int index) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.recallRat(role, rid, index));
    }

    public void refreshHouYuanItem(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.refreshHouYuanItem(role));
    }

    public void refreshHouYuanList(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.refreshHouYuanList(role));
    }

    public void superRefreshHouYuanItem(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.superRefreshHouYuanItem(role));
    }

    public void toHouYuan(Role role, long rid) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.toHouYuan(role, rid));
    }

    public void checkRat(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.checkRat(role));
    }

    public void ratAutoItem(Role role, List<Integer> cid) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.ratAutoItem(role, cid));
    }

    public void ratAutoFreeUse(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.ratAutoFreeUse(role));
    }

    public void reqJuBaoPenReward(Role role) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.reqJuBaoPenReward(role));
    }

    public void reqRatAutoStateChange(Role role, int state) {
        ScriptEngine.invoke1t1(IHouYuanScript.class, s -> s.reqRatAutoStateChange(role, state));
    }
}
