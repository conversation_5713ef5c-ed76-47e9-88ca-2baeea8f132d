package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.TipUtil;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

/**
 * 传送（指定传送点）
 */
public class ItemUsageForStoneHeartCertain extends ItemUsage {


    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        MapConfig mapConfig = getMapConfig(role);
        if (mapConfig == null) {
            return false;
        }
        if (mapConfig.getHome() == 1) {
            TipUtil.show(role, CommonTips.服务_当前地图无法使用);
            return false;
        }

        int[][] useParam = config.getUseParam();
        return useParam.length >= 1 && useParam[0].length >= 1;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        TeleportManager.getInstance().reqTeleport(role, config.getUseParam()[0][0]);
        return true;
    }

    @Override
    public int getUsedType() {
        return 115;
    }
}
