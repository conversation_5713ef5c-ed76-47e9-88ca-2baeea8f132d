package com.sh.game.system.imperialedictchallenge;

import com.sh.game.common.config.model.EdictChallengeConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.imperialedictchallenge.entity.RoleEdictChallenge;
import com.sh.game.system.imperialedictchallenge.script.IEdictChallengeScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * 皇榜挑战
 *
 * <AUTHOR>
 * @date 2022/5/30 13:42
 */
public class EdictChallengeManage {

    private static final EdictChallengeManage INSTANCE = new EdictChallengeManage();

    private EdictChallengeManage() {
    }

    public static EdictChallengeManage getInstance() {
        return INSTANCE;
    }

    /**
     * 根据角色id获取角色当前挑战信息
     *
     * @param id 角色id roleId
     * @return {@link RoleEdictChallenge} 角色当前挑战信息
     */
    public RoleEdictChallenge find(long id) {
        return ScriptEngine.invoke1t1WithRet(IEdictChallengeScript.class, script -> script.find(id));
    }

    /**
     * 判断是否有在接取的任务
     *
     * @param id 角色id roleId
     * @return boolean 是否有在接取的任务
     */
    public boolean isHasTask(long id) {
        return ScriptEngine.invoke1t1WithRet(IEdictChallengeScript.class, script -> script.isHasTask(id));
    }

    /**
     * 创建任务
     *
     * @param role 角色
     */
    public void creatTask(Role role) {
        ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.creatTask(role));
    }

    /**
     * 获取角色当前挑战信息
     *
     * @param role 角色
     */
    public void sendEdictInfo(Role role) {
        ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.sendEdictInfo(role.getRoleId()));
    }

    /**
     * 领取奖励
     *
     * @param role 角色
     * @param isExtra 是否领取额外奖励
     */
    public void gainReward(Role role, boolean isExtra) {
        ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.gainReward(role, isExtra));
    }

    /**
     * 获取满足当前角色条件的皇榜挑战配置列表
     * 满足:
     * 开服控制条件、
     * 大陆控制条件、
     * 角色当前所在地图大陆id对应配置大陆id
     *
     * @param role 角色
     * @return List<EdictChallengeConfig> 皇榜挑战配置列表
     */
    public List<EdictChallengeConfig> getEdictChallengeConfigList(Role role) {
        return ScriptEngine.invoke1t1WithRet(IEdictChallengeScript.class, script -> script.getEdictChallengeConfigList(role));
    }

    /**
     * 放弃挑战
     *
     * @param role 角色
     */
    public void abandonEdict(Role role) {
       ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.abandonEdict(role));
    }

    /**
     * 刷新挑战
     *
     * @param role 角色
     */
    public void refreshEdict(Role role) {
        ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.refreshEdict(role));
    }

    /**
     * 接取指定怪物挑战(暂时只给GM命令使用)
     *
     * @param role          角色
     * @param monsterId     怪物配置id
     */
    public void creatAppointTask(Role role, int monsterId) {
        ScriptEngine.invoke1t1(IEdictChallengeScript.class, script -> script.creatAppointTask(role, monsterId));
    }
}
