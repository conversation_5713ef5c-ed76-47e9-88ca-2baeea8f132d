package com.sh.game.system.king.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.king.KingManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.king.ReqKingInfoMessage;

/**
 * <p>请求帝王信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqKingInfoHandler extends AbstractHandler<ReqKingInfoMessage> {

    @Override
    public void doAction(ReqKingInfoMessage msg) {
        KingManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
