package com.sh.game.system.sectskill.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.sectskill.SectSkillManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.sectskill.ReqSectSkillUpgradeMessage;

/**
 * <p>请求门派技能升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqSectSkillUpgradeHandler extends AbstractHandler<ReqSectSkillUpgradeMessage> {

    @Override
    public void doAction(ReqSectSkillUpgradeMessage msg) {
        SectSkillManager.getInstance().reqSectSkillUpgrade(SessionUtil.getRole(msg), msg.getGroup());
    }

}
