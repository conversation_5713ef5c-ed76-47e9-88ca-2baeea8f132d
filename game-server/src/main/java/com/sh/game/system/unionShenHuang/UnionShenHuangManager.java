package com.sh.game.system.unionShenHuang;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.unionShenHuang.script.IUnionShenHuangScript;
import com.sh.script.ScriptEngine;

public class UnionShenHuangManager {

    private static final UnionShenHuangManager INSTANCE = new UnionShenHuangManager();

    public static UnionShenHuangManager getInstance() {
        return INSTANCE;
    }

    public void reqUnionShenHuangUp(Role role, int count) {
        ScriptEngine.invoke1t1(IUnionShenHuangScript.class, script -> script.reqUnionShenHuangUp(role, count));
    }

    public void reqUnionShenHuangInfo(Role role) {
        ScriptEngine.invoke1t1(IUnionShenHuangScript.class, script -> script.reqUnionShenHuangInfo(role));
    }
}
