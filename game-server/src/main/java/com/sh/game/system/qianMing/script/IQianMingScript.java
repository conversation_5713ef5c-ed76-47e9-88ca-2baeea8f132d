package com.sh.game.system.qianMing.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IQianMingScript extends IScript {
    /**
     * 请求镶嵌
     *
     * @param role
     * @param index     装备位
     * @param fashionId 签名时装id
     */
    void reqInlayQianMing(Role role, int index, int fashionId);

    /**
     * 请求拆除
     *
     * @param role
     * @param index 装备位
     */
    void reqDismantleQianMing(Role role, int index);

    /**
     * 请求签名信息
     *
     * @param role
     */
    void reqQianMingInfo(Role role);
}
