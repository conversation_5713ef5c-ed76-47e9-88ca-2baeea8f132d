package com.sh.game.system.quickdressup;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.quickdressup.script.IQuickDressUpScript;
import com.sh.script.ScriptEngine;
import lombok.Getter;

public class QuickDressUpManager {
    @Getter
    private static final QuickDressUpManager instance = new QuickDressUpManager();

    private QuickDressUpManager() {
    }

    public void quickDressUp(Role role, int cid) {
        ScriptEngine.invoke1t1(IQuickDressUpScript.class, script -> script.quickDressUp(role, cid));
    }
}
