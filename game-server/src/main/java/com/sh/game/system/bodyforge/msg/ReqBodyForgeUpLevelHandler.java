package com.sh.game.system.bodyforge.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.bodyforge.BodyForgeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.bodyforge.ReqBodyForgeUpLevelMessage;

/**
 * <p>请求锻体升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqBodyForgeUpLevelHandler extends AbstractHandler<ReqBodyForgeUpLevelMessage> {

    @Override
    public void doAction(ReqBodyForgeUpLevelMessage msg) {
        BodyForgeManager.getInstance().reqUpgrade(SessionUtil.getRole(msg), msg.getType(), msg.getInsure());
    }

}
