package com.sh.game.system.foundation;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.foundation.script.IFoundationScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class FoundationManager {
    private static final FoundationManager INSTANCE = new FoundationManager();

    private FoundationManager() {
    }

    public static FoundationManager getInstance() {
        return INSTANCE;
    }

    private Optional<IFoundationScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IFoundationScript.class));
    }


    public void receiveFound(Role role, int cid, int type) {
        getScript().ifPresent(iFoundationScript -> iFoundationScript.receiveFound(role, cid, type));
    }
}
