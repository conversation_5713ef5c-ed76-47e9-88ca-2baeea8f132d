package com.sh.game.system.chaijia.msg;

import com.sh.game.common.communication.msg.system.chaijia.ReqRecycleItemMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chaijia.ChaiJiaManager;
import com.sh.server.AbstractHandler;

@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqRecycleItemHandler extends AbstractHandler<ReqRecycleItemMessage> {

    @Override
    public void doAction(ReqRecycleItemMessage msg) {
        ChaiJiaManager.getInstance().recycleItem(SessionUtil.getRole(msg), msg.getItemUid(), msg.getType());
    }

}