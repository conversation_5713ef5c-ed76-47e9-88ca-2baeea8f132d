package com.sh.game.system.welfare.entity;

import com.google.common.collect.Lists;
import io.protostuff.Tag;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * Date  : 2017-11-24 15:03
 * Desc  :
 */
@lombok.Getter
@lombok.Setter
public class RoleSign {

    /**
     * 总的签到天数（配置表的id)
     */
    @Tag(1)
    private int signDays = 0;

    /**
     * 最新领奖时间
     */
    @Tag(2)
    private long lastTime = 0;

    /**
     * 累计签到次数(永久)
     */
    @Tag(4)
    private int signDaysForever;

    /**
     * 已获得的累计签到奖励
     */
    @Tag(5)
    private List<Integer> hasDrawList = Lists.newArrayList();

    /**
     * 当前的签到数据
     */
    @Tag(6)
    private Map<Integer, Integer> acquired = new HashMap<>();
}
