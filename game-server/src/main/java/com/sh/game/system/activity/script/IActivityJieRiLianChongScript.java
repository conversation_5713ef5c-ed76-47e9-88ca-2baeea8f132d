package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2022/5/5 16:13
 */
public interface IActivityJieRiLianChongScript extends IScript {

    /**
     * 请求连充信息
     *
     * @param role
     */
    void info(Role role);

    /**
     * 请求每日充值奖励
     *
     * @param cfgId @{@link com.sh.game.common.config.model.JieRiLianChongConfig}
     */
    void reqDayRechargeReward(Role role, int cfgId);

    /**
     * 请求活动连充奖励
     *
     * @param cfgId @{@link com.sh.game.common.config.model.JieRiLianChongRewardConfig}
     */
    void reqActivityRechargeReward(Role role, int cfgId);

    /**
     * 请求活动累充奖励
     *
     * @param cfgId @{@link com.sh.game.common.config.model.JieRiLianChongFinalConfig}
     */
    void reqTotalRechargeReward(Role role, int cfgId);
}
