package com.sh.game.system.shengqi.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shengqi.ShengQiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.shhengqi.ReqShengQiInfoMessage;

/**
 * <p>请求圣器信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShengQiInfoHandler extends AbstractHandler<ReqShengQiInfoMessage> {

    @Override
    public void doAction(ReqShengQiInfoMessage msg) {
        ShengQiManager.getInstance().info(SessionUtil.getRole(msg));
    }

}
