package com.sh.game.system.season.msg;

import com.sh.game.common.communication.msg.system.season.ReqSeasonPreRewardStateMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.season.SeasonManager;
import com.sh.server.MessageHandler;
import java.lang.Override;

/**
 * 请求赛季预告期奖励领取状态信息
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqSeasonPreRewardStateHandler extends MessageHandler<ReqSeasonPreRewardStateMessage> {
  @Override
  public void doAction(ReqSeasonPreRewardStateMessage msg) {
    SeasonManager.getInstance().sendPreRewardState(SessionUtil.getRole(msg.getSession()));
  }
}
