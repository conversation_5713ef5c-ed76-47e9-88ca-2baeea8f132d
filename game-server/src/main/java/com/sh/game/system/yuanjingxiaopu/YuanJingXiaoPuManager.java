package com.sh.game.system.yuanjingxiaopu;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.yuanjingxiaopu.script.IYuanJingXiaoPuScript;
import com.sh.script.ScriptEngine;
import lombok.Getter;

public class YuanJingXiaoPuManager {
    @Getter
    private static final YuanJingXiaoPuManager instance = new YuanJingXiaoPuManager();

    private YuanJingXiaoPuManager() {
    }

    public void drawCard(Role role, int cid, int num) {
        ScriptEngine.invoke1t1(IYuanJingXiaoPuScript.class, script -> script.drawCard(role, cid, num));
    }

    public void sendMsg(Role role) {
        ScriptEngine.invoke1t1(IYuanJingXiaoPuScript.class, script -> script.sendMsg(role));
    }
}
