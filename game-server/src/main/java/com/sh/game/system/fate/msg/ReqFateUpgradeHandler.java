package com.sh.game.system.fate.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fate.FateManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.fate.ReqFateUpgradeMessage;

/**
 * <p>请求天命升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqFateUpgradeHandler extends AbstractHandler<ReqFateUpgradeMessage> {

    @Override
    public void doAction(ReqFateUpgradeMessage msg) {
        FateManager.getInstance().reqFateUpgrade(SessionUtil.getRole(msg), msg.getType());
    }

}
