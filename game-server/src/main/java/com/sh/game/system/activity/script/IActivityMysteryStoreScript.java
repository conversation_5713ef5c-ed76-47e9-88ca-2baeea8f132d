package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleMysteryStore;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-11-20
 **/
public interface IActivityMysteryStoreScript extends IScript {

    RoleMysteryStore find(long rid);

    void reqInfo(Role role);

    void reqFlushGoods(Role role);

    void reqBuyGoods(Role role, int cid);

    void loginFlushMysteryStore(Role role, long lastLoginTimestamp);

}
