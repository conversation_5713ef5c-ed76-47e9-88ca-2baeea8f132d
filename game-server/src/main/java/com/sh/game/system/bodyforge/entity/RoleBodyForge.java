package com.sh.game.system.bodyforge.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.handbooksublimation.entity.HandBookSublimationData;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 神魔锻体
 *
 * <AUTHOR>
 * @date 2022/07/23 22:25
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleBodyForge extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 神魔锻体信息
     * key:     type 类型
     * value:   level 等级
     */
    @Tag(2)
    private Map<Integer, Integer> bodyForgeMap = new HashMap<>();
}
