package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityTouZiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqTouZiRewardMessage;

/**
 * <p>请求投资奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqTouZiRewardHandler extends AbstractHandler<ReqTouZiRewardMessage> {

    @Override
    public void doAction(ReqTouZiRewardMessage msg) {
        ActivityTouZiManager.getInstance().reward(SessionUtil.getRole(msg), msg.getCid());
    }

}
