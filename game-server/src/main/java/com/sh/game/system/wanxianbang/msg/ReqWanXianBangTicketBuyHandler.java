package com.sh.game.system.wanxianbang.msg;

import com.sh.game.common.communication.msg.system.wanxianbang.ReqWanXianBangTicketBuyMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.wanxianbang.WanXianBangManager;
import com.sh.server.AbstractHandler;


@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWanXianBangTicketBuyHandler extends AbstractHandler<ReqWanXianBangTicketBuyMessage> {

    @Override
    public void doAction(ReqWanXianBangTicketBuyMessage msg) {
        WanXianBangManager.getInstance().buyTicket(SessionUtil.getRole(msg), msg.getCount());
    }

}
