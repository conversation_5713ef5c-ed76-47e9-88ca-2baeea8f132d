package com.sh.game.system.storygd.entity;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/28 10:39
 */
@Getter
@Setter
public class StoryGdData {
    @Tag(20)
    private int storyMainChapter;
    @Tag(21)
    private int storyMainStep;
    @Tag(22)
    private int storyMainGameId;
    @Tag(23)
    private boolean storyMainGameComplete;
    @Tag(24)
    private List<Integer> storyMainGameStepList = new ArrayList<>();
    @Tag(25)
    private List<Integer> storyList = new ArrayList<>();
}
