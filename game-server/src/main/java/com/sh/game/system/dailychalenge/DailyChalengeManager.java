package com.sh.game.system.dailychalenge;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.dailychalenge.entity.RoleChalenge;
import com.sh.game.system.dailychalenge.script.IDailyChalengeScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/7/13
 */
public class DailyChalengeManager {
    private static final DailyChalengeManager INSTANCE = new DailyChalengeManager();


    private DailyChalengeManager() {

    }

    public static DailyChalengeManager getInstance() {
        return INSTANCE;
    }

    /**
     * 模块获取对象
     *
     * @param rid
     * @return
     */
    public RoleChalenge find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IDailyChalengeScript.class, script -> script.find(rid));
    }

    /**
     * 请求信息
     *
     * @param role
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqInfo(role));
    }

    /**
     * 领取奖励
     *
     * @param role
     * @param mul   多倍领取，0表示普通，其他对应task表mutireward的倍数
     */
    public void reqReward(Role role, int mul) {
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqReward(role, mul));
    }

    /**
     * 接取任务
     *
     * @param role
     */
    public void reqAccept(Role role) {
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqAccept(role));
    }

    /**
     * 刷新任务
     *
     * @param role
     */
    @Deprecated
    public void reqRefresh(Role role) {
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqRefresh(role));
    }

    /**
     * 一键完成每日任务
     * @param role
     */
    @Deprecated
    public void reqFinishAll(Role role){
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqFinishAll(role));
    }

    /**
     * 请求重新刷新当前接受的任务
     *
     * @param role 角色
     */
    public void reqRefreshSingle(Role role) {
        ScriptEngine.invoke1t1(IDailyChalengeScript.class, script -> script.reqRefreshSingle(role));
    }

}
