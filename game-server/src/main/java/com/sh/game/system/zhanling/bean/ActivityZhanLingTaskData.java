package com.sh.game.system.zhanling.bean;

import com.sh.game.system.task.entity.TaskRecord;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ActivityZhanLingTaskData {

    /**
     * key：战令任务类型
     * value:任务列表
     */
    @Tag(1)
    private Map<Integer, List<TaskRecord>> taskRecord = new HashMap<>();

    /**
     * 战令积分
     */
    @Tag(2)
    private int integral;
}
