package com.sh.game.system.shabake.msg;

import com.sh.game.common.communication.msg.system.shabake.ReqFixProtectMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shabake.ShaBaKeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求修改护卫防护方式</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_SOCIAL)
public class ReqFixProtectHandler extends AbstractHandler<ReqFixProtectMessage> {

    @Override
    public void doAction(ReqFixProtectMessage msg) {
        ShaBaKeManager.getInstance().reqFixProtect(SessionUtil.getRole(msg.getSession()), msg.getType());
    }

}
