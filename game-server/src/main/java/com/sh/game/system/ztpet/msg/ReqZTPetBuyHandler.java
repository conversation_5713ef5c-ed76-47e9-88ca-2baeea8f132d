package com.sh.game.system.ztpet.msg;

import com.sh.game.common.communication.msg.system.ztPet.ReqZTPetBuyMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.ztpet.ZTPetManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求购买宠物
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqZTPetBuyHandler extends MessageHandler<ReqZTPetBuyMessage> {
    @Override
    public void doAction(ReqZTPetBuyMessage msg) {
        ZTPetManager.getInstance().reqZTPetBuy(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }
}
