package com.sh.game.system.chat.chattype.impl;

import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.system.chat.chattype.IChat;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/7/29 16:43
 * desc  : 系统
 * Copyright(©) 2017 by xiaomo.
 */
public class SystemChat implements IChat {

    /**
     * 系统聊天
     *
     * @param role   发送者
     * @param target 接收者
     * @param msg    消息内容
     */
    @Override
    public void chat(Role role, long target, ResChatMessage msg, int banFlag) {
        if (banFlag == 1) {
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        MessageUtil.sendToWorld(msg);
    }


}
