package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

public class ItemUsageForBoxBuy extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[] cost = config.getUseParam()[1];
        stash.decrease(cost[0], cost[1] * count);
        stash.increase(BoxUtil.openBox(config.getUseParam()[0][0], count));
        return true;
    }

    @Override
    public int getUsedType() {
        return 211;
    }
}
