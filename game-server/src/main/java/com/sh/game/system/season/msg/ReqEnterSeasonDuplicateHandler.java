package com.sh.game.system.season.msg;

import com.sh.game.common.communication.msg.system.season.ReqEnterSeasonDuplicateMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.season.SeasonManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求进入赛季副本
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEnterSeasonDuplicateHandler extends MessageHandler<ReqEnterSeasonDuplicateMessage> {
    @Override
    public void doAction(ReqEnterSeasonDuplicateMessage msg) {
        SeasonManager.getInstance().reqEnterDuplicate(SessionUtil.getRole(msg.getSession()), msg.getDuplicateId());
    }
}
