package com.sh.game.system.shabake.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IShaBaKeScript extends IScript {
    void reqCallProtect(Role role, int type, int useType);

    void req<PERSON><PERSON><PERSON><PERSON><PERSON>(Role role);

    void reqPut<PERSON><PERSON>(Role role, long coin);

    void reqMonsterHp(Role role);

    void reqG<PERSON><PERSON><PERSON>(Role role, long coin);

    void reqFixWall(Role role);

    void reqFixProtect(Role role, int type);

    void reqShaBaKeInfo(Role role);

//    void reqDailyShaBaKeInfo(Role role);

    void onMapReady();

    void onLoginFixShaBakeTitle(Role role);

    void reqShaBaKeRedPack(Role role, int count);

    void reqShaBaKeAlloc(Role role, long uid, long itemId);

    void reqShaBaKeAllocInfo(long roleId);
}
