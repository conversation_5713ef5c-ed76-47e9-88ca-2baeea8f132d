package com.sh.game.system.equip;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.item.EquipAttribute;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleEquipJuexing;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.event.IEventOnRoleEquipStarUp;
import com.sh.game.system.equip.entity.XilianInfo;
import com.sh.game.system.equip.script.*;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 装备管理类
 *
 * <AUTHOR> 2017年6月10日 下午4:24:08
 */
@Slf4j
public class EquipManager {

    /**
     * 洗练属性
     */
    private static Map<Long, XilianInfo> xilianAttrs = new HashMap<>();

    private static final EquipManager INSTANCE = new EquipManager();

    private EquipManager() {
    }

    public static EquipManager getInstance() {
        return INSTANCE;
    }

    /**
     * 修理装备
     */
    public void equipRepair(Role role, long lid) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.equipRepair(role, lid));
    }

    /**
     * 装备升星
     *
     * @param role
     * @param itemID
     */
    public void reqEquipStarUp(Role role, long itemID) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipStarUp(role, itemID));
    }

    /**
     * 装备星数变更
     *
     * @param role
     */
    public void onEquipStarUp(Role role) {
        ScriptEngine.invoke1tn(IEventOnRoleEquipStarUp.class, script -> script.onRoleEquipStarUp(role));
    }

    public void createHeroEquip(Role role) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.createHeroEquip(role));
    }

    /**
     * 更新魔血石
     *
     * @param role
     * @param actorId
     * @param pos
     * @param magicBloodStoneValue
     */
    public void magicBloodStoneUpdate(Role role, long actorId, int pos, int magicBloodStoneValue) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.magicBloodStoneUpdate(role, actorId, pos, magicBloodStoneValue));
    }

    /**
     * 添加洗练属性
     *
     * @param roleID
     * @param uid
     * @param attribute
     */
    public void addXilianAttr(long roleID, long uid, EquipAttribute attribute) {
        XilianInfo info = new XilianInfo();
        info.setItemID(uid);
        info.setAttribute(attribute);
        xilianAttrs.put(roleID, info);
    }

    /**
     * 获取洗练信息
     *
     * @param roleID
     * @return
     */
    public XilianInfo getXilianInfo(long roleID) {
        return xilianAttrs.remove(roleID);
    }

    /**
     * 洗练装备
     *
     * @param role
     * @param uid
     */
    public void reqXilianEquip(Role role, long uid) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqXilianEquip(role, uid));
    }

    /**
     * 保存洗练属性
     *
     * @param role
     */
    public void reqXilianSave(Role role) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqXilianSave(role));
    }

    public void reqQiLinBi(Role role, long id) {
        ScriptEngine.invoke1t1(IEquipQiLinBiScript.class,s->{s.sendQiLinBi(role.getId(),id);});
    }

    /**
     * 麒麟臂增加经验
     * @param role  角色
     * @param addExp 经验值
     */
    public void upExpQiLinBi(Role role, int addExp){
        ScriptEngine.invoke1t1(IEquipQiLinBiScript.class,s->{s.addExpQiLinGe(role,addExp);});
    }

    public RoleEquipJuexing findJueXing(long roleId) {
        return ScriptEngine.invoke1t1WithRet(IEquipJuexingScript.class, script -> script.find(roleId));
    }

    public void juexing(Role role, int type) {
        ScriptEngine.invoke1t1(IEquipJuexingScript.class, script -> script.juexing(role, type));
    }

    public void reqJuexing(Role role) {
        ScriptEngine.invoke1t1(IEquipJuexingScript.class, script -> script.reqJuexing(role));
    }

    public void shenzhu(Role role, int type) {
        ScriptEngine.invoke1t1(IEquipJuexingScript.class, script -> script.shenzhu(role, type));
    }

    public void reqShenzhu(Role role) {
        ScriptEngine.invoke1t1(IEquipJuexingScript.class, script -> script.reqShenzhu(role));
    }

    public void fuhun(Role role, long itemID, int gemId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.fuhun(role, itemID, gemId));
    }

    public void refreshAttribute(Role role) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.refreshAttribute(role));
    }

    public void toubao(Role role, long id) {
        ScriptEngine.invoke1t1(IEquipTouBaoScript.class, s -> {
            s.toubao(role, id);
        });
    }

    public void qibao(Role role, long id) {
        ScriptEngine.invoke1t1(IEquipTouBaoScript.class, s -> {
            s.qibao(role, id);
        });
    }

    public Attribute equipAttrCount(Set<Integer> equipSuits, Storage storage, int career, IAvatar avatar) {
        return ScriptEngine.invoke1t1WithRet(IEquipScript.class, script -> script.equipAttrCount(equipSuits, storage, career, avatar));
    }

    /**
     * 请求安装兽决装备
     */
    public void installShouJueEquip(Role role, int sourceIndex){
        ScriptEngine.invoke1t1(IEquipShouJueScript.class, script -> script.installShouJueEquip(role, sourceIndex));
    }

    /**
     * 请求锁定一个兽决内丹
     */
    public void lockShouJueEquip(Role role,int isLock, int sourceIndex){
        ScriptEngine.invoke1t1(IEquipShouJueScript.class, script -> script.lockShouJueEquip(role, isLock, sourceIndex));
    }

    public void queryShouJueInfo(Role role){
        ScriptEngine.invoke1t1(IEquipShouJueScript.class, script -> script.queryShouJueInfo(role));

    }

    public void reqQiangHuaShouJueEquip(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipShouJueScript.class, script -> script.reqQiangHuaShouJueEquip(role, equipId));
    }


    public TwoTuple<Integer, Integer> getShouJueCanLockCount(){
        TwoTuple<Integer, Integer> retValue = new TwoTuple<>(0, 0);
        JinhaoIntegerListConverter converter = new JinhaoIntegerListConverter();
        List<Integer> globaCanUnLockSet = converter.convert(GlobalUtil.getGlobalValue(GameConst.GlobalId.SHOUJUEEQUIP_UNLOCKCNTSET));
        JinHaoAndShuXianListConverter converter2 = new JinHaoAndShuXianListConverter();
        List<int[]> globaLockSet = converter2.convert(GlobalUtil.getGlobalValue(GameConst.GlobalId.SHOUJUEEQUIP_LOCKLSET));

        if (globaCanUnLockSet.size() >= 2){
            retValue.setFirst(globaCanUnLockSet.get(0));
            retValue.setSecond(globaCanUnLockSet.get(1));
            if (retValue.getSecond() > globaLockSet.size()){
                retValue.setSecond(globaLockSet.size());
            }
            return retValue;
        }

        retValue.setSecond(globaLockSet.size());
        if (globaCanUnLockSet.size() >= 1){
            retValue.setFirst(globaCanUnLockSet.get(0));
        }else{
            retValue.setFirst(4);
            log.warn("Global表的470005配置有错误!!");
        }
        return retValue;
    }

    /**
     * 请求特戒淬炼
     *
     * @param role      角色
     * @param equipId   装备唯一id
     */
    public void reqRefinement(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipRefinementScript.class, script -> script.reqRefinement(role, equipId));
    }

    /**
     * 请求混沌装备附魂
     *
     * @param role          角色
     * @param equipId       装备唯一id
     * @param chaoticGemId  混沌宝石配置id
     */
    public void reqChaoticInlay(Role role, long equipId, int chaoticGemId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqChaoticInlay(role, equipId, chaoticGemId));
    }

    /**
     * 请求混沌装备拆分
     *
     * @param role      角色
     * @param equipId   装备唯一id
     */
    public void reqChaoticSeparate(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqChaoticSeparate(role, equipId));
    }

    /**
     * 请求混沌灵源赋灵
     *
     * @param role      角色
     * @param equipId   装备唯一id
     */
    public void reqChaoticPower(Role role, long equipId) {
        ScriptEngine.invoke1tn(IEquipScript.class, script -> script.reqChaoticPower(role, equipId));
    }

    public void equipItem(Role role,int cid) {
        ScriptEngine.invoke1tn(IEquipScript.class, script -> script.equipItem(role, cid));
    }

    public void reqDuanZaoInfo(Role role) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqDuanZaoInfo(role));
    }

    public void reqDuanZao(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqDuanZao(role, equipId));
    }

    public void reqEquipCiZuiInfo(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiZuiInfo(role, equipId));
    }

    public void reqEquipCiZuiSave(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiZuiSave(role, equipId));
    }

    public void reqEquipCiZuiXiLian(Role role, long equipId, int index) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiZuiXiLian(role, equipId, index));
    }

    public void reqEquipLock(Role role, long equipId, boolean lock) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipLock(role, equipId, lock));
    }

    public void reqEquipTakeOff(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipTakeOff(role, equipId));
    }

    public void reqEquipCiFu(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiFu(role, equipId));
    }

    public void reqEquipCiFuReset(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiFuReset(role, equipId));
    }

    public void reqEquipCiFuReforging(Role role, long equipId, int ciFuId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqEquipCiFuReforging(role, equipId, ciFuId));
    }

    public void reqEquipCuiQu(Role role, long equipId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqCuiQu(role, equipId));
    }

    public void reqEquipCuiQuInfo(Role role) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqCuiQuInfo(role));
    }

    public void reqCuiQuTakeOff(Role role, int cuiQuId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqCuiQuTakeOff(role, cuiQuId));
    }

    public void reqCuiQuWear(Role role, int cuiQuId) {
        ScriptEngine.invoke1t1(IEquipScript.class, script -> script.reqCuiQuWear(role, cuiQuId));
    }
}
