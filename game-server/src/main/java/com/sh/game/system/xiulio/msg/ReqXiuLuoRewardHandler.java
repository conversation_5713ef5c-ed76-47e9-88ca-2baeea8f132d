package com.sh.game.system.xiulio.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.xiulio.XiuLuoManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.xiuluo.ReqXiuLuoRewardMessage;

/**
 * <p>请求3v3奖励信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqXiuLuoRewardHandler extends AbstractHandler<ReqXiuLuoRewardMessage> {

    @Override
    public void doAction(ReqXiuLuoRewardMessage msg) {
        XiuLuoManager.getInstance().reqXiuLuoReward(SessionUtil.getRole(msg), msg.getCfgId());
    }

}
