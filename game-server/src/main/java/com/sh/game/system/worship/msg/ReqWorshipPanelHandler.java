package com.sh.game.system.worship.msg;

import com.sh.game.common.communication.msg.system.worship.ReqWorshipPanelMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.worship.WorshipManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求打开膜拜面板</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWorshipPanelHandler extends AbstractHandler<ReqWorshipPanelMessage> {

    @Override
    public void doAction(ReqWorshipPanelMessage msg) {
        WorshipManager.getInstance().reqWorshipPanelMessage(SessionUtil.getRole(msg.getSession()));
    }

}
