package com.sh.game.system.worship.msg;

import com.sh.game.common.communication.msg.system.worship.ReqRefreshRatioMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.worship.WorshipManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求刷新奖励倍率</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqRefreshRatioHandler extends AbstractHandler<ReqRefreshRatioMessage> {

    @Override
    public void doAction(ReqRefreshRatioMessage msg) {
        WorshipManager.getInstance().reqRefreshRatioMessage(SessionUtil.getRole(msg.getSession()), msg.getRatioType());
    }

}

