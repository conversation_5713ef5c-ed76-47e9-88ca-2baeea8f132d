package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqGainOnlineRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityOnlineGiftManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求领取在线豪礼</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqGainOnlineRewardHandler extends AbstractHandler<ReqGainOnlineRewardMessage> {

    @Override
    public void doAction(ReqGainOnlineRewardMessage msg) {
        ActivityOnlineGiftManager.getInstance().reqGainReward(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }

}
