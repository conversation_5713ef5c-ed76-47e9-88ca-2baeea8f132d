package com.sh.game.system.xuanbing.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * 玄兵
 *
 * <AUTHOR>
 * @date 2022/10/09 14:33
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleXuanBing extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 当前配置id
     */
    @Tag(2)
    private int configId = 1;
}
