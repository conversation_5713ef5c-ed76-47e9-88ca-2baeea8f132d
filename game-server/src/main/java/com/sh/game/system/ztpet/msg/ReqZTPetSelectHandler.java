package com.sh.game.system.ztpet.msg;

import com.sh.game.common.communication.msg.system.ztPet.ReqZTPetSelectMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.ztpet.ZTPetManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求选择跟随宠物
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqZTPetSelectHandler extends MessageHandler<ReqZTPetSelectMessage> {
    @Override
    public void doAction(ReqZTPetSelectMessage msg) {
        ZTPetManager.getInstance().reqZTPetSelect(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }
}
