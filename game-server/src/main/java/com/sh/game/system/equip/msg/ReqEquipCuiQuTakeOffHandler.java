package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqEquipCuiQuTakeOff;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.MessageHandler;

/**
 * 请求装备萃取脱下
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEquipCuiQuTakeOffHandler extends MessageHandler<ReqEquipCuiQuTakeOff> {
    @Override
    public void doAction(ReqEquipCuiQuTakeOff msg) {
        EquipManager.getInstance().reqCuiQuTakeOff(SessionUtil.getRole(msg), msg.getCuiQuId());
    }
}
