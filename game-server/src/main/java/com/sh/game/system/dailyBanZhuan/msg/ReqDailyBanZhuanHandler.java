package com.sh.game.system.dailyBanZhuan.msg;

import com.sh.game.common.communication.msg.system.dailyBanZhuan.ReqDailyBanZhuanMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.dailyBanZhuan.DailyBanZhuanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求每日搬砖</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDailyBanZhuanHandler extends AbstractHandler<ReqDailyBanZhuanMessage> {

    @Override
    public void doAction(ReqDailyBanZhuanMessage msg) {
        DailyBanZhuanManager.getInstance().reqChaiChu(SessionUtil.getRole(msg), msg.getCfgId());
    }

}
