package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqXilianSaveMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求保存洗练结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_COMMON)
public class ReqXilianSaveHandler extends AbstractHandler<ReqXilianSaveMessage> {

    @Override
    public void doAction(ReqXilianSaveMessage msg) {
        EquipManager.getInstance().reqXilianSave(SessionUtil.getRole(msg));
    }

}
