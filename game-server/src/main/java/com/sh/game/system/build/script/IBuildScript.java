package com.sh.game.system.build.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/26 16:52
 */
public interface IBuildScript extends IScript {
    void reqBuildInfo(Role role);

    /** 建筑区域开拓 */
    void reqBuildExploitOperate(Role role, int buildId, int itemCount);

    /** 建筑建造升级 */
    void reqBuildOperate(Role role, int buildId, int itemCount);

    /** 协助加速生产 */
    void reqBuildAssistProduction(Role role, int buildId, int cdRound);

    /** 建筑临时背包仓储 */
    void reqBuildTempBagWarehousing(Role role, int fromWhere, int itemId, int itemCount, int toWhere);

    /** 获取建筑等级 */
    int getBuildLevel(Role role, int buildId);
}
