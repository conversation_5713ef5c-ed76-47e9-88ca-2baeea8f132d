package com.sh.game.system.yuanjingxiaopu.msg;

import com.sh.game.common.communication.msg.system.yuanjing.ReqYuanJingStoreInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.yuanjingxiaopu.YuanJingXiaoPuManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求源晶小铺信息
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqYuanJingStoreInfoHandler extends MessageHandler<ReqYuanJingStoreInfoMessage> {
    @Override
    public void doAction(ReqYuanJingStoreInfoMessage msg) {
        YuanJingXiaoPuManager.getInstance().sendMsg(SessionUtil.getRole(msg.getSession()));
    }
}
