package com.sh.game.system.role;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import com.sh.game.common.communication.msg.system.role.bean.HeroBean;
import com.sh.game.common.config.model.AttributeScoreConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.system.role.entity.Hero;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RoleUtil {

    public static AttributeBean toAttributeBean(Attribute attribute) {
        AttributeBean attributeBean = new AttributeBean();
        ConfigDataManager.getInstance().getList(AttributeScoreConfig.class).stream().filter(cfg -> cfg.getTransmit() == 1).forEach(config -> {
            if (!attribute.getAttributeMap().containsKey(config.getId())) {
                return;
            }
            attributeBean.getAttributeType().add(config.getId());
            attributeBean.getAttributeValue().add(attribute.getAttributeMap().get(config.getId()));
        });

        return attributeBean;
    }

    public static HeroBean toHeroBean(Hero hero) {
        HeroBean bean = new HeroBean();
        if (hero == null) {
            return bean;
        }
        bean.setHp(hero.getHp());
        bean.setMp((int) hero.getMp());
        AttributeBean attribute = toAttributeBean(hero.getFinalAttribute());
        bean.setId(hero.getId());
        bean.setAttr(attribute);
        bean.setName(hero.getName());
        bean.setCareer(hero.getCareer());
        bean.setSex(hero.getSex());
        bean.setLevel(hero.getLevel());
        bean.setPower(AttributeEnum.FIGHT_POWER.getAttrValue(hero.getFinalAttribute()));
        bean.setReinLevel(hero.getZhuanShengId());
        bean.setLoyalty(hero.getLoyalty());
        bean.setHuoLongValue(hero.getHuoLongValue());
        bean.setCombSkillValue(hero.getCombSkillValue());
        return bean;
    }

    /**
     * 职业是否正确
     *
     * @param roleName roleName
     * @param career   career
     * @return boolean
     */
    public static boolean careerCorrect(String roleName, int career) {
        if (career < RoleConst.Career.ZHAN || career > RoleConst.Career.DAO) {
            log.error("玩家:{} 职业有误{} (脚本可能)", roleName, career);
            return false;
        }
        return true;
    }


    /**
     * 性别
     *
     * @param roleName roleName
     * @param sex      sex
     * @return boolean
     */
    public static boolean sexCorrect(String roleName, int sex) {
        if (sex < RoleConst.Sex.MALE || sex > RoleConst.Sex.FEMALE) {
            log.error("玩家:{} 性别有误{}(脚本可能)", roleName, sex);
            return false;
        }
        return true;
    }

}
