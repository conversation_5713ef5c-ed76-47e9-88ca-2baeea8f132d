package com.sh.game.system.chongWuEquip.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;


public interface IChongWuEquipScript extends IScript {

    /**
     * 请求宠物(装备)部位强化
     *
     * @param role
     * @param pos
     */
    void reqChongWuEquipUp(Role role, int pos);

    /**
     * 请求宠物装备鉴定
     *
     * @param role
     * @param equipId
     */
    void reqChongWuEquipJianDing(Role role, long equipId);

    /**
     * 请求宠物装备洗练
     *
     * @param role
     * @param equipId
     */
    void reqChongWuEquipXiLian(Role role, long equipId);

    void reqChongWuEquipInfo(Role role);

    /**
     * 请求宠物装备点化
     * @param role
     * @param equipId
     */
    void reqChongWuEquipDianHua(Role role, long equipId);
}
