package com.sh.game.system.bodyforge.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.bodyforge.entity.RoleBodyForge;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2022/07/23 21:53
 */
public interface IBodyForgeScript extends IScript {

    /**
     * 查询神魔锻体信息
     *
     * @param roleId 角色id
     * @return RoleBodyForge 神魔锻体
     */
    RoleBodyForge find(long roleId);

    /**
     * 请求锻体升级
     *
     * @param role      角色
     * @param type      锻体类型
     * @param insure    是否投保
     */
    void reqUpgrade(Role role, int type, boolean insure);

    /**
     * 请求角色锻体信息
     *
     * @param role 角色
     */
    void reqInfo(Role role);
}
