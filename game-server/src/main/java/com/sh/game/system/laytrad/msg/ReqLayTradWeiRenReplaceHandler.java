package com.sh.game.system.laytrad.msg;

import com.sh.game.common.communication.msg.system.laytrad.ReqLayTradWeiRenReplaceMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.laytrad.LayTradManager;
import com.sh.server.MessageHandler;
import java.lang.Override;

/**
 * 请求经营委任替换
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqLayTradWeiRenReplaceHandler extends MessageHandler<ReqLayTradWeiRenReplaceMessage> {
  @Override
  public void doAction(ReqLayTradWeiRenReplaceMessage msg) {
    LayTradManager.getInstance().reqLayTradWeiRenReplace(SessionUtil.getRole(msg), msg.getWeirenLbId(), msg.getOldWeirenHeroId(), msg.getNewWeirenHeroId());
  }
}
