package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqUnionYaoXieBiXieZhenMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionYaoXieManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求可使用的行会随机名</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqUnionYaoXieBiXieZhenHandler extends AbstractHandler<ReqUnionYaoXieBiXieZhenMessage> {

    @Override
    public void doAction(ReqUnionYaoXieBiXieZhenMessage msg) {
        UnionYaoXieManager.getInstance().biXie(SessionUtil.getRole(msg));
    }

}
