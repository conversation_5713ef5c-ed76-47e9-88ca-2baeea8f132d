package com.sh.game.system.activity.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityRechargeRebateManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityRechargeRebateInfoMessage;

/**
 * <p>请求活动数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityRechargeRebateInfoHandler extends AbstractHandler<ReqActivityRechargeRebateInfoMessage> {

    @Override
    public void doAction(ReqActivityRechargeRebateInfoMessage msg) {
        ActivityRechargeRebateManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
