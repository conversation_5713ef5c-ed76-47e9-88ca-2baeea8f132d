package com.sh.game.system.user.msg;

import com.sh.game.common.communication.msg.system.user.ReqEnterGameMessage;
import com.sh.game.system.user.UserManager;
import com.sh.server.AbstractHandler;

/**
 * <p>客户端请求进入游戏</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-07-31 14:48:10
 */
public class ReqEnterGameHandler extends AbstractHandler<ReqEnterGameMessage> {

    @Override
    public void doAction(ReqEnterGameMessage msg) {
        if (msg.getToken() != null && msg.getToken().length() > 0){
            //进行断线重连
            UserManager.getInstance().disconnectLogin(msg.getSession(), msg.getRid(), msg.getToken());
        }else {
            UserManager.getInstance().enterGame(msg.getSession(), msg.getRid());
        }

    }

}
