package com.sh.game.system.activity.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityCompeteLevelManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityCompeteLevelAcquireMessage;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityCompeteLevelAcquireHandler extends AbstractHandler<ReqActivityCompeteLevelAcquireMessage> {

    @Override
    public void doAction(ReqActivityCompeteLevelAcquireMessage msg) {
        ActivityCompeteLevelManager.getInstance().reqAcquire(SessionUtil.getRole(msg.getSession()), msg.getCid());
    }

}
