package com.sh.game.system.magicweapon.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.magicweapon.MagicWeaponManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.magicweapon.ReqMagicWeaponSlotMessage;

/**
 * <p>请求法宝开孔</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqMagicWeaponSlotHandler extends AbstractHandler<ReqMagicWeaponSlotMessage> {

    @Override
    public void doAction(ReqMagicWeaponSlotMessage msg) {
        MagicWeaponManager.getInstance().reqSlot(SessionUtil.getRole(msg), msg.getConfigId());
    }

}
