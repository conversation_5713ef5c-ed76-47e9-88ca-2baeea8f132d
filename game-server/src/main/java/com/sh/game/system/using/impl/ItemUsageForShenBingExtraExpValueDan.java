package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.count.ResCountDataMessage;
import com.sh.game.common.communication.msg.system.count.bean.CountBean;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.ShenBingHuiYuanConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.role.RoleManager;
import com.sh.game.system.shenBingHuiYuan.ShenBingManager;
import com.sh.game.system.shenBingHuiYuan.entity.RoleShenbingVip;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

import static com.sh.game.common.constant.CountConst.CountType.ITEM_USE_NUM;

/**
 * 神兵精元的使用（增加固定值经验）
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-08-18
 **/

public class ItemUsageForShenBingExtraExpValueDan extends ItemUsage {
    @Override
    public int useCount(Role role, Item item, ItemConfig config, int count) {
        return super.useCount(role, item, config, count);
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int curShenBingLevel = role.findCurShenBingLevel();
        if (curShenBingLevel <= 0) {
            TipUtil.show(role, CommonTips.脚本_神兵等级不足);
            return false;
        }

        ShenBingHuiYuanConfig shenBingHuiYuanConfig = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, curShenBingLevel);
        if (shenBingHuiYuanConfig == null) {
            return false;
        }

        // 需补充的经验值
        long expValue = (long) Math.ceil(1L * count * config.getUseParam()[0][0]);

        RoleShenbingVip roleShenBingVip = ShenBingManager.getInstance().findMoudel(role.getRoleId());
        if (roleShenBingVip.getExtraExp() - expValue < 0) {
            // 经验不足
            TipUtil.show(role, CommonTips.神兵_经验不足);
            return false;
        }

        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int curShenBingLevel = role.findCurShenBingLevel();
        ShenBingHuiYuanConfig shenBingHuiYuanConfig = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, curShenBingLevel);
        if (shenBingHuiYuanConfig == null) {
            return false;
        }

        // 需补充的经验值
        long expValue = (long) Math.ceil(1L * count * config.getUseParam()[0][0]);

        RoleShenbingVip roleShenBingVip = ShenBingManager.getInstance().findMoudel(role.getRoleId());
        // 减少当日使用经验的计数
        roleShenBingVip.countExtraExp(-expValue);
        DataCenter.updateData(roleShenBingVip);

        BackpackStash shenBingExpStash = new BackpackStash(role);
        shenBingExpStash.increase(BagConst.ItemId.ROLE_EXP, expValue);
        shenBingExpStash.commit(role, LogAction.EXP_ON_MONSTER_SHENBING, true);

        // 通知客户端更新当日使用的额外神兵经验
        RoleManager.getInstance().reqShenBingExtraDailyExp(role);

        return true;
    }

    @Override
    public boolean verifyUseCountLimit(Role role, ItemConfig config, int count) {
        // 使用次数上限
        int useLimit = config.getUseLimit();

        // 无次数限制
        if (useLimit <= 0) {
            return false;
        }

        int useCount = CountManager.getInstance().getCount(role, ITEM_USE_NUM, config.getType());

        int extraNum = shenBingHuiYuanUsageCountUp(role, config.getId());

        // 已使用数量加批量使用数量超出上限
        if (useCount + count > useLimit + extraNum) {
            TipUtil.show(role, CommonTips.服务_道具达到使用上限);
            return true;
        }
        return false;
    }

    @Override
    public void addUseCount(Role role, ItemConfig config, int count) {
        // 使用次数上限
        int useLimit = config.getUseLimit();

        //有使用限制，增加次数
        if (useLimit <= 0) {
            return;
        }

        CountManager.getInstance().count(role, ITEM_USE_NUM, config.getType(), count);
        //发送次数更新消息
        ResCountDataMessage countDataMessage = new ResCountDataMessage();
        CountBean bean = new CountBean();
        bean.setGroupId(config.getType());
        bean.setCountType(ITEM_USE_NUM.getType());
        bean.setCountNum(CountManager.getInstance().getCount(role, ITEM_USE_NUM, config.getType()));
        countDataMessage.getCountList().add(bean);
        MessageUtil.sendMsg(countDataMessage, role.getId());
    }

    @Override
    public int getUsedType() {
        return 305;
    }
}
