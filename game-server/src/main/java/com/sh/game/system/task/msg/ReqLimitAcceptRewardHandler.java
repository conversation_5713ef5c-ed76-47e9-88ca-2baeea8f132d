package com.sh.game.system.task.msg;

import com.sh.game.common.communication.msg.system.task.ReqLimitAcceptRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.task.TaskManager;
import com.sh.server.AbstractHandler;

/**
 * <p>领奖并接取下阶段限时任务</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqLimitAcceptRewardHandler extends AbstractHandler<ReqLimitAcceptRewardMessage> {

    @Override
    public void doAction(ReqLimitAcceptRewardMessage msg) {
        TaskManager.getInstance().reqLimitAcceptReward(SessionUtil.getRole(msg));
    }

}
