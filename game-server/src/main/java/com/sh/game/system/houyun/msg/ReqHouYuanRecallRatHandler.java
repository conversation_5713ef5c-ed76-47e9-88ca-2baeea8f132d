package com.sh.game.system.houyun.msg;

import com.sh.game.common.communication.msg.system.houyuan.ReqHouYuanRecallRatMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.houyun.HouYuanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求地狱之路信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqHouYuanRecallRatHandler extends AbstractHandler<ReqHouYuanRecallRatMessage> {

    @Override
    public void doAction(ReqHouYuanRecallRatMessage msg) {
        HouYuanManager.getInstance().recallRat(SessionUtil.getRole(msg), msg.getRid(), msg.getIndex());
    }

}
