package com.sh.game.system.storygd;

import com.sh.game.common.config.model.StoryGdMainConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleStory;
import com.sh.game.data.DataCenter;
import com.sh.game.system.storygd.script.IStoryGdScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/16 14:11
 */
public class StoryGdManager {
    private static StoryGdManager ourInstance = new StoryGdManager();
    public static StoryGdManager getInstance() {
        return ourInstance;
    }
    private StoryGdManager() {
    }

    /**
     * 角色剧情功能类
     * @param role
     * @return
     */
    public RoleStory getRoleStory(Role role) {
        RoleStory data = DataCenter.get(RoleStory.class, role.getId());
        if (data == null){
            data = new RoleStory();
            data.setId(role.getId());
            DataCenter.insertData(data, true);
        }
        return data;
    }

    public void reqStoryMainInfo(Role role) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.reqStoryMainInfo(role));
    }

    public void reqTriggerStoryMain(Role role) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.reqTriggerStoryMain(role));
    }

    public void reqTriggerStoryMainGame(Role role, int gameStepId) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.reqTriggerStoryMainGame(role, gameStepId));
    }

    public void reqStoryList(Role role) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.reqStoryList(role));
    }

    public void reqTriggerStory(Role role, List<Integer> storyId) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.reqTriggerStory(role, storyId));
    }

    public void setStoryMain(Role role, StoryGdMainConfig minStoryGdMainConfig) {
        ScriptEngine.invoke1tn(IStoryGdScript.class, s -> s.setStoryMain(role, minStoryGdMainConfig));
    }

}
