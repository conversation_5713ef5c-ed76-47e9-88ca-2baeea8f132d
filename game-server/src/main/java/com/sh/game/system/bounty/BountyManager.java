package com.sh.game.system.bounty;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.bounty.script.IBountyScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @data 2020/9/14 14:27
 */
public class BountyManager {
    private static final BountyManager INSTANCE = new BountyManager();

    private BountyManager() {
    }

    public static BountyManager getInstance() {
        return INSTANCE;
    }

    public void sendBountyInfo(Role role) {
        IBountyScript iBountyScript = ScriptEngine.get1t1(IBountyScript.class);
        if (iBountyScript == null) {
            return;
        }
        iBountyScript.sendBountyInfo(role);
    }

    public void getHellBountyReward(Role role, int groupId) {
        IBountyScript iBountyScript = ScriptEngine.get1t1(IBountyScript.class);
        if (iBountyScript == null) {
            return;
        }
        iBountyScript.getHellBountyReward(role, groupId);
    }

    public void countMonsterBounty(Role role, int monsterId) {
        IBountyScript iBountyScript = ScriptEngine.get1t1(IBountyScript.class);
        if (iBountyScript == null) {
            return;
        }
        iBountyScript.countMonsterBounty(role, monsterId);
    }
}
