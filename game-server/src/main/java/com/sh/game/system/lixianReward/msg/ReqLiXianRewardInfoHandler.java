package com.sh.game.system.lixianReward.msg;

import com.sh.game.common.communication.msg.system.lixianReward.ReqLiXianRewardInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.lixianReward.LiXianRewardManager;
import com.sh.server.MessageHandler;

import java.lang.Override;

/**
 * 请求离线奖励信息
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqLiXianRewardInfoHandler extends MessageHandler<ReqLiXianRewardInfoMessage> {
    @Override
    public void doAction(ReqLiXianRewardInfoMessage msg) {
        LiXianRewardManager.getInstance().sendMsg(SessionUtil.getRole(msg.getSession()));
    }
}
