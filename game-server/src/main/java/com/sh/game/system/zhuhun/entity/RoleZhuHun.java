package com.sh.game.system.zhuhun.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 灵装铸魂
 *
 * <AUTHOR>
 * @date 2022/09/22 19:59
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleZhuHun extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 铸魂数据
     * key:     pos 部位
     * value:   data 铸魂数据
     */
    @Tag(2)
    private Map<Integer, ZhuHunData> zhuHunMap = new HashMap<>();
}
