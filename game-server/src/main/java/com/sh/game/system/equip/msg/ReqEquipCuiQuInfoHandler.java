package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqEquipCuiQuInfo;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.MessageHandler;

/**
 * 请求装备萃取信息
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEquipCuiQuInfoHandler extends MessageHandler<ReqEquipCuiQuInfo> {
    @Override
    public void doAction(ReqEquipCuiQuInfo msg) {
        EquipManager.getInstance().reqEquipCuiQuInfo(SessionUtil.getRole(msg.getSession()));
    }
}
