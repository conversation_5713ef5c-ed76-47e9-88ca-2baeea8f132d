package com.sh.game.system.laytrad;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.laytrad.script.IBuildWeiRenAttrProduceScript;
import com.sh.script.ScriptEngine;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/6 19:41
 */
public class BuildWeiRenAttrProduceManager {
    private static final BuildWeiRenAttrProduceManager INSTANCE = new BuildWeiRenAttrProduceManager();
    private BuildWeiRenAttrProduceManager() {}
    public static BuildWeiRenAttrProduceManager getInstance() {
        return INSTANCE;
    }

    public void updateBuildWeirenAttr(Role role, boolean sendInfo) {
        ScriptEngine.invoke1t1(IBuildWeiRenAttrProduceScript.class, s -> s.updateBuildWeirenAttr(role, sendInfo));
    }
}
