package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IActivityShaChengJuanXianScript extends IScript {

    /**
     * 请求提交沙城捐献
     * @param role
     * @param count
     */
    void reqSubmit<PERSON>uan<PERSON><PERSON>(Role role,int count);

    /**
     * 请求沙城捐献信息
     * @param role
     */
    void reqJuanXianInfo(Role role);
}
