package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityShaChengJuanXianManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqShaChengJuanXianInfoMessage;

/**
 * <p>请求沙城捐献信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShaChengJuanXianInfoHandler extends AbstractHandler<ReqShaChengJuanXianInfoMessage> {

    @Override
    public void doAction(ReqShaChengJuanXianInfoMessage msg) {
        ActivityShaChengJuanXianManager.getInstance().reqJuanXianInfo(SessionUtil.getRole(msg));
    }

}
