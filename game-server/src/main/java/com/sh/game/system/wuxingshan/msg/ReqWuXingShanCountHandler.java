package com.sh.game.system.wuxingshan.msg;

import com.sh.game.common.communication.msg.system.wuxingshan.ReqWuXingShanCountMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.wuxingshan.WuXingShanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求武道</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWuXingShanCountHandler extends AbstractHandler<ReqWuXingShanCountMessage> {

    @Override
    public void doAction(ReqWuXingShanCountMessage msg) {
        WuXingShanManager.getInstance().reqCount(SessionUtil.getRole(msg.getSession()));
    }

}
