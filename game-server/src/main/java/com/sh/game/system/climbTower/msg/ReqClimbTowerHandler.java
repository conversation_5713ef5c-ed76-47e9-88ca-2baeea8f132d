package com.sh.game.system.climbTower.msg;

import com.sh.game.common.communication.msg.system.climbTower.ReqClimbTowerMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.climbTower.ClimbTowerManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqClimbTowerHandler extends AbstractHandler<ReqClimbTowerMessage> {

    @Override
    public void doAction(ReqClimbTowerMessage msg) {
        ClimbTowerManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
