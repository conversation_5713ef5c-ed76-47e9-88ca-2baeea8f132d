package com.sh.game.system.shenBingHuiYuan.msg;

import com.sh.game.common.communication.msg.system.shenBingHuiYuan.ReqShenBingStatusMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shenBingHuiYuan.ShenBingManager;
import com.sh.server.AbstractHandler;

/**
 * 请求已解锁神兵状态
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-07-23
 **/
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShenBingStatusHandler extends AbstractHandler<ReqShenBingStatusMessage> {

    @Override
    public void doAction(ReqShenBingStatusMessage msg) {
        ShenBingManager.getInstance().reqShenBingStatus(SessionUtil.getRole(msg.getSession()));
    }
}
