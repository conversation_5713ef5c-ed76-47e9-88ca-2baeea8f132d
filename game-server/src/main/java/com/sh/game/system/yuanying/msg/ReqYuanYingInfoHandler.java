package com.sh.game.system.yuanying.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.yuanying.YuanYingManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.yuanying.ReqYuanYingInfoMessage;

/**
 * <p>请求元婴强化面板</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqYuanYingInfoHandler extends AbstractHandler<ReqYuanYingInfoMessage> {

	@Override
	public void doAction(ReqYuanYingInfoMessage msg) {
		YuanYingManager.getInstance().reqYuanYingInfo(SessionUtil.getRole(msg));
	}

}
