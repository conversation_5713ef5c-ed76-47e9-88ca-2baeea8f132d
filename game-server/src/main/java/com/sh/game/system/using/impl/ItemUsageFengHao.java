package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.FengHaoConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.fenghao.FengHaoManager;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 封号使用道具卡
 */
@Slf4j
public class ItemUsageFengHao extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        if (useParam.length <= 0 || useParam[0].length <= 0) {
            return false;
        }

        return useParam[0].length >= 2;
     }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        //升级数量
        int fengHaoId = useParam[0][0];
        int fenghaoItem = useParam[0][1];

        FengHaoConfig fengHaoConfig = ConfigDataManager.getInstance().getById(FengHaoConfig.class, fengHaoId);
        if (fengHaoConfig == null) {
            log.error("封号道具卡使用失败,玩家:{},{}找不到封号id:{}", role.getName(), role.getId(), fengHaoId);
            return false;
        }

        //修改封号等级
        FengHaoManager.getInstance().changeLv(role, fengHaoConfig, 1);

        //删除以前的封号id
        FengHaoManager.getInstance().deleteAllTitle(role);

        //给与新的封号id
        BackpackStash backpackStash = new BackpackStash(role);
        backpackStash.increase(fenghaoItem, 1);
        backpackStash.commit(role, LogAction.Item_FengHao);


        return true;
    }



    @Override
    public int getUsedType() {
        return 420;
    }

}
