package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqUnionSetLeaderVXMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求行会事件</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqUnionSetLeaderVxHandler extends AbstractHandler<ReqUnionSetLeaderVXMessage> {

    @Override
    public void doAction(ReqUnionSetLeaderVXMessage msg) {
        UnionManager.getInstance().setLeaderVX(SessionUtil.getRole(msg.getSession()), msg.getVx());
    }

}
