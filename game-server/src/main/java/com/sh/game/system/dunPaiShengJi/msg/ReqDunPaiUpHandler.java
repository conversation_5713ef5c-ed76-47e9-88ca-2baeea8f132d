package com.sh.game.system.dunPaiShengJi.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.dunPaiShengJi.DunPaiUpManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.dunPaiShengJi.ReqDunPaiUpMessage;

/**
 * <p>请求盾牌升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDunPaiUpHandler extends AbstractHandler<ReqDunPaiUpMessage> {

    @Override
    public void doAction(ReqDunPaiUpMessage msg) {
        DunPaiUpManager.getInstance().reqSpecialUp(SessionUtil.getRole(msg), msg.getType());
    }

}
