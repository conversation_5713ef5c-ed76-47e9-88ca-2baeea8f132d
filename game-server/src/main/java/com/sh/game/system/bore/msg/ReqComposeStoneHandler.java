package com.sh.game.system.bore.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.bore.BoreManager;
import com.sh.game.common.communication.msg.system.bore.ReqComposeStoneMessage;
import com.sh.server.MessageHandler;

/**
 * <p>请求宝石合成</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqComposeStoneHandler extends MessageHandler<ReqComposeStoneMessage> {

    @Override
    public void doAction(ReqComposeStoneMessage msg) {
        BoreManager.getInstance().compose(SessionUtil.getRole(msg.getSession()), msg.getLid());
    }

}
