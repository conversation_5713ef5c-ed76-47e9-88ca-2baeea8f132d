package com.sh.game.system.injectsoul.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.injectsoul.InjectSoulManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.injectsoul.ReqInjectSoulInfoMessage;

/**
 * <p>请求注灵信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqInjectSoulInfoHandler extends AbstractHandler<ReqInjectSoulInfoMessage> {

    @Override
    public void doAction(ReqInjectSoulInfoMessage msg) {
        InjectSoulManager.getInstance().reqInjectSoulInfo(SessionUtil.getRole(msg));
    }

}
