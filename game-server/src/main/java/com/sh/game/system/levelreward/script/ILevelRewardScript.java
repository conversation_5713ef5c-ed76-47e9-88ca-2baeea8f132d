package com.sh.game.system.levelreward.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.levelreward.entity.RoleLevelRewardItem;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @since 2022-04-18 17:10
 **/
public interface ILevelRewardScript extends IScript {

    /**
     * 获取用户等级奖励信息
     *
     * @param roleId 角色id
     * @return {@link RoleLevelRewardItem} 等级奖励
     **/
    RoleLevelRewardItem find(long roleId);

    /**
     * 请求获取等级奖励
     *
     * @param role 角色
     * @param cid cfg_grade_reward等级奖励表id
     */
    void gainLevelReward(Role role, int cid);

    /**
     * 发送等级奖励领取信息
     *
     * @param role 角色
     */
    void sendLevelRewardInfo(Role role);
}
