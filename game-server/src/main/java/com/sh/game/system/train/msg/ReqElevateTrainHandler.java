package com.sh.game.system.train.msg;

import com.sh.game.common.communication.msg.system.train.ReqElevateTrainMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.train.TrainManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求接受任务</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqElevateTrainHandler extends AbstractHandler<ReqElevateTrainMessage> {

    @Override
    public void doAction(ReqElevateTrainMessage msg) {
        TrainManager.getInstance().reqElevateTrain(SessionUtil.getRole(msg), msg.getTargetType(), msg.getTrainType());
    }

}
