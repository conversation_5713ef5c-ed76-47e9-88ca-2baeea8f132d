package com.sh.game.system.sbremould.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.sbremould.ShenBingRemouldManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.sbremould.ReqShenBingRemouldInfoMessage;

/**
 * <p>请求神兵改造信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShenBingRemouldInfoHandler extends AbstractHandler<ReqShenBingRemouldInfoMessage> {

    @Override
    public void doAction(ReqShenBingRemouldInfoMessage msg) {
        ShenBingRemouldManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
