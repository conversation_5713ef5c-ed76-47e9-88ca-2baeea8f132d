package com.sh.game.system.goal.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.script.IScript;

/**
 * ATO：yumo<br>;
 * 时间：2020/11/30 14:39<br>;
 * 版本：1.0<br>;
 * 描述：
 */
public interface IGoalScript extends IScript {

    /**
     * 创建任务记录
     *
     * @param role
     * @param taskID 任务编号
     * @param sGoals 任务目标
     * @param allCheck 是否所有都
     * @param state 默认状态
     * @return
     */
    TaskRecord createTaskRecord(Role role, int taskID, int[] sGoals, boolean allCheck, int state);

    /**
     * 检查任务是否完成
     *
     * @param record
     * @return
     */
    boolean checkCompleted(TaskRecord record);

    /**
     * 更新指定类型任务目标进度
     *
     * @param role
     * @param record
     * @param goalType
     * @param params
     * @return
     */
    boolean doupdate(Role role, TaskRecord record, int goalType, int... params);

    /**
     * 检查任务进度
     *
     * @param role
     * @param record
     */
    boolean checkTaskSchedule(Role role, TaskRecord record);
}
