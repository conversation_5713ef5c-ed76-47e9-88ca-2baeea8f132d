package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqEquipCuiQuWear;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.MessageHandler;

/**
 * 请求装备萃取穿戴
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEquipCuiQuWearHandler extends MessageHandler<ReqEquipCuiQuWear> {
    @Override
    public void doAction(ReqEquipCuiQuWear msg) {
        EquipManager.getInstance().reqCuiQuWear(SessionUtil.getRole(msg), msg.getCuiQuId());
    }
}
