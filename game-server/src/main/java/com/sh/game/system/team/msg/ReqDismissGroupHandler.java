package com.sh.game.system.team.msg;

import com.sh.game.common.communication.msg.system.team.ReqDismissGroupMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.team.TeamManager;
import com.sh.server.AbstractHandler;

/**
 * <p>解散队伍请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_SOCIAL)
public class ReqDismissGroupHandler extends AbstractHandler<ReqDismissGroupMessage> {

    @Override
    public void doAction(ReqDismissGroupMessage msg) {
        TeamManager.getInstance().reqDismissTeam(SessionUtil.getRole(msg));
    }

}
