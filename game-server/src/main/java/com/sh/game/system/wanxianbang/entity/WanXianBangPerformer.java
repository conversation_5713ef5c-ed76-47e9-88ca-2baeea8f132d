package com.sh.game.system.wanxianbang.entity;

import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.shenmo.bean.ShenMo;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/17 13:17
 */
@Getter
@Setter
public class WanXianBangPerformer {

    private long rid;

    private String name;

    private Attribute attr = new Attribute();

    private int useMount;

    private int level;

    private int sex;

    private int career;

    /**
     * 时装穿戴  类型< - >穿戴
     */
    @Tag(2)
    private Map<Integer, Integer> wears = new HashMap<>();

    private int zhuanShengId;

    private Map<Integer, HuoBan> shangZhenMap = new HashMap<>();

    private List<ShenMo> shenMoGoBattles = new ArrayList<>();

    /**
     * 如果是真人当前值为0
     */
    private int robotCfgId;

    private List<Integer> monsterPet = new ArrayList<>();

    private int sid;

    private int pid;

    private Map<Integer, Integer> hoardTypeCountMap = new HashMap<>();
}
