package com.sh.game.system.wanxianbang.msg;

import com.sh.game.common.communication.msg.system.wanxianbang.ReqWanXianBangRankMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.wanxianbang.WanXianBangManager;
import com.sh.server.AbstractHandler;


@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWanXianBangRankHandler extends AbstractHandler<ReqWanXianBangRankMessage> {

    @Override
    public void doAction(ReqWanXianBangRankMessage msg) {
        WanXianBangManager.getInstance().reqRank(SessionUtil.getRole(msg));
    }

}
