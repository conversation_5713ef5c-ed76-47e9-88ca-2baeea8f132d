package com.sh.game.system.social.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.social.SocialManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.social.ReqFriendApplyOpMessage;

/**
 * <p>好友申请列表操作</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqFriendApplyOpHandler extends AbstractHandler<ReqFriendApplyOpMessage> {

    @Override
    public void doAction(ReqFriendApplyOpMessage msg) {
        SocialManager.getInstance().friendApplyOp(SessionUtil.getRole(msg.getSession()), msg.getOp(), msg.getTargetId());
    }

}
