package com.sh.game.system.activity.entity.role;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/1 9:26
 */
@Getter
@Setter
public class ShenBingKuangHuanData {

    @Tag(1)
    private Map<Integer,List<Integer>> reward = new HashMap<>();

    @Tag(2)
    private Map<Integer,Integer> rechargeMap = new HashMap<>();
}
