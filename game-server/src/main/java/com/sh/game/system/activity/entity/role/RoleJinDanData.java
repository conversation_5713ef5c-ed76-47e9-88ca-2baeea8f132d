package com.sh.game.system.activity.entity.role;

import com.sh.game.common.entity.backpack.item.Item;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class RoleJinDanData {
    /**
     * 活动期间充值金额
     */
    @Tag(1)
    private int rechargeCount;

    /**
     * 已领取的翻牌奖励表id
     * key: index
     * value: 奖励
     */
    @Tag(2)
    private Map<Integer, Item> requiredCard = new HashMap<>();

    /**
     * 可用转盘次数
     */
    @Tag(3)
    private int count;

    /**
     * 已充值的挡位
     */
    @Tag(4)
    private List<Integer> rechargeList = new ArrayList<>();

    /**
     * 已抽取过的权重下标
     */
    @Tag(5)
    private List<Integer> rewardIndexList = new ArrayList<>();
}
