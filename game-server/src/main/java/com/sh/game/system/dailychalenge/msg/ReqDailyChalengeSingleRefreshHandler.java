package com.sh.game.system.dailychalenge.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.dailychalenge.DailyChalengeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.dailychalenge.ReqDailyChalengeSingleRefreshMessage;

/**
 * <p>请求重新刷新当前接受的任务</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDailyChalengeSingleRefreshHandler extends AbstractHandler<ReqDailyChalengeSingleRefreshMessage> {

    @Override
    public void doAction(ReqDailyChalengeSingleRefreshMessage msg) {
        DailyChalengeManager.getInstance().reqRefreshSingle(SessionUtil.getRole(msg));
    }

}
