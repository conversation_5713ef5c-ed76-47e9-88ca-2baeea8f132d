package com.sh.game.system.door.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * 玄界之门
 *
 * <AUTHOR>
 * @date 2022/11/03 21:44
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleDoor extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 当前配置id
     * 初始id为1
     */
    @Tag(2)
    private int doorId = 1;

    /**
     * 当前进度
     */
    @Tag(3)
    private long exp;
}
