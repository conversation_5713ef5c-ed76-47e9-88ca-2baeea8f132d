package com.sh.game.system.daily.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.daily.script.IDailyTimedEvilGodCallingScript;
import com.sh.script.ScriptEngine;

public class DailyTimedEvilGodCallingManager {
    private static final DailyTimedEvilGodCallingManager INSTANCE = new DailyTimedEvilGodCallingManager();

    private DailyTimedEvilGodCallingManager() {

    }

    public static DailyTimedEvilGodCallingManager getInstance() {
        return INSTANCE;
    }


    public boolean canCall(Role role) {
        IDailyTimedEvilGodCallingScript script = ScriptEngine.get1t1(IDailyTimedEvilGodCallingScript.class);
        if (script == null) {
            return false;
        }

        return script.canCall(role);
    }

    public void afterCall(Role role, int monsterId) {
        IDailyTimedEvilGodCallingScript script = ScriptEngine.get1t1(IDailyTimedEvilGodCallingScript.class);
        if (script == null) {
            return;
        }

        script.afterCall(role, monsterId);
    }

}
