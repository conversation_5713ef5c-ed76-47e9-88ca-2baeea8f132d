package com.sh.game.system.arena.entity;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * 竞技场日志
 * <AUTHOR>
 * @date 2023/11/9
 */
@Getter
@Setter
public class ArenaLog {
    @Tag(1)
    private long battleId;
    @Tag(2)
    private boolean attackerWin;
    @Tag(3)
    private long attackerId;
    @Tag(4)
    private long defenderId;
    @Tag(5)
    private int scoreChange;
    @Tag(6)
    private int time;
    @Tag(7)
    private boolean challenge;
    @Tag(8)
    private long defenderPower;
    @Tag(9)
    private long attackPower;
}
