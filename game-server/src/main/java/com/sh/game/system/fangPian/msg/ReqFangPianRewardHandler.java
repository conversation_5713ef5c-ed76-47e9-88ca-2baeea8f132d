package com.sh.game.system.fangPian.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fangPian.FangPianManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.fangPian.ReqFangPianRewardMessage;

/**
 * <p>请求领取玩家防骗奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqFangPianRewardHandler extends AbstractHandler<ReqFangPianRewardMessage> {

    @Override
    public void doAction(ReqFangPianRewardMessage msg) {
        FangPianManager.getInstance().reqReceiveReWard(SessionUtil.getRole(msg));
    }

}
