package com.sh.game.system.welfare.dig.impl;

import com.sh.game.common.communication.notice.NpcAddNotice;
import com.sh.game.common.config.model.DigEventConfig;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleDaily;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleMeetNpcStore;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.welfare.dig.IDigEvent;
import com.sh.game.system.welfare.dig.TypeDigEvent;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/14 11:04
 */
@Slf4j
public class AddSceneNpc implements IDigEvent {
    @Override
    public void handler(Role role, DigEventConfig config) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null) {
            return;
        }
        int[] params = config.getEventParams();
        if (params == null || params.length < 2) {
            return;
        }

        int npcId = params[0];
        long mapId = role.getMapId();
        int[] pos = config.getPos();

        if (pos == null || pos.length < 2) {
            return;
        }
        int pianYi = GlobalUtil.getGlobalInt(GameConst.GlobalId.CANGBANGTU_PIANYI);
        List<int[]> npcParam = Collections.singletonList(new int[]{npcId, pos[0] + pianYi, pos[1] + pianYi});

        NpcAddNotice notice = new NpcAddNotice();
        notice.setMapId(mapId);
        notice.getNpcs().addAll(npcParam);

        int overTime = TimeUtil.getNowOfSeconds() + config.getTime();
        notice.setOverTime(overTime);
        notice.setBelongRid(role.getRoleId());
        notice.setCheckPoint(true);
        notice.setRange(config.getSRange());

        RoleDaily daily = role.getRoleDaily();
        daily.setNpcStoreBuyTime(overTime);
        daily.setRefresh(true);
        daily.setStoreType(params[1]);
        DataCenter.updateData(daily);

        player.sendNotice(notice);

        log.info("玩家挖宝，挖出npc，玩家id {} name {} npcId {} 挖宝事件id {}", role.getId(), role.getName(), npcId, config.getId());

        AnnounceManager.getInstance().post(ChatConst.AnnounceId.CANG_BAO_TU_SHANG_REN, 0L, role);
        ScriptEngine.invoke1tn(IEventOnRoleMeetNpcStore.class,s -> s.meetNpcStore(role));
    }

    @Override
    public int getEventType() {
        return TypeDigEvent.CURRENT_SCENE_NPC;
    }
}
