package com.sh.game.system.role.msg;

import com.sh.game.common.communication.msg.system.role.ReqShenBingExtraDailyExpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.role.RoleManager;
import com.sh.server.AbstractHandler;

/**
 * 请求当日已累计的额外经验
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-08-10
 **/
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqShenBingExtraDailyExpHandler extends AbstractHandler<ReqShenBingExtraDailyExpMessage> {

    @Override
    public void doAction(ReqShenBingExtraDailyExpMessage msg) {
        RoleManager.getInstance().reqShenBingExtraDailyExp(SessionUtil.getRole(msg));
    }

}
