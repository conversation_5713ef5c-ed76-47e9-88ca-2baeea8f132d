package com.sh.game.system.welfare;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.welfare.script.IOnlineRewardScript;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OnlineRewardManager {
    private static final OnlineRewardManager INSTANCE = new OnlineRewardManager();

    private OnlineRewardManager() {

    }

    public static OnlineRewardManager getInstance() {
        return INSTANCE;
    }


    public void sendOnlineRewardInfo(Role role) {
        ScriptEngine.invoke1t1(IOnlineRewardScript.class, script -> script.sendOnlineRewardInfo(role));
    }

    public void receiveReward(Role role, int confId) {
        ScriptEngine.invoke1t1(IOnlineRewardScript.class, script -> script.receiveReward(role, confId));
    }

}
