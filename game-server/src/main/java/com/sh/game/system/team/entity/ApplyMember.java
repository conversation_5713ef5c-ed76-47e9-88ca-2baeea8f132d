package com.sh.game.system.team.entity;

import com.sh.game.common.communication.msg.system.team.bean.ApplyInfoBean;
import com.sh.game.system.union.UnionManager;
import com.sh.game.system.user.NameManager;
import lombok.Getter;
import lombok.Setter;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/18 16:09<br>;
 * 版本：1.0<br>;
 * 描述：
 */
@Setter
@Getter
public class ApplyMember {

    /**
     * 玩家id
     */
    private long roleID;

    /**
     * 家族编号
     */
    private long unionID;

    /**
     * 等级
     */
    private int level;

    public ApplyInfoBean toAppleyBean() {
        ApplyInfoBean bean = new ApplyInfoBean();
        bean.setRid(roleID);
        bean.setLevel(level);
        bean.setPlayerName(NameManager.getInstance().getNameByRid(roleID));
        bean.setUnionName(UnionManager.getInstance().getUnionName(unionID));
        return bean;
    }
}
