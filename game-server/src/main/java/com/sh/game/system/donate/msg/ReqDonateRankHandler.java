package com.sh.game.system.donate.msg;

import com.sh.game.common.communication.msg.system.donate.ReqDonateRankMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.donate.DonateManager;
import com.sh.server.AbstractHandler;

/**
 * <p>捐献排行榜请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqDonateRankHandler extends AbstractHandler<ReqDonateRankMessage> {

    @Override
    public void doAction(ReqDonateRankMessage msg) {
        DonateManager.getInstance().reqDonateRank(SessionUtil.getRole(msg));
    }

}
