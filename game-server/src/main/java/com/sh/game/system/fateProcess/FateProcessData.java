package com.sh.game.system.fateProcess;

import com.sh.game.system.task.entity.TaskRecord;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FateProcessData extends TaskRecord {
    @Exclude
    private long openTime; // 开放时间
    @Exclude
    private long closeTime; // 关闭时间
    @Tag(10)
    private long completedTime; // 玩家完成该任务的时间

    public FateProcessData(TaskRecord record) {
        this.setTaskId(record.getTaskId());
        this.setState(record.getState());
        this.setAllCheck(record.isAllCheck());
        this.setGoalList(record.getGoalList());
        this.setFinishTimes(record.getFinishTimes());
        this.setStartTime(record.getStartTime());
    }
}
