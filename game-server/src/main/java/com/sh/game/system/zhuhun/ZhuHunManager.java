package com.sh.game.system.zhuhun;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.zhuhun.entity.RoleZhuHun;
import com.sh.game.system.zhuhun.script.IZhuHunScript;
import com.sh.script.ScriptEngine;

/**
 * 灵装铸魂
 *
 * <AUTHOR>
 * @date 2022/09/22 19:57
 */
public class ZhuHunManager {

    private static final ZhuHunManager INSTANCE = new ZhuHunManager();

    private ZhuHunManager() {
    }

    public static ZhuHunManager getInstance() {
        return INSTANCE;
    }

    /**
     * 根据角色id获取铸魂信息
     *
     * @param roleId 角色id
     * @return RoleZhuHun 铸魂信息
     */
    public RoleZhuHun find(long roleId) {
        return ScriptEngine.invoke1t1WithRet(IZhuHunScript.class, script -> script.find(roleId));
    }

    /**
     * 请求铸魂信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IZhuHunScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求铸魂
     *
     * @param role      角色
     * @param pos       部位
     * @param expert    是否高级铸魂
     */
    public void reqZhuHun(Role role, int pos, boolean expert) {
        ScriptEngine.invoke1t1(IZhuHunScript.class, script -> script.reqZhuHun(role, pos, expert));
    }

    /**
     * 请求对锁操作
     *
     * @param role  角色
     * @param pos   部位
     * @param index 锁下标
     * @param flag true: 加锁 false: 解锁
     */
    public void reqLock(Role role, int pos, int index, boolean flag) {
        ScriptEngine.invoke1t1(IZhuHunScript.class, script -> script.reqLock(role, pos, index, flag));
    }

    /**
     * 请求铸魂替换
     *
     * @param role  角色
     * @param pos   部位
     */
    public void reqReplace(Role role, int pos) {
        ScriptEngine.invoke1t1(IZhuHunScript.class, script -> script.reqReplace(role, pos));
    }
}
