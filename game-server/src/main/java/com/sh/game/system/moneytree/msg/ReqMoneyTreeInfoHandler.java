package com.sh.game.system.moneytree.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.moneytree.MoneyTreeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.moneytree.ReqMoneyTreeInfoMessage;

/**
 * <p>请求摇钱树个人信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqMoneyTreeInfoHandler extends AbstractHandler<ReqMoneyTreeInfoMessage> {

    @Override
    public void doAction(ReqMoneyTreeInfoMessage msg) {
        MoneyTreeManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
