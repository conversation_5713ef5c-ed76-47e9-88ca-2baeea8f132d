package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityFengMoZhanYiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqFengMoSelfInfoMessage;

/**
 * <p>请求自身信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqFengMoSelfInfoHandler extends AbstractHandler<ReqFengMoSelfInfoMessage> {

    @Override
    public void doAction(ReqFengMoSelfInfoMessage msg) {
        ActivityFengMoZhanYiManager.getInstance().reqSelfInfo(SessionUtil.getRole(msg));
    }

}
