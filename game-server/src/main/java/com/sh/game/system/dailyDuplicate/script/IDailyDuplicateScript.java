package com.sh.game.system.dailyDuplicate.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/11/1.
 */
public interface IDailyDuplicateScript extends IScript {


    void enterDailyDuplicate(Role role, int duplicateId);

    void sendDailyInfo(Role role, int duplicateType);

    void faQiHuiYuanShao<PERSON>ang(Role role, int duplicateId);
}
