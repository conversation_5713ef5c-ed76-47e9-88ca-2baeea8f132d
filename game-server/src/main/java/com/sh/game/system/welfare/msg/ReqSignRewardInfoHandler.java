package com.sh.game.system.welfare.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.welfare.SignManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.welfare.ReqSignRewardInfoMessage;

/**
 * <p>请求签到领奖信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqSignRewardInfoHandler extends AbstractHandler<ReqSignRewardInfoMessage> {

    @Override
    public void doAction(ReqSignRewardInfoMessage msg) {
        SignManager.getInstance().sendSignInfo(SessionUtil.getRole(msg.getSession()));
    }
}
