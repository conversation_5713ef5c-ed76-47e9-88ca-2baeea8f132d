package com.sh.game.system.fuwen;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.fuwen.script.IFuWenScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

/**
 * ATO：lrf;
 * 时间：2020/12/9 14:12;
 * 版本：1.0;
 * 描述：火之符文
 */
public class FuWenManager {
    private static final FuWenManager INSTANCE = new FuWenManager();

    public static FuWenManager getInstance() {
        return INSTANCE;
    }

    private FuWenManager() {
    }

    private Optional<IFuWenScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IFuWenScript.class));
    }

    /**
     * 火龙之心升级
     *
     * @param role
     * @param itemId
     */
    public void reqHuoLongLevel(Role role, long itemId) {
        getScript().ifPresent(script -> script.reqHuoLongLevel(role, itemId));
    }

    /**
     * 装备或替换符文
     *
     * @param role
     * @param index
     * @param itemId
     */
    public void reqEquipFuWen(Role role, int index, long itemId) {
        getScript().ifPresent(script -> script.reqEquipFuWen(role, index, itemId));
    }

    /**
     * 卸下符文
     *
     * @param role
     * @param index
     */
    public void reqOffFuWen(Role role, int index) {
        getScript().ifPresent(script -> script.reqOffFuWen(role, index));
    }

    /**
     * 符文兑换
     *
     * @param role
     * @param itemId
     * @param count
     */
    public void reqFuWenEqual(Role role, int itemId, int count) {
        getScript().ifPresent(script -> script.reqFuWenEqual(role, itemId, count));
    }

    /**
     * 符文强化
     *
     * @param role
     * @param index
     */
    public void reqHuoLongStrong(Role role, int index) {
        getScript().ifPresent(script -> script.reqHuoLongStrong(role, index));
    }

    /**
     * 创建英雄给他一件初始火龙装备
     *
     * @param role
     */
    public void equipHuoLong(Role role) {
        getScript().ifPresent(script -> script.equipHuoLong(role));
    }

    /**
     * 符文合成
     *
     * @param role
     * @param itemId
     */
    public void reqFuWenCompose(Role role, int itemId) {
        getScript().ifPresent(script -> script.reqFuWenCompose(role, itemId));
    }
}
