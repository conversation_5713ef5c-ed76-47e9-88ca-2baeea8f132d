package com.sh.game.system.daily.entity;


import com.sh.game.common.communication.msg.system.daily.ResDailyTimedOpensMessage;
import com.sh.game.common.communication.msg.system.daily.bean.DailyTimedStatusBean;
import com.sh.game.common.config.model.DailyScheduleConfig;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DailySchedule {

    private boolean ignore = false;

    /**
     * 活动编号
     */
    private int id;

    /**
     * 配置
     */
    private DailyScheduleConfig conf;

    /**
     * 开始前，公告时间
     */
    private int periodPrepare;

    /**
     * 活动开始
     */
    private int periodBegin;

    /**
     * 结束时间
     */
    private int periodEnd;

    /**
     * 活动结束前3分钟
     */
    private int periodClose;

    /**
     * 活动状态
     */
    private int status;

    public boolean getIgnore() {
        return ignore;
    }

    /**
     * 活动状态变更
     *
     * @param status
     */
    public void setStatus(int status) {
        this.status = status;
        ResDailyTimedOpensMessage msg = new ResDailyTimedOpensMessage();
        DailyTimedStatusBean bean = new DailyTimedStatusBean();
        bean.setScheduleID(conf.getId());
        bean.setDailyID(conf.getType());
        bean.setStatus(status);
        bean.setBeginTime(periodBegin);
        bean.setEndTime(periodEnd);
        msg.getOpens().add(bean);
        MessageUtil.sendToWorld(msg);
    }

    /**
     * 设置活动开始时间
     *
     * @param beginTime
     */
    public void startAt(int beginTime) {
        int dayEndTime = TimeUtil.dayZeroSecondsFromTime(beginTime) - 5 + (int) TimeUtil.ONE_DAY_IN_SECONDS;
        int prepareTime = beginTime - conf.getBeginRemindAhead();
        int endTime = beginTime + conf.getLastTime();
        if (endTime > dayEndTime) {
            endTime = dayEndTime;
        }
        if (prepareTime > beginTime) prepareTime = beginTime;
        if (endTime < beginTime) endTime = beginTime;

        this.periodPrepare = prepareTime;
        this.periodBegin = beginTime;
        this.periodEnd = endTime;
        this.periodClose = (int) (this.periodEnd - 3 * TimeUtil.ONE_MINUTE_IN_SECONDS);
    }

    /**
     * 设置活动截止时间
     *
     * @param endTime
     */
    public void stopAt(int endTime) {
        this.periodEnd = endTime;
        this.periodClose = (int) (this.periodEnd - 3 * TimeUtil.ONE_MINUTE_IN_SECONDS);
    }

    /**
     * 是否在活动时间内
     *
     * @param time
     * @return
     */
    public boolean isInPeriod(int time) {
        return (time >= periodBegin && time <= periodEnd);
    }
}
