package com.sh.game.system.equipCollect.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.equipCollect.entity.RoleEquipCollect;
import com.sh.script.IScript;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-04-01
 **/
public interface IActivityEquipCollectScript extends IScript {

    RoleEquipCollect find(long rid);

    void reqInfo(Role role);

    void gainReward(Role role, int cid);
}
