package com.sh.game.system.bore.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2020/10/12 20:21
 */
public interface IBoreScript extends IScript {

    void unlock(Role role, int index);

    void levelUp(Role role, int index);

    void putOnGem(Role role, int index, int cid);

    void sendBoreInfo(Role role);

    void compose(Role role, List<Long> lid);
}
