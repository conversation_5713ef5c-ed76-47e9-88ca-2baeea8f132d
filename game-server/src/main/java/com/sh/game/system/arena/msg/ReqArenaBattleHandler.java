package com.sh.game.system.arena.msg;

import com.sh.game.common.communication.msg.system.arena.ReqArenaBattleMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.arena.ArenaManager;
import com.sh.server.AbstractHandler;

@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqArenaBattleHandler extends AbstractHandler<ReqArenaBattleMessage> {

    @Override
    public void doAction(ReqArenaBattleMessage msg) {
        ArenaManager.getInstance().reqArenaBattle(SessionUtil.getRole(msg), msg.getRoleId(), msg.getBattleId());
    }

}
