package com.sh.game.system.chat.script;

import com.sh.commons.tuple.ThreeTuple;
import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 19:03
 */
public interface IChatScript extends IScript {

    /**
     * 聊天
     *
     * @param role        玩家信息
     * @param channel     聊天频道
     * @param target      聊天对象（仅私聊时有效）
     * @param content     聊天内容
     * @param itemUniques 道具列表
     * @param coordinate  地图ID，x、y坐标
     */
    void chat(Role role, int channel, long target, String content, List<Long> itemUniques, ThreeTuple<Integer, Integer, Integer> coordinate);

}
