package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.PeakZhanLingBoxConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.peak.PeakZhanLingManager;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 巅峰联赛战令宝箱
 *
 * <AUTHOR>
 * @date 2022/11/22 18:13
 */
@Slf4j
public class ItemUsageForPeakZhanLingBox extends ItemUsage {

    @Override
    public int getUsedType() {
        return 1001;
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        if (count != 1) {
            log.error("巅峰联赛战令宝箱-不能批量使用,role:{} {},configId:{},count:{}", role.getRoleId(), role.getName(), config.getId(), count);
            return false;
        }

        int[][] useParam = config.getUseParam();
        if (useParam == null || useParam.length < 1 || useParam[0].length < 1) {
            log.error("巅峰联赛战令宝箱-参数错误,role:{} {},configId:{},useParam:{}", role.getRoleId(), role.getName(), config.getId(), useParam);
            return false;
        }
        PeakZhanLingBoxConfig boxConfig = ConfigDataManager.getInstance().getById(PeakZhanLingBoxConfig.class, useParam[0][0]);
        if (boxConfig == null) {
            log.error("巅峰联赛战令宝箱-宝箱配置不存在,role:{} {},configId:{},boxId:{}", role.getRoleId(), role.getName(), config.getId(), useParam[0][0]);
            return false;
        }
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        PeakZhanLingManager.getInstance().openBox(role, config.getUseParam()[0][0]);
        return true;
    }
}
