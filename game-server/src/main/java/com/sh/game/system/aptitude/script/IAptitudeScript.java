package com.sh.game.system.aptitude.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * 角色资质
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-09-13
 **/
public interface IAptitudeScript extends IScript {

    /**
     * 资质鉴定
     *
     * @param role
     * @param itemUid 待鉴定物品
     */
    void aptitudeIdentification(Role role, long itemUid);

    /**
     * 资质批量合成
     *
     * @param role
     * @param cfgId         资质合成表id
     * @param compoundCount 合成数量
     */
    void batchCompound(Role role, int cfgId, int compoundCount);

    /**
     * 资质装备重新洗练
     *
     * @param role    角色
     * @param itemUid 装备id
     */
    void aptitudeConcise(Role role, long itemUid);
}
