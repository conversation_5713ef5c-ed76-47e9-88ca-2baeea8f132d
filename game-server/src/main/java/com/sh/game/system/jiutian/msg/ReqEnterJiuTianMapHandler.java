package com.sh.game.system.jiutian.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.jiutian.JiuTianZhiDianManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.jiutian.ReqEnterJiuTianMapMessage;

/**
 * <p>请求进入九天之巅地图</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqEnterJiuTianMapHandler extends AbstractHandler<ReqEnterJiuTianMapMessage> {

    @Override
    public void doAction(ReqEnterJiuTianMapMessage msg) {
        JiuTianZhiDianManager.getInstance().reqEnterJiuTianMap(SessionUtil.getRole(msg), msg.getCfgId());
    }

}
