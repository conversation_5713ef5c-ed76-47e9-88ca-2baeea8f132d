package com.sh.game.system.magiccircle;

import com.sh.game.common.entity.usr.Role;

import com.sh.game.system.magiccircle.entity.RoleMagicCircle;
import com.sh.script.ScriptEngine;



/**
 * 法阵系统
 *
 * <AUTHOR>
 * @since 2022/5/26 15:32
 */
public class MagicCircleManager {

    private static final MagicCircleManager INSTANCE = new MagicCircleManager();

    private MagicCircleManager() {
    }

    public static MagicCircleManager getInstance() {
        return INSTANCE;
    }

    /**
     * 根据角色id获取角色当前法阵信息
     *
     * @param id 角色id roleId
     * @return {@link RoleMagicCircle} 角色当前法阵信息
     */
    public RoleMagicCircle find(long id) {
        return ScriptEngine.invoke1t1WithRet(IMagicCircleScript.class, script -> script.find(id));
    }

    /**
     * 根据法阵类型升级法阵
     *
     * @param role 角色
     * @param type 法阵类型
     */
    public void magicCircleLevelUp(Role role, int type) {
        ScriptEngine.invoke1t1(IMagicCircleScript.class, script -> script.magicCircleLevelUp(role, type));
    }

    /**
     * 获取角色当前法阵信息
     *
     * @param role 角色
     */
    public void sendMagicCircleInfo(Role role) {
        ScriptEngine.invoke1t1(IMagicCircleScript.class, script -> script.sendMagicCircleInfo(role));
    }
}
