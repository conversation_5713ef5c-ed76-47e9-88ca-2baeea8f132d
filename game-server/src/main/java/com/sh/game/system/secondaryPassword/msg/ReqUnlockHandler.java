package com.sh.game.system.secondaryPassword.msg;

import com.sh.game.common.communication.msg.system.secondaryPassword.ReqUnlockMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.secondaryPassword.SecondaryPasswordManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求解锁</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqUnlockHandler extends AbstractHandler<ReqUnlockMessage> {

    @Override
    public void doAction(ReqUnlockMessage msg) {
        SecondaryPasswordManager.getInstance().reqUnlock(SessionUtil.getRole(msg.getSession()), msg.getPassword());
    }

}