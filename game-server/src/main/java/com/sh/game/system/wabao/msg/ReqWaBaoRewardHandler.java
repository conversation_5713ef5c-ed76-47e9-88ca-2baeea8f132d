package com.sh.game.system.wabao.msg;

import com.sh.game.common.communication.msg.system.wabao.ReqWaBaoRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWaBaoManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求寻宝奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqWaBaoRewardHandler extends AbstractHandler<ReqWaBaoRewardMessage> {

    @Override
    public void doAction(ReqWaBaoRewardMessage msg) {
        ActivityWaBaoManager.getInstance().reqWaBaoReward(SessionUtil.getRole(msg.getSession()), msg.getAll());
    }

}
