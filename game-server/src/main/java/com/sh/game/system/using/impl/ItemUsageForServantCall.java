package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

public class ItemUsageForServantCall extends ItemUsage {

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        return null;
    }

    @Override
    public int getUsedType() {
        return 126;
    }

}
