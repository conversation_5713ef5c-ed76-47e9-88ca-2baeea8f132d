package com.sh.game.system.rank.type;

import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.system.rank.entity.RankData;

/**
 * 通用排行榜接口
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/12/6.
 */
public class AbstractRankCommon extends AbstractRank {
    @Override
    protected RankData buildRankData(RoleSummary summary, int rank) {
        return null;
    }

    @Override
    protected String getQuerySql() {
        return null;
    }

    @Override
    public int getRankType() {
        return 0;
    }
}
