package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityRechargeChallengeScript;
import com.sh.script.ScriptEngine;

/**
 * 充值挑战
 */
public class ActivityRechargeChallengeManager {

    private final static ActivityRechargeChallengeManager INSTANCE = new ActivityRechargeChallengeManager();

    public static ActivityRechargeChallengeManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求充值挑战活动信息
     *
     * @param role
     */
    public void reqRechargeChallengeInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityRechargeChallengeScript.class, script -> script.reqRechargeChallengeInfo(role));
    }

    /**
     * 请求挑战
     *
     * @param role
     * @param cfgId
     */
    public void reqChallengeBoss(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityRechargeChallengeScript.class, script -> script.reqChallengeBoss(role, cfgId));
    }

}
