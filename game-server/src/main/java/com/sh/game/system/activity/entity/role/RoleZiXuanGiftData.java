package com.sh.game.system.activity.entity.role;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class RoleZiXuanGiftData {

    /**
     * k礼包id， v购买次数
     */
    @Tag(1)
    private Map<Integer, Integer> buyDataMap = new HashMap<>();

    /**
     * k礼包id
     * vMap k 格子下标 v 奖励下标
     */
    @Tag(2)
    private Map<Integer, Map<Integer, Integer>> selectRewardMap = new HashMap<>();
}
