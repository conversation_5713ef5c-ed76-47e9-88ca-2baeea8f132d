package com.sh.game.system.pets.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.pets.RoleChongWuManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.pets.ReqReselectChongWuMessage;

/**
 * <p>重新选择宠物</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_PLAYER)
public class ReqReselectChongWuHandler extends AbstractHandler<ReqReselectChongWuMessage> {

    @Override
    public void doAction(ReqReselectChongWuMessage msg) {
        RoleChongWuManager.getInstance().reqReselectRoleChongWu(SessionUtil.getRole(msg),msg.getCfgId());
    }

}
