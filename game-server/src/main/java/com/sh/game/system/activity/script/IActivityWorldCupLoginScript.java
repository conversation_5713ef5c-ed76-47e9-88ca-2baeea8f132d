package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 17:35
 */
public interface IActivityWorldCupLoginScript extends IScript {

    void info(Role role);

    void reqAcquire<PERSON>ree(Role role, int cid);

    void reqAcquirePay(Role role, int cid);

    void reqAcquireLei<PERSON>i(Role role , List<Integer> idList);

    void reqBuQian(Role role, int cid);
}
