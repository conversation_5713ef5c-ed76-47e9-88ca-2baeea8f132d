package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ItemUsageForExpPromote extends ItemUsage {

    @Override
    public boolean needMapVerify(Role role, Item item, ItemConfig config) {
        return true;
    }

    @Override
    public int getUsedType() {
        return 125;
    }
}
