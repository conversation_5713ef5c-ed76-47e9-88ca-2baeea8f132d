package com.sh.game.processor;

import com.alibaba.fastjson.JSON;
import com.sh.concurrent.QueueDriver;
import com.sh.game.GameContext;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ExecutorUtil;
import com.sh.game.notice.NoticePool;
import com.sh.game.server.CommandProcessor;
import com.sh.game.server.SessionUtil;
import com.sh.server.Session;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * server玩家消息处理器
 *
 */
@Slf4j
public class RoleProcessor extends CommandProcessor {
    private static final Map<Long, QueueDriver> ROLE_DRIVER = new ConcurrentHashMap<>();


    @Override
    public byte id() {
        return ProcessorId.SERVER_PLAYER;
    }

    @Override
    protected QueueDriver getDriver(long key) {
        return ROLE_DRIVER.computeIfAbsent(key, k -> new QueueDriver(ExecutorUtil.PLAYER_DRIVER_EXECUTOR, "玩家Driver-" + key, key, 500));
    }

    @Override
    protected NoticePool getNoticePool() {
        return GameContext.getGameServer().getNoticePool();
    }

    @Override
    protected boolean isSessionLegal(Session session) {
        Role role = SessionUtil.getRole(session);
        if (role == null) {
            log.error("session illegal sessionInfo {}", JSON.toJSONString(session));
        }
        return role != null;
    }

    public interface PrimaryFriend {
        default Map<Long, QueueDriver> getDriverMap() {
            return ROLE_DRIVER;
        }
    }
}
