package com.sh.game.api.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 16:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleResult extends MessageResult {

    private int career;

    private int level;

    private long fightPower;

    private String ip;

    private int loginTime;

    private int offlineTime;

    private boolean online;

    private int official;

    private String name;

    private long uid;

    private int zhuansheng;

    private long heroFightPower;

    private List<String> equip = new ArrayList<>();

    private List<String> heroEquip = new ArrayList<>();

    private long bindDiamond;

    private long diamond;

    private String roleAccount;

    private int heroZhuansheng;

    private int heroLevel;

    private int huoLevel;

    private int heroSex;

    private String heroName;
}
