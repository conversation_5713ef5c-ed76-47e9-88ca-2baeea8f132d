package com.sh.game.data.mysql.factory;

import com.sh.game.common.constant.DataType;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.data.mysql.PersistAutoCTable;
import com.sh.game.data.mysql.factory.abs.CGlibProxyPersistFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RoleActivityPersistFactory extends CGlibProxyPersistFactory<RoleActivity> implements PersistAutoCTable {

    private static final String INSERT = "insert into p_activity (id, data) values (?, ?)";

    private static final String UPDATE = "update p_activity set data = ? where id = ?";

    private static final String DELETE = "delete from p_activity where id = ?";

    @Override
    public String name() {
        return null;
    }

    @Override
    public int dataType() {
        return DataType.ACTIVITY;
    }

    @Override
    public String createInsertSql() {
        return INSERT;
    }

    @Override
    public String createUpdateSql() {
        return UPDATE;
    }

    @Override
    public String createDeleteSql() {
        return DELETE;
    }

    @Override
    public long taskPeriod() {
        return 60 * 1000;
    }

    @Override
    public String createTableSql() {
        return "CREATE TABLE IF NOT EXISTS  `p_activity` " +
                "(" +
                "  `id` bigint(20) NOT NULL" +
                ",  `data` mediumblob" +
                ", PRIMARY KEY (`id`)" +
                ") ENGINE=InnoDB    COMMENT='基于自动建表' ;";
    }

}
