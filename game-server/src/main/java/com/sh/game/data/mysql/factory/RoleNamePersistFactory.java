package com.sh.game.data.mysql.factory;

import com.sh.common.persist.PersistFactory;
import com.sh.common.persist.Persistable;
import com.sh.game.common.constant.DataType;
import com.sh.game.common.entity.usr.RoleName;
import com.sh.game.data.mysql.PersistAutoCTable;


public class RoleNamePersistFactory implements PersistFactory, PersistAutoCTable {
    private static final String UPDATE = "INSERT INTO p_name (roleId, name) VALUES (?,?) ON DUPLICATE KEY UPDATE state=VALUES(state)";

    @Override
    public String name() {
        return "roleName";
    }

    @Override
    public int dataType() {
        return DataType.RNAME;
    }

    @Override
    public String createInsertSql() {
        return null;
    }

    @Override
    public String createDeleteSql() {
        return null;
    }

    @Override
    public String createUpdateSql() {
        return UPDATE;
    }

    @Override
    public Object[] createInsertParameters(Persistable persistable) {
        RoleName user = (RoleName) persistable;
        return new Object[]{
                user.getId(),
                user.getName()};

    }

    @Override
    public Object[] createDeleteParameters(Persistable persistable) {
        return new Object[]{persistable.getId()};
    }

    @Override
    public Object[] createUpdateParameters(Persistable persistable) {
        RoleName user = (RoleName) persistable;
        return new Object[]{
                user.getId(),
                user.getName()};
    }


    @Override
    public long taskPeriod() {
        return 60 * 1000;
    }

    @Override
    public String createTableSql() {

        return "CREATE TABLE IF  NOT EXISTS `p_name` (\n" +
                "  `roleId` bigint(20) NOT NULL,\n" +
                "  `name` varchar(64) DEFAULT NULL,\n" +
                "  PRIMARY KEY (`roleId`)\n" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    }
}
