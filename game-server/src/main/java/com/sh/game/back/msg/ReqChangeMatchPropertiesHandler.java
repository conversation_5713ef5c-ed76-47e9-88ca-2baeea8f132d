package com.sh.game.back.msg;

import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.back.ReqChangeMatchPropertiesMessage;

/**
 * <p>请求修改匹配服配置</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqChangeMatchPropertiesHandler extends AbstractHandler<ReqChangeMatchPropertiesMessage> {

    @Override
    public void doAction(ReqChangeMatchPropertiesMessage msg) {
//        BackProtos.ReqChangeMatchProperties proto = msg.getProto();
//        BackManager.getInstance().changeMatchConfig(msg.getSequence(), msg.getSession(), proto.getMatchAddress(), proto.getMatchGroup());
    }

}
