package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqSendAllUnionMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>查询所有帮会信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqSendAllUnionHandler extends AbstractHandler<ReqSendAllUnionMessage> {

    @Override
    public void doAction(ReqSendAllUnionMessage msg) {
        BackManager.getInstance().sendAllUnionInfo(msg.getSession(), msg.getSequence());

    }

}
