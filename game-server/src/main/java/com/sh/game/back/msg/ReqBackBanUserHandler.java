package com.sh.game.back.msg;

import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.back.ReqBackBanUserMessage;

/**
 * <p>ban user</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqBackBanUserHandler extends AbstractHandler<ReqBackBanUserMessage> {

    @Override
    public void doAction(ReqBackBanUserMessage msg) {
//        BackProtos.ReqBackBanUser proto = msg.getProto();
//        BackManager.getInstance().banUser(msg.getSequence(), msg.getSession(), proto.getSid(), proto.getUid(), proto.getTime(), proto.getReason());
    }

}
