package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqBackOnlineCountMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>在线人数</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqBackOnlineCountHandler extends AbstractHandler<ReqBackOnlineCountMessage> {

    @Override
    public void doAction(ReqBackOnlineCountMessage msg) {
//        BackManager.getInstance().onlineCount(msg.getSession(), msg.getSequence());

    }

}
