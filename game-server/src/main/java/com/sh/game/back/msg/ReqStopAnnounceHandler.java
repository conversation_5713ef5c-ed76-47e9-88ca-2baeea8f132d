package com.sh.game.back.msg;

import com.sh.game.common.communication.msg.system.back.ReqStopAnnounceMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>停止发公告(后台用)</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqStopAnnounceHandler extends AbstractHandler<ReqStopAnnounceMessage> {

    @Override
    public void doAction(ReqStopAnnounceMessage msg) {
//        BackProtos.ReqStopAnnounce proto = msg.getProto();
//        BackManager.getInstance().stopAnnounce(proto.getUniqueId());

    }

}
