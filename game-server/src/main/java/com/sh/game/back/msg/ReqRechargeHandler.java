package com.sh.game.back.msg;

import com.sh.game.common.communication.msg.system.back.ReqRechargeMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>充值</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqRechargeHandler extends AbstractHandler<ReqRechargeMessage> {

    @Override
    public void doAction(ReqRechargeMessage msg) {
//        BackProtos.ReqRecharge proto = msg.getProto();
//        RoleRechargeNotice notice = new RoleRechargeNotice();
//        notice.setSequence(msg.getSequence());
//        notice.setSession(msg.getSession());
//        notice.setPlatform(proto.getPlatform());
//        notice.setChannel(proto.getChannel());
//        notice.setUid(proto.getUid());
//        notice.setPid(proto.getPid());
//        notice.setSid(proto.getSid());
//        notice.setRid(proto.getRid());
//        notice.setRmb(proto.getRmb());
//        notice.setOrderSn(proto.getOrderSn());
//        notice.setOrderId(proto.getOrderId());
//        notice.setGoodsId(proto.getGoodsId());
//        notice.setWay(proto.getWay());
//
//        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.SERVER_PLAYER, notice, notice.getRid());
    }

}
