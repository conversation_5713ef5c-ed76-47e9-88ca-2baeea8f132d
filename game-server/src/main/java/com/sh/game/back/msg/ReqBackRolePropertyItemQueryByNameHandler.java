package com.sh.game.back.msg;

import com.sh.game.common.communication.msg.system.back.ReqBackRolePropertyItemQueryByNameMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.server.AbstractHandler;

/**
 * <p>请求玩家道具信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.SERVER_AUTH)
public class ReqBackRolePropertyItemQueryByNameHandler extends AbstractHandler<ReqBackRolePropertyItemQueryByNameMessage> {

    @Override
    public void doAction(ReqBackRolePropertyItemQueryByNameMessage msg) {
//        BackProtos.ReqBackRolePropertyItemQueryByName proto = msg.getProto();
//        long rid = NameManager.getInstance().getRidByName(proto.getUName());
//        BackManager.getInstance().rolePropertyItemQuery(rid);
    }

}
