package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.back.ReqBackRolePropertyCurrencyQueryMessage;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqBackRolePropertyCurrencyQueryHandler extends AbstractHandler<ReqBackRolePropertyCurrencyQueryMessage> {

    @Override
    public void doAction(ReqBackRolePropertyCurrencyQueryMessage msg) {
//        BackProtos.ReqBackRolePropertyCurrencyQuery proto = msg.getProto();
//        BackManager.getInstance().rolePropertyCoinQuery(proto.getRid());
    }

}
