package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqBackLoginMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>通过后台登录</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqBackLoginHandler extends AbstractHandler<ReqBackLoginMessage> {

    @Override
    public void doAction(ReqBackLoginMessage msg) {
        BackManager.getInstance().backLogin(msg.getSession(), msg.getSequence(), msg.getLoginName());

    }

}
