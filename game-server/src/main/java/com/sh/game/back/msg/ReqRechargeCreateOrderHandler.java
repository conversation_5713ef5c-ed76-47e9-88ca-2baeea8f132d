package com.sh.game.back.msg;

import com.sh.game.common.communication.msg.system.back.ReqRechargeCreateOrderMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>请求创建订单</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqRechargeCreateOrderHandler extends AbstractHandler<ReqRechargeCreateOrderMessage> {

    @Override
    public void doAction(ReqRechargeCreateOrderMessage msg) {
//        BackProtos.ReqRechargeCreateOrder proto = msg.getProto();
//        long orderId = RechargeManager.getInstance().createOrder(proto.getRoleId(), proto.getProductId());
//        ResRechargeOrderIdMessage rechargeOrderIdMessage = new ResRechargeOrderIdMessage();
//        rechargeOrderIdMessage.setSequence(msg.getSequence());
//        rechargeOrderIdMessage.setProto(RechargeProtos.ResRechargeOrderId.newBuilder()
//                .setOrderNo(String.valueOf(orderId))
//                .setProductId(proto.getProductId())
//                .build());
//        msg.getSession().sendMessage(rechargeOrderIdMessage);
    }

}
