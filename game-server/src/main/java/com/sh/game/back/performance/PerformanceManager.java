package com.sh.game.back.performance;

import com.sh.net.PackageCounter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控相关逻辑
 * <AUTHOR>
 * @date 2019/6/26 10:40
 */
public class PerformanceManager {

    private static final Logger log = LoggerFactory.getLogger("performance");

    private static final PerformanceManager INSTANCE = new PerformanceManager();

    private static boolean open;

    private PerformanceManager() {

    }

    public static final PerformanceManager getInstance() {
        return INSTANCE;
    }


    public void openMsgCount(boolean bool) {
        PackageCounter.setMsgCount(bool);
    }

    public void msgCountDetail() {
        if(open) {
            Map<Integer, AtomicLong> downPackageCountMap = PackageCounter.getDownPackageCountMap();
            Map<Integer, AtomicLong> downPackageSizeMap = PackageCounter.getDownPackageSizeMap();

            downPackageCountMap.forEach((k, v) -> {
                AtomicLong downPackageSize = downPackageSizeMap.get(k);
                log.info("消息/消息个数/消息字节数: [{}]/{}/{}",
                        k,
                        v.get(),
                        downPackageSize != null ? downPackageSize.get() : 0);
            });
        }
    }

    public boolean switchOpenState() {
        open = !open;
        return open;
    }

    public String msgCountDetailStr() {
        Map<Integer, AtomicLong> downPackageCountMap = PackageCounter.getDownPackageCountMap();
        Map<Integer, AtomicLong> downPackageSizeMap = PackageCounter.getDownPackageSizeMap();


        StringBuilder sb = new StringBuilder();
        downPackageCountMap.forEach((k, v) -> {
            AtomicLong downPackageSize = downPackageSizeMap.get(k);
            sb.append("消息/消息个数/消息字节数:").append(
                    String.format("[%d]/%d/%d\n", k, v.get(), downPackageSize != null ? downPackageSize.get() : 0));
        });
        return sb.toString();
    }
}
