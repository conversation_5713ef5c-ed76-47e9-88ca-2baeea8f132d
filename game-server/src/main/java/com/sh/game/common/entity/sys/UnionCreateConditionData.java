package com.sh.game.common.entity.sys;

import com.sh.game.common.entity.SysDataType;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 创建行会条件数据
 *
 * <AUTHOR>
 * @date 2022/10/09 20:03
 */
@Getter
@Setter
public class UnionCreateConditionData extends AbstractSysData {

    /**
     * 后台配置的行会创建条件
     */
    @Tag(1)
    List<int[]> condition = new ArrayList<>();

    /**
     * condition字符串值
     */
    @Tag(2)
    String value = "";

    /**
     * 设置id
     *
     */
    @Override
    public void setId(long id) {

    }

    @Override
    public long getId() {
        return SysDataType.UNION_CREATE_CONDITION;
    }
}
