package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.huoban.entity.HuoBanFormation;
import com.sh.game.system.huoban.entity.RefreshHuoBan;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleHuoBan extends AbstractRoleEntity {

    /**
     * rid
     */
    @Tag(1)
    private long id;

    @Tag(2)
    private int maxSize;

    /**
     * 伙伴背包
     * k 伙伴配置id
     */
    @Tag(3)
    private Map<Integer, HuoBan> huoBanBag = new HashMap<>();

    /**
     * 召唤的伙伴，未获得的
     */
    @Tag(4)
    private List<RefreshHuoBan> refreshHuoBans = new ArrayList<>();

    /**
     * 购买宠物栏位次数
     */
    @Tag(5)
    private int buySizeCount = 1;

    /**
     * 各品质伙伴抽取次数
     * k:伙伴品质 v:伙伴抽取次数
     */
    @Tag(6)
    private Map<Integer, Integer> qualityRefreshMap = new HashMap<>();

    /**
     * 激活月卡后，刷新伙伴计数（一次刷新会抽取多个伙伴）
     */
    @Tag(7)
    private int cardRefreshCount;

    /**
     * 伙伴编队
     */
    @Tag(8)
    private Map<Integer, HuoBanFormation> formations = new HashMap<>();

    /**
     * 当前正在使用的编队索引
     */
    @Tag(9)
    private int inUseFormation = 1;


    public HuoBanFormation findFormationsByIndex(int index){
        if(index <= 0){
            return null;
        }
        formations.putIfAbsent(index, new HuoBanFormation());
        return formations.get(index);
    }

    public HuoBanFormation findInUseFormation() {
        formations.putIfAbsent(inUseFormation, new HuoBanFormation());
        return formations.get(inUseFormation);
    }

    public int getLevelCount() {
        if (MapUtils.isEmpty(huoBanBag)) {
            return 0;
        }
        return huoBanBag.values().stream().mapToInt(HuoBan::getLevel).sum();
    }

    public int getSkillLevelCount() {
        if (MapUtils.isEmpty(huoBanBag)) {
            return 0;
        }
        return huoBanBag.values().stream().mapToInt(HuoBan::getSkillLevel).sum();
    }

}
