package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: Wangbl
 * @create: 2022/02/24 11:35
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleSignIn extends AbstractRoleEntity {
    /**
     * rid
     */
    @Tag(1)
    private long id;

    /**
     * 已签到天数 开服天数
     */
    @Tag(2)
    private List<Integer> sigInDayList = new ArrayList<>();

    /**
     * 已领奖的id
     */
    @Deprecated
    @Tag(3)
    private List<Integer> requiredCidList = new ArrayList<>();

    /**
     * key: 轮次开始时的开服天数
     * value：签到表id
     */
    @Tag(4)
    private Map<Integer, List<Integer>> requiredCycleReward = new HashMap<>();

    /**
     * 已补签天数
     */
    @Tag(5)
    private List<Integer> signedDayList = new ArrayList<>();
}
