package com.sh.game.common.entity.usr;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.jdbc.SerializerUtil;
import com.sh.commons.tuple.TwoTuple;
import com.sh.commons.util.Symbol;
import com.sh.game.GameContext;
import com.sh.game.common.cd.CD;
import com.sh.game.common.cd.CDObject;
import com.sh.game.common.communication.msg.system.backpack.ResBackpackChangedMessage;
import com.sh.game.common.communication.msg.system.rank.bean.RankDataBean;
import com.sh.game.common.communication.notice.SyncDataNotice;
import com.sh.game.common.communication.notice.entity.RoleSimpleData;
import com.sh.game.common.config.cache.BookSwordCache;
import com.sh.game.common.config.cache.SeasonConfigCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.avatar.AvatarBackState;
import com.sh.game.common.entity.avatar.AvatarUnion;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackCommitController;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.CoinGain;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.map.PlayerDTO;
import com.sh.game.common.entity.map.ServantDTO;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.sys.ShaBaKeCrossDataProxy;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.common.sync.Sync;
import com.sh.game.common.sync.SyncBean;
import com.sh.game.common.sync.SyncData;
import com.sh.game.common.sync.SyncDataCreator;
import com.sh.game.common.util.*;
import com.sh.game.condition.RoleConditionValidator;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleCoinGainScript;
import com.sh.game.event.IEventOnRoleOrHeroAtkChanged;
import com.sh.game.event.IEventOnRoleOrHeroFightPowerChanged;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.system.activity.impl.ActivityValueCardManager;
import com.sh.game.system.arena.entity.RoleArena;
import com.sh.game.system.backpack.BackpackController;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.game.system.bodyforge.entity.RoleBodyForge;
import com.sh.game.system.booksword.BookSwordManager;
import com.sh.game.system.booksword.entity.RoleBookSword;
import com.sh.game.system.chaodu.ChaoDuManager;
import com.sh.game.system.chongWuEquip.entity.RolePetQiangHua;
import com.sh.game.system.climbTower.entity.RoleClimbTower;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.cross.CrossStorageManager;
import com.sh.game.system.danYao.entity.RoleDanYao;
import com.sh.game.system.dunPaiShengJi.entity.RoleDunPai;
import com.sh.game.system.equip.EquipManager;
import com.sh.game.system.equip.EquipUtil;
import com.sh.game.system.faqihuiyuan.entity.RoleFaQiHuiYuanData;
import com.sh.game.system.fate.FateManager;
import com.sh.game.system.fate.entity.RoleFate;
import com.sh.game.system.foundation.entity.RoleFoundation;
import com.sh.game.system.friend.entity.FriendData;
import com.sh.game.system.handbook.HandbookManager;
import com.sh.game.system.handbook.entity.RoleHandbook;
import com.sh.game.system.houyuanwork.entity.RoleWorkData;
import com.sh.game.system.houyun.entity.RoleHouYuanData;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.huoban.entity.RefreshHuoBan;
import com.sh.game.system.jianzhong.entity.RoleJianZhong;
import com.sh.game.system.lineage.LineageManager;
import com.sh.game.system.lineage.entity.RoleLineageItem;
import com.sh.game.system.log.LogManager;
import com.sh.game.system.magiccircle.entity.MagicCircleInfo;
import com.sh.game.system.magiccircle.entity.RoleMagicCircle;
import com.sh.game.system.miJiGem.entity.MiJiGemData;
import com.sh.game.system.militaryRank.MilitaryRankManger;
import com.sh.game.system.militaryRank.entity.RoleMilitaryRank;
import com.sh.game.system.orehole.entity.OreHole;
import com.sh.game.system.rank.RankFactory;
import com.sh.game.system.rank.entity.RankData;
import com.sh.game.system.rank.type.AbstractRank;
import com.sh.game.system.realm.RealmManager;
import com.sh.game.system.realm.entity.RoleRealm;
import com.sh.game.system.recoverycard.entity.RecoveryCard;
import com.sh.game.system.recycle.RecycleManager;
import com.sh.game.system.ringGem.entity.RingGemData;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.game.system.role.entity.*;
import com.sh.game.system.roleMount.entity.RoleMount;
import com.sh.game.system.season.entity.RoleSeason;
import com.sh.game.system.shenBingHuiYuan.ShenBingManager;
import com.sh.game.system.shenBingHuiYuan.entity.RoleShenbingVip;
import com.sh.game.system.skill.entity.RoleSkillTree;
import com.sh.game.system.social.entity.RoleSocial;
import com.sh.game.system.strangeFire.entity.RoleFire;
import com.sh.game.system.strangeFire.entity.RoleStrangeFire;
import com.sh.game.system.talent.talent.RoleTalent;
import com.sh.game.system.task.TaskManager;
import com.sh.game.system.tianchi.entity.RoleTianChi;
import com.sh.game.system.tianfu.entity.RoleTianFu;
import com.sh.game.system.tianshu.TianShuManager;
import com.sh.game.system.tianshu.entity.TianshuItem;
import com.sh.game.system.touying.RoleLineUpManager;
import com.sh.game.system.union.UnionManager;
import com.sh.game.system.unionCamp.UnionCampManager;
import com.sh.game.system.vip.entity.RoleCard;
import com.sh.game.system.wudao.WuDaoManager;
import com.sh.game.system.wudao.entity.RoleWuDao;
import com.sh.game.system.xialv.entity.RoleXiaLv;
import com.sh.game.system.xuanbing.XuanBingManager;
import com.sh.game.system.xuanbing.entity.RoleXuanBing;
import com.sh.game.system.xunbao.entity.RoleXunBao;
import com.sh.game.system.yuanjingxiaopu.entity.RoleYuanJingStore;
import com.sh.game.system.yuanying.entity.RoleYuanYing;
import com.sh.game.system.zhanyi.entity.RoleZhanYi;
import com.sh.game.system.zhenYan.entity.RoleZhenYan;
import com.sh.game.system.zhenyaota.bean.ZhenYaoTaData;
import com.sh.game.system.ztpet.entity.RoleZTPet;
import com.sh.script.ScriptEngine;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 角色基本数据
 * <p>
 * 存放一些玩家基本的数据或是找不到归属的数据
 */
@Getter
@Setter
@Slf4j
@PersistDaoTemplate(PersistSimpleDao.class)
public class Role extends AbstractRoleEntity implements SyncData, CDObject, IAvatar {
    /**
     * 角色roleId
     */
    @Tag(1)
    private long id;

    /**
     * user id（账号id）
     */
    @Tag(2)
    private long uid;

    /**
     * 角色名字
     */
    @Sync(launcher = true)
    @Tag(3)
    private String name;

    /**
     * 角色职业
     */
    @Sync(launcher = true)
    @Tag(4)
    private int career;

    /**
     * 角色性别
     */
    @Sync(launcher = true)
    @Tag(5)
    private int sex;

    /**
     * 角色发型
     */
    @Sync(launcher = true)
    @Tag(6)
    private int hair;

    /**
     * 玩家登录信息
     */
    @Tag(7)
    private Login roleLogin = new Login();

    // 地图相关的信息
    /**
     * 血量
     */
    @Tag(8)
    private long hp;

    /**
     * 蓝量
     */
    @Tag(9)
    private long mp;

    /**
     * 当前地图
     */
    @Tag(10)
    private int mapCfgId;

    @Exclude
    private long mapId;

    /**
     * 当前x，非实时更新
     */
    @Tag(11)
    private int x;

    /**
     * 当前y，非实时更新
     */
    @Tag(12)
    private int y;

    /**
     * 上个地图
     */
    @Tag(13)
    private int lastMapCfgId;

    @Exclude
    private long lastMapId;

    /**
     * 上个地图x
     */
    @Tag(14)
    private int lastX;

    /**
     * 上个地图y
     */
    @Tag(15)
    private int lastY;

    /**
     * 战斗模式
     */
    @Tag(16)
    private int fightModel;

    /**
     * pk值
     */
    @Tag(17)
    private int pkValue;

    /**
     * pk值减少累积时间
     */
    @Tag(18)
    private int pkValueTotalTime;

    /**
     * server部分的角色总属性
     */
    @Tag(21)
    private Attribute finalAttribute = new Attribute();

    /**
     * cd相关(server用)
     */
    @Tag(22)
    private Map<Long, CD> cdMap = new HashMap<>();

    /**
     * 商店信息
     */
    @Tag(23)
    private RoleStore roleStore = new RoleStore();

    /**
     * 随从信息
     */
    @Tag(26)
    private List<ServantDTO> servantDTOS = new ArrayList<>();

    /**
     * 社交列表
     */
    @Tag(27)
    private RoleSocial roleSocial = new RoleSocial();

    /**
     * 行会信息
     */
    @Sync(launcher = true)
    @Tag(28)
    private AvatarUnion union = new AvatarUnion();

    /**
     * 充值数据
     */
    @Tag(30)
    private Recharge recharge = new Recharge();

    @Tag(33)
    private Hero hero;

//    /**
//     * 官职信息
//     */
//    @Tag(34)
//    private RoleOfficial official = new RoleOfficial();

    /**
     * 膜拜信息
     */
    @Tag(35)
    private RoleWorship worship = new RoleWorship();

    /**
     * 寻宝信息
     */
    @Tag(36)
    private RoleTreasureHunt treasureHunt = new RoleTreasureHunt();

    /**
     * 官职编号
     */
    @Tag(37)
    private int officialID = 1;

    /**
     * 二级密码
     */
    @Tag(38)
    private RoleSecondPassword secondPassword = new RoleSecondPassword();

    /**
     * 主题特戒可合成次数
     */
    @Tag(39)
    private Map<Integer, Integer> ringsMap = new HashMap<>();

    /**
     * 拍卖行数据
     */
    @Tag(40)
    private RoleAuctionInfo auctionInfo = new RoleAuctionInfo();


    /**
     * 战意
     */
    @Tag(41)
    private RoleZhanYi zhanyi = new RoleZhanYi();

    /**
     * 天赋
     */
    @Tag(42)
    private RoleTalent talent = new RoleTalent();

    @Tag(43)
    private int fengHaoId = 1000; //默认1000

    /**
     * 侠侣
     */
    @Tag(44)
    private RoleXiaLv xialv = new RoleXiaLv();

    /**
     * 武道
     */
    @Tag(45)
    private RoleWuDao wuDao = new RoleWuDao();

    /**
     * 天池宫
     */
    @Tag(46)
    private RoleTianChi tianChi = new RoleTianChi();

    /**
     * 元婴
     */
    @Tag(47)
    private RoleYuanYing yuanYing = new RoleYuanYing();

    /**
     * 剑冢
     */
    @Tag(48)
    private RoleJianZhong jianzhong = new RoleJianZhong();

    /**
     * 江湖名望id,{@link MingWangConfig} 的id
     */
    @Tag(49)
    private int mingWang;

    @Tag(50)
    private RoleXunBao xunBao = new RoleXunBao();

    /**
     * 回收卡
     */
    @Tag(51)
    private RecoveryCard recoveryCard = new RecoveryCard();

    /**
     * 天赋存储
     */
    @Tag(52)
    private RoleTianFu roleTianFu = new RoleTianFu();

    /**
     * 神威系统存储
     * key: 类型 {@link ShenWeiConfig#getType()}
     * value：神威cfgId {@link ShenWeiConfig#getId()}
     */
    @Tag(53)
    private Map<Integer, Integer> roleShenWei = new HashMap<>();

    /**
     * 宠物cfgId
     */
    @Tag(54)
    private int roleChongWuCfgId;

    /**
     * 法器会员等级
     * //
     */
    @Tag(55)
    private RoleFaQiHuiYuanData faQiHuYuanData = new RoleFaQiHuiYuanData();

    @Tag(56)
    private RoleDanYao roleDanYao = new RoleDanYao();

    /**
     * 兽决装备锁定列表
     */
    @Tag(57)
    private List<Integer> shouJueEquipLock = new ArrayList<>();

    /**
     * 装备位pos，value每个位置的数据
     */
    @Tag(58)
    private Map<Integer, RingGemData> ringGemDataMap = new HashMap<>();

    /**
     * 兽决装备玩家通过灵兽锁(957) 解锁的个数
     */
    @Tag(59)
    private int shouJueExtUnLock = 0;

    /**
     * 护盾值
     */
    @Tag(60)
    private long shield;

    /**
     * 玩家盾牌、时装升级数据
     * key类型1时装，2盾牌
     */
    @Tag(61)
    private Map<Integer, RoleDunPai> SpecialUp = new HashMap<>();

    /**
     * 装备位pos，value每个位置的数据
     */
    @Tag(62)
    private Map<Integer, MiJiGemData> miJiGemDataMap = new HashMap<>();

    /**
     * 宠物装备强化
     * key部位
     */
    @Tag(63)
    private Map<Integer, RolePetQiangHua> rolePetQiangHua = new HashMap<>();

    /**
     * 修罗勋章配置id
     */
    @Tag(64)
    private int medalConfigId = 100001;

    /**
     * 个人坐骑信息
     */
    @Tag(65)
    private RoleMount roleMount = new RoleMount();

    /**
     * 异火系统
     */
    @Tag(66)
    private RoleStrangeFire roleStrangeFire = new RoleStrangeFire();

    /**
     * key装备位，value fashionId
     */
    @Tag(67)
    private Map<Integer, Integer> roleQianMing = new HashMap<>();

    /**
     * 真言系统
     */
    @Tag(68)
    private RoleZhenYan roleZhenYan = new RoleZhenYan();


    /**
     * 基金系统
     */
    @Tag(70)
    private RoleFoundation foundation = new RoleFoundation();

    /**
     * server各系统角色属性
     */
    @Exclude
    private Map<AttributeConst.AttributeType, Attribute> attributes = new HashMap<>();

    /**
     * server各系统角色不受加成影响属性
     */
    @Exclude
    private Map<AttributeConst.AttributeType, Attribute> noBonusAttributes = new HashMap<>();

    /**
     * server各系统角色属性汇总
     */
    @Exclude
    private Attribute attribute = new Attribute();

    public void setAttribute(Attribute attribute) {
        this.attribute = attribute;
    }

    /**
     * 服务器id
     */
    @Sync(launcher = true)
    @Exclude
    private int sid;

    /**
     * 当前是否在线
     */
    @Exclude
    private boolean online = false;

    /**
     * 套装数据
     */
    @Exclude
    private Set<Integer> equipSuits;

    @Exclude
    private Object conditionParam;

    @Exclude
    private int mountCoinLimit = -1;

    @Exclude
    private RoleMemory memory = new RoleMemory();

    @Exclude
    private boolean dirty = false;

    @Exclude
    private int platform;
    @Exclude
    private String channel = "";
    @Exclude
    private int pid;
    /**
     * 矿洞
     */
    @Exclude
    private OreHole oreHole = new OreHole();

    /**
     * 账户
     */
    @Exclude
    private String account;

    /**
     * 角色状态（由后台控制）
     */
    @Exclude
    private AvatarBackState avatarBackState = new AvatarBackState();

    @Override
    public int dataType() {
        return DataType.ROLE;
    }

    @Override
    public void sendSyncDataNotice(SyncDataNotice notice) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(this.id);
        if (player == null) {
            return;
        }
        player.sendNotice(notice);
    }

    @Override
    public void afterDeserializer() {
        if (hero != null) {
            Hero syncHero = SyncDataCreator.create(Hero.class);
            byte[] bytes = SerializerUtil.encode(hero, Hero.class);
            SerializerUtil.decode(bytes, Hero.class, syncHero);
            setHero(syncHero);
        }
    }

    public boolean isOwner(long actorId) {
        if (getId() == actorId) {
            return true;
        }
        Hero hero = getHero();
        if (hero == null || actorId != hero.getId()) {
            return false;
        }
        return true;
    }

    /**
     * 设置最终属性时调用
     */
    public void onSetFinalAttribute() {
        if (finalAttribute == null) {
            return;
        }
        ScriptEngine.invoke1tn(IEventOnRoleOrHeroAtkChanged.class, script -> script.onRoleAtkChanged(this));
        ScriptEngine.invoke1tn(IEventOnRoleOrHeroAtkChanged.class, script -> script.onRoleAtkMaxChanged(this));
        ScriptEngine.invoke1tn(IEventOnRoleOrHeroFightPowerChanged.class, script -> script.onRoleFightPowerChanged(this));
    }

    @Override
    public int getLevel() {
        return getRoleAdvance().getLevel();
    }

    @Override
    public int getZhuanShengId() {
        return getRoleAdvance().getZhuanshengId();
    }

    @Override
    public Map<Integer, Integer> getBossChallengeInfo() {
        return DataCenter.get(RoleDaily.class, this.id).getBossChallengeInfo();
    }

    @Override
    public int getMaxCompleteBarrier() {
        return findBarrier().getMaxBarrierNo();
    }

    @Override
    public boolean checkDuplicate(int minDupId, int maxDupId) {
        SeasonConfigCache cache = ConfigCacheManager.getInstance().getCache(SeasonConfigCache.class);
        if (maxDupId == minDupId) {
            if (cache.containsSeasonDup(minDupId)) {
                return findSeason().getDuplicateSet().contains(minDupId);
            } else {
                return getMaxCompleteBarrier() >= minDupId;
            }
        } else {
            return getMaxCompleteBarrier() >= minDupId && getMaxCompleteBarrier() <= maxDupId;
        }
    }

    @Override
    public long getUnionId() {
        return getUnion().getId();
    }

    @Override
    public int getSkillLevel(int skillID) {
        Skill skill = getRoleAdvance().getRoleSkill().getSkillMap().get(skillID);
        return skill != null ? skill.getLevel() : 0;
    }

    @Exclude
    private Map<Integer, Integer> tempDestinyGens;

    public int getDestinyGen(int id) {
        if (tempDestinyGens == null) {
            tempDestinyGens = new HashMap<>();
        }

        if (!tempDestinyGens.containsKey(id)) {
            tempDestinyGens.put(id, getRoleTask().getDestinyGens().getOrDefault(id, 0));
        }

        return tempDestinyGens.get(id);
    }

    public void mergeDestinyGens() {
        if (tempDestinyGens == null) {
            return;
        }
        for (Map.Entry<Integer, Integer> entry : tempDestinyGens.entrySet()) {
            getRoleTask().getDestinyGens().put(entry.getKey(), entry.getValue());
        }
        DataCenter.updateData(getRoleTask());
        tempDestinyGens = null;
    }

    /**
     * 获取装备套装
     *
     * @return
     */
    public Set<Integer> getEquipSuits() {
        BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(this);
        equipSuits = EquipUtil.getEquipSuits(this, getBackpack().fetchStorage(equipPlace));
        return equipSuits;
    }

    public void clearEquipSuits() {
        this.equipSuits = null;
    }

    @Override
    public long getItemCount(int id, BackpackConst.Place[] browse) {
        return getBackpack().fetchCountLByCfgId(id, browse);
    }

    @Override
    public List<Integer> findBackpackEquipCfgIdList(BackpackConst.Place[] browse) {
        return getBackpack().findItemByItemType(BagConst.ItemType.EQUIP, browse)
                .stream().map(Item::getCfgId).collect(Collectors.toList());
    }

    @Override
    public int getRechargeDaily() {
        return (int) recharge.getRechargedDaily();
    }

    @Override
    public int getRechargeTotal() {
        return (int) recharge.getRechargedTotal();
    }

    @Override
    public Object getSlotEquip(int slot) {
        Backpack backpack = DataCenter.getBackpack(getId());
        BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(this);
        Storage storage = backpack.fetchStorage(equipPlace);
        return storage.getData().get(slot);
    }

    /**
     * 获取玩家背包
     *
     * @return
     */
    @Override
    public Backpack getBackpack() {
        return DataCenter.getBackpack(getId());
    }

    /**
     * 获得货币后一些模块处理逻辑
     *
     * @param gain
     */
    @Override
    public void coinGain(CoinGain gain) {
        ScriptEngine.invoke1tn(IEventOnRoleCoinGainScript.class, script -> script.onRoleCoinGain(this, gain));
    }

    /**
     * 通知客户端背包变化
     *
     * @param backpack
     * @param msg
     */
    @Override
    public void updateBackpack(Backpack backpack, ResBackpackChangedMessage msg, LogAction action) {
        DataCenter.updateData(backpack);
        if (LogAction.ROLE_INIT.equals(action)) {
            return;
        }
        MessageUtil.sendMsg(msg, getId());
    }

    /**
     * 更新背包
     *
     * @param changes
     */
    @Override
    public void updateBackpack(List<ItemChange> changes) {
        BackpackManager.getInstance().onUpdate(this, changes);
    }

    /**
     * 更新货币
     *
     * @param changes
     * @param action
     */
    @Override
    public void updateBackpack(Map<Integer, Long> changes, int action) {
        BackpackManager.getInstance().onUpdate(this, changes, action);
    }

    /**
     * 道具日志处理
     *
     * @param changes
     */
    public void itemLog(List<ItemChange> changes, LogAction logAction) {
        BackpackManager.getInstance().itemLog(this, changes, logAction);
    }

    /**
     * 货币日志
     *
     * @param changes
     * @param oldValues
     * @param action
     */
    @Override
    public void coinLog(Map<Integer, Long> changes, Map<Integer, Long> oldValues, int action) {
        BackpackManager.getInstance().coinLog(this, changes, oldValues, action);
    }

    @Override
    public void sendNotice(byte processorId, ProcessNotice notice) {
        notice.setSourceHost(GameContext.getHostId());
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(processorId, notice, getId());
    }

    @Deprecated
    @Override
    public void sendErrorTips(String tips) {
        TipUtil.error(getId(), tips);
    }

    @Override
    public int fetchPickups(int id) {
        return getRoleDaily().getPickups().getOrDefault(id, 0);
    }

    @Override
    public void updatePickups(int id, int count) {
        getRoleDaily().getPickups().put(id, count);
        DataCenter.updateData(getRoleDaily());
    }

    @Override
    public BackpackCommitController getBackpackCommitController(LogAction action) {
        return BackpackController.getController(action);
    }

    @Override
    public int getUnionLevel() {
        Union union = UnionManager.getInstance().getUnion(this);
        return union != null ? union.getUnionLevel() : 0;
    }

    @Override
    public int getTitleLevel() {
        return getRoleAdvance().getTitle();
    }

    /**
     * 判断特权月卡是否可用
     * timeout为-1时为永久
     *
     * @param id 月卡id
     * @return {@link boolean} true:可用  false:不可用
     **/
    @Override
    public boolean monthCardAvailable(int id) {
        RoleCard roleCard = getRoleAdvance().getMonthCard().get(id);
        return roleCard != null && (roleCard.getTimeout() == -1 || roleCard.getTimeout() > TimeUtil.getNowOfSeconds());
    }

    @Override
    public Object getConditionParam() {
        return conditionParam;
    }

    public void setConditionParam(Object param) {
        this.conditionParam = param;
    }

    @Override
    public void sendPost(int postId) {
        TipUtil.post(getId(), postId);
    }

    @Override
    public boolean conditionValidate(int[] params) {
        return RoleConditionValidator.getInstance().validate(this, params);
    }

    @Override
    public int getStoreBuys(int id) {
        return getRoleTask().getStoreBuys().getOrDefault(id, 0);
    }

    @Override
    public boolean checkMainTask(int id, int state) {
        return TaskManager.getInstance().getMainTaskState(this, id) == state;
    }

    /**
     * 检查支线任务状态
     *
     * @param id    任务id
     * @param state 任务状态
     * @return 是否完成
     */
    @Override
    public boolean checkBranchTask(int id, int state) {
        return TaskManager.getInstance().getBranchTaskState(this, id) >= state;
    }

    @Override
    public int getUnionBossRanking() {
        Union union = UnionManager.getInstance().getUnion(this);
        return union != null ? union.getBossRanking() : 0;
    }

    @Override
    public int getBoreMaxLevel() {
        int level = 0;
        for (BackpackConst.Place place : BackpackConst.Browse.GEM_ALL) {
            Storage storage = getBackpack().fetchStorage(place);
            if (storage == null) {
                continue;
            }
            for (Item item : storage.getData().values()) {
                if (level < item.boreLevel()) {
                    level = item.boreLevel();
                }
            }
        }

        return level;
    }

    public boolean proxyCall(Consumer<ProxyPlayer> consumer) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(getId());
        if (player == null) {
            return false;
        }
        consumer.accept(player);
        return true;
    }

    public RoleAdvance getRoleAdvance() {
        return DataCenter.get(RoleAdvance.class, this.id);
    }

    public RoleAdvance getRoleAdvance(long actorId) {
        return getId() == actorId ? getRoleAdvance() : getHero().getRoleAdvance();
    }

    public Attribute getAttribute(long actorId) {
        return getId() == actorId ? getAttribute() : getHero().getAttribute();
    }

    public RoleDaily getRoleDaily() {
        return DataCenter.get(RoleDaily.class, this.id);
    }

    public RoleActivity getRoleActivity() {
        return DataCenter.get(RoleActivity.class, this.id);
    }

    public RoleTask getRoleTask() {
        return DataCenter.get(RoleTask.class, this.id);
    }

    public void updateSyncAll(List<SyncBean> syncBeans) {
        if (syncBeans == null) {
            return;
        }

        Map<Integer, List<SyncBean>> syncs = new HashMap<>();
        syncBeans.forEach(syncBean -> {
            List<SyncBean> syncBeanList = syncs.computeIfAbsent(syncBean.getDataType(), k -> new ArrayList<>());
            syncBeanList.add(syncBean);
        });
        syncs.forEach((dataType, syncBeansList) -> {
            switch (dataType) {
                case DataType.ROLE:
                    updateSync(syncBeansList);
                    DataCenter.updateData(this);
                    break;
                case DataType.ADVANCE:
                    getRoleAdvance().updateSync(syncBeansList);
                    DataCenter.updateData(getRoleAdvance());
                    break;
                case DataType.ACTIVITY:
                    getRoleActivity().updateSync(syncBeansList);
                    DataCenter.updateData(getRoleActivity());
                    break;
                case DataType.TASK:
                    getRoleTask().updateSync(syncBeansList);
                    DataCenter.updateData(getRoleTask());
                    break;
            }
        });
    }

    /**
     * 同步player数据
     *
     * @param playerDTO
     */
    public void syncPlayerDTO(PlayerDTO playerDTO) {
        if (playerDTO == null) {
            return;
        }
        getRoleAdvance().getBuffs().setBuffMap(playerDTO.getBuffs().getBuffMap());
        setHp(playerDTO.getHp());
        setMp(playerDTO.getMp());
        setShield(playerDTO.getShield());
        setPkValueTotalTime(playerDTO.getPkValueTotalTime());
        setCdMap(playerDTO.getCdMap());
        if (playerDTO.getDurableMap() != null) {
            Backpack backpack = getBackpack();
            BackpackStash stash = new BackpackStash(this);
            playerDTO.getDurableMap().forEach((k, v) -> {
                Item item = backpack.findItemByUniqueId(k);
                if (item != null) {
                    stash.update(item, up -> up.eData().setDurable(v[1]));
                }
            });
            stash.commit(this, LogAction.DURABLE_COST);
        }
        if (getXialv().getFollowId() > 0) {
            getXialv().setHp(playerDTO.getXiaLvHp());
        }
        setServantDTOS(playerDTO.getServantDTOS());
        updateSyncAll(playerDTO.getSyncBeans());
    }

    /**
     * 根据背包类型获取IAvatar
     */
    public IAvatar getIAvatarByItemWhere(int where) {
        IAvatar avatar = this;
        BackpackConst.Place place = BackpackConst.Place.getPlace(where);
        if (ArrayUtils.contains(BackpackConst.Browse.BACKPACK_HERO_ALL, place)) {
            Hero hero = getHero();
            if (hero == null) {
                return null;
            }
            avatar = hero;
        }
        return avatar;
    }

    public IAvatar getIAvatar(long actorId) {
        if (getId() == actorId) {
            return this;
        }
        Hero hero = getHero();
        if (hero == null) {
            return null;
        }
        return hero;
    }

    @Override
    public List<Integer> getRoleAndHeroCareer() {
        if (hero == null) {
            return Collections.EMPTY_LIST;
        }
        List<Integer> list = new ArrayList<>();
        list.add(getCareer());
        list.add(hero.getCareer());
        return list;
    }

    @Override
    public boolean checkOfficial(int officialID) {
        return this.officialID >= officialID;
    }

    @Override
    public boolean checkLoyalty(int loyalty) {
        if (hero == null) {
            return false;
        }
        return hero.getLoyalty() >= loyalty;
    }

    @Override
    public boolean checkNormalTreasureHuntCount(int count) {
        return this.getTreasureHunt().getLastHuntCount() >= count;
    }

    @Override
    public boolean checkLimitTreasureHuntCount(int count) {
        return this.getTreasureHunt().getLastLimitHuntCount() >= count;
    }

    @Override
    public Map<Integer, Skill> getSkillMap() {
        return getRoleAdvance().getRoleSkill().getSkillMap();
    }

    public long getShield() {
        return shield;
    }

    public void setShield(long shield) {
        this.shield = shield;
    }

    /**
     * 获取祭坛次数
     *
     * @return
     */
    public int getJiTanCount() {
        int limit = Integer.parseInt(GlobalUtil.getGlobalValue(GameConst.GlobalId.JI_TAN_COUNT).split(Symbol.JINHAO)[0]);
        int leftCount = CountManager.getInstance().getCount(this, CountConst.CountType.JI_TAN_BOSS_BUY_COUNT) + limit
                - CountManager.getInstance().getCount(this, CountConst.CountType.JI_TAN_BOSS_COUNT)
                + CountManager.getInstance().getCount(this, CountConst.CountType.JI_TAN_BOSS_CHANGE_COUNT);
        return leftCount;
    }

    /**
     * 获取英雄编号
     *
     * @return
     */
    public long getHeroID() {
        if (hero == null) {
            return 0;
        }
        return hero.getId();
    }

    @Override
    public int getAchievementLevel() {
        RoleTask roleTask = getRoleTask();
        return roleTask.getAchieveLevel();
    }

    @Override
    public int getZhanYiLevel() {
        ZhanyiConfig zhanyiConfig = ConfigDataManager.getInstance().getById(ZhanyiConfig.class, zhanyi.getCfgId());
        return zhanyiConfig.getLevel();
    }

    @Override
    public int findCurShenBingLevel() {
        RoleShenbingVip roleShenBingVip = ShenBingManager.getInstance().findMoudel(getId());
        return roleShenBingVip.getShengBingVipLevel();
    }

    @Override
    public int findMaxGainExpShenBingId() {
        // 提供最大泡点经验值
        int gainExp = 0;
        int configId = 0;
        RoleShenbingVip roleShenBingVip = ShenBingManager.getInstance().findMoudel(getId());
        for (Integer shenBingId : roleShenBingVip.getRequiredShenBingIds()) {
            ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, shenBingId);
            if (config == null || config.getGainPerSec() == null || config.getGainPerSec().length != 2) {
                continue;
            }
            if (gainExp < config.getGainPerSec()[1]) {
                gainExp = config.getGainPerSec()[1];
                configId = config.getId();
            }
        }
        return configId;
    }

    @Override
    public Set<Integer> getFashion() {
        CommonData data = SysDataProvider.get(CommonData.class);
        return data.getTitles().keySet();
    }

    @Override
    public Map<Integer, Integer> getXiaLvs() {
        return xialv.getCfgIds();
    }

    @Override
    public boolean checkClimbTowerFloor(int val) {
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class, getId());
        RoleClimbTower towerInfo = roleDaily.getClimbTower();
        return towerInfo.getIds().contains(val);
    }

    @Override
    public int findFengHaoId() {
        return getFengHaoId();
    }

    @Override
    public int findWuDaoId() {
        return WuDaoManager.getInstance().findSuitId(this);
    }

    @Override
    public Map<Integer, Integer> getTitles() {
        return getRoleAdvance().getAppearance().getStatus();
    }

    @Override
    public int getYuanYingToTalLevel() {
        return getYuanYing().getYuanYingLevel();
    }

    @Override
    public Map<Integer, Integer> getYuanYingLevelMap() {
        return getYuanYing().getYuanYing();
    }

    @Override
    public int findTianShuLevel() {
        TianshuItem tianshuItem = TianShuManager.getInstance().find(getRoleId());
        return tianshuItem.getLevel();
    }

    @Override
    public Map<Integer, List<RankDataBean>> getRankData() {
        Map<Integer, AbstractRank> map = RankFactory.getAllRankHandler();
        Map<Integer, List<RankDataBean>> rankMap = new HashMap<>(map.size());
        for (Map.Entry<Integer, AbstractRank> entry : map.entrySet()) {
            List<RankDataBean> dataList = entry.getValue().getRankList().stream().map(RankData::toBean).collect(Collectors.toList());
            rankMap.put(entry.getKey(), dataList);
        }
        return rankMap;
    }

    @Override
    public Map<Integer, Integer> findJueXingLevel() {
        RoleEquipJuexing juexing = EquipManager.getInstance().findJueXing(getId());
        return juexing.getJuexingLevel();
    }

    @Override
    public int findXiaLvGuardianLevel() {
        return getXialv().getGuardianLevel();
    }

    @Override
    public int findXiaLvGuanghuanLevel() {
        XiaLvGuangHuanConfig config = ConfigDataManager.getInstance().getById(XiaLvGuangHuanConfig.class, getXialv().getGuanghuanId());
        if (config == null) {
            return 0;
        }
        return config.getLevel();
    }

    /**
     * 四大传承等级
     *
     * @return {@link Map} key: 传承类型type   value: 传承等级level
     */
    @Override
    public Map<Integer, Integer> findLineageLevel() {
        RoleLineageItem roleLineageItem = LineageManager.getInstance().find(getRoleId());
        return roleLineageItem.getLineageMap();
    }

    /**
     * 人民币已兑换金额
     *
     * @return int 人民币已兑换金额
     */
    @Override
    public int findUnUseRmbVal() {
        return RecycleManager.getInstance().getUnUseRMBVal(this);
    }

    /**
     * 神威数据
     *
     * @return
     */
    @Override
    public Map<Integer, Integer> findShenWei() {
        return getRoleShenWei();
    }

    @Override
    public FaQiHuiYuanConfig findFaQiHuiYuanConfig() {
        FaQiHuiYuanConfig retObj = ConfigDataManager.getInstance().getById(FaQiHuiYuanConfig.class, faQiHuYuanData.getLevelID());
        if (retObj == null) {
            retObj = new FaQiHuiYuanConfig();
        }
        return retObj;
    }

    /**
     * 法阵等级
     *
     * @return
     */
    @Override
    public int findMagicCircleLevel() {
        RoleMagicCircle roleMagicCircle = DataCenter.get(RoleMagicCircle.class, getRoleId());
        if (roleMagicCircle == null) {
            return 0;
        }
        MagicCircleInfo magicCircleInfo = roleMagicCircle.getMagicCircleMap().get(MagicCircleConst.TypeEnum.CIRCLE.getType());
        if (magicCircleInfo == null) {
            return 0;
        }
        return magicCircleInfo.getLevel();
    }


    /**
     * 法阵等级
     *
     * @param type 类型
     * @return int 等级
     */
    @Override
    public int findMagicCircleLevel(int type) {
        RoleMagicCircle roleMagicCircle = DataCenter.get(RoleMagicCircle.class, getRoleId());
        if (roleMagicCircle == null) {
            return 0;
        }
        MagicCircleInfo magicCircleInfo = roleMagicCircle.getMagicCircleMap().get(type);
        if (magicCircleInfo == null) {
            return 0;
        }
        return magicCircleInfo.getLevel();
    }

    /**
     * 获取角色激活灵根天命总数量
     *
     * @return int 获取角色激活灵根天命总数量
     */
    @Override
    public int getHandBookCount() {
        RoleHandbook roleHandbook = HandbookManager.getInstance().find(getRoleId());
        return roleHandbook.getUnlockedHandbookList().size();
    }

    @Override
    public int findPetLevel() {
        ChongWuConfig config = ConfigDataManager.getInstance().getById(ChongWuConfig.class, this.getRoleChongWuCfgId());
        if (config == null) {
            return 0;
        }
        return config.getLevel();
    }

    @Override
    public int findRoleRingsLevel() {
        int addLevel = 0;
        BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(this);
        Storage storage = getBackpack().fetchStorage(equipPlace);
        List<Integer> ringPosList = GlobalUtil.findJingHaoList(GameConst.GlobalId.SPECIALRING_POS);
        if (ringPosList == null) {
            return 0;
        }
        for (Integer pos : ringPosList) {
            Item item = storage.fetch(pos);
            if (item == null) {
                continue;
            }
            ItemConfig itemConfig = item.findItemConfig();
            if (itemConfig == null) {
                continue;
            }
            addLevel = addLevel + itemConfig.getLevel();
        }
        return addLevel;
    }

    @Override
    public boolean findShabakeUnionChairman() {
        ShaBaKeCrossDataProxy shaBaKeCrossData = CrossStorageManager.getInstance().findShaBaKeCrossData();
        RoleSimpleData chairman = shaBaKeCrossData.getChairman();
        if (chairman == null) {
            return false;
        }
        return chairman.getRid() == this.getRoleId();
    }

    @Override
    public int findBodyForge(int type) {
        RoleBodyForge roleBodyForge = DataCenter.get(RoleBodyForge.class, getRoleId());
        if (roleBodyForge == null) {
            return 0;
        }
        return roleBodyForge.getBodyForgeMap().getOrDefault(type, 0);
    }

    @Override
    public int getRoleCreationTime() {
        return roleLogin.getCreateTime();
    }

    @Override
    public int findFateTotalLevel() {
        RoleFate roleFate = FateManager.getInstance().find(getRoleId());
        if (roleFate == null) {
            return 0;
        }
        int totalLevel = 0;
        for (Integer level : roleFate.getFateMap().values()) {
            totalLevel += level;
        }
        return totalLevel;
    }

    public RoleNormal findNormal() {
        RoleNormal normal = DataCenter.get(RoleNormal.class, this.getId());
        if (normal == null) {
            normal = new RoleNormal();
            normal.setId(this.getId());
            DataCenter.insertData(normal, true);
        }
        return normal;
    }

    public RoleWanXianBang findWanXianBang() {
        RoleWanXianBang wanXianBang = DataCenter.get(RoleWanXianBang.class, this.getId());
        if (wanXianBang == null) {
            wanXianBang = new RoleWanXianBang();
            wanXianBang.setId(this.getId());
            DataCenter.insertData(wanXianBang, true);
        }
        return wanXianBang;
    }

    public RoleHuoBan findHuoBan() {
        RoleHuoBan huoBan = DataCenter.get(RoleHuoBan.class, this.getId());
        if (huoBan == null) {
            huoBan = new RoleHuoBan();
            huoBan.setId(this.getId());
            huoBan.setMaxSize(GlobalUtil.getGlobalInt(GameConst.GlobalId.HUO_BAN_BAG_INIT_SIZE));
            refreshFirstHuoBan(huoBan);
            if (CollectionUtils.isEmpty(huoBan.getRefreshHuoBans())) {
                refreshHuoBan(huoBan);
            }
            DataCenter.insertData(huoBan, true);
        }
        return huoBan;
    }

    private void refreshFirstHuoBan(RoleHuoBan huoBan) {
        List<Integer> cfgList = GlobalUtil.findJingHaoList(GameConst.GlobalId.HUO_BAN_FIRST_REFRESH);
        if (CollectionUtils.isEmpty(cfgList)) {
            return;
        }
        int index = 1;
        for (int i : cfgList) {
            HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, i);
            if (config == null) {
                continue;
            }
            RefreshHuoBan refreshHuoBan = new RefreshHuoBan();
            refreshHuoBan.setConfigId(config.getId());
            refreshHuoBan.setIndex(index);
            huoBan.getQualityRefreshMap().merge(config.getQuality(), 1, Integer::sum);
            huoBan.getRefreshHuoBans().add(refreshHuoBan);
            index++;
        }
    }

    private void refreshHuoBan(RoleHuoBan huoBan) {
        List<HuoBanConfig> list = ConfigDataManager.getInstance().getList(HuoBanConfig.class);
        if (!list.isEmpty()) {
            List<Integer> collect = list.stream().map(HuoBanConfig::getFre_weight).collect(Collectors.toList());
            for (int i = 0; i < GlobalUtil.getGlobalInt(GameConst.GlobalId.HUO_BAN_REFRESH_COUNT); i++) {
                int index = i + 1;
                int prob = RandomUtil.randomIndexByProb(collect);
                HuoBanConfig config = list.get(prob);
                RefreshHuoBan refreshHuoBan = new RefreshHuoBan();
                refreshHuoBan.setConfigId(config.getId());
                refreshHuoBan.setIndex(index);
                huoBan.getRefreshHuoBans().add(refreshHuoBan);
            }
        }
    }

    public RoleChaiJia findChaiJia() {
        RoleChaiJia chaiJia = DataCenter.get(RoleChaiJia.class, this.getId());
        if (chaiJia == null) {
            chaiJia = new RoleChaiJia();
            chaiJia.setId(this.getId());
            DataCenter.insertData(chaiJia, true);
        }
        return chaiJia;
    }

    public RoleBarrier findBarrier() {
        RoleBarrier roleBarrier = DataCenter.get(RoleBarrier.class, this.getId());
        if (roleBarrier == null) {
            roleBarrier = new RoleBarrier();
            roleBarrier.setId(this.getId());
            DataCenter.insertData(roleBarrier, true);
        }
        return roleBarrier;
    }

    public RoleShenMo findShenMo() {
        RoleShenMo roleShenMo = DataCenter.get(RoleShenMo.class, this.getId());
        if (roleShenMo == null) {
            roleShenMo = new RoleShenMo();
            roleShenMo.setId(this.getId());
            DataCenter.insertData(roleShenMo, true);
        }
        return roleShenMo;
    }

    public RoleArena findArena() {
        RoleArena roleArena = DataCenter.get(RoleArena.class, this.getId());
        if (roleArena == null) {
            roleArena = new RoleArena();
            roleArena.setId(this.getId());
            DataCenter.insertData(roleArena, true);
        }
        return roleArena;
    }

    public RoleHouYuanData findRoleHouYuan() {
        RoleHouYuanData roleHouYuanData = DataCenter.get(RoleHouYuanData.class, this.getId());
        if (roleHouYuanData == null) {
            roleHouYuanData = new RoleHouYuanData();
            roleHouYuanData.setId(this.getId());
            DataCenter.insertData(roleHouYuanData, true);
        }
        return roleHouYuanData;
    }

    public RoleClientData findClientData() {
        RoleClientData roleClientData = DataCenter.get(RoleClientData.class, this.getId());
        if (roleClientData == null) {
            roleClientData = new RoleClientData();
            roleClientData.setId(this.getId());
            DataCenter.insertData(roleClientData, true);
        }
        return roleClientData;
    }

    public RoleWorkData findWorkData() {
        RoleWorkData workData = DataCenter.get(RoleWorkData.class, this.getId());
        if (workData == null) {
            workData = new RoleWorkData();
            workData.setId(this.getId());
            DataCenter.insertData(workData, true);
        }
        return workData;
    }

    @Override
    public int findYuPei() {
        return findNormal().getYuPeiId();
    }

    public RoleXiuLuo findRoleXiuLuo() {
        RoleXiuLuo xiuLuo = DataCenter.get(RoleXiuLuo.class, this.getId());
        if (xiuLuo == null) {
            xiuLuo = new RoleXiuLuo();
            xiuLuo.setId(this.getId());
            DataCenter.insertData(xiuLuo, true);
        }
        return xiuLuo;
    }

    public RoleSkillTree findRoleSkillTree() {
        RoleSkillTree roleSkillTree = DataCenter.get(RoleSkillTree.class, this.getId());
        if (roleSkillTree == null) {
            roleSkillTree = new RoleSkillTree();
            roleSkillTree.setId(this.getId());
            DataCenter.insertData(roleSkillTree, true);
        }
        return roleSkillTree;
    }

    @Override
    public String findChannel() {
        return this.channel;
    }

    /**
     * 获取天书、神剑总等级
     *
     * @param type 天书1 神剑2
     * @return int 总等级
     */
    @Override
    public int findBookSwordLevel(int type) {
        RoleBookSword roleBookSword = BookSwordManager.getInstance().find(this.getRoleId());
        BookSwordCache cache = ConfigCacheManager.getInstance().getCache(BookSwordCache.class);
        int totalLevel = 0;
        for (Map.Entry<Integer, Integer> entry : roleBookSword.getBookSwordMap().entrySet()) {
            int sub = entry.getKey();
            int level = entry.getValue();
            BookSwordConfig config = cache.getConfig(sub, level);
            if (config.getType() == type) {
                totalLevel += level;
            }
        }
        return totalLevel;
    }

    /**
     * 判断玩家该时装是否解锁
     *
     * @return
     */
    public boolean findFashionUnlock(int fashionId) {
        RoleAdvance roleAdvance = getRoleAdvance();
        return roleAdvance.getAppearance().getStatus().containsKey(fashionId);
    }

    @Override
    public int findCampType() {
        return UnionCampManager.getInstance().findUnionCampType(this);
    }

    @Override
    public int findMilitaryRankCfgId() {
        RoleMilitaryRank roleMilitaryRank = MilitaryRankManger.getInstance().find(getRoleId());
        return roleMilitaryRank.getMilitaryRankCfgId();
    }

    @Override
    public int findXuanBingLevel() {
        RoleXuanBing roleXuanBing = XuanBingManager.getInstance().find(this.getRoleId());
        XuanBingConfig config = ConfigDataManager.getInstance().getById(XuanBingConfig.class, roleXuanBing.getConfigId());
        if (config == null) {
            return 0;
        }
        return config.getPart();
    }

    @Override
    public int findRoleFireLevel() {
        int level = 0;
        RoleStrangeFire roleStrangeFire = this.getRoleStrangeFire();
        Map<Integer, RoleFire> roleFireMap = roleStrangeFire.getRoleFireMap();
        for (Map.Entry<Integer, RoleFire> entry : roleFireMap.entrySet()) {
            RoleFire roleFire = entry.getValue();
            if (roleFire == null) {
                continue;
            }
            FireConfig fireConfig = ConfigDataManager.getInstance().getById(FireConfig.class, roleFire.getUpCfgId());
            if (fireConfig == null) {
                continue;
            }
            level = level + fireConfig.getLv();
        }
        return level;
    }

    @Override
    public int findRoleDanYaoPoint() {
        int point = 0;
        RoleDanYao roleDanYao = this.getRoleDanYao();
        for (Map.Entry<Integer, Integer> entry : roleDanYao.getTalentDataMap().entrySet()) {
            point = point + entry.getValue();
        }
        return point + roleDanYao.getRemainingPoints();
    }

    public RoleSeason findSeason() {
        RoleSeason season = DataCenter.get(RoleSeason.class, this.getId());
        if (season == null) {
            season = new RoleSeason();
            season.setId(this.getId());

            DataCenter.insertData(season, true);
        }
        return season;
    }

    public RoleYuanJingStore findYuanJingStore() {
        RoleYuanJingStore yuanJingStore = DataCenter.get(RoleYuanJingStore.class, this.getId());
        if (yuanJingStore == null) {
            yuanJingStore = new RoleYuanJingStore();
            yuanJingStore.setId(this.getId());
            DataCenter.insertData(yuanJingStore, true);
        }
        return yuanJingStore;
    }

    /**
     * 校验玩家今日是否执行过零点事件
     * 时间为0、当日创角算执行过
     *
     * @return boolean true:未执行  false:已执行
     */
    public boolean verifyMidnight() {
        return this.getRoleLogin().getResetTime() > 0
                && !TimeUtil.isToday(this.getRoleCreationTime())
                && !TimeUtil.isToday(this.getRoleLogin().getResetTime());
    }

    public RoleLineUps findLineUp() {
        RoleLineUps lineUp = DataCenter.get(RoleLineUps.class, this.getId());
        if (lineUp == null) {
            lineUp = new RoleLineUps();
            lineUp.setId(this.getId());
            DataCenter.insertData(lineUp, true);
            RoleLineUpManager.getInstance().unlockFreeLineUp(this);
        }
        return lineUp;
    }

    public RoleLiXian findRoleLiXian() {
        RoleLiXian roleLiXian = DataCenter.get(RoleLiXian.class, this.getId());
        if (roleLiXian == null) {
            roleLiXian = new RoleLiXian();
            roleLiXian.setId(this.getId());
            DataCenter.insertData(roleLiXian, true);
        }
        return roleLiXian;
    }

    public RoleDrawCardData findDrawCardData() {
        RoleDrawCardData drawCardData = DataCenter.get(RoleDrawCardData.class, this.getId());
        if (drawCardData == null) {
            drawCardData = new RoleDrawCardData();
            drawCardData.setId(this.getId());
            DataCenter.insertData(drawCardData, true);
        }
        return drawCardData;
    }

    public RoleZTPet findZTPet() {
        RoleZTPet ztPet = DataCenter.get(RoleZTPet.class, this.getId());
        if (ztPet == null) {
            ztPet = new RoleZTPet();
            ztPet.setId(this.getId());
            DataCenter.insertData(ztPet, true);
        }
        return ztPet;
    }

    @Override
    public int findRoleRealm() {
        RoleRealm roleRealm = RealmManager.getInstance().find(this.id);
        return roleRealm.getRealmId();
    }

    @Override
    public int findShengQiTotalLevel() {
        Map<Integer, Integer> shengQiMap = findNormal().getShengQiMap();
        int level = 0;
        for (Integer cfgId : shengQiMap.values()) {
            ShengQiConfig config = ConfigDataManager.getInstance().getById(ShengQiConfig.class, cfgId);
            if (config != null) {
                level += config.getLv();
            }
        }
        return level;
    }

    @Override
    public int findShenMoLevel(int shenMoId) {
        return findShenMo().getShenMoLevel(shenMoId);
    }

    @Override
    public int findFriendLevel(int friendId) {
        return findNormal().getFriendDataMap().getOrDefault(friendId, new FriendData()).getLevelCfgId();
    }

    @Override
    public int findHouYuanRatCount() {
        return findRoleHouYuan().getRatMap().size();
    }

    @Override
    public int findZhenYaoTaLayer() {
        ZhenYaoTaData data = findNormal().getZhenYaoTaData();
        return data.getHighestBarrierId();
    }

    @Override
    public int findArenaSuccessCount() {
        return CountManager.getInstance().getCount(this, CountConst.CountType.ARENA_SUCCESS_COUNT);
    }

    @Override
    public int findBossKingId() {
        return findNormal().getBosskingCfgId();
    }

    @Override
    public int findHuoBanLevel(int configId) {
        Map<Integer, HuoBan> huoBanBag = findHuoBan().getHuoBanBag();
        HuoBan ban = huoBanBag.getOrDefault(configId, null);
        if (ban == null) {
            return 0;
        }
        return ban.getLevel();
    }

    @Override
    public int findChaiJiaLevel() {
        RoleChaiJia chaiJia = this.findChaiJia();
        return chaiJia.getChaiJiaCfgId();
    }

    @Override
    public int findHouYuanWorkLevel(int type) {
//        RoleWorkData roleWorkData = this.findWorkData();
//        RoleWork roleWork = roleWorkData.getWorkShopMap().getOrDefault(type, null);
//        if (roleWork == null) {
//            return 0;
//        }
//        WorkShopConfig config = ConfigDataManager.getInstance().getById(WorkShopConfig.class, roleWork.getCurrentCid());
//        if (config == null) {
//            return 0;
//        }
//        return config.getLevel();
        //用最新建筑判断
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(this);
        Map<Integer, TwoTuple<Integer, Integer>> buildLevel = roleExtend.getBuildData().getBuildLevel();
        if (!buildLevel.containsKey(type)) {
            return 0;
        }
        return buildLevel.get(type).getFirst();
    }

    @Override
    public int findShenMoXiYingCount() {
        return CountManager.getInstance().getCount(this, CountConst.CountType.SHEN_MO_XI_YING);
    }

    @Override
    public int findShenMoCount() {
        return this.findShenMo().getShenMoOwnMap().size();
    }

    @Override
    public int findHuoBanBuyCount() {
        return CountManager.getInstance().getCount(this, CountConst.CountType.HUO_BAN_BUY);
    }

    @Override
    public int findHuoBanCount() {
        return this.findHuoBan().getHuoBanBag().size();
    }

    @Override
    public int findXiXingLayer() {
        return 0;
    }

    @Override
    public boolean findOwnSpecialCard(int type) {
        return ActivityValueCardManager.getInstance().hasTeQuanCard(this, type);
    }

    @Override
    public void beforeStashChange(BackpackStash stash) {
        LogManager.getInstance().beforeStashChange(stash, this);
    }

    @Override
    public void afterStashChange(BackpackStash stash) {
        LogManager.getInstance().afterStashChange(stash, this);
    }

    @Override
    public void beforeStashFlush(BackpackStash stash) {
        LogManager.getInstance().beforeStashFlush(stash, this);
    }

    @Override
    public void afterStashFlush(BackpackStash stash) {
        LogManager.getInstance().afterStashFlush(stash, this);
    }

    @Override
    public void beforeStashDoCommit(BackpackStash stash) {
        LogManager.getInstance().beforeStashDoCommit(stash, this);
    }

    @Override
    public void afterStashDoCommit(BackpackStash stash) {
        LogManager.getInstance().afterStashDoCommit(stash, this);
    }

    @Override
    public boolean findHuoBanById(int huoBanId) {
        RoleHuoBan huoBan = this.findHuoBan();
        if (huoBan == null) {
            return false;
        }
        return huoBan.getHuoBanBag().containsKey(huoBanId);
    }

    @Override
    public boolean findShenMoById(int shenMoId) {
        RoleShenMo shenMo = this.findShenMo();
        if (shenMo == null) {
            return false;
        }
        return shenMo.getShenMoOwnMap().containsKey(shenMoId);
    }

    @Override
    public int findChaoDuCount() {
        RoleChaoDu roleChaoDu = ChaoDuManager.getInstance().findChaoDu(this.getId());
        return roleChaoDu.getChaoDuCount();
    }

    @Override
    public long findExp() {
        return Optional.ofNullable(getBackpack().getCoin().getOrDefault(BagConst.ItemId.ROLE_EXP, 0L)).orElse(0L);
    }

    @Override
    public int minCompleteTime(int no) {
        SeasonConfigCache cache = ConfigCacheManager.getInstance().getCache(SeasonConfigCache.class);
        BarrierProgress progress;
        if (cache.containsSeasonDup(no)) {
            progress = findSeason().getDuplicateProgress().getOrDefault(no, null);
        } else {
            if (findBarrier().getBarrierProgress() == null) {
                findBarrier().setBarrierProgress(new HashMap<>());
            }
            progress = findBarrier().getBarrierProgress().get(no);
        }
        if (progress == null)
            return 0;
        return progress.getMinCompleteTime();
    }

    public BarrierProgress getBarrierProgress(int no) {
        if (findBarrier().getBarrierProgress() == null) {
            findBarrier().setBarrierProgress(new HashMap<>());
        }
        return findBarrier().getBarrierProgress().get(no);
    }
}
