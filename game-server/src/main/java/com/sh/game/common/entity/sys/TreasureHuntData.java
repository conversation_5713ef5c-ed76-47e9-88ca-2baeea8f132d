package com.sh.game.common.entity.sys;

import com.sh.game.common.entity.SysDataType;
import com.sh.game.system.treasureHunt.entity.TreasureHuntRecord;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;

@Setter
@Getter
public class TreasureHuntData extends AbstractSysData {

    /**
     * 寻宝记录
     */
    @Tag(1)
    private LinkedList<TreasureHuntRecord> huntRecord = new LinkedList<>();

    @Override
    public void setId(long id) {

    }

    @Override
    public long getId() {
        return SysDataType.TREASURE_HUNT;
    }
}
