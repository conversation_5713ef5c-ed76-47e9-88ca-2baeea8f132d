package com.sh.game.common.entity.sys;


import com.sh.game.common.entity.Ban;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@Setter
public class BanData extends AbstractSysData {

    @Tag(1)
    private long id;

    @Tag(2)
    private Map<Long, Ban> banUsers = new ConcurrentHashMap<>();

    @Tag(3)
    private Map<Long, Ban> banRoles = new ConcurrentHashMap<>();

    @Tag(4)
    private Map<Long, Ban> banChats = new ConcurrentHashMap<>();

    @Tag(5)
    private Map<String, Ban> banIPs = new ConcurrentHashMap<>();

    @Tag(6)
    private Map<String, Ban> banDevices = new ConcurrentHashMap<>();

    //服务器注册限制
    @Tag(7)
    private Ban cantCreate=new Ban();
}
