package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleLiXian extends AbstractRoleEntity {
    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 离线累计时间,单位s
     */
    @Tag(2)
    private int time;
}
