package com.sh.game.common.util;

import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.turn_based.constant.TurnBasedConst;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BackPackStashUtil {
    public static boolean decrease(Role role, int itemId, int count, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, count);
        if (!stash.commit(role, logAction)) {
            return false;
        }
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, itemId, count);
        return true;
    }

    public static boolean decrease(Role role, int itemId, int count, LogAction logAction, boolean tips) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, count);
        if (!stash.commit(role, logAction, tips)) {
            return false;
        }
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, itemId, count);
        return true;
    }

    public static boolean decrease(Role role, List<int[]> items, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(items);
        if (!stash.commit(role, logAction)) {
            return false;
        }
        for (int[] ints : items) {
            if (ints.length < 2) {
                continue;
            }
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, ints[0], ints[1]);
        }

        return true;
    }

    public static boolean decrease(Role role, List<int[]> items, double multiple, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(items, multiple);
        if (!stash.commit(role, logAction)) {
            return false;
        }
        for (int[] ints : items) {
            if (ints.length < 2) {
                continue;
            }
            long cn = (long) (ints[1] * multiple);
            if (cn < 0) {
                continue;
            }
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, ints[0], (int) cn);
        }

        return true;
    }

    public static boolean decrease(Role role, Map<Integer, Long> itemMap, double multiple, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemMap, multiple);
        if (!stash.commit(role, logAction)) {
            return false;
        }
        for (Map.Entry<Integer, Long> entry : itemMap.entrySet()) {
            long cn = (long) (entry.getValue() * multiple);
            if (cn < 0) {
                continue;
            }
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, entry.getKey(), (int) cn);
        }

        return true;
    }

    public static boolean increase(Role role, Map<Integer, Long> itemMap, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemMap);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else {
            RewardInfoManager.getInstance().resRewardInfoByItemMap(role, TurnBasedConst.BattleType.NORMAL, itemMap);
        }
        return true;
    }

    public static boolean increaseItemList(Role role, List<Item> items, LogAction logAction) {
        return increaseItemList(role, items, logAction, TurnBasedConst.BattleType.NORMAL);
    }

    public static boolean increaseItemList(Role role, List<Item> items, LogAction logAction, int rewardType) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else {
            RewardInfoManager.getInstance().resRewardInfoByItem(role, rewardType, items);
        }
        return true;
    }

    public static boolean increase(Role role, List<int[]> items, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else {
            RewardInfoManager.getInstance().resRewardInfo(role, TurnBasedConst.BattleType.NORMAL, items);
        }
        return true;
    }

    public static boolean increase(Role role, List<int[]> items, double multiple, LogAction logAction, boolean resRewardInfo) {
        BackpackStash stash = new BackpackStash(role);
        List<int[]> trueItem = new ArrayList<>(items.size());
        for (int[] ints : items) {
            if (ints.length < 2) {
                continue;
            }
            long cn = (long) (ints[1] * multiple);
            if (cn < 0) {
                continue;
            }
            trueItem.add(new int[]{ints[0], (int) cn});
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, ints[0], (int) cn);
        }
        stash.increase(trueItem);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else {
            if(resRewardInfo) {
                RewardInfoManager.getInstance().resRewardInfo(role, TurnBasedConst.BattleType.NORMAL, trueItem);
            }
        }

        return true;
    }

    public static boolean increase(Role role, int itemId, int itemCount, BackpackConst.Place place, LogAction logAction, int rewardType) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemId, itemCount, place);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else {
            if(rewardType > 0) {
                List<int[]> items = new ArrayList<>();
                int[] array = new int[] {itemId, itemCount};
                items.add(array);
                RewardInfoManager.getInstance().resRewardInfo(role, rewardType, items);
            }
        }
        return true;
    }

    public static boolean increase(Role role, int itemId, int itemCount, BackpackConst.Place place, LogAction logAction, boolean resRewardInfo) {
        return increase(role, itemId, itemCount, place, logAction, resRewardInfo ? TurnBasedConst.BattleType.NORMAL : 0);
    }

    public static boolean increase(Role role, int itemId, int itemCount, LogAction logAction, int rewardType) {
        return increase(role, itemId, itemCount, BackpackConst.Place.BACKPACK, logAction, rewardType);
    }

    /**
     * 仅仅判断道具是否满足，不扣减移除
     *
     * @param items  道具列表
     * @param places 背包
     * @return 是否足够
     */
    public static boolean checkDecrease(Role role, List<int[]> items, BackpackConst.Place... places) {
        return role.getBackpack().verifyItemCount(items, places);
    }

    /**
     * 仅仅判断道具是否满足，不扣减移除
     *
     * @param items  道具列表
     * @param places 背包
     * @return 是否足够
     */
    public static boolean checkDecrease(Role role, Map<Integer, Integer> items, BackpackConst.Place... places) {
        List<int[]> list = new ArrayList<>();
        items.forEach((k,v)-> {
            int[] temp = new int[]{k, v};
            list.add(temp);
        });
        return role.getBackpack().verifyItemCount(list, places);
    }

    /**
     * 仅仅判断道具是否满足，不扣减移除
     *
     * @param itemId  道具
     * @param itemCount  道具
     * @param places 背包
     * @return 是否足够
     */
    public static boolean checkDecrease(Role role, int itemId, int itemCount, BackpackConst.Place... places) {
        List<int[]> list = new ArrayList<>();
        int[] temp = new int[]{itemId, itemCount};
        list.add(temp);
        return role.getBackpack().verifyItemCount(list, places);
    }
}
