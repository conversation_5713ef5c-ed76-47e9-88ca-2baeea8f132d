package com.sh.game.common.entity.usr;

import com.sh.game.common.constant.DataType;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/8/19
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleEquipJuexing extends AbstractRoleEntity  {
    @Tag(1)
    private long id;

    @Tag(2)
    private Map<Integer, Integer> juexingLevel = new HashMap<>();

    @Tag(3)
    private Map<Integer, Integer> shenzhuLevel = new HashMap<>();

    @Override
    public int dataType() {
        return DataType.EQUIP_JUEXING;
    }
}
