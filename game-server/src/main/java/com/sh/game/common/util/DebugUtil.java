package com.sh.game.common.util;

import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.map.move.ReqRunMessage;
import com.sh.game.common.communication.msg.map.move.ReqWalkMessage;
import com.sh.game.common.communication.msg.map.move.ResRunMessage;
import com.sh.game.common.communication.msg.map.move.ResWalkMessage;
import com.sh.game.common.communication.msg.system.user.ReqHeartMessage;
import com.sh.game.common.communication.msg.system.user.ResHeartMessage;
import com.sh.net.Message;
import com.sh.server.Session;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/9/20 15:02
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 * <AUTHOR>
 */
@Slf4j
public class DebugUtil {

    /**
     * 不需要日志的消息
     */
    private static Set<String> notLogMessage = new HashSet<>();

    static {
        addNotLogMsg(ReqHeartMessage.class);
        addNotLogMsg(ResHeartMessage.class);
        addNotLogMsg(ReqWalkMessage.class);
        addNotLogMsg(ResWalkMessage.class);
        addNotLogMsg(ReqRunMessage.class);
        addNotLogMsg(ResRunMessage.class);
    }

    /**
     * 添加不需要日志消息
     *
     * @param clz
     */
    private final static void addNotLogMsg(Class<?> clz) {
        notLogMessage.add(clz.getName());
    }

    /**
     * 添加日志输出
     *
     * @param session
     * @param message
     * @param isIn
     */
    public final static void logMessage(Session session, Message message, boolean isIn) {
        if (GameContext.isDebug()) {
            String msgName = message.getClass().getName();
            if (notLogMessage.contains(msgName)) {
                return;
            }
            StringBuffer buffer = new StringBuffer();
            if (isIn) {
                buffer.append("MsgIn[");
            } else {
                buffer.append("MsgOut[");
            }
            buffer.append(session.getChannel().remoteAddress().toString());
            buffer.append("]***[(").append(message.getId()).append(")").append(msgName);
            buffer.append("]").append(TimeUtil.getNowOfMills());
            log.warn(buffer.toString());
        }
    }
}
