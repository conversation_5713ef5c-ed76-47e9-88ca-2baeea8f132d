package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.xiulio.entity.XiuLuoZhanBao;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15 14:23
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleXiuLuo extends AbstractRoleEntity {

    @Tag(1)
    private long id;

    @Tag(2)
    private List<XiuLuoZhanBao> zhanBao = new ArrayList<>();

    /**
     * 匹配次数
     */
    @Tag(3)
    private int matchCount;

    /**
     * 购买匹配次数
     */
    @Tag(4)
    private int buyCount;

    /**
     * 段位积分
     */
    @Tag(5)
    private int jiFen;

    /**
     * 修罗段位配置id
     */
    @Tag(6)
    private int duanWeiCfgId = 100001;

    /**
     * 本赛季杀人数
     */
    @Tag(7)
    private int killCount;

    /**
     * 本赛玩法积分数
     */
    @Tag(8)
    private int gameJiFen;

    /**
     * 胜利场次
     */
    @Tag(9)
    private int lianSheng;

    /**
     * 已领取的奖励配置
     */
    @Tag(10)
    private List<Integer> xiuLuoReward = new ArrayList<>();

    /**
     * 下次结算时间
     */
    @Tag(11)
    private long jieSuanTime;

    /**
     * 累计段位积分
     */
    @Tag(12)
    private int totalJiFen;

    /**
     * 段位积分更新时间
     */
    @Tag(13)
    private int jiFenUpDateTime;

    /**
     * 总匹配次数
     */
    @Tag(14)
    private int totalMatch;

    @Tag(15)
    private int mapCfgId;

    @Tag(16)
    private long mapId;

    @Tag(17)
    private int mapEndTime;

    /**
     * 本次结算增加的积分
     */
    @Tag(18)
    private int addJifen;

    /**
     * 每日胜场
     */
    @Tag(19)
    private int dailyShengLi;

    /**
     * 终身匹配次数
     */
    @Tag(20)
    private int lifeMatchCount;

    /**
     * 终身击杀数
     */
    @Tag(21)
    private int lifeKillCount;

    /**
     * 终身夺旗数量
     */
    @Tag(22)
    private int lifeDuoQiCount;

    /**
     * 终身mvp数
     */
    @Tag(23)
    private int lifeMvpCount;

    /**
     * 终身胜利场次
     */
    @Tag(24)
    private int lifeShengLiCount;

    /**
     * 获取到排行榜奖励时间
     */
    @Tag(25)
    private int rankRewardGetTime;

    @Tag(26)
    private int chengJiuCid = 100001;

    /**
     * 已领取奖励的成就任务
     */
    @Tag(27)
    private List<Integer> taskReward = new ArrayList<>();

    /**
     * 已领取的成就奖励
     */
    @Tag(28)
    private List<Integer> chengJiuReward = new ArrayList<>();

    /**
     * 修罗成就点
     */
    @Tag(29)
    private int chengJiuPoint;

    @Tag(30)
    private int lifeDieCount;

    /**
     * 大段位
     */
    @Tag(31)
    private int bigDuanWei = 1;

    /**
     * 是否在战斗中
     */
    @Tag(32)
    private boolean inFight;
}
