package com.sh.game.common.entity.sys;

import com.sh.game.common.entity.SysDataType;
import com.sh.game.system.chat.entity.ChatCondition;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/5/6 16:23
 */
@Getter
@Setter
public class ChatConditionData extends AbstractSysData{

    /**
     * 聊天条件表
     * key 聊天频道
     */
    @Tag(1)
    private Map<Integer, ChatCondition> conditionMap = new ConcurrentHashMap<>();


    /**
     * 聊天总开关，1 全部频道不能聊天
     */
    @Tag(2)
    private int closeChat;

    /**
     * 是否禁止修改角色昵称
     */
    @Tag(3)
    private boolean changeName;


    /**
     * 创建行会自定义名称开关
     */
    @Tag(4)
    private boolean customNameEnable;

    @Override
    public void setId(long id) {

    }

    @Override
    public long getId() {
        return SysDataType.CHAT_CONDITION;
    }
}
