package com.sh.game.server;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.processor.CommonProcessor;
import com.sh.game.processor.AuthProcessor;
import com.sh.game.processor.RoleProcessor;
import com.sh.game.processor.SocialProcessor;

/**
 * 游戏服务器命令路由
 */
public class LogicCommandRouter extends  CommandRouter  {


    public void register() {
        //登录和下线
        this.registerProcessor(ProcessorId.SERVER_AUTH, new AuthProcessor());
        //场景队列
        this.registerProcessor(ProcessorId.SERVER_PLAYER, new RoleProcessor());
        //帮会队列
        this.registerProcessor(ProcessorId.SERVER_SOCIAL, new SocialProcessor());
        //内部逻辑公用队列
        this.registerProcessor(ProcessorId.SERVER_COMMON, new CommonProcessor());
    }


}
