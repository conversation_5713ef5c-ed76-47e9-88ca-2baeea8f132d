package com.sh.game.server;

import com.sh.game.common.communication.msg.map.ReqSummonCrossBossMessage;
import com.sh.game.common.communication.msg.map.play.ReqBuyExploreMessage;
import com.sh.game.common.communication.msg.map.play.ReqMapBossHpMessage;
import com.sh.game.common.communication.msg.map.play.ReqUseFightItemInfosMessage;
import com.sh.game.common.communication.msg.system.activity.*;
import com.sh.game.common.communication.msg.system.appearance.ReqAppearanceUnWearMessage;
import com.sh.game.common.communication.msg.system.appearance.ReqAppearanceUnlockMessage;
import com.sh.game.common.communication.msg.system.appearance.ReqAppearanceWearMessage;
import com.sh.game.common.communication.msg.system.aptitude.ReqAptitudeChangeMessage;
import com.sh.game.common.communication.msg.system.aptitude.ReqAptitudeInfoMessage;
import com.sh.game.common.communication.msg.system.backpack.*;
import com.sh.game.common.communication.msg.system.bore.ReqBoreMessage;
import com.sh.game.common.communication.msg.system.bore.ReqComposeStoneMessage;
import com.sh.game.common.communication.msg.system.bore.ReqRoleBoreInfoMessage;
import com.sh.game.common.communication.msg.system.bossOffer.ReqBossOfferAcquireMessage;
import com.sh.game.common.communication.msg.system.bossOffer.ReqBossOfferInfoMessage;
import com.sh.game.common.communication.msg.system.bounty.ReqHellBountyInfoMessage;
import com.sh.game.common.communication.msg.system.bounty.ReqHellBountyRewardMessage;
import com.sh.game.common.communication.msg.system.camp.ReqCampMissionMessage;
import com.sh.game.common.communication.msg.system.camp.ReqCampRewardMessage;
import com.sh.game.common.communication.msg.system.camp.ReqGetCampRewardMessage;
import com.sh.game.common.communication.msg.system.chat.ReqChatHistoryMessage;
import com.sh.game.common.communication.msg.system.chat.ReqChatMessage;
import com.sh.game.common.communication.msg.system.chat.ReqGMCommandMessage;
import com.sh.game.common.communication.msg.system.climbTower.*;
import com.sh.game.common.communication.msg.system.compound.ReqBagCompoundMessage;
import com.sh.game.common.communication.msg.system.compound.ReqCompoundSpecialItemInfosMessage;
import com.sh.game.common.communication.msg.system.cross.ReqCrossInfoMessage;
import com.sh.game.common.communication.msg.system.cross.ReqCrossServersMessage;
import com.sh.game.common.communication.msg.system.daily.*;
import com.sh.game.common.communication.msg.system.datasync.ReqDataCodeCompareMessage;
import com.sh.game.common.communication.msg.system.datasync.ReqDataCodeMatchMessage;
import com.sh.game.common.communication.msg.system.equip.*;
import com.sh.game.common.communication.msg.system.function.ReqFunctionInfoMessage;
import com.sh.game.common.communication.msg.system.function.ReqFunctionMessage;
import com.sh.game.common.communication.msg.system.fusion.ReqFusionBuyMessage;
import com.sh.game.common.communication.msg.system.fusion.ReqFusionSellMessage;
import com.sh.game.common.communication.msg.system.fusion.ReqFusionViewMessage;
import com.sh.game.common.communication.msg.system.identify.ReqIdentifyEquipMessage;
import com.sh.game.common.communication.msg.system.identify.ReqIdentifySaveMessage;
import com.sh.game.common.communication.msg.system.mail.ReqDeleteMailMessage;
import com.sh.game.common.communication.msg.system.mail.ReqGetMailListMessage;
import com.sh.game.common.communication.msg.system.mail.ReqReadMailMessage;
import com.sh.game.common.communication.msg.system.mail.ReqTakeMailMessage;
import com.sh.game.common.communication.msg.system.material.ReqGetMaterialRewardMessage;
import com.sh.game.common.communication.msg.system.material.ReqOpenMaterialInfoMessage;
import com.sh.game.common.communication.msg.system.mix.ReqMixBreakMessage;
import com.sh.game.common.communication.msg.system.mix.ReqMixInfoMessage;
import com.sh.game.common.communication.msg.system.mix.ReqMixMergeMessage;
import com.sh.game.common.communication.msg.system.mounts.*;
import com.sh.game.common.communication.msg.system.recharge.ReqGetRechargeInfoMessage;
import com.sh.game.common.communication.msg.system.recharge.ReqRechargeOrderIdMessage;
import com.sh.game.common.communication.msg.system.recycle.ReqRecycleMessage;
import com.sh.game.common.communication.msg.system.role.ReqCreateHeroMessage;
import com.sh.game.common.communication.msg.system.role.ReqShenBingExtraDailyExpMessage;
import com.sh.game.common.communication.msg.system.shenBingHuiYuan.*;
import com.sh.game.common.communication.msg.system.skill.ReqSkillInfoMessage;
import com.sh.game.common.communication.msg.system.skill.ReqUpSkillMessage;
import com.sh.game.common.communication.msg.system.social.ReqFriendApplyOpMessage;
import com.sh.game.common.communication.msg.system.social.ReqSocialAddMessage;
import com.sh.game.common.communication.msg.system.social.ReqSocialDeleteMessage;
import com.sh.game.common.communication.msg.system.social.ReqSocialOpenPanelMessage;
import com.sh.game.common.communication.msg.system.store.ReqBuyStoreGoodsMessage;
import com.sh.game.common.communication.msg.system.store.ReqSellItemsMessage;
import com.sh.game.common.communication.msg.system.store.ReqStoreSoldStatusMessage;
import com.sh.game.common.communication.msg.system.suitExtend.ReqSuitAttrExtendMessage;
import com.sh.game.common.communication.msg.system.suitExtend.ReqSuitExtendUpgradeMessage;
import com.sh.game.common.communication.msg.system.summon.ReqSummonAnswerMessage;
import com.sh.game.common.communication.msg.system.teleport.ReqTeleportMessage;
import com.sh.game.common.communication.msg.system.tianshu.ReqExtractMessage;
import com.sh.game.common.communication.msg.system.tianshu.ReqTianShuExpUpMessage;
import com.sh.game.common.communication.msg.system.tianshu.ReqTianShuInfoMessage;
import com.sh.game.common.communication.msg.system.tower.ReqTowerMessage;
import com.sh.game.common.communication.msg.system.tower.ReqTowerRankMessage;
import com.sh.game.common.communication.msg.system.union.*;
import com.sh.game.common.communication.msg.system.user.*;
import com.sh.game.common.communication.msg.system.using.ReqUsingItemMessage;
import com.sh.game.common.communication.msg.system.welfare.*;
import com.sh.game.common.communication.msg.system.xialv.ReqXiaLvInfoMessage;
import com.sh.game.common.communication.msg.system.xialv.ReqXiaLvLevelUpMessage;
import com.sh.game.common.communication.msg.system.zhanyi.*;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.play.msg.ReqBuyExploreHandler;
import com.sh.game.map.play.msg.ReqMapBossHpHandler;
import com.sh.game.map.play.msg.ReqUseFightItemInfosHandler;
import com.sh.game.system.activity.msg.*;
import com.sh.game.system.advance.equipInherit.msg.ReqInheritEquipHandler;
import com.sh.game.system.advance.mix.msg.ReqMixBreakHandler;
import com.sh.game.system.advance.mix.msg.ReqMixInfoHandler;
import com.sh.game.system.advance.mix.msg.ReqMixMergeHandler;
import com.sh.game.system.appearance.msg.ReqAppearanceUnWearHandler;
import com.sh.game.system.appearance.msg.ReqAppearanceUnlockHandler;
import com.sh.game.system.appearance.msg.ReqAppearanceWearHandler;
import com.sh.game.system.aptitude.msg.ReqAptitudeChangeHandler;
import com.sh.game.system.aptitude.msg.ReqAptitudeInfoHandler;
import com.sh.game.system.backpack.msg.*;
import com.sh.game.system.bore.msg.ReqBoreHandler;
import com.sh.game.system.bore.msg.ReqComposeStoneHandler;
import com.sh.game.system.bore.msg.ReqRoleBoreInfoHandler;
import com.sh.game.system.bossOffer.msg.ReqBossOfferAcquireHandler;
import com.sh.game.system.bossOffer.msg.ReqBossOfferInfoHandler;
import com.sh.game.system.bounty.msg.ReqHellBountyInfoHandler;
import com.sh.game.system.bounty.msg.ReqHellBountyRewardHandler;
import com.sh.game.system.camp.msg.ReqCampMissionHandler;
import com.sh.game.system.camp.msg.ReqCampRewardHandler;
import com.sh.game.system.camp.msg.ReqGetCampRewardHandler;
import com.sh.game.system.chat.msg.ReqChatHandler;
import com.sh.game.system.chat.msg.ReqChatHistoryHandler;
import com.sh.game.system.chat.msg.ReqGMCommandHandler;
import com.sh.game.system.climbTower.msg.*;
import com.sh.game.system.compound.msg.ReqBagCompoundHandler;
import com.sh.game.system.compound.msg.ReqCompoundSpecialItemInfosHandler;
import com.sh.game.system.cross.msg.ReqCrossInfoHandler;
import com.sh.game.system.cross.msg.ReqCrossServersHandler;
import com.sh.game.system.cross.msg.ReqSummonCrossBossHandler;
import com.sh.game.system.daily.msg.*;
import com.sh.game.system.datasync.msg.ReqDataCodeCompareHandler;
import com.sh.game.system.datasync.msg.ReqDataCodeMatchHandler;
import com.sh.game.system.equip.msg.ReqEquipRepairHandler;
import com.sh.game.system.function.msg.ReqFunctionHandler;
import com.sh.game.system.function.msg.ReqFunctionInfoHandler;
import com.sh.game.system.fusion.msg.ReqFusionBuyHandler;
import com.sh.game.system.fusion.msg.ReqFusionSellHandler;
import com.sh.game.system.fusion.msg.ReqFusionViewHandler;
import com.sh.game.system.identify.msg.ReqIdentifyEquipHandler;
import com.sh.game.system.identify.msg.ReqIdentifySaveHandler;
import com.sh.game.system.mail.msg.ReqDeleteMailHandler;
import com.sh.game.system.mail.msg.ReqGetMailListHandler;
import com.sh.game.system.mail.msg.ReqReadMailHandler;
import com.sh.game.system.mail.msg.ReqTakeMailHandler;
import com.sh.game.system.material.msg.ReqGetMaterialRewardHandler;
import com.sh.game.system.material.msg.ReqOpenMaterialInfoHandler;
import com.sh.game.system.recharge.msg.ReqGetRechargeInfoHandler;
import com.sh.game.system.recharge.msg.ReqRechargeOrderHandler;
import com.sh.game.system.recycle.msg.ReqRecycleHandler;
import com.sh.game.system.redpack.msg.ReqRedPackGetHandler;
import com.sh.game.system.redpack.msg.ReqRedPackPanelInfoHandler;
import com.sh.game.system.redpack.msg.ReqRedPackSendHandler;
import com.sh.game.system.role.msg.ReqCreateHeroHandler;
import com.sh.game.system.role.msg.ReqShenBingExtraDailyExpHandler;
import com.sh.game.system.shenBingHuiYuan.msg.*;
import com.sh.game.system.skill.msg.ReqSkillInfoHandler;
import com.sh.game.system.skill.msg.ReqUpSkillHandler;
import com.sh.game.system.social.msg.ReqFriendApplyOpHandler;
import com.sh.game.system.social.msg.ReqSocialAddHandler;
import com.sh.game.system.social.msg.ReqSocialDeleteHandler;
import com.sh.game.system.social.msg.ReqSocialOpenPanelHandler;
import com.sh.game.system.store.msg.ReqBuyStoreGoodsHandler;
import com.sh.game.system.store.msg.ReqSellItemsHandler;
import com.sh.game.system.store.msg.ReqStoreSoldStatusHandler;
import com.sh.game.system.suitExtend.msg.ReqSuitAttrExtendHandler;
import com.sh.game.system.suitExtend.msg.ReqSuitExtendUpgradeHandler;
import com.sh.game.system.summon.msg.ReqSummonAnswerHandler;
import com.sh.game.system.teleport.msg.ReqTeleportHandler;
import com.sh.game.system.tianshu.msg.ReqExtractHandler;
import com.sh.game.system.tianshu.msg.ReqTianShuExpUpHandler;
import com.sh.game.system.tianshu.msg.ReqTianShuInfoHandler;
import com.sh.game.system.tower.msg.ReqTowerHandler;
import com.sh.game.system.tower.msg.ReqTowerRankHandler;
import com.sh.game.system.union.msg.*;
import com.sh.game.system.user.msg.*;
import com.sh.game.system.using.msg.ReqUsingItemHandler;
import com.sh.game.system.welfare.msg.*;
import com.sh.game.system.xialv.msg.ReqXiaLvInfoHandler;
import com.sh.game.system.xialv.msg.ReqXiaLvLevelUpHandler;
import com.sh.server.MessageHandler;
import com.sh.server.AbstractMessage;

/**
 * 游戏服的消息池
 *
 * <AUTHOR>
 */
public class LogicHandlerPool extends AbstractHandlerPool {

    /**
     * 注册消息
     * 如果新加模块 则添加一个新方法进来
     */
    public LogicHandlerPool() {


        registerUser(); //1
        registerActivity(); //4
        registerChat(); //6
        registerRole(); //8
        registerSkill(); //9
        registerBag(); //10
        registerEquipment();//13
        registerStore();// 16
        registerUnion();// 23
        registerBattle(); //25
        registerSign(); // 27
        registerCompound(); //31

        registerMail(); //36
        registerRecharge();//39

        registerPlay(); //73

        registerCross();//84
        registerSocial();//100
        registerTrade(); // 104

        registerDataSync(); //172
        registerTeleport(); // 174

        registerDaily(); // 175
        registerEnergy(); // 178

        registerMaterial(); // 179
        registerFusion(); // 180

        registerIdentify(); // 183
        registerRecycle(); // 184
        registerBounty();//185
        registerMix(); // 186
        registerTower(); // 189

        registerSummon(); // 194
        registerBore(); // 196

        registerCamp();//197
        registerAppearance();//200
        registerFunction();//202
        registerBossOffer(); //204
        registerXiaLv(); //302

        registerClimbTower(); //309
        registerTianShu();
    }

    private void registerXiaLv() {
        register(new ReqXiaLvInfoMessage(), ReqXiaLvInfoHandler.class);
        register(new ReqXiaLvLevelUpMessage(), ReqXiaLvLevelUpHandler.class);
    }

    private void registerBossOffer() {
        register(new ReqBossOfferInfoMessage(), ReqBossOfferInfoHandler.class);
        register(new ReqBossOfferAcquireMessage(), ReqBossOfferAcquireHandler.class);
    }

    private void registerFunction() {
        register(new ReqFunctionMessage(), ReqFunctionHandler.class);
        register(new ReqFunctionInfoMessage(), ReqFunctionInfoHandler.class);
    }

    private void registerCamp() {
        register(new ReqCampMissionMessage(), ReqCampMissionHandler.class);
        register(new ReqCampRewardMessage(), ReqCampRewardHandler.class);
        register(new ReqGetCampRewardMessage(), ReqGetCampRewardHandler.class);
    }

    private void registerBore() {
        register(new ReqBoreMessage(), ReqBoreHandler.class);
        register(new ReqComposeStoneMessage(), ReqComposeStoneHandler.class);
        register(new ReqRoleBoreInfoMessage(), ReqRoleBoreInfoHandler.class);
    }

    private void registerTower() {
        register(new ReqTowerMessage(), ReqTowerHandler.class);
        register(new ReqTowerRankMessage(), ReqTowerRankHandler.class);
    }

    private void registerBounty() {
        register(new ReqHellBountyInfoMessage(), ReqHellBountyInfoHandler.class);
        register(new ReqHellBountyRewardMessage(), ReqHellBountyRewardHandler.class);
    }

    private void registerMix() {
        register(new ReqMixInfoMessage(), ReqMixInfoHandler.class);
        register(new ReqMixMergeMessage(), ReqMixMergeHandler.class);
        register(new ReqMixBreakMessage(), ReqMixBreakHandler.class);
    }

    private void registerMaterial() {
        register(new ReqGetMaterialRewardMessage(), ReqGetMaterialRewardHandler.class);
        register(new ReqOpenMaterialInfoMessage(), ReqOpenMaterialInfoHandler.class);
    }

    private void registerActivity() {
        register(new ReqAvailableActivitiesMessage(), ReqAvailableActivitiesHandler.class);

        // 首充
        register(new ReqActivityFirstPurchaseInfoMessage(), ReqActivityFirstPurchaseInfoHandler.class);
        register(new ReqActivityFirstPurchaseAcquireMessage(), ReqActivityFirstPurchaseAcquireHandler.class);

        // 直购礼包
        register(new ReqActivityPurchasePresentInfoMessage(), ReqActivityPurchasePresentInfoHandler.class);
        register(new ReqActivityPurchasePresentAcquireDailyMessage(), ReqActivityPurchasePresentAcquireDailyHandler.class);
        register(new ReqActivityPurchasePresentAcquireDaysMessage(), ReqActivityPurchasePresentAcquireDaysHandler.class);
        register(new ReqActivityPurchasePresentAcquireRechargeMessage(), ReqActivityPurchasePresentAcquireRechargeHandler.class);

        // 等级冲榜
        register(new ReqActivityRankingLevelMessage(), ReqActivityRankingLevelHandler.class, ProcessorId.SERVER_COMMON);
        // 攻击冲榜
        register(new ReqActivityRankingAtkMessage(), ReqActivityRankingAtkHandler.class, ProcessorId.SERVER_COMMON);
        // 累充排行
        register(new ReqActivityRankingRechargeMessage(), ReqActivityRankingRechargeHandler.class, ProcessorId.SERVER_COMMON);
        register(new ReqActivityRankingRechargeTopMessage(), ReqActivityRankingRechargeTopHandler.class, ProcessorId.SERVER_COMMON);
        // 消费排行
        register(new ReqActivityRankingConsumeMessage(), ReqActivityRankingConsumeHandler.class, ProcessorId.SERVER_COMMON);
        register(new ReqActivityRankingConsumeTopMessage(), ReqActivityRankingConsumeTopHandler.class, ProcessorId.SERVER_COMMON);

        // 装备回收
        register(new ReqActivityEquipRecycleInfoMessage(), ReqActivityEquipRecycleInfoHandler.class);
        register(new ReqActivityEquipRecycleAcquireMessage(), ReqActivityEquipRecycleAcquireHandler.class);

        // 限时特惠
        register(new ReqActivitySaleBagInfoMessage(), ReqActivitySaleBagInfoHandler.class);
        register(new ReqActivitySaleBagAcquireMessage(), ReqActivitySaleBagAcquireHandler.class);

        // 等级竞技
        register(new ReqActivityCompeteLevelInfoMessage(), ReqActivityCompeteLevelInfoHandler.class);
        register(new ReqActivityCompeteLevelAcquireMessage(), ReqActivityCompeteLevelAcquireHandler.class);
        // 宝石竞技
        register(new ReqActivityCompeteAtkInfoMessage(), ReqActivityCompeteAtkInfoHandler.class);
        register(new ReqActivityCompeteAtkAcquireMessage(), ReqActivityCompeteAtkAcquireHandler.class);

        //寻宝
        register(new ReqActivityTreasureMonsterInfoMessage(), ReqActivityTreasureMonsterInfoHandler.class);
        register(new ReqActivityTreasureMonsterAcquireMessage(), ReqActivityTreasureMonsterAcquireHandler.class);
        register(new ReqActivityTreasureMonsterCallMessage(), ReqActivityTreasureMonsterCallHandler.class);

        // 累计充值
        register(new ReqActivityRechargeRebateInfoMessage(), ReqActivityRechargeRebateInfoHandler.class);
        register(new ReqActivityRechargeRebateAcquireMessage(), ReqActivityRechargeRebateAcquireHandler.class);

        // 充值抽奖
        register(new ReqActivityRechargeDrawInfoMessage(), ReqActivityRechargeDrawInfoHandler.class);
        register(new ReqActivityRechargeDrawAcquireMessage(), ReqActivityRechargeDrawAcquireHandler.class);

        // 装备首暴
        register(new ReqFirstGetRewardMessage(), ReqFirstGetRewardHandler.class);
        register(new ReqFirstGetMessage(), ReqFirstGetHandler.class);

        // 专属装备首爆
        register(new ReqZhuanShuMessage(), ReqZhuanShuHandler.class);
        register(new ReqZhuanShuRewardMessage(), ReqZhuanShuRewardHandler.class);
        register(new ReqZhuanShuBoxRewardMessage(), ReqZhuanShuBoxRewardHandler.class);

        // 宝石开启
        register(new ReqActivityGemGoalInfoMessage(), ReqActivityGemGoalInfoHandler.class);
        register(new ReqActivityGemGoalAcquireMessage(), ReqActivityGemGoalAcquireHandler.class);

        // 周末 每日充值
        register(new ReqActivityRechargeDailyInfoMessage(), ReqActivityRechargeDailyInfoHandler.class);
        register(new ReqActivityRechargeDailyAcquireMessage(), ReqActivityRechargeDailyAcquireHandler.class);

        // 周末 特惠礼包
        register(new ReqActivityDiscountGiftInfoMessage(), ReqActivityDiscountGiftInfoHandler.class);
        register(new ReqActivityDiscountGiftAcquireDailyMessage(), ReqActivityDiscountGiftAcquireDailyHandler.class);

        // 充值点
        register(new ReqActivityRechargePointInfoMessage(), ReqActivityRechargePointInfoHandler.class);

        // 翻牌
        register(new ReqActivityLuckDrawInfoMessage(), ReqActivityLuckDrawInfoHandler.class);
        register(new ReqActivityLuckDrawStartMessage(), ReqActivityLuckDrawStartHandler.class);
        register(new ReqActivityResetLuckDrawRecordMessage(), ReqActivityResetLuckDrawRecordHandler.class);

        // 礼包活动(giftpack)
        register(new ReqGiftPackInfoMessage(), ReqGiftPackInfoHandler.class);
        register(new ReqBuyGiftPackMessage(), ReqBuyGiftPackHandler.class);

        // 封魔战意
        register(new ReqFengMoBuyZhanYiMessage(), ReqFengMoBuyZhanYiHandler.class);
        register(new ReqFengMoGearsRewardMessage(), ReqFengMoGearsRewardHandler.class);
        register(new ReqFengMoRankInfoMessage(), ReqFengMoRankInfoHandler.class);
        register(new ReqFengMoSelfInfoMessage(), ReqFengMoSelfInfoHandler.class);
    }

    private void registerBattle() {
        //register(new ReqMonsterDieMessage(), ReqMonsterDieMessage.class);
    }

    public void registerSign() {
        register(new ReqSignRewardInfoMessage(), ReqSignRewardInfoHandler.class);
        register(new ReqGetSignRewardMessage(), ReqGetSignRewardHandler.class);
        register(new ReqDrawSummaryDayRewardMessage(), ReqDrawSummaryDayRewardHandler.class);
        register(new ReqReceiveOnlineRewardMessage(), ReqReceiveOnlineRewardHandler.class);
        register(new ReqLoginPushOnlineRewardMessage(), ReqLoginPushOnlineRewardHandler.class);
    }

    /**
     * group 1
     * 注册用户相关请求
     */
    private void registerUser() {
        register(new ReqHeartMessage(), ReqHeartHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqLoginMessage(), ReqLoginHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqCreateRoleMessage(), ReqCreateRoleHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqRandomNameMessage(), ReqRandomNameHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqDeleteRoleMessage(), ReqDeleteRoleHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqEnterGameMessage(), ReqEnterGameHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqQuitGameMessage(), ReqQuitGameHandler.class, ProcessorId.SERVER_AUTH);
        register(new ReqChangeRoleNameMessage(), ReqChangeRoleNameHandler.class, ProcessorId.SERVER_PLAYER);
        register(new ReqServerEnvirMessage(), ReqServerEnvirHandler.class, ProcessorId.SERVER_AUTH);
    }

    /**
     * group 6
     * 注册聊天相关请求
     */
    private void registerChat() {
        register(new ReqChatMessage(), ReqChatHandler.class);
        register(new ReqChatHistoryMessage(), ReqChatHistoryHandler.class);
        register(new ReqGMCommandMessage(), ReqGMCommandHandler.class);
    }

    /**
     * group 8
     * 注册角色相关请求
     */
    private void registerRole() {
        register(new ReqCreateHeroMessage(), ReqCreateHeroHandler.class);
        register(new ReqShenBingExtraDailyExpMessage(), ReqShenBingExtraDailyExpHandler.class);
    }

    /**
     * group 9
     * 注册技能相关请求
     */
    private void registerSkill() {
        register(new ReqSkillInfoMessage(), ReqSkillInfoHandler.class);
        register(new ReqUpSkillMessage(), ReqUpSkillHandler.class);
    }

    /**
     * group 10
     * 注册背包相关请求
     */
    private void registerBag() {
        register(new ReqBackpackCoinMessage(), ReqBackpackCoinHandler.class);
        register(new ReqBackpackGridMessage(), ReqBackpackGridHandler.class);
        register(new ReqBackpackUnlockMessage(), ReqBackpackUnlockHandler.class);
        register(new ReqBackpackSortMessage(), ReqBackpackSortHandler.class);
        register(new ReqBackpackMergeMessage(), ReqBackpackMergeHandler.class);
        register(new ReqBackpackDiscardMessage(), ReqBackpackDiscardHandler.class);
        register(new ReqBackpackFetchTreasureMessage(), ReqBackpackFetchTreasureHandler.class);

        register(new ReqUsingItemMessage(), ReqUsingItemHandler.class);
        register(new ReqExchangeExpMessage(), ReqExchangeExpHandler.class);
        register(new ReqExchangeExpInfoMessage(), ReqExchangeExpInfoHandler.class);
    }

    /**
     * group 13
     * 注册装备相关请求
     */
    private void registerEquipment() {
        register(new ReqEquipRepairMessage(), ReqEquipRepairHandler.class);
        register(new ReqInheritEquipMessage(), ReqInheritEquipHandler.class);
    }

    /**
     * group 16
     * 注册商城相关请求
     */
    private void registerStore() {
//        register(new ReqQuicklyBuyMessage(), ReqQuicklyBuyHandler.class);
//        register(new ReqOpenStoreByTypeMessage(), ReqOpenStoreByTypeHandler.class);
//        register(new ReqMaxBuyCountMessage(), ReqMaxBuyCountHandler.class);
//        register(new ReqMaualFreshMessage(), ReqMaualFreshHandler.class);
//        register(new ReqBargainPricesMessage(), ReqBargainPricesHandler.class);
//        register(new ReqBargainMessage(), ReqBargainHandler.class);

        register(new ReqStoreSoldStatusMessage(), ReqStoreSoldStatusHandler.class);
        register(new ReqBuyStoreGoodsMessage(), ReqBuyStoreGoodsHandler.class);
        register(new ReqSellItemsMessage(), ReqSellItemsHandler.class);
    }

    /**
     * 23 帮会
     */
    private void registerUnion() {
        register(new ReqGetAllUnionInfoMessage(), ReqGetAllUnionInfoHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqGetPlayerUnionInfoMessage(), ReqGetPlayerUnionInfoHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqCreateUnionMessage(), ReqCreateUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqDissolveUnionMessage(), ReqDissolveUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqChangeUnionSettingsMessage(), ReqChangeUnionSettingsHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqChangeAnnouncementMessage(), ReqChangeAnnouncementHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqInviteForEnterUnionMessage(), ReqInviteForEnterUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqAgreeUnionInviteMessage(), ReqAgreeUnionInviteHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqApplyForEnterUnionMessage(), ReqApplyForEnterUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqOneKeyApplyForUnionMessage(), ReqOneKeyApplyForUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqGetApplyListInfoMessage(), ReqGetApplyListInfoHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqCheckApplyListMessage(), ReqCheckApplyListHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqChangePositionMessage(), ReqChangePositionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqKickOutMemberMessage(), ReqKickOutMemberHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqQuitUnionMessage(), ReqQuitUnionHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionEventsMessage(), ReqUnionEventsHandler.class, ProcessorId.SERVER_SOCIAL);

        // 行会捐献 升级
        register(new ReqUnionDonateMessage(), ReqUnionDonateHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionIntensifyMessage(), ReqUnionIntensifyHandler.class, ProcessorId.SERVER_SOCIAL);

        //红包
        register(new ReqRedPackPanelInfoMessage(), ReqRedPackPanelInfoHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqRedPackSendMessage(), ReqRedPackSendHandler.class);
        register(new ReqRedPackGetMessage(), ReqRedPackGetHandler.class, ProcessorId.SERVER_SOCIAL);

        // 结盟
        register(new ReqUnionAllyMessage(), ReqUnionAllyHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionAlignMessage(), ReqUnionAlignHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionAlignAcceptMessage(), ReqUnionAlignAcceptHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionAlignBreakMessage(), ReqUnionAlignBreakHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqUnionEnemyMessage(), ReqUnionEnemyHandler.class, ProcessorId.SERVER_SOCIAL);
    }

    /**
     * 31 合成
     */
    private void registerCompound() {
        register(new ReqBagCompoundMessage(), ReqBagCompoundHandler.class);
        register(new ReqCompoundSpecialItemInfosMessage(), ReqCompoundSpecialItemInfosHandler.class);
    }

    /**
     * 邮件
     * 36
     */
    private void registerMail() {
        register(new ReqGetMailListMessage(), ReqGetMailListHandler.class, ProcessorId.SERVER_PLAYER);
        register(new ReqReadMailMessage(), ReqReadMailHandler.class, ProcessorId.SERVER_PLAYER);
        register(new ReqTakeMailMessage(), ReqTakeMailHandler.class, ProcessorId.SERVER_PLAYER);
        register(new ReqDeleteMailMessage(), ReqDeleteMailHandler.class, ProcessorId.SERVER_PLAYER);

    }

    /**
     * 充值
     * 39
     */
    private void registerRecharge() {
        register(new ReqGetRechargeInfoMessage(), ReqGetRechargeInfoHandler.class,ProcessorId.SERVER_PLAYER);
        register(new ReqRechargeOrderIdMessage(), ReqRechargeOrderHandler.class,ProcessorId.SERVER_PLAYER);
    }

    public void registerPlay() {
        register(new ReqMapBossHpMessage(), ReqMapBossHpHandler.class, ProcessorId.SERVER_PLAYER);

        register(new ReqBuyExploreMessage(), ReqBuyExploreHandler.class, ProcessorId.SERVER_PLAYER);

        register(new ReqUseFightItemInfosMessage(), ReqUseFightItemInfosHandler.class, ProcessorId.SERVER_PLAYER);

    }

    public void registerCross() {
        register(new ReqCrossInfoMessage(), ReqCrossInfoHandler.class);
        register(new ReqCrossServersMessage(), ReqCrossServersHandler.class);
        register(new ReqSummonCrossBossMessage(), ReqSummonCrossBossHandler.class);
    }

    public void registerTrade() {
//        register(new ReqTradeAppendMessage(), ReqTradeAppendHandler.class);
//        register(new ReqTradeRemoveMessage(), ReqTradeRemoveHandler.class);
    }

    /**
     * 100
     * 社交
     */
    public void registerSocial() {
        register(new ReqSocialOpenPanelMessage(), ReqSocialOpenPanelHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqSocialAddMessage(), ReqSocialAddHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqSocialDeleteMessage(), ReqSocialDeleteHandler.class, ProcessorId.SERVER_SOCIAL);
        register(new ReqFriendApplyOpMessage(), ReqFriendApplyOpHandler.class, ProcessorId.SERVER_SOCIAL);
    }

    protected <T extends AbstractMessage> void register(T message, Class<? extends MessageHandler<T>> handlerClazz) {
        super.register(message, handlerClazz, ProcessorId.SERVER_PLAYER);
    }


    /**
     *
     */
    public void registerDataSync() {
        register(new ReqDataCodeCompareMessage(), ReqDataCodeCompareHandler.class);
        register(new ReqDataCodeMatchMessage(), ReqDataCodeMatchHandler.class);
    }

    /**
     *
     */
    public void registerTeleport() {
        register(new ReqTeleportMessage(), ReqTeleportHandler.class, ProcessorId.SERVER_COMMON);
    }

    public void registerDaily() {
        register(new ReqDailyTimedScheduleMessage(), ReqDailyTimedScheduleHandler.class);
        register(new ReqDailyTimedOpensMessage(), ReqDailyTimedOpensHandler.class);
        register(new ReqDailyTimesInfoMessage(), ReqDailyTimesInfoHandler.class);

        register(new ReqDailyBossChallengeInfoMessage(), ReqDailyBossChallengeInfoHandler.class);
        register(new ReqDailyTimedNextTimeMessage(), ReqDailyTimedNextTimeHandler.class);

        register(new ReqDailyTimedShaBaKePanelInfoMessage(), ReqDailyTimedShaBaKePanelInfoHandler.class, ProcessorId.SERVER_SOCIAL);

        register(new ReqDailyTimedRingAppearanceMessage(), ReqDailyTimedRingAppearanceHandler.class);
    }

    public void registerEnergy() {
    }

    public void registerFusion() {
        register(new ReqFusionBuyMessage(), ReqFusionBuyHandler.class, ProcessorId.SERVER_COMMON);
        register(new ReqFusionViewMessage(), ReqFusionViewHandler.class, ProcessorId.SERVER_COMMON);
        register(new ReqFusionSellMessage(), ReqFusionSellHandler.class, ProcessorId.SERVER_COMMON);
    }

    public void registerIdentify() {
        register(new ReqIdentifyEquipMessage(), ReqIdentifyEquipHandler.class);
        register(new ReqIdentifySaveMessage(), ReqIdentifySaveHandler.class);

        //资质
        register(new ReqAptitudeChangeMessage(), ReqAptitudeChangeHandler.class);
        register(new ReqAptitudeInfoMessage(), ReqAptitudeInfoHandler.class);

        // 专属套装继承
        register(new ReqSuitAttrExtendMessage(), ReqSuitAttrExtendHandler.class);
        // 专属套装继承升级
        register(new ReqSuitExtendUpgradeMessage(), ReqSuitExtendUpgradeHandler.class);
    }

    public void registerRecycle() {
        register(new ReqRecycleMessage(), ReqRecycleHandler.class);
    }

    public void registerSummon() {
        register(new ReqSummonAnswerMessage(), ReqSummonAnswerHandler.class, ProcessorId.SERVER_COMMON);
    }

    public void registerAppearance() {
        register(new ReqAppearanceUnlockMessage(), ReqAppearanceUnlockHandler.class);
        register(new ReqAppearanceWearMessage(), ReqAppearanceWearHandler.class);
        register(new ReqAppearanceUnWearMessage(), ReqAppearanceUnWearHandler.class);
        register(new ReqShenBingStatusMessage(), ReqShenBingStatusHandler.class);
        register(new ReqUnlockShenBingMessage(), ReqUnlockShenBingHandler.class);
        register(new ReqBuyShenBingGiftMessage(), ReqBuyShenBingGiftHandler.class);
        register(new ReqShenBingRewardMessage(), ReqShenBingRewardHandler.class);
        register(new ReqShenBingDailyRewardMessage(), ReqShenBingDailyRewardHandler.class);
        register(new ReqBuyShenBingMessage(), ReqBuyShenBingHandler.class);
    }

    /**
     * 通天塔
     */
    public void registerClimbTower(){
        register(new ReqAcquireClimbTowerTurnMessage(), ReqAcquireClimbTowerTurnHandler.class);
        register(new ReqClimbTowerMessage(), ReqClimbTowerHandler.class);
        register(new ReqClimbTowerTurnMessage(), ReqClimbTowerTurnHandler.class);
        register(new ReqJumpClimbTowerMessage(), ReqJumpClimbTowerHandler.class);
        register(new ReqRollClimbTowerMessage(), ReqRollClimbTowerHandler.class);
        register(new ReqRollClimbTowerTurnMessage(), ReqRollClimbTowerTurnHandler.class);
        register(new ReqStartClimbTowerMessage(), ReqStartClimbTowerHandler.class);
    }

    public void registerTianShu() {
        register(new ReqTianShuInfoMessage(), ReqTianShuInfoHandler.class);
        register(new ReqExtractMessage(), ReqExtractHandler.class);
        register(new ReqTianShuExpUpMessage(), ReqTianShuExpUpHandler.class);
    }

}