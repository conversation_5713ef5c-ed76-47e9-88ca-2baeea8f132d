package com.sh.game.map;

import com.sh.game.option.ServerOption;

/**
 * LocalMap
 *
 * <AUTHOR>
 * @date 2020/8/12 10:49
 */
public class LocalMap {

    //private LocalModule module = new LocalModule();
    //private LocalServer server;

    /**
     *  创建一个本地地图
     * @param option
     * @throws Exception
     */
    public void create(ServerOption option) throws Exception {
        /*MapOption mapOption = new MapOption();
        mapOption.setPlatformId(option.getPlatformId());
        mapOption.setServerId(option.getServerId());
        mapOption.setVersion(option.getVersion());
        mapOption.setServerType(option.getServerType());
        mapOption.setHost("127.0.0.1");
        mapOption.setGameServerPort(0);
        mapOption.setConfigDataPath(option.getConfigDataPath());
        server = module.create(mapOption);*/
    }




    /**
     * 连接本地地图
     */
    public void connect() {
        /*AbstractHandlerPool handlerPool = new AbstractHandlerPool();
        handlerPool.merge(new CrossClientHandlerPool());
        handlerPool.merge(new SceneClientHandlerPool());

        //创建一个host，给对用，对方可以直接调用这个host，进行消息进程内交换

        LocalClient clientToClient = new LocalClient(new CrossClientCommandRouter(), handlerPool);
        clientToClient.setName("clientToGame");

        Localhost hostTocClient = new Localhost();
        hostTocClient.setClient(clientToClient);

        hostTocClient.setId(GameContext.getHostId());


        RemoteHost hostToServer = module.connected(hostTocClient);

        //游戏消息直接投递给Consumer，所以调用consumer方法的时候，需要传递对端的channel，这里进行设置
        LocalClient clientToServer = (LocalClient) hostToServer.getClient();
        clientToServer.setPeerChannel(clientToClient.getChannel(0));

        //注册到SceneClientHostManager
        HashSet<String> ipAndPortSet = new HashSet<>();
        ipAndPortSet.add("127.0.0.1:0");
        Map<Integer, Set<String>> ipAndPortMap = SceneClientHostManager.getInstance().getIpAndPortMap();
        ipAndPortMap .put(GameConst.RemoteType.LOCAL, ipAndPortSet);

        Map<Integer, Map<String, RemoteHost>> addressMap = SceneClientHostManager.getInstance().getAddressMap();
        Map<String, RemoteHost> remoteHostMap = new HashMap<>();
        remoteHostMap.put("127.0.0.1:0", hostToServer);
        addressMap.put(GameConst.RemoteType.LOCAL, remoteHostMap);

        //登录
        ReqLoginMapServerMessage req = new ReqLoginMapServerMessage();
        HostInfo hostInfo = new HostInfo();
        hostInfo.setHostId(GameContext.getHostId());
        hostInfo.setHostPort(0);
        hostInfo.setHostIp("127.0.0.1");
        hostInfo.setIndex(0);
        hostInfo.setOpenTime(GameContext.getOpenDayZeroTime());
        hostInfo.setCombineTime(GameContext.getCombineDayZeroTime());
        req.setHost(hostInfo);
        hostToServer.getClient().sendMsg(req);*/

    }
}
