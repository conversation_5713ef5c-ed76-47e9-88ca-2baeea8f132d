package com.sh.game.map.entity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.entity.map.MonsterHpDto;
import io.protostuff.Tag;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/11/20
 */
public class RoleMonsterHpRecord {
    /**
     * 副本id->怪物血量列表
     */
    @Tag(1)
    private Map<Integer, List<MonsterHpDto>> monsters = new HashMap<>();

    public void record(int duplicateId, List<MonsterHpDto> list) {
        monsters.put(duplicateId, list);
    }

    public List<MonsterHpDto> find(int duplicateId) {
        return monsters.getOrDefault(duplicateId, Collections.emptyList());
    }

    public void reset(int duplicateId) {
        monsters.remove(duplicateId);
    }

    public void reset() {
        monsters.clear();
    }

    public void resetByDuplicateCate(int duplicateCate){
        for (Map.Entry<Integer, List<MonsterHpDto>> entry : monsters.entrySet()) {
            int id = entry.getKey();
            DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, id);
            if (config.getDuplicateCate() == duplicateCate){
                monsters.remove(id);
                return;
            }
        }
    }
}
