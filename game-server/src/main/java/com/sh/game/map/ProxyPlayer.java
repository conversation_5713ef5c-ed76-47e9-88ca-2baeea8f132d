package com.sh.game.map;

import com.google.common.primitives.Ints;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.communication.notice.*;
import com.sh.game.common.communication.notice.logic.player.AppearanceAddToSceneNotice;
import com.sh.game.common.communication.notice.logic.player.AppearanceRemoveToSceneNotice;
import com.sh.game.common.communication.notice.scene.ShenWeiNotice;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.avatar.AvatarBackState;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.map.MapAllocContext;
import com.sh.game.common.entity.map.SkillDTO;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.log.entity.RoleMonsterDropLog;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.notice.TimeProcessNotice;
import com.sh.game.notice.TimeoutHandler;
import com.sh.game.system.activity.ActivityManager;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.xialv.entity.RoleXiaLv;
import lombok.Getter;
import lombok.Setter;

import java.util.*;


/**
 * 代理玩家，主要是用于逻辑层这边获取到地图中Player的基本信息
 */
@Getter
@Setter
public class ProxyPlayer {

    private long rid;

    private String name;

    private int mapCfgId;

    private long mapId;

    private int hostId;

    private int mapEnterType = MapConst.ENTER_TYPE.LOGIN;

    private long mapChangeAvailableAt = 0;

    /**
     * 缓存玩家要进入的地图 比如还没创建完毕得
     */
    private MapAllocContext pendingContext;

    private final Object MAP_ENTER_STATUS_LOCK = new Object();

    private boolean mapEntered = false;

    /**
     * 勇于神塔用
     */
    private boolean flag;

    private Queue<ProcessNotice> pending = new LinkedList<>();

    /**
     * 持续时间开始时间戳，秒
     */
    private int keepStart;

    public void sendNotice(ProcessNotice notice) {
        synchronized (this.MAP_ENTER_STATUS_LOCK) {
            if (!mapEntered) {
                pending.offer(notice);
                return;
            }
        }
        notice.setSourceHost(GameContext.getHostId());
        notice.addHost(hostId);
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, notice, mapId);
    }


    public void reqPosition(NoticeCallback<GetPlayerPositionRetNotice> callback) {
        GetPlayerPositionNotice notice = new GetPlayerPositionNotice();
        notice.setQueueId(AppContext.getProcessor());
        notice.setRoleId(getRid());

        notice.setWaitTime(2000);
        notice.setHandler(new TimeoutHandler() {
            @Override
            public void timeout(TimeProcessNotice notice) {
                if (callback != null) {
                    callback.setNotice(new GetPlayerPositionRetNotice());
                    callback.doAction();
                }
            }
        });
        notice.setCallback(callback);

        sendNotice(notice);
    }

    public void createMonster(int monsterCfgId, int count, boolean withSpawn, boolean needSafe, boolean announce) {
        CreateMonsterNotice notice = new CreateMonsterNotice();
        notice.setRid(rid);
        notice.setMid(monsterCfgId);
        notice.setCount(count);
        notice.setWithSpawn(withSpawn);
        notice.setNeedSafe(needSafe);
        notice.setAnnounce(announce);
        sendNotice(notice);
    }

    /**
     * 玩家位置上召唤怪物
     *
     * @param monsterCfgId
     * @param count
     * @param needOwner
     */
    public void createMonster(int monsterCfgId, int count, boolean needOwner,int overTime) {
        CreateMonsterNotice notice = new CreateMonsterNotice();
        notice.setRid(rid);
        notice.setMid(monsterCfgId);
        notice.setCount(count);
        notice.setNeedOwner(needOwner);
        notice.setOverTime(overTime);
        sendNotice(notice);
    }

    /**
     * 玩家位置上召唤怪物（怪物偏移）
     *
     * @param monsterCfgId
     * @param count
     * @param needOwner
     * @param pianyi 偏移坐标
     * @param isAnnounce 是否发公告
     */
    public void createMonster(int monsterCfgId, int count, boolean needOwner,int overTime, int pianyi, boolean isAnnounce) {
        CreateMonsterNotice notice = new CreateMonsterNotice();
        notice.setRid(rid);
        notice.setMid(monsterCfgId);
        notice.setCount(count);
        notice.setNeedOwner(needOwner);
        notice.setOverTime(overTime);
        notice.setPianyi(pianyi);
        notice.setAnnounce(isAnnounce);
        sendNotice(notice);
    }

    public void sendBuffAdd(int buffId, int globalId) {
        BuffAddNotice notice = new BuffAddNotice();
        notice.setGlobalId(globalId);
        notice.setBuffId(buffId);
        notice.setRid(getRid());

        sendNotice(notice);
    }

    public void equipUpdate(Integer index, long actorId, int cfgId, long id, int durable, int magicBloodStoneValue) {
        EquipUpdateNotice notice = new EquipUpdateNotice(getRid(), actorId, index, cfgId, id, durable, magicBloodStoneValue);
        sendNotice(notice);
    }

    public void itemUsingMapApply(long actorId, int id, int finalCount) {
        RoleMapItemUsingApplyNotice notice = new RoleMapItemUsingApplyNotice();
        notice.setRoleId(getRid());
        notice.setItemCfgId(id);
        notice.setItemCount(finalCount);
        notice.setActorId(actorId);
        sendNotice(notice);
    }

    public void itemUsingMapVerify(long id, int cfgId, int count, List<Integer> params) {
        RoleMapItemUsingVerifyNotice notice = new RoleMapItemUsingVerifyNotice();

        notice.setRoleId(getRid());
        notice.setItemId(id);
        notice.setItemCfgId(cfgId);
        notice.setItemCount(count);
        notice.setParams(params);
        sendNotice(notice);
    }

    public void tradeAppend(int grid, Item item, boolean isTotal) {
        TradeAppendNotice notice = new TradeAppendNotice();
        notice.setRoleId(getRid());
        notice.setGrid(grid);
        notice.setItem(item);
        notice.setTotal(isTotal);
        sendNotice(notice);
    }

    public void monsterDropFilterRes(int monsterCfgId, int dropType, long mapKey, int x, int y, List<Item> dropIds, List<BuffConfig> dropBuffs, int hung) {
        MonsterDropFilterResNotice notice = new MonsterDropFilterResNotice();
        notice.setRid(getRid());
        notice.setMonsterCfgId(monsterCfgId);
        notice.setDropType(dropType);
        notice.setMapKey(mapKey);
        notice.setDropBuffList(dropBuffs);
        notice.setX(x);
        notice.setY(y);
        notice.setDropList(dropIds);
        notice.setHung(hung);

        sendNotice(notice);
        //怪物掉落记录日志
        RoleMonsterDropLog roleMonsterDropLog = new RoleMonsterDropLog();
        roleMonsterDropLog.paddingDropLog(monsterCfgId, mapCfgId, rid, x, y, dropIds);
        roleMonsterDropLog.submit();
    }

    public void mapChat(int chatType, ResChatMessage msg) {
        MapChatNotice notice = new MapChatNotice();
        notice.setRoleId(getRid());
        notice.setChatType(chatType);
        notice.setMsg(msg);
        sendNotice(notice);
    }

    public void titleUpdate(int title) {
        TitleUpdateNotice notice = new TitleUpdateNotice();
        notice.setRoleId(getRid());
        notice.setTitle(title);
        sendNotice(notice);
    }

    @Deprecated
    public void skillUpdate(long actorId, List<SkillDTO> skillDTOList) {
//        SkillUpdateNotice notice = new SkillUpdateNotice(getRid(), actorId, skillDTOList);
//        sendNotice(notice);
    }

    public void battleSkillUpdate(long actorId, List<SkillBattleConfig> configList) {
        List<SkillDTO> skillDTOList = new ArrayList<>();
        for (SkillBattleConfig config : configList) {
            SkillDTO dto = new SkillDTO();
            dto.setSkillId(config.getId());
            dto.setLevel(config.getLevel());
            skillDTOList.add(dto);
        }
        SkillUpdateNotice notice = new SkillUpdateNotice(getRid(), actorId, skillDTOList);
        sendNotice(notice);
    }

    public void huoBanUpdate(long actorId, Set<Integer> added, Set<Integer> removed) {
        HuoBanUpdateNotice notice = new HuoBanUpdateNotice();
        notice.setActorId(actorId);
        notice.setRid(getRid());
        notice.setAdded(added);
        notice.setRemoved(removed);
        sendNotice(notice);
    }

    public void mountUpdateNotice(int mounting) {
        MountUpdateNotice notice = new MountUpdateNotice();
        notice.setRoleId(getRid());
        notice.setMount(mounting);
        sendNotice(notice);
    }

    public void ztPetUpdateNotice(int id, int level, int rank) {
        ZTPetUpdateNotice notice = new ZTPetUpdateNotice();
        notice.setRoleId(getRid());
        notice.setId(id);
        notice.setLevel(level);
        notice.setRank(rank);
        sendNotice(notice);
    }

    public void equipUpdateNotice(int index, int id, long lid, int durable, int magicBloodStoneValue, long actorId) {
        EquipUpdateNotice notice = new EquipUpdateNotice(getRid(), actorId, index, id, lid, durable, magicBloodStoneValue);
        sendNotice(notice);
    }

    public void equipSuitUpdateNotice(long actorId, Set<Integer> suitSet) {
        EquipSuitUpdateNotice notice = new EquipSuitUpdateNotice(getRid(), actorId, suitSet);
        sendNotice(notice);
    }

    public void nameUpdateNotice(long rid, String newName, int isRole) {
        RoleNameChangeNotice notice = new RoleNameChangeNotice();
        notice.setRid(rid);
        notice.setNewName(newName);
        notice.setIsRole(isRole);
        sendNotice(notice);
    }

    public void dragonWingStrengthNotice(int level) {
        DragonWingStrengthNotice notice = new DragonWingStrengthNotice();
        notice.setRid(getRid());
        notice.setLevel(level);
        sendNotice(notice);
    }

    /**
     * 将道具丢到场景中
     *
     * @param itemList
     */
    public void dropItemNotice(List<int[]> itemList) {
        if (itemList == null || itemList.size() == 0) {
            return;
        }
        DropMultipleItemNotice notice = new DropMultipleItemNotice();
        notice.setRid(getRid());
        notice.setItems(itemList);
        sendNotice(notice);
    }

    public void attributeUpdateNotice(Attribute copy, long actorId) {
        AttributeUpdateNotice notice = new AttributeUpdateNotice();
        notice.setRid(getRid());
        notice.setActorId(actorId);
        notice.setAttribute(copy);
        sendNotice(notice);
    }

    public void gmNotice(String[] strings) {
        GMNotice notice = new GMNotice();
        notice.setRid(getRid());
        notice.setParam(strings);
        sendNotice(notice);
    }

    public void treasureMonsterCallNotice(int monster, List<Item> itemAdds) {
        TreasureMonsterCallNotice notice = new TreasureMonsterCallNotice();
        notice.setRid(getRid());
        notice.setMonsterId(monster);
        notice.setItemAdds(itemAdds);
        sendNotice(notice);
    }

    public void treasureMonsterCheckCallNotice(int cid, Role role) {
        TreasureMonsterCheckCallNotice notice = new TreasureMonsterCheckCallNotice();
        notice.setRid(role.getId());
        notice.setCid(cid);
        notice.setHandler(timeProcessNotice -> {
        });
        notice.setCallback(new NoticeCallback() {
            @Override
            public void callback(ProcessNotice notice) {
                checkCallRet(role, cid);
            }
        });
        sendNotice(notice);
    }

    private void checkCallRet(Role role, int cid) {
        ActivitySchedule schedule = ActivityManager.getInstance().getAvailable(1011);
        if (schedule == null) {
            return;
        }

        int activityId = schedule.getActivityID();

        TreasureMonsterConfig treasureMonsterConfig = ConfigDataManager.getInstance().getById(TreasureMonsterConfig.class, cid);
        if (treasureMonsterConfig.getActivityID() != activityId) {
            return;
        }

        if (!ConditionUtil.validate(role, treasureMonsterConfig.getCondition())) {
            TipUtil.show(role, CommonTips.服务_条件不满足);
            return;
        }

        TreasureServerTimeNotice notice = new TreasureServerTimeNotice();
        notice.setRoleId(role.getId());
        notice.setCid(cid);
        notice.setActivityId(schedule.getActivityID());
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.SERVER_COMMON, notice, 0L);
    }

    public void playBuff(Integer... plays) {
        if (plays.length <= 0) {
            return;
        }

        BuffPlayNotice notice = new BuffPlayNotice();
        notice.setRoleId(getRid());
        notice.setBuffIds(Arrays.asList(plays));
        sendNotice(notice);
    }

    public void functionCount(int itemId) {
        FunctionCountNotice notice = new FunctionCountNotice();
        notice.setRid(getRid());
        notice.setItemId(itemId);
        sendNotice(notice);
    }

    public void addBuff(int buffId) {
        AddBuffNotice notice = new AddBuffNotice();
        notice.setRid(getRid());
        notice.setBuffId(buffId);
        sendNotice(notice);
    }

    public void removeBuff(int buffId) {
        RemoveBuffNotice notice = new RemoveBuffNotice();
        notice.setRid(getRid());
        notice.setBuffId(buffId);
        sendNotice(notice);
    }

    public void createHero(Hero hero) {
        CreateHeroNotice notice = new CreateHeroNotice();
        notice.setHeroDTO(DTOUtil.createHeroDTO(hero));
        sendNotice(notice);
    }

    /**
     * 更新挖矿状态
     *
     * @param rid
     * @param state
     */
    public void updateDigState(long rid, boolean state) {
        OreHoleUpdateNotice notice = new OreHoleUpdateNotice(rid, state);
        sendNotice(notice);
    }

    /**
     * 更新挖矿朝向并传送
     *
     * @param map
     * @param mapKey
     * @param x
     * @param y
     * @param face
     */
    public void oreHoleTelportNotice(int map, long mapKey, int x, int y, int face) {
        OreHoleTelportNotice notice = new OreHoleTelportNotice(rid, map, mapKey, x, y, face);
        sendNotice(notice);
    }

    /**
     * 扣除呆在地图中消耗
     *
     * @param role
     */
    public void decreaseMapKeep(Role role,int nextMapCfgId) {
        if (keepStart < 1) {
            return;
        }
        MapConfig config = ConfigDataManager.getInstance().getById(MapConfig.class, mapCfgId);
        if (config == null || !config.isNeedCostPerSec()) {
            return;
        }


        int now = TimeUtil.getNowOfSeconds();
        int freeSce = config.getCostFreeSec();
        int count = now - keepStart - freeSce;//减掉免费消耗时间，才是真正的消耗时间
        if (count <= 0) {
            return;
        }
        keepStart = now;
        int itemID = config.getCostPerSec()[0];
        long total = config.getCostPerSec()[1] * count;
        long max = role.getBackpack().fetchCountLByCfgId(itemID);
        BackpackStash stash = new BackpackStash(role);

        // 改字段为空代表该地图没有子地图，时间正常扣，改字段不为空表示有子地图如果下个地图是子地图则正常扣除，否则全部扣除
        if (config.getFromInheritFreeTimeMaps().isEmpty() || config.getFromInheritFreeTimeMaps().contains(nextMapCfgId)) {
            stash.decrease(itemID, Math.min(total, max));
        } else {
            stash.decrease(itemID, max);
        }
        stash.commit(role, LogAction.ENTER_MAP);
    }

    /**
     * 购买地图消耗道具
     *
     * @param role
     * @param itemID
     */
    public void buyKeepItem(Role role, int itemID) {
        if (keepStart < 1) {
            return;
        }
        MapConfig config = ConfigDataManager.getInstance().getById(MapConfig.class, mapCfgId);
        if (config == null || !config.isNeedCostPerSec()) {
            return;
        }
        if (config.getCostPerSec()[0] != itemID) {
            return;
        }
        decreaseMapKeep(role,0);
        MapKeepItemNotice notice = new MapKeepItemNotice();
        long count = role.getBackpack().fetchCountLByCfgId(config.getCostPerSec()[0]);
        notice.setTotalOwn(Ints.checkedCast(count));
        notice.setRoleId(role.getId());
        sendNotice(notice);
    }

    /**
     * 更新祭坛次数
     *
     * @param rid
     * @param count
     */
    public void updateJiTanCount(long rid, int count) {
        JiTanRoleCountNotice notice = new JiTanRoleCountNotice(rid, count);
        sendNotice(notice);
    }

    public void xialvUpdate(Role role) {
        // 解锁后跟随
        List<Integer> equipIds = new ArrayList<>();
        Storage xiaLvStorage = role.getBackpack().fetchStorage(BackpackConst.Place.XIALV_EQUIP);
        for (Item item : xiaLvStorage.getData().values()) {
            if (item != null) {
                equipIds.add(item.getCfgId());
            }
        }
        RoleXiaLv xialv = role.getXialv();
        // 装扮中获取
        Appearance appearance = role.getRoleAdvance().getAppearance();

        int monsterCid = 0;
        int fashionCid = appearance.getWears().getOrDefault(RoleConst.AppearanceType.XIALV, 0);
        AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionCid);
        if (appearanceConfig != null) {
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, appearanceConfig.getItemId());
            if (itemConfig != null) {
                monsterCid = itemConfig.getModel();
            }
        }
        if (xialv.getFollowId() <= 0) {
            return;
        }

        XiaLvUpdateNotice notice = new XiaLvUpdateNotice(getRid(), xialv.getFollowId(),
                monsterCid, xialv.getGuardianLevel(), equipIds, xialv.getLastDieTime(), xialv.getHp());
        sendNotice(notice);
    }

    public void xialvGuanghuanUpdate(int cfgId) {
        XiaLvGuanhuanUpdateNotice notice = new XiaLvGuanhuanUpdateNotice(getRid(), cfgId);
        sendNotice(notice);
    }

    public void xiaLvOwnUpdate(Map<Integer, Integer> xiaLvCidByLevelMap) {
        XiaLvOwnUpdateNotice notice = new XiaLvOwnUpdateNotice(getRid(), xiaLvCidByLevelMap);
        sendNotice(notice);
    }

    public void xiaLvAttrUpdate(int xiaLvGuardianLevel, List<Integer> equipIds) {
        XiaLvAttrUpdateNotice notice = new XiaLvAttrUpdateNotice(getRid(), xiaLvGuardianLevel, equipIds);
        sendNotice(notice);
    }

    public void zhanyiUpdate(int zhanyiLv) {
        ZhanYiUpdateNotice notice = new ZhanYiUpdateNotice(getRid(), zhanyiLv);
        sendNotice(notice);
    }

    /**
     * 增加已激活装扮
     * @param appearanceId
     */
    public void appearanceAdd(int appearanceId){
        AppearanceAddToSceneNotice notice = new AppearanceAddToSceneNotice(getRid(), appearanceId);
        sendNotice(notice);
    }

    /**
     * 移除已激活装扮
     * @param appearanceId
     */
    public void appearanceRemove(int appearanceId){
        AppearanceRemoveToSceneNotice notice = new AppearanceRemoveToSceneNotice(getRid(), appearanceId);
        sendNotice(notice);
    }

    public void jueXingUpdate(Map<Integer, Integer> jueXingLevel) {
        JueXingUpdateNotice notice = new JueXingUpdateNotice(getRid(), jueXingLevel);
        sendNotice(notice);
    }

    public void monthCardUpdate(Map<Integer, Integer> monthCard) {
        MonthCardUptateNotice notice = new MonthCardUptateNotice(getRid(), monthCard);
        sendNotice(notice);
    }

    public void shenWeiUpdate(Map<Integer, Integer> shenWei){
        ShenWeiNotice notice = new ShenWeiNotice(getRid(),shenWei);
        sendNotice(notice);
    }

    /**
     * 更新神威boss每日掉落次数
     *
     * @param count     掉落次数
     */
    public void updateShenWeiBossDropCount(int count, int addCount) {
        ShenWeiBossDailyDropCountRetNotice notice = new ShenWeiBossDailyDropCountRetNotice();
        notice.setRoleId(rid);
        notice.setCount(count);
        notice.setFaQiHuiYuanAddshenWeiBossCount(addCount);
        sendNotice(notice);
    }

    /**
     * 解锁宠物，跟随
     * @param role
     */
    public void chongWuUpdate(Role role){
        ChongWuUpdateNotice notice = new ChongWuUpdateNotice();
        notice.setRoleId(role.getRoleId());
        notice.setChongWuCfgId(role.getRoleChongWuCfgId());
        ChongWuConfig config = ConfigDataManager.getInstance().getById(ChongWuConfig.class, role.getRoleChongWuCfgId());
        if (config != null) {
            notice.setChongwuMonsterModel(config.getMonsterid());
        }
        sendNotice(notice);

    }


    /**
     *
     * @param oldBuffId
     * @param buffId
     */
    public void updateJuanXianBuff(int oldBuffId, int buffId){
        JuanXianBuffNotice notice = new JuanXianBuffNotice();
        notice.setRoleId(rid);
        notice.setOldBuffId(oldBuffId);
        notice.setBuffId(buffId);
        sendNotice(notice);
    }

    /**
     * 请求强制复活
     *
     * @param relivePercent 复活后血量百分比
     */
    public void relive(int relivePercent) {
        ReliveNotice notice = new ReliveNotice();
        notice.setRoleId(rid);
        notice.setRelivePercent(relivePercent);
        sendNotice(notice);
    }

    /**
     * 更新刀刀元宝每日获取量
     *
     * @param count     掉落次数
     */
    public void updateGoldDailyCount(long count) {
        GoldDailyRetNotice notice = new GoldDailyRetNotice();
        notice.setRoleId(rid);
        notice.setCount(count);
        sendNotice(notice);
    }

    /**
     * 更新行会神皇信息
     *
     * @param shenhaungId
     */
    public void unionShengHuangLevelUpdate(int shenhaungId, long rid) {
        UnionShenHuangNotice notice = new UnionShenHuangNotice();
        notice.setShenHuangId(shenhaungId);
        notice.setRid(rid);
        sendNotice(notice);
    }


    public void roleBackStateChange(long rid, AvatarBackState avatarBackState) {
        AvatarBackStateChangeNotice notice = new AvatarBackStateChangeNotice();
        notice.setRid(rid);
        notice.setAvatarBackState(avatarBackState);
        sendNotice(notice);
    }
}
