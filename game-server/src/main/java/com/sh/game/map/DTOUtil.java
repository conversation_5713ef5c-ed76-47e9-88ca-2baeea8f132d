package com.sh.game.map;

import com.google.common.collect.Maps;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.common.config.model.SkillConfig;
import com.sh.game.common.config.model.XiaLvGuangHuanConfig;
import com.sh.game.common.constant.MonsterType;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.map.*;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.sync.SyncBean;
import com.sh.game.common.sync.SyncDataCreator;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.system.militaryRank.MilitaryRankManger;
import com.sh.game.system.realm.RealmManager;
import com.sh.game.system.realm.entity.RoleRealm;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.transformSuperMan.TransformSuperManManager;
import com.sh.game.system.transformSuperMan.entity.RoleToSuperMan;
import com.sh.game.system.union.UnionManager;
import com.sh.game.system.union.entity.UnionShenHuang;
import com.sh.game.system.unionCamp.UnionCampManager;
import com.sh.game.system.ztpet.entity.RoleZTPet;
import com.sh.game.system.ztpet.entity.ZTPetData;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/8/30 17:46
 */
public class DTOUtil {

    public static RoleDTO createPlayerDTO(Role role) {

        RoleDTO player = new RoleDTO();
        player.setCreateTime(role.getRoleLogin().getCreateTime());
        List<SyncBean> syncBeans = new ArrayList<>();
        syncBeans.addAll(SyncDataCreator.pack(role));
        RoleAdvance roleAdvance = role.getRoleAdvance();
        syncBeans.addAll(SyncDataCreator.pack(roleAdvance));
//        syncBeans.addAll(SyncDataCreator.pack(role.getRoleDaily()));
        syncBeans.addAll(SyncDataCreator.pack(role.getRoleActivity()));
        syncBeans.addAll(SyncDataCreator.pack(role.getRoleTask()));
        player.setSyncBeans(syncBeans);

        List<SkillBattleConfig> roleSkill = SkillManager.getInstance().findRoleSkill(role);
        for (SkillBattleConfig skillBattleConfig : roleSkill) {
            createSkillDTO(skillBattleConfig, player.getSkillMap());
        }

        UnionCampManager.getInstance().checkUnionCampBuff(role, roleAdvance.getBuffs());

        //复制系统buff
        player.setBuffs(roleAdvance.getBuffs());
//        //威望
//        RoleBag bag = DataCenter.getRoleBag(role.getId());
//        player.setWeiWang(bag.getWeiWang());

        //pk值
        player.setPkValue(role.getPkValue());
        player.setPkValueTotalTime(role.getPkValueTotalTime());

        //装备
        Backpack backpack = DataCenter.getBackpack(role.getId());
        Storage storage = backpack.fetchStorage(BackpackConst.Place.EQUIP);
        storage.getData().forEach((k, v) -> {
            if (v != null) {
                player.getEquipMap().put(k, EquipDTO.createDto(k, v));
            }
        });

        //战斗模式
        player.setFightModel(role.getFightModel());

        //复制属性
        Attribute finalAttr = new Attribute();
        finalAttr.fixAdd(role.getAttribute());

        player.setServantDTOS(createServantDTOS(role.getServantDTOS()));
        player.setAttribute(finalAttr);

        //登录时血量溢出处理{针对gm命令 @调整属性}
//        role.setHp(Math.min(role.getHp(), role.getFinalAttribute().getMaxHp()));
//        role.setMp(Math.min(role.getMp(), role.getFinalAttribute().getMaxMp()));
        //TODO 因为属性改变的问题，现在屏蔽属性限制
        role.setHp(role.getHp());
        role.setMp(role.getMp());
        long hp = role.getHp();
        if (hp <= 0) {
            hp = 1;
        }
        player.setHp(hp);
        player.setMp((int) role.getMp());
        player.setShield(role.getShield());

       // player.setMount(roleAdvance.getMounting());
        player.setMount(role.getRoleMount().getUseingMount());
        //创建英雄
        player.setHeroDTO(createHeroDTO(role.getHero()));
        player.setXialvId(role.getXialv().getFollowId());
        player.setXiaLvGuardianLevel(role.getXialv().getGuardianLevel());
        List<Integer> equipIds = new ArrayList<>();
        Storage xiaLvStorage = role.getBackpack().fetchStorage(BackpackConst.Place.XIALV_EQUIP);
        for (Item item : xiaLvStorage.getData().values()) {
            if (item != null) {
                equipIds.add(item.getCfgId());
            }
        }
        player.setXiaLvEquipList(equipIds);
        player.setXiaLvLastDieTime(role.getXialv().getLastDieTime());
        player.setXiaLvHp(role.getXialv().getHp());
        XiaLvGuangHuanConfig guangHuanConfig = ConfigDataManager.getInstance().getById(XiaLvGuangHuanConfig.class
                , role.getXialv().getGuanghuanId());
        player.setXialvGuanghuan(guangHuanConfig.getTexiao());
        player.setXialvUpTeXiao(guangHuanConfig.getUptexiao());
        player.setCareer(role.getCareer());

        // 名望
        player.setMingWang(role.getMingWang());

        // 狂暴之力
        RoleToSuperMan roleToSuperMan = TransformSuperManManager.getInstance().find(role.getRoleId());
        player.setSuperMan(roleToSuperMan.isSuperMan());
        player.setProtect(roleToSuperMan.isProtect());
        player.setRandomProtect(roleToSuperMan.isRandomProtect());

        //神威属性
        player.setShenWei(role.getRoleShenWei());

        // 宠物
        player.setPetCfgId(role.getRoleChongWuCfgId());

        // 法器id
        player.setFaQiLevel(role.findFaQiHuiYuanConfig().getVipLevel());

        // 玉佩id
        player.setYuPeiId(role.findYuPei());

        //刀刀元宝每日获取数
        player.setGoldCount(role.getRoleDaily().getGoldDailyCount());
        //神皇id
        Union union = UnionManager.getInstance().getUnion(role);
        if (union != null) {
            UnionShenHuang unionShenHuang = union.getShenHuangInfo().get(union.getId());
            if (unionShenHuang != null) {
                player.setShenHuangId(unionShenHuang.getId());
            }
        }
        player.setAvatarBackState(role.getAvatarBackState());

        // 阵营类型
        player.setCampType(UnionCampManager.getInstance().findUnionCampType(role));
        player.setMilitaryRankCfgId(MilitaryRankManger.getInstance().find(role.getId()).getMilitaryRankCfgId());

        RoleRealm roleRealm = RealmManager.getInstance().find(role.getRoleId());
        player.setRealmId(roleRealm.getRealmId());
        player.setSex(role.getSex());
        player.setEquipSuits(role.getEquipSuits());
        player.setZtPet(createZTDTO(role));
        return player;
    }

    public static HeroDTO createHeroDTO(Hero hero) {
        if (hero == null) {
            return null;
        }
        HeroDTO heroDTO = new HeroDTO();
        List<SyncBean> syncBeans = new ArrayList<>();
        syncBeans.addAll(SyncDataCreator.pack(hero));
        RoleAdvance roleAdvance = hero.getRoleAdvance();
        syncBeans.addAll(SyncDataCreator.pack(roleAdvance));
        heroDTO.setSyncBeans(syncBeans);
        heroDTO.setId(hero.getId());
        heroDTO.setOwnerId(hero.getOwnerId());
        heroDTO.setCallTime(hero.getCallTime());
        heroDTO.setLoyalty(hero.getLoyalty());
        heroDTO.setBuffs(roleAdvance.getBuffs());
//        //职业技能
//        roleAdvance.getRoleSkill().getSkillMap().forEach((k, v) -> {
//            if (!v.isAutoUse()) {
//                return;
//            }
//            createSkillDTO(k, v.getLevel(), heroDTO.getSkillMap());
//        });
        //装备
        Backpack backpack = DataCenter.getBackpack(hero.getOwnerId());
        Storage storage = backpack.fetchStorage(BackpackConst.Place.HERO_EQUIP);
        storage.getData().forEach((k, v) -> {
            if (v != null) {
                heroDTO.getEquipMap().put(k, EquipDTO.createDto(k, v));
            }
        });
        //复制属性
        Attribute finalAttr = new Attribute();
        finalAttr.fixAdd(hero.getAttribute());
        heroDTO.setAttribute(finalAttr);

        heroDTO.setServantDTOS(createServantDTOS(hero.getServantDTOS()));

        //登录时血量溢出处理{针对gm命令 @调整属性}
        hero.setHp(Math.min(hero.getHp(), hero.getFinalAttribute().findMaxHp()));
        hero.setMp(Math.min(hero.getMp(), hero.getFinalAttribute().findMaxMp()));
        long hp = hero.getHp();
        heroDTO.setHp(hp);
        heroDTO.setMp(hero.getMp());
        return heroDTO;
    }

    private static void createSkillDTO(SkillBattleConfig config, Map<Integer, SkillDTO> skillMap) {
        SkillDTO skillDTO = new SkillDTO();
        skillDTO.setSkillId(config.getId());
        skillDTO.setLevel(config.getLevel());
        skillMap.put(config.getId(), skillDTO);
    }

    private static List<ServantDTO> createServantDTOS(List<ServantDTO> dtos) {
	    if (dtos == null) {
	  	    return Collections.emptyList();
	    }
        int curTime = TimeUtil.getNowOfSeconds();
        List<ServantDTO> list = new ArrayList<>();
        dtos.forEach(servantDTO -> {
            //type为9的5分钟后过期
            if (servantDTO.getMonsterType() == MonsterType.SKILL_CALL && servantDTO.getCreateTime() + 5 * 60 < curTime) {
                return;
            }
            ServantDTO dto = servantDTO.copy();
            list.add(dto);
        });

        return list;
    }

    public static Map<Integer, List<Integer>> createBuffMap(Map<Integer, List<Integer>> sysBuffMap) {
        Map<Integer, List<Integer>> retBuffMap = Maps.newHashMap();
        for (List<Integer> buffList : sysBuffMap.values()) {
            for (int buffId : buffList) {
                BuffConfig config = ConfigDataManager.getInstance().getById(BuffConfig.class, buffId);
                if (config == null) {
                    continue;
                }
                List<Integer> list = retBuffMap.computeIfAbsent(config.getBufferType(), k -> new ArrayList<>());
                list.add(buffId);
            }
        }
        return retBuffMap;
    }

    public static ZTPetDTO createZTDTO(Role role) {
        ZTPetDTO ztPet = new ZTPetDTO();
        RoleZTPet roleZTPet = role.findZTPet();
        ZTPetData data = roleZTPet.getPetData().getOrDefault(roleZTPet.getSelectPetId(), null);
        if (data != null) {
            ztPet.setId(data.getId());
            ztPet.setLevel(data.getLevel());
            ztPet.setRank(data.getRank());
        }

        return ztPet;
    }
}
