package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

/**
 * 神兵等级（VIP）
 * 1、账号
 * 2、角色名
 * 3、战意+等级
 * 4、当前等级
 * 5、升级后等级
 * 6、提升方式（道具直升或任务）
 * 7、时间
 */
@Getter
@Setter
public class RoleShenbingVipLog extends BaseRoleLog {

    public RoleShenbingVipLog(Role role) {
        super(role);
    }

    public RoleShenbingVipLog() {
    }

    /**
     * 账号
     */
    private long userId;

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 战意+等级
     */
    private String zhanYiDengJi;

    /**
     * 当前等级
     */
    private int nowLevel;

    /**
     * 升级后等级
     */
    private int afterLevel;

    /**
     * 提升方式（道具直升或任务）
     */
    private String hoistType;

    /**
     * 时间
     */
    private int time;

    /**
     * 账户
     */
    private String account;

}
