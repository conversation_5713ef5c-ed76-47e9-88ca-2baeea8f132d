package com.sh.game.log.entity;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

/**
 * 战意（转生）
 */
@Getter
@Setter
public class RoleZhuanshengLog extends BaseRoleLog {

    public RoleZhuanshengLog(Role role){
    super(role);
    }

    public RoleZhuanshengLog() {
    }

    /**
     * 原id
     */
    private int oldId;

    /**
     * 薪id
     */
    private int newId;

    /**
     * 是否英雄
     */
    private int hero;

    /**
     * 账号
     */
    private long userId;

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 战意等级+进度
     */
    private String zhanYiDengJi;

    /**
     * 类型（如战意直升卡、消耗材料）
     */
    private int type;

    /**
     * 操作时间
     */
    private int operatingTime;

    /**
     * 账户
     */
    private String account;

}
