package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
/**
 * 称号日志
 */
public class RoleAppearanceLog extends BaseRoleLog {

	/**
	 * 角色id
	 */
	private long roleId;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 时间
	 */
	private int time;

	/**
	 * 称号id
	 */
	private int appearanceId;

	/**
	 * 称号名称
	 */
	private String appearanceName;

	/**
	 * 区服id
	 */
	private int serverId;

	/**
	 * 账户
	 */
	private String account;

    public RoleAppearanceLog(Role role) {
        super(role);
    }

    public RoleAppearanceLog() {
        super();
    }


}
