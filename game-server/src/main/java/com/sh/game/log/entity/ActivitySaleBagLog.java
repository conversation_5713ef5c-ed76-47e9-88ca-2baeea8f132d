package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

@Setter
@Getter
/**
 * 开服礼包（活动）日志
 */
public class ActivitySaleBagLog extends BaseRoleLog {

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 账号
     */
    private long uid;

    /**
     * 领取奖励时间
     */
    private int acquireTime;

    /**
     * 奖励物品的记录：奖励物品id，数量
     */
    private List<int[]> rewardList=new LinkedList<>();

    /**
     * 对应礼包消耗的货币数量：货币类型，数量
     */
    private int[] coin;

    /**
     * 账户
     */
    private String account;


    /**
     * 打折后消耗货币数量：货币类型，数量
     */
    private int[] discountDecrease;

    public ActivitySaleBagLog(Role role) {
        super(role);

    }

    public ActivitySaleBagLog() {
        super();

    }

}
