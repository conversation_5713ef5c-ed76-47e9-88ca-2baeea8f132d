package com.sh.game.log.entity.abs;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sh.engine.log.JsonLog;
import com.sh.engine.log.LogService;
import com.sh.game.GameContext;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.util.IDUtil;
import com.sh.game.common.util.StringUtil;
import com.sh.game.common.util.TimeUtil;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Getter
@Setter
public abstract class BaseLog implements JsonLog {

    private static final Logger logger = LoggerFactory.getLogger(BaseLog.class);
    //es要求索引名为小写
    private String logtype;

    /**
     * 日志ID
     */
    private long id;

    /**
     * 时间
     */
    private int time;

    /**
     * 区服id
     */
    private int sid;

    private String logName;

    /**
     * 平台id
     */
    private int platformId;

    public BaseLog() {
        id = IDUtil.getId(IDConst.LOG);
        time = TimeUtil.getNowOfSeconds();
        sid = GameContext.getServerId();

        logtype = StringUtil.camelToUnderline(this.getClass().getSimpleName());
        logtype = logtype.toLowerCase();
        logtype = logtype.replace("log", "");
        logtype = logtype.substring(0, logtype.length() - 1);

        platformId = GameContext.getOption().getPlatformId();
    }

    @Override
    public String toJSON() {
        return JSON.toJSONString(this, SerializerFeature.WriteNonStringKeyAsString);
    }

    public void submit() {
        LogService.submit(this);
    }
}
