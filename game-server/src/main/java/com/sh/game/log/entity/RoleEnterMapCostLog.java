package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 进入地图消耗日志
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-12-24
 **/
@Getter
@Setter
public class RoleEnterMapCostLog extends BaseRoleLog {

    public RoleEnterMapCostLog() {

    }

    /**
     * 账户
     */
    private String account;


    /**
     * 角色id
     */
    private long roleId;


    /**
     * 地图
     */
    private int mapCfgId;

    /**
     * 消耗物
     */
    List<int[]> cost;

    /**
     * 时间
     */
    private int time;

    public RoleEnterMapCostLog(Role role) {
        super(role);
    }
}
