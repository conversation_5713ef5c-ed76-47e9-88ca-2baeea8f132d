package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoleFuHunLog extends BaseRoleLog {

    //装备唯一id
    private long uniqueId;

    //装备配置id
    private int equipCfgId;

    //宝石唯一id
    private long gemId;

    //宝石配置id
    private int gemCfgId;

    public RoleFuHunLog(Role role){
        super(role);
    }

    public RoleFuHunLog(){}
}
