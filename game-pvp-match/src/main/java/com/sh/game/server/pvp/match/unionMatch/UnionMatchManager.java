package com.sh.game.server.pvp.match.unionMatch;


import com.sh.game.common.communication.msg.pvp.*;
import com.sh.game.common.communication.msg.pvp.bean.BattleUnionBean;
import com.sh.game.common.communication.msg.pvp.bean.MatchResultBean;
import com.sh.game.common.communication.msg.pvp.bean.SampleUnionBean;
import com.sh.game.common.communication.msg.pvp.bean.UnionStartUpBean;
import com.sh.game.common.constant.MatchConst;
import com.sh.game.server.pvp.match.BattleServerManager;
import com.sh.game.server.pvp.match.GameServer;
import com.sh.game.server.pvp.match.bserver.BattleServer;
import com.sh.game.server.pvp.match.bserver.ServerInfo;
import com.sh.game.server.pvp.match.unionMatch.entity.UnionBattle;
import com.sh.game.server.pvp.match.unionMatch.entity.UnionMatchBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 跨服帮战 匹配管理器
 */
@Slf4j
public class UnionMatchManager {

    private static final UnionMatchManager _instance = new UnionMatchManager();

    public static final UnionMatchManager getInstance() {
        return _instance;
    }

    private int battleId = 1;

    /**
     * key = matchGroup
     * value = 所有报名的帮会
     */
    public Map<Integer, Map<Long,UnionMatchBean>> unionPool = new ConcurrentHashMap<>();

    /**
     * 已经组成匹配的
     */
    public Map<Long, List<UnionMatchBean>> ridAndUnionList = new ConcurrentHashMap<>();

    /**
     * 已经创建完成的战斗
     */
    public List<UnionBattle> battleList = new ArrayList<>();

    private UnionMatchManager() {

    }

    //开始报名
    public void startSignUp(ReqStartSignUpMessage msg) {
        int matchGroup = msg.getMatchGroup();
        long uId = msg.getUId();
        int matchType = msg.getMatchType();
        long leaderId = msg.getLeaderId();
        long startUpLeaderId = msg.getStartUpLeader();
        String serverName = msg.getServerName();
        Map<Long, UnionMatchBean> orDefault = unionPool.getOrDefault(matchGroup, new HashMap<>());
        if (orDefault.containsKey(uId)) {
            ResUnionMatchProssMessage message = new ResUnionMatchProssMessage();
            List<Long> list = new ArrayList<>();
            list.add(uId);
            if (startUpLeaderId > 0) {
                message.setLeaderId(startUpLeaderId);
            } else {
                message.setLeaderId(leaderId);
            }
            message.setUnionId(list);
            message.setReason("报名成功!");
            message.setProcess(MatchConst.UnionMatchConst.PROCESSCODE_SUCESS);
            msg.getSession().sendMessage(message);

            UnionMatchBean matchBean = orDefault.get(uId);
            ServerInfo server = (ServerInfo) msg.getSession().getValue();
            if (matchBean.getHostId() != server.getHostId()) {
                matchBean.setHostId(server.getHostId());
                log.info("帮会:【{}】->【{}】,更新hostId成功,新hostId为:【{}】", matchBean.getUnionId(), matchBean.getUnionName(), matchBean.getHostId());
            }
            return;
        }
        //检查支持的pvp类型
        if (!MatchConst.PVPType.contains(matchType)) {
            log.info("不支持的pvp类型:{}", matchType);
            return;
        }
        if (battleList.size() > 0) {
            ResUnionMatchProssMessage message = new ResUnionMatchProssMessage();
            message.setProcess(MatchConst.UnionMatchConst.PROCESSCODE_ERR);
            message.setReason("报名已经结束!");
            msg.getSession().sendMessage(message);
            return;
        }
        UnionMatchBean bean = new UnionMatchBean();
        ServerInfo server = (ServerInfo) msg.getSession().getValue();
        bean.setHostId(server.getHostId());
        bean.setServerName(serverName);
        bean.setScore(msg.getUnionScore());
        bean.setTotalPower(msg.getTotalPower());
        bean.setUnionId(uId);
        bean.setMatchGroup(matchGroup);
        bean.setUnionName(msg.getUnionName());
        bean.setMatchType(msg.getMatchType());
        bean.setLeaderName(msg.getLeaderName());
        bean.setUnionLevel(msg.getUnionLevel());
        bean.setCreatePlayerTime(msg.getPlayerCreateDays());
        if (unionPool.containsKey(matchGroup)) {
            Map<Long, UnionMatchBean> unionMatchBeanMap = unionPool.get(bean.getMatchGroup());
            unionMatchBeanMap.put(bean.getUnionId(), bean);
        } else {
            Map<Long, UnionMatchBean> unionMatchBeanMap = new HashMap<>();
            unionMatchBeanMap.put(bean.getUnionId(), bean);
            unionPool.put(matchGroup, unionMatchBeanMap);
        }

        ResUnionMatchProssMessage message = new ResUnionMatchProssMessage();
        List<Long> list = new ArrayList<>();
        list.add(uId);
        if (startUpLeaderId > 0) {
            message.setLeaderId(startUpLeaderId);
        } else {
            message.setLeaderId(leaderId);
        }
        message.setUnionId(list);
        message.setReason("报名成功!");
        message.setProcess(MatchConst.UnionMatchConst.PROCESSCODE_SUCESS);
        msg.getSession().sendMessage(message);
        log.info("帮会【{}】->【{}】,报名成功!，战力【{}】。创角天数:【{}】", bean.getUnionId(), bean.getUnionName(), bean.getTotalPower(), bean.getCreatePlayerTime());
    }

    public void cancelSignUp(ReqCancleSignUpMessage msg){
        if(ridAndUnionList.containsKey(msg.getUId())){
            ResCancelSignUpMessage message = new ResCancelSignUpMessage();
            message.setReason("已匹配成功不能取消!");
            message.setLeaderId(msg.getLeaderId());
            message.setUnionId(msg.getUId());
            msg.getSession().sendMessage(message);

            return;
        }
        Map<Long, UnionMatchBean> orDefault = unionPool.getOrDefault(msg.getMatchGroup(), new HashMap<>());
        if(!orDefault.containsKey(msg.getUId())){
            return;
        }
        orDefault.remove(msg.getUId());
        ResCancelSignUpMessage message = new ResCancelSignUpMessage();
        message.setReason("已取消!");
        message.setLeaderId(msg.getLeaderId());
        message.setUnionId(msg.getUId());
        message.setState(true);
        msg.getSession().sendMessage(message);
        log.info("帮会【{}】,取消报名", message.getUnionId());
    }

    public void startMatch(){
        //判断当前时间 是否是匹配的时间
        LocalDateTime time = LocalDateTime.now();
        clearCatchMatchData(time);

        boolean duringDailyActivity = isMatchResultTime(time);

        if(!duringDailyActivity){
            return;
        }
        log.info("------------------群雄逐鹿匹配结果生成---------------");
        if(unionPool.size()<=0){
            return;
        }
        if(battleList.size()>0){
            battleList.clear();
        }
        //分组
        Map<Integer, List<UnionMatchBean>> one = new HashMap<>();
        Map<Integer, List<UnionMatchBean>> two = new HashMap<>();
        Map<Integer, List<UnionMatchBean>> three = new HashMap<>();
        Map<Integer, List<UnionMatchBean>> four = new HashMap<>();

        List<Integer> limitList = new ArrayList<>();
        for (MatchConst.matchLimits type : MatchConst.matchLimits.values()) {
            limitList.add(type.type);
        }
        Collections.sort(limitList);
        for (Map.Entry<Integer, Map<Long, UnionMatchBean>> entry : unionPool.entrySet()) {
            List<UnionMatchBean> group45 = new ArrayList<>();
            List<UnionMatchBean> group76 = new ArrayList<>();
            List<UnionMatchBean> group106 = new ArrayList<>();
            List<UnionMatchBean> group135 = new ArrayList<>();
            for (UnionMatchBean bean : entry.getValue().values()) {
                if (bean.getCreatePlayerTime() >= limitList.get(0) && bean.getCreatePlayerTime() < limitList.get(1)) {
                    group45.add(bean);
                } else if (bean.getCreatePlayerTime() >= limitList.get(1) && bean.getCreatePlayerTime() < limitList.get(2)) {
                    group76.add(bean);
                } else if (bean.getCreatePlayerTime() >= limitList.get(2) && bean.getCreatePlayerTime() < limitList.get(3)) {
                    group106.add(bean);
                } else if (bean.getCreatePlayerTime() >= limitList.get(3)) {
                    group135.add(bean);
                } else if (bean.getCreatePlayerTime() < limitList.get(0)) {
                    group45.add(bean);
                }
            }
            one.put(entry.getKey(), group45);
            two.put(entry.getKey(), group76);
            three.put(entry.getKey(), group106);
            four.put(entry.getKey(), group135);
        }
        Map<Integer, List<UnionMatchBean>> residueMap = new HashMap<>();
        int countRecord = 0;
        for (List<UnionMatchBean> beans : one.values()) {
            match(beans, residueMap);
            countRecord += beans.size();
        }
        log.info("45-75天组 匹配成功！总帮会数【{}】", one.size());
        for (List<UnionMatchBean> beans : two.values()) {
            match(beans, residueMap);
            countRecord += beans.size();
        }
        log.info("75-105天组 匹配成功！总帮会数【{}】", two.size());
        for (List<UnionMatchBean> beans : three.values()) {
            match(beans, residueMap);
            countRecord += beans.size();
        }
        log.info("105-135天组 匹配成功！总帮会数【{}】", three.size());
        for (List<UnionMatchBean> beans : four.values()) {
            match(beans, residueMap);
            countRecord += beans.size();
        }
        log.info("135以上天数组 匹配成功！总帮会数【{}】", four.size());

        for (List<UnionMatchBean> beans : residueMap.values()) {
            Map<Integer, List<UnionMatchBean>> residueMapTemp = new HashMap<>();
            match(beans, residueMapTemp);
            sendResidueBean(residueMapTemp);
            log.info("将筹不够队伍的帮会重新组合后还有【{}】未能匹配", new ArrayList<>(residueMapTemp.values()));
        }
        log.info("当前总参与帮会数量:【{}】", countRecord);
        sendMatchResultInfoMessage();
    }

    public void match(List<UnionMatchBean> unionMatchBeans, Map<Integer, List<UnionMatchBean>> residueMap) {
        Collections.sort(unionMatchBeans);
        List<UnionMatchBean> unionMatchBeansTemp = new ArrayList<>();
        unionMatchBeansTemp.addAll(unionMatchBeans);
        int part = unionMatchBeans.size() / MatchConst.UnionMatchConst.MAXMATCHCOUNT;
        int subNum = unionMatchBeans.size() > MatchConst.UnionMatchConst.MAXMATCHCOUNT ? MatchConst.UnionMatchConst.MAXMATCHCOUNT : unionMatchBeans.size();

        for (int i = 0; i < part; i++) {
            List<UnionMatchBean> list = unionMatchBeansTemp.subList(0, subNum);
            for (UnionMatchBean bean : list) {
                ridAndUnionList.put(bean.getUnionId(), list);
            }
            UnionBattle battle = new UnionBattle();
            battle.setId(this.getBattleId());
            battle.setMatchType(MatchConst.PVPType._UnionPK);
            battle.getUnionList().addAll(list);
            battleList.add(battle);
            unionMatchBeansTemp.subList(0, subNum).clear();
        }
        //将每个组筹不够的记录起来
        if (unionMatchBeansTemp.size() > 0) {
            UnionMatchBean bean = unionMatchBeansTemp.get(0);
            if (residueMap.containsKey(bean.getMatchGroup())) {
                residueMap.get(bean.getMatchGroup()).addAll(unionMatchBeansTemp);
            } else {
                residueMap.put(bean.getMatchGroup(), unionMatchBeansTemp);
            }
            log.info("剩余参战数量不足的帮会记录成功!--->【{}】", unionMatchBeansTemp);
        }
    }

    public void sendResidueBean(Map<Integer, List<UnionMatchBean>> unionMatchBeansTemp) {
        for (List<UnionMatchBean> beans : unionMatchBeansTemp.values()) {
            for (UnionMatchBean bean : beans) {
                ResUnionMatchProssMessage msg = new ResUnionMatchProssMessage();
                msg.getUnionId().add(bean.getUnionId());
                msg.setProcess(MatchConst.UnionMatchConst.PROCESSCODE_MATCH_ERR);
                ServerInfo server = BattleServerManager.getInstance().findGameServer(bean.getHostId());
                if (server != null) {
                    server.getSession().sendMessage(msg);
                    log.info("帮会:【{}】->【{}】,参战帮会数量不足未能成功匹配！", bean.getUnionId(), bean.getUnionName());
                } else {
                    log.error("帮会:【{}】->【{}】,未找到Host:【{}】的链接！,匹配失败信息发送失败!", bean.getUnionId(), bean.getUnionName(), bean.getHostId());
                }
            }
        }
    }

    public void tryCreateBattle(){
        if (!isStartTime(LocalDateTime.now())) {
            return;
        }
        log.info("--------群雄逐鹿开始创建战斗-------");
        int index = 0;
        for (UnionBattle battle : battleList){
            Pair<Integer, BattleServer> pair = BattleServerManager.getInstance().findBattleByIndex(battle.getMatchType(), ++ index);
            if (pair == null) {
                log.info("找不到可用的战斗服,battleId:{},unionList:{}",battle.getUnionList());
                //通知游戏服
                List<ServerInfo> servers = new ArrayList<>();
                battle.getServerList(servers);
                //发送给指定服务器
                for (ServerInfo server : servers){
                    List<UnionMatchBean> unionList = battle.getUnionList();
                    List<UnionMatchBean> collect = unionList.stream().filter(unionMatchBean -> unionMatchBean.getHostId() == server.getHostId()).collect(Collectors.toList());
                    List<Long> list = new ArrayList<>();
                    for (UnionMatchBean bean :collect){
                        list.add(bean.getUnionId());
                    }
                    ResUnionMatchProssMessage msg = new ResUnionMatchProssMessage();
                    msg.setUnionId(list);
                    msg.setProcess(MatchConst.UnionMatchConst.PROCESSCODE_ERR);
                    msg.setReason("服务器异常，匹配失败!");
                    server.getSession().sendMessage(msg);
                }
                continue;
            }
            index = pair.getKey();
            battle.setMatched(true);
            battle.setHostId(pair.getValue().getHostId());
            ReqCreateBattleByUnionMessage message = new ReqCreateBattleByUnionMessage();
            message.setInnerMapId(MatchConst.UnionMatchConst.PALACE_INNER_MAPID);
            message.setOutMapId(MatchConst.UnionMatchConst.PALACE_OUT_MAPID);
            message.setBattleId(battle.getId());
            pair.getValue().getSession().sendMessage(message);
            try {
                Thread.sleep(300L);
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.currentThread().interrupt();
            }
        }
    }

    public void afterCreateBattle(ResCreatUnionBattleMessage msg){
        //设置 battle 信息 副本id
        int battleId = msg.getBattleId();
        int innerMapId = msg.getInnerMapId();
        int outMapId = msg.getOutMapId();
        Optional<UnionBattle> any = battleList.stream().filter(unionBattle -> unionBattle.getId() == battleId).findFirst();
        if(!any.isPresent()){
            log.error("战斗创建失败!,未找到缓存的战力对象");
            return;
        }
        UnionBattle battle = any.get();
        battle.setInnerMapId(innerMapId);
        battle.setOutMapId(outMapId);
        BattleServer battleServer = BattleServerManager.getInstance().findBattleServerById(battle.getHostId());

        //发送消息
        ResCreateSucessMessage message = new ResCreateSucessMessage();
        List<ServerInfo> list = new ArrayList<>();
        battle.getServerList(list);
        List<UnionMatchBean> unionList = battle.getUnionList();
        List<SampleUnionBean> sampleUnionBeans = new ArrayList<>();
        for (UnionMatchBean bean : unionList){
            SampleUnionBean unionBean = new SampleUnionBean();
            unionBean.setLeaderName(bean.getLeaderName());
            unionBean.setServerName(bean.getServerName());
            unionBean.setUnionName(bean.getUnionName());
            unionBean.setUnionLevel(bean.getUnionLevel());
            unionBean.setUnionId(bean.getUnionId());
            sampleUnionBeans.add(unionBean);
        }

        for (UnionMatchBean bean : unionList){
            BattleUnionBean battleUnionBean = new BattleUnionBean();
            battleUnionBean.setIp(battleServer.getIp());
            battleUnionBean.setPort(battleServer.getPort());
            battleUnionBean.setUnionId(bean.getUnionId());
            battleUnionBean.setMatchType(MatchConst.PVPType._UnionPK);
            battleUnionBean.setHostId(battle.getHostId());
            battleUnionBean.setInnerMapId(innerMapId);
            battleUnionBean.setOutMapId(outMapId);
            battleUnionBean.setSampleUnionBean(sampleUnionBeans);
            message.getBattleUnionBean().add(battleUnionBean);
        }
        Map<Integer, List<UnionMatchBean>> collect = unionList.stream().collect(Collectors.groupingBy(UnionMatchBean::getHostId));
        for (Integer hostId : collect.keySet()) {
            GameServer server = BattleServerManager.getInstance().findGameServer(hostId);
            if (server != null) {
                server.getSession().sendMessage(message);
            }
        }
        log.info("群雄逐鹿副本创建成功,帮会信息:【{}】",unionList);
    }

    private boolean isStartTime(LocalDateTime time) {
        int nowMinute = time.getHour() * 60 + time.getMinute();
        int startTime = 1199;
        int weekDay = 7;

        if (nowMinute == startTime
                && weekDay == (time.getDayOfWeek().getValue())) {
            return true;
        }
        return false;
    }

    private boolean isMatchResultTime(LocalDateTime time) {
        int nowMinute = time.getHour() * 60 + +time.getMinute();
        int startTime = 1139;
        int weekDay = 7;

        if (nowMinute == startTime
                && weekDay == (time.getDayOfWeek().getValue())) {
            return true;
        }
        return false;
    }

    /**
     * 活动结束 定时检测清理缓存数据
     * 晚上9点清理数据
     * @param time
     */
    private void clearCatchMatchData(LocalDateTime time){

        int nowMinute = time.getHour() * 60;

        int overTime = 1260;

        if (nowMinute + time.getMinute() > overTime) {
            if (unionPool.size() > 0) {
                unionPool.clear();
                battleList.clear();
                ridAndUnionList.clear();
                log.info("------------群雄逐鹿报名缓存数据清理完成！--------------");
            }
        }
    }

    /**
     *  游戏服 匹配服 断线重连
     */
    public void resetMatchInfo(ReqResetMatchInfoMessage msg){
        int matchGroup = msg.getMatchGroup();
        String serverName = msg.getServerName();
        List<UnionStartUpBean> StartUpBeans = msg.getUnionStartUpBean();
        //如果unionPool 为空 说明匹配服重启了
        if(unionPool.size()<=0){
                //如果不在报名时间内 需要更新unionPool
            Map<Long, UnionMatchBean> matchBeanMap = new HashMap<>();
            unionPool.put(matchGroup,matchBeanMap);
            for (UnionStartUpBean bean : StartUpBeans){
                UnionMatchBean matchBean = new UnionMatchBean();
                matchBean.setServerName(serverName);
                matchBean.setHostId(bean.getHostId());
                matchBean.setMatchGroup(matchGroup);
                matchBean.setUnionLevel(bean.getLevel());
                matchBean.setLeaderName(bean.getGuildLeaderName());
                matchBean.setScore(bean.getUnionScore());
                matchBean.setTotalPower(bean.getTotalPower());
                matchBean.setMatchType(MatchConst.PVPType._UnionPK);
                matchBean.setUnionName(bean.getUnionName());
                matchBean.setUnionId(bean.getUId());
                matchBeanMap.put(matchBean.getUnionId(),matchBean);
            }
            log.info("重置报名数据成功,帮会数据：【{}】",matchBeanMap);
        }else{
            //如果还没匹配战斗 只需要修改 unionPool
            if(battleList.size()<=0){
                Map<Long, UnionMatchBean> unionMatchBeanMap = unionPool.get(matchGroup);
                if (unionMatchBeanMap != null && unionMatchBeanMap.size() > 0) {
                    for (UnionMatchBean matchBean :unionMatchBeanMap.values()){
                        UnionStartUpBean startUpBean = StartUpBeans.stream().filter(unionStartUpBean ->
                                unionStartUpBean.getUId() == matchBean.getUnionId()).findFirst().orElse(null);
                        if(startUpBean!=null){
                            matchBean.setServerName(serverName);
                            matchBean.setHostId(startUpBean.getHostId());
                        }
                        log.info("【{}】更新报名数据成功",matchBean);
                    }
                }
            }else{ //如果已经匹配战斗了 battleList需要修改
                for (UnionBattle battle: battleList){
                    List<UnionMatchBean> unionList = battle.getUnionList();
                    for (UnionMatchBean matchBean : unionList){
                        UnionStartUpBean startUpBean = StartUpBeans.stream().filter(unionStartUpBean ->
                                unionStartUpBean.getUId() == matchBean.getUnionId()).findFirst().orElse(null);
                        if(startUpBean!=null){
                            matchBean.setHostId(startUpBean.getHostId());
                            matchBean.setServerName(serverName);
                        }
                        log.info("【{}】更新战斗数据成功",matchBean);
                    }
                }
            }
        }
    }

    public void sendMatchResultInfoMessage() {
        for (UnionBattle battle : battleList) {
            List<UnionMatchBean> unionList = battle.getUnionList();
            List<MatchResultBean> list = new ArrayList<>();
            for (UnionMatchBean bean : unionList) {
                MatchResultBean matchResultBean = new MatchResultBean();
                matchResultBean.setLeaderName(bean.getLeaderName());
                matchResultBean.setServerName(bean.getServerName());
                matchResultBean.setUnionName(bean.getUnionName());
                list.add(matchResultBean);
            }
            for (UnionMatchBean bean : unionList) {
                GameServer server = BattleServerManager.getInstance().findGameServer(bean.getHostId());
                if (server == null) {
                    log.error("群雄逐鹿副本创建失败!，帮会信息:【{}】", bean);
                    continue;
                }
                ResPvpSBKMatchResultMessage msg = new ResPvpSBKMatchResultMessage();
                msg.setMatchResultBean(list);
                msg.setUnionId(bean.getUnionId());
                server.getSession().sendMessage(msg);
            }
        }
    }

    private int getBattleId(){
        return this.battleId++ ;
    }


    public void testMatch(){
        startMatch();
        //开始 创建战斗
        tryCreateBattle();
    }
}
