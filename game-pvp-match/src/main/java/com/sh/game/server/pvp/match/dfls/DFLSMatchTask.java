package com.sh.game.server.pvp.match.dfls;

import com.sh.concurrent.AbstractCommand;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2022/10/24 16:56
 */
@Slf4j
public class DFLSMatchTask extends AbstractCommand {


    @Override
    public void doAction() {
        DFLSMatchManager.getInstance().match();
        DFLSMatchManager.getInstance().createMap();
    }
}
