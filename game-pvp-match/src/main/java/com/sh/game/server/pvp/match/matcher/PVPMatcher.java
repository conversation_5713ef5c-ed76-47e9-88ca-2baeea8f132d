package com.sh.game.server.pvp.match.matcher;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.server.pvp.match.bean.Battle;
import com.sh.game.server.pvp.match.bean.MatchGroup;

import java.util.List;

/**
 *
 * PVP匹配服务器
 * 1、接受数据
 * 2、匹配算法
 * 3、分配服务器
 * 4、返回服务器id
 * 5、战斗服注册
 * 6、战斗服管理
 * <AUTHOR>
 * @date 2019/7/30 10:10
 */

/**

 */
public interface PVPMatcher {

    /**
     * 匹配组输入
     * @param group
     */
    void in(MatchGroup group);

    /**
     * 匹配逻辑，会被调度器多次调用完成匹配
     */
    void match() ;

    /**
     * 匹配成功输出结果
     */
    List<Battle> out();

    /**
     * 检查合法性
     * @param group
     * @return
     */
    TwoTuple<Boolean, String> check(MatchGroup group);

    /**
     * 取消匹配
     * @param group
     * @return
     */
    boolean cancel(MatchGroup group);

    /**
     * 当游戏服和匹配服断开连接时
     * 清空匹配器中残留数据
     */
    void clean(int hostId);

}
