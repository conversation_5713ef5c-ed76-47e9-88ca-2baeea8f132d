package com.sh.game.server.pvp.match.process;

import com.sh.concurrent.IQueueDriverCommand;
import com.sh.concurrent.QueueDriver;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.notice.NoticePool;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.server.CommandProcessor;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/11.
 */
@Deprecated
public class PVPProcessor extends CommandProcessor {

    private final Executor executor = Executors.newSingleThreadExecutor(r -> new Thread(r, "PVP通信线程"));

    @Override
    public byte id() {
        return ProcessorId._11_PVP;
    }

    @Override
    public void process(IQueueDriverCommand command, long key) {
        executor.execute(command);
        super.process(command, key);
    }

    @Override
    public void process(ProcessNotice notice, long key) {
//        NoticeAction action = register.getAction(notice.id());
//        if (action == null) {
//            log.error("找不到对应的action：{}", notice.getClass().getName());
//            return;
//        }
//        action.setNotice(notice);
//        process(action, key);
    }



    @Override
    protected QueueDriver getDriver(long key) {
        return null;
    }

    @Override
    protected NoticePool getNoticePool() {
        return null;
    }
}
