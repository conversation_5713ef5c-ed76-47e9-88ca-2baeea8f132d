package com.sh.game.server.pvp.match.dfls.entity;

import com.alibaba.fastjson.JSON;
import com.sh.game.common.communication.notice.logic.player.DFLSJuanXianJieSuanRetNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.match.DFLSJuanXianInfo;
import com.sh.game.server.pvp.match.BattleServerManager;
import com.sh.game.server.pvp.match.MatchContext;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/15 21:12
 */
@Getter
@Setter
@Slf4j
public class DFLSJiangJin {

    private int dflsGroup;

    /**
     * 灵符奖池
     */
    private long lingFuJiangChi;

    public void addJiangJin(int count) {

        if (getLingFuJiangChi() >= 3000000) {
            return;
        }

        int rate = 10000;
        if (getLingFuJiangChi() > 500000) {
            rate = 5000;
        }
        if (getLingFuJiangChi() > 1000000) {
            rate = 2500;
        }
        if (getLingFuJiangChi() > 2000000) {
            rate = 100;
        }

        int finalCount = (int) (count / 10000D * rate);
        this.lingFuJiangChi += finalCount;

        log.info("巅峰联赛，奖金池更新，巅峰联赛组 {} 本次更新值 {} 缩减比例 {} 更新后奖金 {}", dflsGroup, count, rate, this.lingFuJiangChi);
    }
}
