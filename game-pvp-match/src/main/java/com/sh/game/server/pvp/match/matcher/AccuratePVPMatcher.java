package com.sh.game.server.pvp.match.matcher;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.server.pvp.match.*;
import com.sh.game.server.pvp.match.bean.Battle;
import com.sh.game.server.pvp.match.bean.MatchGroup;
import com.sh.game.server.pvp.match.bean.MatchTeam;
import com.sh.game.server.pvp.match.bean.Player;
import com.sh.game.server.pvp.match.matcher.constent.MatchConst;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * author : zhouzijing
 * Date: 2019/12/7 9:59
 */
@Slf4j
public class AccuratePVPMatcher implements PVPMatcher {

    //匹配状态
    private int matchState;

    private int groupPlayerCount;

    //pvp类型
    private int pvpType;

    //pvp地图id
    private int mapId;

    //组队战斗力匹配上边界
    private int groupUpOffset = 1000000;
    //组队战斗力匹配下边界
    private int groupDownOffset = 1000000;
    //组队每秒时间加权
    private int groupTimeWeight = 100000;

    //对战战斗力匹配上边界
    private int matchUpOffset = 200000;
    //对战战斗力匹配下边界
    private int matchDownOffset = 200000;

    //对战每秒时间加权
    private int matchTimeWeight = 100000;

    //每个玩家的原生组
    private Map<Long, MatchGroup> playerGroupMap= new HashMap<>();

    //等待缓冲区队伍
    private Map<Long, MatchGroup> readyBufferGroups = new HashMap<>();

    //等待区队伍
    private Map<Long, MatchGroup> readyGroups = new HashMap<>();

    //匹配区队伍
    private  Map<Long, MatchGroup> matchGroups = new TreeMap<>();

    private List<Battle> battleList = new ArrayList<>();


    /**
     *  所有红队
     */
    private Set<MatchTeam> redTeams = new HashSet<>();


    /**
     *  匹配出一个红队， 剩余所有队伍能组成的蓝队所有可能
     */
    private Map<MatchTeam, Set<MatchTeam>> blueTeams = new HashMap<>();

    /**
     * 匹配中队伍取消匹配状态的队伍
     */
    private Map<Long, MatchGroup> cancelGroup = new HashMap<>();



    public AccuratePVPMatcher(int groupPlayerCount,int pvpType, int mapId,
                              int groupUpOffset, int groupDownOffset, int groupTimeWeight,
                              int matchUpOffset, int matchDownOffset, int matchTimeWeight
    ) {
        this.groupPlayerCount = groupPlayerCount;
        this.groupUpOffset = groupUpOffset;
        this.groupDownOffset = groupDownOffset;
        this.groupTimeWeight = groupTimeWeight;
        this.matchUpOffset = matchUpOffset;
        this.matchDownOffset = matchDownOffset;
        this.matchTimeWeight = matchTimeWeight;
        this.pvpType = pvpType;
        this.mapId = mapId;
        this.matchState = MatchConst.MatchState.readying;
    }

    public AccuratePVPMatcher(int groupPlayerCount,int pvpType, int mapId ) {
        this.groupPlayerCount = groupPlayerCount;
        this.pvpType = pvpType;
        this.mapId = mapId;
        this.matchState = MatchConst.MatchState.readying;
    }

    @Override
    public void in(MatchGroup group) {
        //如果匹配状态在准备中，将队伍放到等待匹配缓冲区
        if (this.matchState == MatchConst.MatchState.readying){
            readyBufferGroups.put(group.getId(), group);
        }
        if (this.matchState == MatchConst.MatchState.matching){
            readyGroups.put(group.getId(), group);
        }

        for (Player player : group.getPlayerList()){
            playerGroupMap.put(player.getId(), group);
        }


    }

    @Override
    public void match() {
        //todo 这里可以放一个区间， 比如一开始匹配池的队伍必须50个才能匹配随着时间增长而缩小这个池子大小以实现精准匹配的效果

        //改变匹配状态
        this.matchState = MatchConst.MatchState.matching;

        //等待缓冲区放入等待区
        readyGroups.putAll(readyBufferGroups);
        readyBufferGroups.clear();
        battleList.clear();

        log.info("正在匹配池中的队伍为:{}", matchGroups.size());

        for (Map.Entry<Long, MatchGroup> entry : matchGroups.entrySet()){
            MatchTeam team = new MatchTeam();
            buildTeamA(team, entry.getKey() - 1);
        }
        //最小战力差
        long minDiff = 999999999999L;

        if (!blueTeams.isEmpty()) {
            Battle bestBattle = new Battle();
            for (Map.Entry<MatchTeam, Set<MatchTeam>> entry : blueTeams.entrySet()) {

                for (MatchTeam team : entry.getValue()) {
                    long abs = Math.abs(entry.getKey().getFightPower() - team.getFightPower());
                    if (abs < minDiff) {
                        minDiff = abs;
                        bestBattle.setA(entry.getKey());
                        bestBattle.setB(team);
                        bestBattle.setMapCfgId(this.mapId);
                        bestBattle.setPvpType(this.pvpType);
                    }
                }

            }

            //成功后 且两个Team里的队伍都没有取消过匹配
            boolean start = true;
            for (MatchGroup group : bestBattle.getA().getGroupList()){
                if (cancelGroup.containsKey(group.getId())){
                    start = false;
                    break;
                }
            }
            for (MatchGroup group : bestBattle.getB().getGroupList()){
                if (cancelGroup.containsKey(group.getId())){
                    start = false;
                    break;
                }
            }
            if (start) {

                boolean result = true;
                //匹配池中挪出
                for (MatchGroup group : bestBattle.getA().getGroupList()){
                    MatchGroup remove = matchGroups.remove(group.getId());
                    if (remove == null){
                        log.error("队伍A没被移除");
                        result = false;
                    }
                    for (Player player : group.getPlayerList()){
                        playerGroupMap.remove(player.getId());
                    }
                }
                for (MatchGroup group : bestBattle.getB().getGroupList()){
                    MatchGroup remove = matchGroups.remove(group.getId());
                    if (remove == null){
                        log.error("队伍B没被移除");
                        result = false;
                    }
                    for (Player player : group.getPlayerList()){
                        playerGroupMap.remove(player.getId());
                    }
                }

                if (result) {
                    bestBattle.setId(MatchManager.getInstance().getBattleId());
                    log.info("匹配成功:battleId->{},{}vs{}", bestBattle.getId(), bestBattle.getA().info(), bestBattle.getB().info());
                    battleList.add(bestBattle);
                    log.info("TeamA的总战斗力为:{}", bestBattle.getA().getFightPower());
                    log.info("TeamB的总战斗力为:{}", bestBattle.getB().getFightPower());
                    int diffFight = Math.abs(bestBattle.getA().getFightPower() - bestBattle.getB().getFightPower());
                    log.info("两者相差战力差为:{}", diffFight);
                }
            }
        }

        this.redTeams.clear();
        this.blueTeams.clear();
        this.matchState = MatchConst.MatchState.readying;
        //将等待区队伍放入匹配池中
        this.matchGroups.putAll(readyGroups);
        this.readyGroups.clear();
        //清除  取消匹配池 里在匹配池的队伍
        for (Map.Entry<Long, MatchGroup> entry : cancelGroup.entrySet()){
            if (matchGroups.containsKey(entry.getKey())){
                matchGroups.remove(entry.getKey());
                for (Player player : entry.getValue().getPlayerList()){
                    playerGroupMap.remove(player.getId());
                }
            }
        }
        cancelGroup.clear();
    }

    /**
     * 检查是否有重复之类的逻辑，以新的为主，发下重复的team直接取消匹配
     * @param group
     * @return
     */
    @Override
    public TwoTuple<Boolean, String> check(MatchGroup group) {

        //重找重复匹配的玩家的组
        List<MatchGroup> repeatedList = new ArrayList<>();
        for (Player player : group.getPlayerList()){
            MatchGroup repeatedGroup = playerGroupMap.get(player.getId());
            if (repeatedGroup != null) {
                log.info("发现重复匹配：playerId:{}, groupId:{}", player.getId(), group.getId());
                repeatedList.add(repeatedGroup);
            }
        }


        //取消重复匹配的组
        if (!repeatedList.isEmpty()) {
            for (MatchGroup repeatedGroup : repeatedList) {
                if (matchGroups.containsKey(repeatedGroup.getId())){
                    cancelGroup.put(repeatedGroup.getId(), repeatedGroup);
                    return new TwoTuple<>(false, "请稍后再试");
                }
                if (readyGroups.containsKey(repeatedGroup.getId())){
                    readyGroups.remove(repeatedGroup.getId());
                    for (Player player : repeatedGroup.getPlayerList()){
                        playerGroupMap.remove(player.getId());
                    }
                }

                if (readyBufferGroups.containsKey(repeatedGroup.getId())){
                    readyBufferGroups.remove(repeatedGroup.getId());
                    for (Player player : repeatedGroup.getPlayerList()){
                        playerGroupMap.remove(player.getId());
                    }
                }
            }
        }
        return new TwoTuple<>(true, "开始匹配.");
    }






    /**
     * 组队战力上下边界（组成一个队伍）
     * @param time
     * @return
     */
    @SuppressWarnings("Duplicates")
    public TwoTuple<Integer, Integer> groupFightPowerRange(MatchTeam team, long time) {


        int waitTime = (int) ((time - team.getStartTime()) / 1000);

        int curGroupTimeWeight;
        if(waitTime <= 10) {
            curGroupTimeWeight = 100000;
        } else if (waitTime <= 20) {
            curGroupTimeWeight = 300000;
        } else if (waitTime <= 30) {
            curGroupTimeWeight = 500000;
        } else {
            curGroupTimeWeight = 800000;
        }
        int up = Math.min(team.getAvgFightPower() + groupUpOffset + waitTime * curGroupTimeWeight, Integer.MAX_VALUE);
        int down = Math.max(team.getAvgFightPower() - groupDownOffset - waitTime * curGroupTimeWeight, 0);
        if (down < 0) {
            down = 0;
        }

        return new TwoTuple<>(down, up);
    }

    /**
     * 战斗匹配上下边界（组成比赛的双方）
     * @param team
     * @param time
     * @return
     */
    @SuppressWarnings("Duplicates")
    public TwoTuple<Integer, Integer> matchFightPowerRange(MatchTeam team, long time) {

        int waitTime = (int) ((time - team.getStartTime()) / 1000);

        int curGroupTimeWeight;
        if(waitTime <= 10) {
            curGroupTimeWeight = 100000;
        } else if (waitTime <= 20) {
            curGroupTimeWeight = 300000;
        } else if (waitTime <= 30) {
            curGroupTimeWeight = 500000;
        } else {
            curGroupTimeWeight = 1000000;
        }

        int up = Math.min(team.getFightPower() + matchUpOffset + waitTime * curGroupTimeWeight, Integer.MAX_VALUE);
        int down = Math.max(team.getFightPower() - matchDownOffset - waitTime * curGroupTimeWeight, 0);
        if (down < 0) {
            down = 0;
        }
        return new TwoTuple<>(down, up);
    }

    /**
     * 两个group是否可以组队
     * @param t1
     * @param t2
     * @return
     */
    public boolean canMerge(MatchTeam t1, MatchTeam t2) {
        if (t1.getPlayerList().size() + t2.getPlayerList().size() > groupPlayerCount) {
            return false;
        }
        return true;
    }

    public boolean canMerge(MatchTeam team, MatchGroup group){
        if (team.getPlayerList().size() + group.getPlayerList().size() > groupPlayerCount) {
            return false;
        }
        for (MatchGroup group1 : team.getGroupList()){
            if (group1.getId() == group.getId()){
                return false;
            }
        }
        return true;
    }




    @Override
    public List<Battle> out() {
        return battleList;
    }

    @Override
    public boolean cancel(MatchGroup group) {
        //如果是在匹配池中队伍
        if (matchGroups.containsKey(group.getId())){
            cancelGroup.put(group.getId(), group);
        }
        //如果是在等待区 和缓冲等待区 直接移除
        if (readyGroups.containsKey(group.getId())){
            readyGroups.remove(group.getId());
            for (Player player : group.getPlayerList()){
                playerGroupMap.remove(player.getId());
            }
        }
        if (readyBufferGroups.containsKey(group.getId())){
            readyBufferGroups.remove(group.getId());
            for (Player player : group.getPlayerList()){
                playerGroupMap.remove(player.getId());
            }
        }


        return true;
    }



    @Override
    public void clean(int hostId) {
        //todo 这个匹配器暂时没用， 先不处理
    }


    private void buildTeamA(MatchTeam team, long groupId){
        MatchTeam copy = copyTeam(team);
        for (Map.Entry<Long, MatchGroup> entry : matchGroups.entrySet()){

            //只往后找
            if (entry.getKey() < groupId){
                continue;
            }
            if (canMerge(copy, entry.getValue())){
                mergeA(copy, entry.getValue());
            }
        }

    }

    public MatchTeam copyTeam(MatchTeam team){
        MatchTeam copy = new MatchTeam();
        copy.getGroupList().addAll(team.getGroupList());
        copy.getPlayerList().addAll(team.getPlayerList());
        copy.setPvpType(this.pvpType);
        copy.setFightPower(team.getFightPower());
        copy.setStartTime(team.getStartTime());
        copy.setId(team.getId());
        return copy;

    }

    private void mergeA(MatchTeam team, MatchGroup group) {
        MatchTeam copy = copyTeam(team);

        copy.getGroupList().add(group);
        copy.getPlayerList().addAll(group.getPlayerList());
        copy.setFightPower(team.getFightPower() + group.getFightPower());


        if (copy.getPlayerList().size() == groupPlayerCount) {
            //合并完成
            redTeams.add(copy);
            Map<Long, MatchGroup> mergeGroup = new HashMap<>();
            for (MatchGroup group1 : copy.getGroupList()) {
                mergeGroup.put(group1.getId(), group1);
            }
            matchB(copy, mergeGroup);
            return;
        }
        //如果没有 5个人还需要继续匹配
        buildTeamA(copy, group.getId());
    }

    //TeamA  匹配出所有可能的TeamB
    private void matchB(MatchTeam teamA, Map<Long, MatchGroup> mergeGroup){

        for (Map.Entry<Long, MatchGroup> entry : matchGroups.entrySet()){
            if (mergeGroup.containsKey(entry.getKey())){
                continue;
            }
            MatchTeam team = new MatchTeam();
            buildTeamB(team, entry.getKey() - 1, teamA, mergeGroup);
        }

    }


    private void buildTeamB(MatchTeam team, long groupId, MatchTeam teamA, Map<Long, MatchGroup> mergeGroup){
        MatchTeam copy = copyTeam(team);

        for (Map.Entry<Long, MatchGroup> entry : matchGroups.entrySet()){
            if (mergeGroup.containsKey(entry.getKey())){
                continue;
            }
            //只往后找
            if (entry.getKey() < groupId){
                continue;
            }
            if (canMerge(copy, entry.getValue())){
                mergeB(copy, entry.getValue(), teamA, mergeGroup);
            }
        }

    }

    private void mergeB(MatchTeam team, MatchGroup group, MatchTeam teamA, Map<Long, MatchGroup> mergeGroup){
        MatchTeam copy = copyTeam(team);

        copy.getGroupList().add(group);
        copy.getPlayerList().addAll(group.getPlayerList());
        copy.setFightPower(team.getFightPower() + group.getFightPower());

        if (copy.getPlayerList().size() == groupPlayerCount){
            //合并完成
            if (!blueTeams.containsKey(teamA)){
                Set<MatchTeam> blues = new HashSet<>();
                blues.add(copy);
                blueTeams.put(teamA, blues);
            }
            else {
                Set<MatchTeam> blues = blueTeams.get(teamA);
                blues.add(copy);
            }
            return;
        }
        //如果没有 5个人还需要继续匹配
        buildTeamB(copy, group.getId(), teamA, mergeGroup);

    }



}
