package com.sh.game.server.pvp.match.bean;

import com.sh.game.server.pvp.match.bserver.ServerInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/7/30 17:13
 */
@Getter
@Setter
@Slf4j
public class Battle {

    private int id;

    private MatchTeam a;
    private MatchTeam b;

    private int state;

    private int mapId;

    private int pvpType;

    private int hostId;

    private int mapCfgId;

    private long mapInstId;





    public Map<ServerInfo, List<MatchGroup>> findServer2GroupMap() {
        Map<ServerInfo, List<MatchGroup>> server2GroupMap = new HashMap<>();
        a.putPlayer2ServerMap(server2GroupMap);
        b.putPlayer2ServerMap(server2GroupMap);
        return server2GroupMap;
    }


}
