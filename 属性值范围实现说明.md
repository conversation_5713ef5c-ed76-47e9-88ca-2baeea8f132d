# 词条属性值范围实现说明

## 📋 图片规则解析

根据提供的图片，词条属性值范围有以下规则：

### 1. 基本规则
- **词条属性值范围**：每种词条的属性值有上下限，每次洗练随机到的词条属性值，必须在该范围内

### 2. 高品质装备特殊处理
- **会有高品质的装备上下限会相同**：此时只随机词条种类，词条的数值不必随机
- **实现**：如果 `attributeNum1 == attributeNum2`，直接返回该值

### 3. 数值类型波动规则
- **整数数值**：unit=0时，每次按上下限随机波动
- **万分比数值**：unit>0时，按万分比步长波动
  - unit=100：每次以1%为最小单位值波动
  - unit=50：每次以0.5%为最小单位值波动
  - unit=25：每次以0.25%为最小单位值波动
  - 以此类推...

## 🚀 实现方案

### 核心方法：`generateValue(EquipCiZuiConfig config)`

```java
private static long generateValue(EquipCiZuiConfig config) {
    double min = config.getAttributeNum1();
    double max = config.getAttributeNum2();
    int unit = config.getUnit();

    // 规则1：如果上下限相同，直接返回该值（高品质装备）
    if (min == max) {
        return (long) min;
    }

    // 规则2：根据unit值判断数值类型并应用相应的波动规则
    if (unit > 0) {
        // unit表示万分比，转换为实际的步长百分比
        // unit=100 -> 1%步长，unit=50 -> 0.5%步长
        double stepPercentage = unit / 100.0;
        return generatePercentageValue(min, max, stepPercentage);
    } else {
        // unit=0或负数，表示整数数值，按上下限随机波动
        return (long) RandomUtil.random(min, max);
    }
}
```

### 辅助方法：`generatePercentageValue(double min, double max, double stepPercentage)`

```java
private static long generatePercentageValue(double min, double max, double stepPercentage) {
    // 计算可以有多少个步长
    int steps = (int) ((max - min) / stepPercentage);

    // 随机选择一个步长
    int randomSteps = (int) (Math.random() * (steps + 1));

    // 计算最终值
    return (long) (min + randomSteps * stepPercentage);
}
```

## 📊 实现示例

### 示例1：高品质装备（上下限相同）
```java
// 配置：attributeNum1=1000, attributeNum2=1000, unit=0
// 结果：直接返回 1000
```

### 示例2：整数数值
```java
// 配置：attributeNum1=800, attributeNum2=1200, unit=0
// 结果：在 800-1200 之间随机，如 950
```

### 示例3：万分比数值（unit=100，1%步长）
```java
// 配置：attributeNum1=10, attributeNum2=20, unit=100
// 步长：100/100.0 = 1.0%
// 结果：以1%步长在10-20之间，如 10, 11, 12...20
```

### 示例4：万分比数值（unit=50，0.5%步长）
```java
// 配置：attributeNum1=5, attributeNum2=10, unit=50
// 步长：50/100.0 = 0.5%
// 结果：以0.5步长在5-10之间，如 5.0, 5.5, 6.0...10.0
```

### 示例5：万分比数值（unit=25，0.25%步长）
```java
// 配置：attributeNum1=100, attributeNum2=200, unit=25
// 步长：25/100.0 = 0.25%
// 结果：以0.25步长在100-200之间，如 100.0, 100.25, 100.5...200.0
```

## ✅ 规则验证

### 1. 范围限制 ✅
- 所有生成的值都在 `[attributeNum1, attributeNum2]` 范围内
- 确保不会超出配置的上下限

### 2. 高品质装备处理 ✅
- 当上下限相同时，直接返回固定值
- 避免不必要的随机计算

### 3. 数值类型区分 ✅
- 根据 `unit` 字段正确识别数值类型
- 应用对应的波动规则

### 4. 精度控制 ✅
- 百分比数值：1% 精度
- 千分比数值：0.5% 精度
- 整数数值：完全随机

## 🔧 配置字段说明

### EquipCiZuiConfig 相关字段
- **attributeNum1**：属性下限
- **attributeNum2**：属性上限
- **unit**：波动单位类型（万分比）
  - `0` 或负数：整数数值，完全随机
  - `100`：万分比数值，1%步长（100/100.0 = 1.0%）
  - `50`：万分比数值，0.5%步长（50/100.0 = 0.5%）
  - `25`：万分比数值，0.25%步长（25/100.0 = 0.25%）
  - 任意正整数：对应的万分比步长（unit/100.0 %）

## 🎯 优势总结

### 1. 完全符合规则
- ✅ 严格按照图片中的规则实现
- ✅ 支持所有数值类型的波动规则
- ✅ 正确处理高品质装备的特殊情况

### 2. 代码简洁
- 逻辑清晰，易于理解和维护
- 方法职责单一，便于测试
- 性能良好，避免不必要的计算

### 3. 扩展性好
- 易于添加新的数值类型
- 可以方便地调整波动规则
- 支持更复杂的精度控制

## 🧪 测试建议

### 1. 边界值测试
```java
// 测试上下限相同的情况
// 测试最小值和最大值
// 测试不同unit值的处理
```

### 2. 精度验证
```java
// 验证百分比数值的1%步长
// 验证千分比数值的0.5%步长
// 验证整数数值的完全随机
```

### 3. 范围检查
```java
// 确保所有生成值都在配置范围内
// 验证不会出现超出范围的值
```

## 📝 总结

通过实现新的 `generateValue` 方法，完全按照图片中的规则处理词条属性值的生成：

1. **高品质装备**：上下限相同时直接返回固定值
2. **整数数值**：在范围内完全随机
3. **百分比数值**：以1%为步长精确控制
4. **千分比数值**：以0.5%为步长精确控制

这样确保了词条属性值的生成既符合游戏规则，又保证了数值的合理性和可控性。
