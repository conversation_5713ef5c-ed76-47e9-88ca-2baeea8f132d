package com.sh.game.merge.tool;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
@Slf4j
public class DruidConfigTemplate {
    //    private static final String TEMPLATE =
//            "url=%s\n"
//                    + "username=%s\n"
//                    + "password=%s\n"
//                    + "initialSize=20\n"
//                    + "minIdle=20\n"
//                    + "maxActive=40\n"
//                    + "maxWait=60000\n"
//                    + "timeBetweenEvictionRunsMillis=60000\n"
//                    + "minEvictableIdleTimeMillis=300000\n"
//                    + "validationQuery=SELECT 'x'\n" + "testWhileIdle=true\n" + "testOnBorrow=false\n" + "testOnReturn=false\n"
//                    + "poolPreparedStatements=false\n" + "maxPoolPreparedStatementPerConnectionSize=20\n";
    private static final String TEMPLATE =
            "jdbcUrl=%s\n"
                    + "username=%s\n"
                    + "password=%s\n"
                    + "poolName=Game\n"
                    + "minimumIdle=20\n"
                    + "maximumPoolSize=40\n"
                    + "maxLifetime=27000000";

    public static String createConfigFile(String name, String url, String user, String password) throws IOException {
        String fileName = name + ".properties";
        try(PrintWriter pw = new PrintWriter(fileName)) {
            pw.println(String.format(TEMPLATE, url, user, password));
        } catch (IOException e) {
            log.error("创建数据库链接文件失败，文件名【{}】",fileName);
            throw new IOException(e);
        }

        log.info("生成数据库配置文件 : {}", fileName);
        return fileName;
    }

    public static void deleteConfigFile(String fileName) {
        File file = new File(fileName);
        if (file.exists()) {
            boolean ret = file.delete();
            if (ret) {
                log.info("删除文件 : {}  成功", fileName);
            } else {
                log.info("删除文件 : {} 失败", fileName);
            }

        }
    }

}
