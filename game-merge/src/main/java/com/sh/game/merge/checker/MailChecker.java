package com.sh.game.merge.checker;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.merge.tool.MergeContext;

public class MailChecker {

    private static JdbcTemplate gameTemplate = MergeContext.getGameTemplate();

    public static void check() {
        deleteEmail();
    }

    /**
     * 删除已提取邮件
     */
    private static void deleteEmail() {
        int day = 2;
        long offTime = TimeUtil.getNowOfSeconds() - (day * 24 * 60 * 60);

        String sql = "delete from p_mail where state >= 2 and time <= ?";
        gameTemplate.update(sql, offTime);
    }
}
