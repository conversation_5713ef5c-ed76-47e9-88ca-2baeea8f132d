---
description:
globs:
alwaysApply: false
---
# 开发指南

## 开发环境设置
1. JDK 版本：1.8
2. 数据库：创建本地数据库 'zetianxy'
3. 配置表：运行 [updateCsv.bat](mdc:conf/updateCsv.bat) 从SVN更新配置表

## 启动项目
1. 确保已完成环境设置
2. 运行 [GameBootStrap](mdc:.run/GameBootStrap.run.xml) 配置

## 项目配置
- JVM参数在 [GameBootStrap.run.xml](mdc:.run/GameBootStrap.run.xml) 中配置
- 游戏服务器参数在 [game.properties](mdc:conf/game.properties) 中配置

## 开发注意事项
1. 代码提交前请确保通过所有单元测试
2. 配置表修改需要同步更新到SVN
3. 日志文件存放在 `logs` 目录下
4. 堆转储文件将保存为 `heapdump.hprof`

## 调试模式
项目支持脚本开发模式，通过以下JVM参数启用：
- `-Dgame.script.dev=true`
- `-Dgame.script.findSuperClassInterface=true`
