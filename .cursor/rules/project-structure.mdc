---
description:
globs:
alwaysApply: false
---
# 项目结构指南

## 项目概述
这是一个基于Java的游戏服务器项目，使用Maven进行依赖管理，主要由多个模块组成。

## 模块结构
- [game-server](mdc:game-server) - 服务器核心模块
- [game-common](mdc:game-common) - 公共组件和工具类
- [game-basic](mdc:game-basic) - 基础功能模块
- [game-gate](mdc:game-gate) - 网关模块，包含主入口类
- [game-scene](mdc:game-scene) - 场景相关模块
- [game-package](mdc:game-package) - 打包模块
- [game-script](mdc:game-script) - 脚本模块
- [game-turn-based](mdc:game-turn-based) - 回合制战斗模块
- [game-merge](mdc:game-merge) - 合服模块
- [game-back-client](mdc:game-back-client) - 后台客户端模块

## 项目启动流程
1. 执行 [配置表更新脚本](mdc:conf/updateCsv.bat) 初始化 csv 配置文件
2. 创建本地数据库 'zetianxy'
3. 使用 [项目启动配置文件](mdc:.run/GameBootStrap.run.xml) 运行项目

## 主要配置文件
- [pom.xml](mdc:pom.xml) - Maven项目配置和依赖管理
- [conf/game.properties](mdc:conf/game.properties) - 游戏服务器配置文件

## 主入口类
项目的主入口类是 `com.sh.game.gate.GameBootstrap`，位于game-gate模块中。
