package com.sh.game.turn_based.constant;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
public interface TurnBasedConst {
    interface BattleType {
        // 测试用
        int DEMO = 0;
        // 关卡
        int BARRIER = 1;
        // 五行山
        int WU_XING_SHAN = 2;
        // 妖王
        int BOSS_KING = 3;
        // 好友
        int FRIEND = 4;
        // 竞技场
        int ARENA = 5;
        // 万仙榜
        int WAN_XIAN_BANG = 6;
        // 妖邪挑战
        int YAO_XIE_TIAO_ZHAN = 7;
        // 镇妖塔
        int ZHEN_YAO_TA = 8;
        // 后院
        int HOU_YUAN = 9;
        // 建筑
        int WORK = 10;
        //离线奖励
        int LI_XIAN_REWARD = 11;
        //源晶小铺
        int YU_JING_XIAO_PU = 12;
        // 针对主线奖励展示用
        int TASK = 9998;
        // 针对奖励展示用
        int NORMAL = 9999;
        // 针对奖励拆分展示用
        int CHAI_FEN = 10086;
        // 行会领取宝箱特殊处理
        int UNION_OPEN_BOX = 10087;
    }

    interface Num {
        // 随机最大数
        int RANDOM_MAX = 10000;
        // 除数
        double RATIO = 10000D;
    }

    interface StateTargetType {
        // 作用自己
        int SELF = 1;
        // 对手
        int RIVAL = 2;
    }

    interface Round {
        // 轮数
        int PERPETUAL = 9999;
    }
}
