package com.sh.game.turn_based.effect;

import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.result.RoundResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 提升N回合的X属性
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
public class AttrPromoteEffectHandler extends AbstractEffectHandler {

    public AttrPromoteEffectHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doEffect0(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        BattlePerformer target = findTrueAttacker(attacker);
        // 增加属性
        ResultFactory.addAttribute(roundResult, attacker, target, effectId, effectParams);
    }
}
