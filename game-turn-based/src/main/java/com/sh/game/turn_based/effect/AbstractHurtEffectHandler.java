package com.sh.game.turn_based.effect;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.BattleEffectConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.turn_based.constant.EffectId;
import com.sh.game.turn_based.constant.PerformerType;
import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.factory.CalculateFactory;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.result.RoundResult;
import com.sh.game.turn_based.state.StateTriggerFactory;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
public abstract class AbstractHurtEffectHandler extends AbstractEffectHandler {

    public AbstractHurtEffectHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doEffect0(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        // 计算伤害
        long dmg = calDmg(attacker, defender, triggerType, roundResult);
        // 触发状态伤害效果
        dmg += calStateEffectDmg(attacker, defender, triggerType, roundResult);
        // 触发状态增伤效果
        dmg = calStateEffectDmgBoost(attacker, defender, triggerType, roundResult, dmg);
        // 最终增减伤
        dmg = CalculateFactory.calRatioDmg(dmg, attacker, AttributeEnum.MG_LAST_HURT_STRONG, defender, AttributeEnum.MG_LAST_HURT_WEAKEN);
        // 最终增减伤2
        dmg = CalculateFactory.calRatioDmg(dmg, attacker, AttributeEnum.MG_LAST_HURT_STRONG2, defender, AttributeEnum.MG_LAST_HURT_WEAKEN2);
        // 伤害不能小于 1
        dmg = CalculateFactory.getFinalDmg(dmg, attacker, defender, triggerType, roundResult);

        long hurt = CalculateFactory.calHPRatioDmg(attacker, defender);
        dmg += hurt;
        defender.addHp(-dmg);

        // 添加战斗结果帧
        ResultFactory.addFightActionResult(roundResult, attacker.getId(), defender.getId(), effectId, AttributeEnum.MG_HP.getType(), -dmg, attacker.getId());

        if (attacker.getType() != PerformerType.SHEN_MO && attacker.getType() != PerformerType.HUO_BAN) {
            // 触发吸血效果
            EffectUtil.castEffect(attacker, defender, roundResult, triggerType, EffectId.SUCKBLOOD, dmg, attacker.getId());
        }

        BattleEffectConfig config = ConfigDataManager.getInstance().getById(BattleEffectConfig.class, this.effectId);
        if (config == null || config.getStopState() == 0) {
            // 触发状态效果
            handleStateEffect(attacker, defender, triggerType, roundResult, dmg);
        }
    }

    private void handleStateEffect(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult, long dmg) {
        StateTriggerFactory.getStateTrigger(StateType.TRIGGER_EFFECT_BY_VOLUME).trigger(attacker, defender, triggerType, roundResult, dmg);

        // 触发反击效果
        StateTriggerFactory.getStateTrigger(StateType.TRIGGER_EFFECT_BY_COUNTER).trigger(attacker, defender, triggerType, roundResult);

        // 触发受伤效果
        StateTriggerFactory.getStateTrigger(StateType.TRIGGER_EFFECT_HURT_AFTER_DEFEND).trigger(attacker, defender, triggerType, roundResult, dmg);
    }

    private long calStateEffectDmg(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        long res = 0;
        res += StateTriggerFactory.getStateTrigger(StateType.EXT_DMG_BY_COUNTER).triggerCal(attacker, defender, triggerType, roundResult);
        return res;
    }

    private long calStateEffectDmgBoost(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult, long dmg) {
        dmg += StateTriggerFactory.getStateTrigger(StateType.EXT_DMG_TARGET_ENEMY_HIGH_HP).triggerCal(attacker, defender, triggerType, roundResult, dmg);
        dmg += StateTriggerFactory.getStateTrigger(StateType.EXT_DMG_TARGET_ENEMY_LOW_HP).triggerCal(attacker, defender, triggerType, roundResult, dmg);
        dmg += StateTriggerFactory.getStateTrigger(StateType.UPGRADE_HURT_DURING_ATTACK).triggerCal(attacker, defender, triggerType, roundResult, dmg);
        return dmg;
    }

    protected abstract long calDmg(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult);
}
