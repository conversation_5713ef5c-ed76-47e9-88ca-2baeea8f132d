package com.sh.game.turn_based.effect;

import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.entity.BattlePet;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.RoundResult;
import com.sh.game.turn_based.state.StateTriggerFactory;

/**
 * <AUTHOR>
 * @date 2023/10/16
 */
public abstract class AbstractPetEffectHandler extends AbstractEffectHandler {

    public AbstractPetEffectHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doEffect0(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        BattlePet pet = null;
        if (attacker instanceof BattlePet) {
            pet = (BattlePet) attacker;
        }
        if (pet == null) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), attacker, "非宠物释放了宠物效果,attacker:{}", attacker);
            return;
        }

        doPetEffect(pet, defender, triggerType, roundResult);

        StateTriggerFactory.getStateTrigger(StateType.TRIGGER_EFFECT_BY_PET_EFFECT).trigger(pet, defender, triggerType, roundResult);
    }


    public abstract void doPetEffect(BattlePet pet, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult);
}
