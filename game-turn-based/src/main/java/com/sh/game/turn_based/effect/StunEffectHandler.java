package com.sh.game.turn_based.effect;

import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.RoundResult;
import com.sh.game.turn_based.state.StateTriggerFactory;

/**
 * 击晕
 * <AUTHOR>
 * @date 2023/9/6
 */
public class StunEffectHandler extends AbstractEffectHandler {

    public StunEffectHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doEffect0(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        if (effectParams.length < 1) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), attacker, "效果参数错误,effectId:{},effectParams:{}", effectId, effectParams);
            return;
        }
        // 击晕回合
        int count = Math.max(effectParams[0], 1);

        int effectRound = roundResult.getRound();
        if (effectParams.length >= 2 && effectParams[1] > 0) {
            effectRound += effectParams[1];
        }
        if (attacker.isDisadvantage() || findTrueAttacker(attacker).isDisadvantage()) {
            effectRound += 1;
        }
        defender.addState(roundResult, StateType.STUN, count, priority, effectRound, effectId, attacker);

        // 增加战斗序列帧
        ResultFactory.addFightActionResult(roundResult, attacker.getId(), defender.getId(), effectId);

        StateTriggerFactory.getStateTrigger(StateType.TRIGGER_EFFECT_BY_STUN).trigger(attacker, defender, triggerType, roundResult);
    }
}
