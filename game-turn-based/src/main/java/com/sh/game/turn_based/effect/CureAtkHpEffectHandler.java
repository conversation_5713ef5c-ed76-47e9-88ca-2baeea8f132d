package com.sh.game.turn_based.effect;

import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.factory.CalculateFactory;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.RoundResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 恢复攻击力X%的生命
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
public class CureAtkHpEffectHandler extends AbstractEffectHandler {

    public CureAtkHpEffectHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doEffect0(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        int effectParamLength = effectParams.length;
        if (effectParamLength != 1) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), attacker, "效果参数错误,effectId:{},effectParams:{}", effectId, effectParams);
            return;
        }
        if (attacker == null) {
            log.error("找不到攻击方");
            return;
        }
        BattlePerformer targetPerformer = findTrueAttacker(attacker);
        if (targetPerformer.containState(StateType.BING_DONG) || targetPerformer.containState(StateType.STUN)) {
            return;
        }
        // 计算治疗量
        long atk = targetPerformer.getAttribute(AttributeEnum.MG_ATK);
        long cure = (long) (atk / TurnBasedConst.Num.RATIO * effectParams[0]);
        cure = CalculateFactory.calRatioCure(cure, targetPerformer, AttributeEnum.MG_CURE_STRONG, defender, AttributeEnum.MG_CURE_WEAKEN);
        targetPerformer.addHp(cure);

        // 增加战斗序列帧
        ResultFactory.addFightActionResult(roundResult, attacker.getId(), targetPerformer.getId(), effectId, AttributeEnum.MG_HP.getType(), cure, attacker.getId());
    }
}
