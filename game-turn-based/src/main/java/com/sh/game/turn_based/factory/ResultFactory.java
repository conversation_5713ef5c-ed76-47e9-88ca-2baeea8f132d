package com.sh.game.turn_based.factory;

import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.entity.BattlePet;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.FightActionResult;
import com.sh.game.turn_based.result.RoundResult;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
public class ResultFactory {
    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId) {
        addFightActionResult(roundResult, castId, targetId, effectId, 0);
    }

    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, int remove) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setRemove(remove);
        roundResult.addAction(fightAction);
    }

    public static void addFightActionRemoveResult(RoundResult roundResult, long castId, long targetId, int effectId, long triggerId) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setRemove(1);
        fightAction.setTriggerId(triggerId);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionRemoveResult(RoundResult roundResult, long castId, long targetId, int effectId, Map<Integer, Long> attributes) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setAttributeMap(attributes);
        fightAction.setRemove(1);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, Map<Integer, Long> attributes) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setAttributeMap(attributes);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, Map<Integer, Long> attributes, long triggerId) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setAttributeMap(attributes);
        fightAction.setTriggerId(triggerId);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, int attributeType, long attributeValue) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        Map<Integer, Long> attributes = new HashMap<>();
        attributes.put(attributeType, attributeValue);
        fightAction.setAttributeMap(attributes);
        fightAction.setTriggerId(castId);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, int attributeType, long attributeValue, long triggerId) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        Map<Integer, Long> attributes = new HashMap<>();
        attributes.put(attributeType, attributeValue);
        fightAction.setAttributeMap(attributes);
        fightAction.setTriggerId(triggerId);
        roundResult.addAction(fightAction);
    }

    /**
     * 增加战斗帧
     */
    public static void addFightActionResult(RoundResult roundResult, long castId, long targetId, int effectId, long triggerId) {
        FightActionResult fightAction = new FightActionResult();
        fightAction.setCastId(castId);
        fightAction.setTargetId(targetId);
        fightAction.setEffectId(effectId);
        fightAction.setTriggerId(triggerId);
        roundResult.addAction(fightAction);
    }

    /**
     * 添加属性
     */
    public static void addAttribute(RoundResult roundResult, BattlePerformer castPerformer, BattlePerformer targetPerformer, int effectId, int[] params) {
        int effectParamLength = params.length;
        // 判断参数
        if (effectParamLength < 3 && effectParamLength % 2 != 1) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), castPerformer, "加属性参数错误,effectId:{},params:{}", effectId, params);
            return;
        }
        Attribute ori = targetPerformer.getFinalAttribute();
        // 生效轮次
        int effectRound = params[0];
        // 计算属性
        Map<Integer, Long> attributeMap = new HashMap<>();
        for (int i = 2; i < effectParamLength; i += 2) {
            attributeMap.put(params[i - 1], (long) params[i]);
        }
        // 增加属性
        targetPerformer.addAttribute(roundResult.getRound() + effectRound, attributeMap, effectId);

        Attribute cur = targetPerformer.getFinalAttribute();
        Map<Integer, Long> diffMap = Attribute.mgCompare(cur, ori);
        // 添加战斗结果帧
        ResultFactory.addFightActionResult(roundResult, castPerformer.getId(), targetPerformer.getId(), effectId, diffMap, castPerformer.getId());
    }

    /**
     * 增加属性
     */
    public static void addAttributeByAttr(RoundResult roundResult, BattlePerformer castPerformer, BattlePerformer targetPerformer, int effectId, int[] params) {
        int effectParamLength = params.length;
        // 判断参数
        if (effectParamLength < 4 && effectParamLength % 2 != 0) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), castPerformer, "加属性参数错误,effectId:{},params:{}", effectId, params);
            return;
        }
        Attribute ori = targetPerformer.getFinalAttribute();
        // 生效轮次
        int effectRound = params[0];
        // 目标属性
        int targetAttr = params[1];
        long targetValue = targetPerformer.getAttribute(AttributeEnum.valueOf(targetAttr));
        // 计算属性
        Map<Integer, Long> attributeMap = new HashMap<>();
        for (int i = 3; i < effectParamLength; i += 2) {
            attributeMap.put(params[i - 1], (long) (params[i] / TurnBasedConst.Num.RATIO * targetValue));
        }
        // 增加属性
        targetPerformer.addAttribute(roundResult.getRound() + effectRound, attributeMap, effectId);

        Attribute cur = targetPerformer.getFinalAttribute();
        Map<Integer, Long> diffMap = Attribute.mgCompare(cur, ori);

        // 添加战斗结果帧
        ResultFactory.addFightActionResult(roundResult, castPerformer.getId(), targetPerformer.getId(), effectId, diffMap);
    }

    public static void addAttributeLimitCount(RoundResult roundResult, BattlePerformer castPerformer, BattlePerformer targetPerformer, int effectId, int[] params) {
        addAttributeLimitCount(roundResult, castPerformer, targetPerformer, effectId, params, 0);
    }

    public static void addAttributeLimitCount(RoundResult roundResult, BattlePerformer castPerformer, BattlePerformer targetPerformer, int effectId, int[] params, long triggerId) {
        int effectParamLength = params.length;
        // 判断参数
        if (effectParamLength < 3 && effectParamLength % 2 != 1) {
            BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), castPerformer, "加属性参数错误,effectId:{},params:{}", effectId, params);
            return;
        }
        if (targetPerformer instanceof BattlePet) {
            targetPerformer = ((BattlePet) targetPerformer).getMaster();
        }
        // 限制次数
        int limitCount = params[0];
        int effectCount = targetPerformer.getEffectCount(effectId);
        if (effectCount >= limitCount) {
            return;
        }

        Attribute ori = targetPerformer.getFinalAttribute();

        // 增加次数
        targetPerformer.incrEffectCount(effectId);

        // 计算属性
        Map<Integer, Long> attributeMap = new HashMap<>();
        for (int i = 2; i < effectParamLength; i += 2) {
            attributeMap.put(params[i - 1], (long) params[i]);
        }
        // 增加属性
        targetPerformer.addAttribute(TurnBasedConst.Round.PERPETUAL, attributeMap, effectId);

        Attribute cur = targetPerformer.getFinalAttribute();
        Map<Integer, Long> diffMap = Attribute.mgCompare(cur, ori);

        // 添加战斗结果帧
        ResultFactory.addFightActionResult(roundResult, castPerformer.getId(), targetPerformer.getId(), effectId, diffMap, triggerId);
    }
}
