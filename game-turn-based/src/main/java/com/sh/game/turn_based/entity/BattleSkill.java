package com.sh.game.turn_based.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Data
public class BattleSkill {
    // buff id
    private int configId;
    // 触发类型
    private int triggerType;
    // effect id
    private List<Integer> effectIds = new ArrayList<>();

    /**
     * 优先级
     */
    private int priority;

    public void addEffectIds(List<Integer> effectIds) {
        this.effectIds.addAll(effectIds);
    }
}
