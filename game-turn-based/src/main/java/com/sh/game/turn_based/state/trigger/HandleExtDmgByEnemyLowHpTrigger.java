package com.sh.game.turn_based.state.trigger;

import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.entity.BattlePerformerState;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.RoundResult;

import java.util.Set;

/**
 * 对生命高于x%的敌人，伤害提升y%
 * <AUTHOR>
 * @date 2023/12/5
 */
public class HandleExtDmgByEnemyLowHpTrigger extends AbstractStateTrigger {
    @Override
    public StateType getStateType() {
        return StateType.EXT_DMG_TARGET_ENEMY_LOW_HP;
    }

    @Override
    public long triggerCal(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult, long... param) {
        // 判断参数
        if (param.length < 1) {
            return 0;
        }
        long dmg = param[0];
        long[] res = new long[1];
        long curHpRatio = defender.getHpRatio();
        Set<BattlePerformerState> stateSet = attacker.getState(getStateType());
        // 有状态
        if (stateSet != null) {
            stateSet.forEach(state -> {
                int[] params = state.getParams();
                // 参数校验
                if (params.length < 2) {
                    BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), attacker, "状态参数错误,state:{},param:{}", state.getStateType(), params);
                    return;
                }
                // 增伤百分比
                int addRatio = params[0];
                // 触发血量百分比
                int hpRatio = params[1];
                if (curHpRatio > hpRatio) {
                    return;
                }
                res[0] += (long) (dmg / TurnBasedConst.Num.RATIO * addRatio);
                BattleLogger.log(roundResult.getBattleId(), roundResult.getRound(), attacker, "触发状态,state:{},param:{}", state.getStateType(), params);
                // 添加战斗结果帧
                ResultFactory.addFightActionResult(roundResult, attacker.getId(), defender.getId(), state.getFromEffectId(), state.getFromPerformerId());
            });
        }
        return res[0];
    }
}
