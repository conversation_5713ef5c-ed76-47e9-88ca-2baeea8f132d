package com.sh.game.turn_based.state.trigger;

import com.sh.game.common.util.RandomUtil;
import com.sh.game.turn_based.constant.StateType;
import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.constant.TurnBasedConst;
import com.sh.game.turn_based.effect.EffectUtil;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.entity.BattlePerformerState;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.log.BattleLogger;
import com.sh.game.turn_based.result.RoundResult;

import java.util.Set;

/**
 * 伤害后x%概率触发效果(作为防守方)
 * <AUTHOR>
 * @date 2023/12/5
 */
public class TriggerEffectHurtAfterDefendTrigger extends AbstractStateTrigger {
    @Override
    public StateType getStateType() {
        return StateType.TRIGGER_EFFECT_HURT_AFTER_DEFEND;
    }

    @Override
    public void trigger(BattlePerformer attacker, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult, long... param) {
        Set<BattlePerformerState> stateSet = defender.getState(getStateType());
        // 有状态
        if (stateSet != null) {
            stateSet.forEach(state -> {
                int[] params = state.getParams();
                // 参数校验
                if (params.length < 3) {
                    BattleLogger.logError(roundResult.getBattleId(), roundResult.getRound(), attacker, "状态参数错误,state:{},param:{}", state.getStateType(), params);
                    return;
                }
                int effectId = params[0];
                // 作用类型
                int targetType = params[1];
                // 触发几率
                int ratio = params[2];
                boolean success = RandomUtil.isGenerate(TurnBasedConst.Num.RANDOM_MAX, ratio);
                BattleLogger.log(roundResult.getBattleId(), roundResult.getRound(), defender, "触发状态,state:{},param:{},success:{}", state.getStateType(), params, success);
                if (!success) {
                    return;
                }
                long targetId = attacker.getId();
                if (targetType == TurnBasedConst.StateTargetType.SELF) {
                    targetId = defender.getId();
                }
                // 添加战斗结果帧
//                ResultFactory.addFightActionResult(roundResult, defender.getId(), targetId, state.getFromEffectId(), state.getFromPerformerId());
                EffectUtil.castEffect(state.getFromPerform(), attacker, roundResult, triggerType, effectId, param);
            });
        }
    }
}
