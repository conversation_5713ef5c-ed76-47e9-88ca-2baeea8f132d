package com.sh.game.turn_based.effect;

import com.sh.game.turn_based.constant.TriggerType;
import com.sh.game.turn_based.entity.BattlePerformer;
import com.sh.game.turn_based.entity.BattlePet;
import com.sh.game.turn_based.factory.ResultFactory;
import com.sh.game.turn_based.result.RoundResult;

/**
 * 提升主人N回合的X属性
 * <AUTHOR>
 * @date 2023/10/23
 */
public class PetMasterAttrPromoteHandler extends AbstractPetEffectHandler {

    public PetMasterAttrPromoteHandler(int effectId) {
        super(effectId);
    }

    @Override
    public void doPetEffect(BattlePet pet, BattlePerformer defender, TriggerType triggerType, RoundResult roundResult) {
        // 增加属性
        BattlePerformer petMaster = pet.getMaster();
        ResultFactory.addAttribute(roundResult, pet, petMaster, effectId, effectParams);
    }
}
