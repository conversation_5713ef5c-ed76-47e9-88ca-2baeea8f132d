package com.sh.game.back.client;

import com.sh.client.*;
import com.sh.game.common.communication.msg.system.back.ReqReloadScriptMessage;
import com.sh.game.common.communication.msg.system.back.ResBackLoginMessage;
import com.sh.game.common.communication.msg.system.back.ResBackRetMessage;
import com.sh.game.common.util.StatUtil;
import com.sh.game.option.ServerOption;


/**
 * 重新加载脚本客户端
 */
public class ReloadScriptClient {

    public static void main(String[] args) throws Exception {

        try {
            ClientBuilder builder = new ClientBuilder();
            String optionPath = args[0];
            ServerOption option = new ServerOption();
            option.build(optionPath);

            builder.setHost("localhost");
            builder.setPort(option.getBackServerPort());
            builder.setEventlistener(new NetworkEventlistenerAdapter());
            builder.setConsumer(new NetworkConsumerAdapter());
            DefaultMessagePool pool = new DefaultMessagePool();
            pool.register(new ResBackRetMessage(), null);
            pool.register(new ResBackLoginMessage(), null);
            builder.setMsgPool(pool);
            builder.setNeedReconnect(false);
            Client client = builder.createClient();
            client.connect(true);

            if(!StatUtil.waitInit(client))
                return;

            if(!StatUtil.login(client, option.getBackLoginSign()))
                return;

            ReqReloadScriptMessage req = new ReqReloadScriptMessage();
            ResBackRetMessage res = (ResBackRetMessage) client.sendSyncMsg(req, 5_000);

            if (res == null) {
                System.out.println("请求失败");
            } else {
                System.out.println(res.getRet());
            }
            client.stopQuickly();

        } catch (Exception e){
            e.printStackTrace();
        } finally {
            System.exit(0);
        }

    }




}
