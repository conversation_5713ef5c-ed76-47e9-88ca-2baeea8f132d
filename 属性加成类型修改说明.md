# 属性加成类型修改说明

## 📋 需求概述

根据图片中的需求，需要在属性计算中区分两种技能加成方式：
1. **type1=1（叠加）**：保持原有的直接叠加逻辑
2. **type1=2（放大）**：使用新的放大公式

## 🔧 修改内容

### 1. AttributeScoreConfig 配置类修改

**文件位置**：`game-common/src/main/java/com/sh/game/common/config/model/AttributeScoreConfig.java`

**新增字段**：
```java
/**
 * 技能加成类型
 * 1=叠加（保持原有逻辑）
 * 2=放大（使用新公式）
 */
private int type1;
```

### 2. Attribute.applyAttributeBonus 方法修改

**文件位置**：`game-common/src/main/java/com/sh/game/common/entity/attr/Attribute.java`

**修改内容**：
- 根据 `type1` 字段区分处理方式
- 增加了详细的注释说明
- 添加了测试方法

**核心逻辑**：
```java
long addValue;
if (config.getType1() == 2) {
    // type1=2：放大公式
    // 放大公式：最终值 = 基础值 * (1 + 加成值/10000)
    // 这里返回的是放大后的总值，而不是增量
    addValue = (long) (baseValue * (1 + ppmValue / 10000.0)) - baseValue;
} else {
    // type1=1或其他：叠加（保持原有逻辑）
    // 叠加公式：增量 = 基础属性 * (技能加成万分比 / 10000)
    addValue = (long) (baseValue * (ppmValue / 10000.0));
}
```

## 📊 计算公式对比

### type1=1（叠加）
- **公式**：增量 = 基础属性 × (技能加成万分比 ÷ 10000)
- **示例**：基础攻击1000，加成20%（2000万分比）
- **计算**：增量 = 1000 × (2000 ÷ 10000) = 200
- **最终值**：1000 + 200 = 1200

### type1=2（放大）
- **公式**：最终值 = 基础属性 × (1 + 技能加成万分比 ÷ 10000)
- **示例**：基础攻击1000，加成20%（2000万分比）
- **计算**：最终值 = 1000 × (1 + 2000 ÷ 10000) = 1000 × 1.2 = 1200
- **增量**：1200 - 1000 = 200

## 🎯 使用方式

### 配置表设置
在 `cfg_attribute_score` 配置表中：
- 设置 `type1=1`：使用叠加逻辑（原有逻辑）
- 设置 `type1=2`：使用放大逻辑（新公式）

### 代码调用
属性计算会自动根据配置表中的 `type1` 字段选择对应的计算方式，无需额外代码修改。

## ✅ 影响范围

### 直接影响
- `AttributeScoreConfig` 配置类
- `Attribute.applyAttributeBonus` 方法
- 所有使用该方法的属性计算逻辑

### 间接影响
- 角色属性计算
- 战力计算
- 技能效果计算

## 🧪 测试建议

### 1. 单元测试
```java
// 调用测试方法
Attribute.testAttributeBonus();
```

### 2. 配置测试
1. 在配置表中设置不同的 `type1` 值
2. 验证计算结果是否符合预期
3. 确保原有功能不受影响

### 3. 集成测试
1. 测试角色属性计算
2. 验证战力计算正确性
3. 检查技能效果是否正常

## ⚠️ 注意事项

### 1. 配置兼容性
- 如果配置表中没有设置 `type1` 字段，默认使用叠加逻辑（type1=1）
- 确保所有相关配置都正确设置了 `type1` 值

### 2. 数值精度
- 计算过程中使用 `double` 类型避免精度丢失
- 最终结果转换为 `long` 类型

### 3. 性能考虑
- 每次属性计算都会查询配置表
- 如有性能要求，可考虑缓存配置信息

## 🔄 后续扩展

如果需要添加更多的加成类型，可以：
1. 在 `type1` 字段中定义新的类型值
2. 在 `applyAttributeBonus` 方法中添加对应的计算逻辑
3. 更新相关文档和测试用例

## 📝 总结

通过新增 `type1` 字段和修改属性计算逻辑，成功实现了两种不同的技能加成方式：
- **叠加模式**：保持原有的直接叠加逻辑
- **放大模式**：使用新的乘法放大公式

这种设计既保证了向后兼容性，又提供了灵活的扩展能力，可以根据游戏需求灵活配置不同属性的加成方式。
