# 词缀生成功能实现说明

## 功能概述

根据提供的图片规则，实现了一个新的词缀属性生成方法 `generateCiZuiAttributes(int id)`，该方法能够按照特定规则生成装备的词缀属性。

## 实现的规则

### 1. 词条属性值随机规则
- 每种词条的属性值在上下限之间随机
- 必须在该范围内，且次随机出来的词条属性值，必须在该范围内

### 2. 词条类型处理

#### 独立类型 (type=1)
- **必出**：独立类型的词条必须出现
- **优先级**：优先放入位置1
- **数量**：每个词缀库只能有一个独立类型词条

#### 一般类型 (type=0)
- **随机**：根据概率权重进行随机选择
- **位置**：填充剩余位置

#### 同源类型 (type=2)
- **互斥性**：如果随机到同源类型，同一装备中只能有一个同源词条
- **限制性**：一旦选择了同源类型，其他位置只能选择一般类型(type=0)

### 3. 位置排序
- 返回的Map使用LinkedHashMap保持插入顺序
- 按照位置1、位置2、位置3、位置4的顺序排列
- 符合图片三中展示的词条组合结构

## 核心方法

### `generateCiZuiAttributes(int id)`
主要的词缀生成方法，实现完整的词缀生成逻辑。

**参数：**
- `id`: 装备配置ID，用于获取词缀库配置

**返回值：**
- `Map<Integer, Long>`: key为属性ID，value为随机生成的属性值
- 使用LinkedHashMap保证顺序

**处理流程：**
1. 获取装备的随机属性配置
2. 获取对应的词缀库配置列表
3. 优先处理独立类型词条（type=1），必出且放在位置1
4. 分离同源类型和一般类型配置
5. 在同源类型和一般类型中随机选择一个词条
6. 如果选中同源类型，后续只能从一般类型中选择
7. 如果选中一般类型，后续可以从所有类型中选择
8. 填充剩余位置，生成最终的属性值映射

### `selectRandomConfigs(List<EquipCiZuiConfig> configs, int count)`
辅助方法，用于从配置列表中根据概率权重随机选择指定数量的配置，确保不重复选择。

**参数：**
- `configs`: 可选择的配置列表
- `count`: 需要选择的数量

**返回值：**
- `List<EquipCiZuiConfig>`: 选中的配置列表（不重复）

**特点：**
- 每次选择后会从候选池中移除已选择的配置
- 避免同一个词条被重复选择

## 技术特点

### 1. 概率权重随机
- 使用 `RandomUtil.randomIndexByProb()` 方法
- 根据每个词条的probability字段进行加权随机
- 避免重复选择相同的词条

### 2. 同源类型互斥处理
- 同源类型与一般类型一起参与随机选择
- 如果选中同源类型，后续位置只能选择一般类型
- 确保每个装备最多只有一个同源类型词条

### 3. 数值生成精度
- 使用现有的 `generateAttributeValue()` 方法
- 支持unit参数控制最小波动精度
- 保证数值在指定范围内的合理分布

## 使用示例

```java
// 生成装备ID为1001的词缀属性
Map<Integer, Long> attributes = ItemUtil.generateCiZuiAttributes(1001);

// 遍历生成的属性（按位置顺序）
for (Map.Entry<Integer, Long> entry : attributes.entrySet()) {
    int attributeId = entry.getKey();    // 属性ID
    long attributeValue = entry.getValue(); // 属性值
    System.out.println("属性ID: " + attributeId + ", 属性值: " + attributeValue);
}
```

## 配置依赖

该功能依赖以下配置表：

1. **EquipSuiJiAttributeConfig** (cfg_equip_suiji_attribute)
   - `cznum`: 词条数量
   - `czkid`: 词缀库ID

2. **EquipCiZuiConfig** (cfg_equip_cizui)
   - `groupId`: 词缀库ID
   - `attributeId`: 属性ID
   - `attributeNum1`: 属性下限
   - `attributeNum2`: 属性上限
   - `unit`: 波动单位
   - `probability`: 随机概率
   - `type`: 词条类型（0=一般，1=独立，2=同源）

## 注意事项

1. **配置完整性**：确保配置表中的数据完整且正确
2. **类型唯一性**：每个词缀库中独立类型（type=1）应该只有一个
3. **概率设置**：确保probability字段设置合理，避免总概率为0的情况
4. **数量限制**：cznum不应超过可用词条的总数

## 兼容性

- 保持与现有 `createCiZuiAttr()` 方法的兼容性
- 可以直接替换现有的词缀生成逻辑
- 不影响其他装备属性生成功能
