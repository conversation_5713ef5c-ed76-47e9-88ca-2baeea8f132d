package com.sh.game.map.notice;

import com.sh.game.common.communication.notice.logic.player.ChangeCareerToSceneNotice;
import com.sh.game.common.communication.notice.scene.ClearServantToPlayerNotice;
import com.sh.game.common.constant.MonsterType;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.Player;
import com.sh.game.map.SceneManager;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.obj.ServantActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.notice.NoticeAction;
import lombok.extern.slf4j.Slf4j;

/**
 * 物品使用相关 NAction
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-08-27
 **/
@NoticeAction
@Slf4j
public class ItemUsageScenceNAction {

    /**
     * 移除技能召唤怪
     **/
    public void ClearServantToSceneNotice(ChangeCareerToSceneNotice notice) {
        long rid = notice.getRid();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }

        // 移除宠物视野
        for (ServantActor servantActor : actor.getServantList()) {
            if (servantActor.getMonsterType() == MonsterType.SKILL_CALL) {
                GameMap map = SceneManager.getInstance().getMap(actor);
                map.removeServant(servantActor);
            }
        }
        actor.getServantList().removeIf(servantActor -> servantActor.getMonsterType() == MonsterType.SKILL_CALL);

        // 清除技能
        actor.getSkillMap().clear();
        actor.getPassiveSkillMap().clear();

//        // 回满血量和蓝量
//        LevelConfig config = ConfigDataManager.getInstance().getById(LevelConfig.class, actor.getLevel());
//        if (config == null) {
//            log.error("level config not found: {}", actor.getLevel());
//            return;
//        }
//
//        long maxHp = config.getAttribute()[notice.getCareer()].get(100);
//        long maxMp = config.getAttribute()[notice.getCareer()].get(102);
//        actor.setHp(maxHp);
//        actor.setMp((int) maxMp);
//        Map<Integer, Long> attributeMap = actor.getFinalAttribute().getAttributeMap();
//        attributeMap.put(100, maxHp);
//        attributeMap.put(102, maxMp);

        // 玩家离线需要再次覆盖随从信息
        if (!actor.isOffline()) {
            return;
        }

        ClearServantToPlayerNotice clearServantNotice = new ClearServantToPlayerNotice(rid);
        actor.sendNotice(ProcessorId.SERVER_PLAYER, clearServantNotice);
    }
}
