package com.sh.game.map.groundevent;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.cache.MapEventCache;
import com.sh.game.common.config.model.MapEventConfig;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;

/**
 * 场景事件的触发器接口
 *
 * <AUTHOR>
 * @date 2014-12-11 下午4:07:37
 */
public abstract class EventTrigger {

    public static final int NONE = 0; // 无此类型
    public static final int DEFAULT = 1;//默认触发（无条件触发）
    public static final int SHABAKE = 2;// 沙巴克
    public static final int HINDER_SKILL = 9;//障碍赛
    /**
     * 以副本创建时间为基础，多少时间后触发
     */
    public static final int DUPLICATE_TIME = 10;

    protected int triggerType;

    public EventTrigger(int triggerType) {
        this.triggerType = triggerType;
    }

    /**
     * 检查触发条件
     *
     * @param event
     * @return
     */
    public boolean check(GameMap map, Event event, PlayerActor player) {
        return conditionCheck(event, player) && triggerCheck(map, event, player) && triggerTimeCheck(event);
    }

    /**
     * 检查触发次数
     * @param event
     * @return
     */
    public boolean triggerTimeCheck(Event event) {
        int triggerTimes = event.getTriggerTimes();
        if (triggerTimes <= 0) {
            return true;
        }
        int triggeredTimes = event.getTriggeredTimes();
        return triggerTimes > triggeredTimes;
    }



    /**
     * 触发器检查
     */
    protected abstract boolean triggerCheck(GameMap map, Event event, PlayerActor player);

    public int getTriggerType() {
        return triggerType;
    }


    /**
     * condition  检测
     *
     * @param event
     * @param player
     * @return
     */
    private boolean conditionCheck(Event event, PlayerActor player) {
        MapEventCache cache = ConfigCacheManager.getInstance().getCache(MapEventCache.class);
        if (cache == null) {
            return false;
        }

        MapEventConfig eventConfig = ConfigDataManager.getInstance().getById(MapEventConfig.class, cache.toMapEventId(event.getId()));
        if (eventConfig != null) {
            return ConditionUtil.validate(player, eventConfig.getCondition(), true);
        }
        return false;
    }

}
