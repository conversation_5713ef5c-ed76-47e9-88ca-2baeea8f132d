package com.sh.game.map.listener;

import com.sh.game.event.EventType;
import com.sh.game.event.IListener;

public class AfterAddBuffToTargetListener implements IListener<HurtEvent> {
    @Override
    public void update(EventType type, HurtEvent param) {
//        Performer target = param.getTarget();
//        if (target instanceof PlayerActor) {
//            GlobalConfig byId = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.CROSS_CEYSTAL_BUFF_LIMIT);
//            if (byId != null) {
//                int[] ints = StringUtil.strToIntArr(byId.getValue(), Symbol.JINHAO);
//                List<Integer> list = StringUtil.intArrToIntList(ints);
//                if (list.contains((int) param.getHurt())) {
//                    PlayerManager.getInstance().collectTerminalCheck((PlayerActor) target);
//                }
//            }
//
//        }
    }
}
