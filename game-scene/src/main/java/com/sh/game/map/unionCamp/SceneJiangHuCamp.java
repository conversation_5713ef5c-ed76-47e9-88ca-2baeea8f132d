package com.sh.game.map.unionCamp;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sh.game.common.communication.notice.SyncCrossCampDataToServerNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.redis.RedisHelper;
import com.sh.game.common.unionCamp.CrossCampData;
import com.sh.game.common.unionCamp.CrossUnionCamp;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.startup.boots.MapContext;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 跨服阵营-势力信息(场景服数据)
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-05-06
 **/
@Slf4j
public class SceneJiangHuCamp {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String findKey() {
        int platformId = MapContext.getOption().getPlatformId();
        int serverId = MapContext.getOption().getServerId();
        return "cross:camp:" + platformId + ":" + serverId;
    }


    /**
     * 查找阵营数据
     *
     * @return 阵营数据
     */
    public static CrossCampData findCampData() {
        CrossCampData crossCampData = new CrossCampData();
        if (MapContext.getServerType() != 2) {
            return crossCampData;
        }
        String campStr = RedisHelper.exec(redisCommands -> redisCommands.get(findKey()));
        if (campStr == null || "null".equals(campStr)) {
            return crossCampData;
        }
        try {
            crossCampData = OBJECT_MAPPER.readValue(campStr, CrossCampData.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return crossCampData;
    }

    /**
     * 更新阵营行会信息
     *
     * @param campType 阵营类型
     * @param union    阵营行会
     * @param isJoin   是否参加
     */
    public static synchronized void updateCampUnion(int campType, CrossUnionCamp union, boolean isJoin) {
        if (MapContext.getServerType() != 2) {
            return;
        }
        CrossCampData campData = findCampData();
        Map<Integer, Set<CrossUnionCamp>> camp = campData.getCamp();
        camp.putIfAbsent(campType, new HashSet<>());

        // 移除旧数据后再更新
        camp.forEach((k, v) -> v.removeIf(e->e.getId() == union.getId()));
        if (isJoin) {
            if (camp.get(campType).add(union)) {
                log.info("加入阵营-成功#逻辑服务器：{}，的行会：{}，行会名：{}，加入阵营：{}",
                        union.getSid(), union.getId(), union.getName(), campType);
            } else {
                log.warn("加入阵营-失败#逻辑服务器：{}，的行会：{}，行会名：{}，加入阵营：{} 失败，已在该阵容中",
                        union.getSid(), union.getId(), union.getName(), campType);
            }
        } else {
            log.info("退出阵营-成功#逻辑服务器：{}，的行会：{}，行会名：{}，退出阵营：{}",
                    union.getSid(), union.getId(), union.getName(), campType);
            campData.getQuitCampStamp().put(union.getId(), TimeUtil.getNowOfSeconds());
        }

        if (saveCampData(campData)) {
            synCampData(union.getId());
        }
    }

    /**
     * 依次接受每个逻辑服数据，全量更新阵营信息
     *
     * @param serverCampData 阵营列表
     */
    public static synchronized void updateOneServerCampUnion(List<CrossUnionCamp> serverCampData) {
        if (MapContext.getServerType() != 2) {
            return;
        }
        CrossCampData campData = findCampData();
        Map<Integer, Set<CrossUnionCamp>> camp = campData.getCamp();
        // 移除
        for (CrossUnionCamp unionCamp : serverCampData) {
            Set<CrossUnionCamp> camps = camp.get(unionCamp.getCampType());
            if (camps == null) {
                continue;
            }
            camps.removeIf(e -> e.getId() == unionCamp.getId());
        }

        // 添加
        for (CrossUnionCamp unionCamp : serverCampData) {
            camp.putIfAbsent(unionCamp.getCampType(), new HashSet<>());
            camp.get(unionCamp.getCampType()).add(unionCamp);
            log.info("更新阵营-午夜更新阵营信息#逻辑服务器：{}，的行会：{}，行会名：{}，详情：{}",
                    unionCamp.getSid(), unionCamp.getId(), unionCamp.getName(), JSON.toJSONString(unionCamp));
        }
        List<Long> updateUnionIdList = serverCampData.stream().map(CrossUnionCamp::getId).collect(Collectors.toList());
        if (saveCampData(campData)) {
            synCampData(updateUnionIdList);
        }
    }


    /**
     * 保存阵营信息
     */
    private static boolean saveCampData(CrossCampData campData) {
        if (MapContext.getServerType() != 2) {
            return false;
        }
        String dateStr = null;
        try {
            dateStr = OBJECT_MAPPER.writeValueAsString(campData);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if (dateStr == null || "null".equals(dateStr)) {
            return false;
        }

        String finalDateStr = dateStr;
        String exec = RedisHelper.exec(redisCommands -> redisCommands.set(findKey(), finalDateStr));
        return exec.equals("OK");
    }


    public static void synCampData(List<Long> unionIdList) {
        SyncCrossCampDataToServerNotice notice = new SyncCrossCampDataToServerNotice();
        notice.setCampData(findCampData());
        notice.setUnionIdList(unionIdList);
        Module.NET.getAllClient().forEach(connection -> {
            if (connection != null && connection.available()) {
                notice.addHost(connection.getHostId());
            }
        });
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);
    }

    public static void synCampData() {
        SyncCrossCampDataToServerNotice notice = new SyncCrossCampDataToServerNotice();
        notice.setCampData(findCampData());
        Module.NET.getAllClient().forEach(connection -> {
            if (connection != null && connection.available()) {
                notice.addHost(connection.getHostId());
            }
        });
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);
    }


    public static void synCampData(long unionId) {
        List<Long> unionIdList = new ArrayList<>();
        unionIdList.add(unionId);
        synCampData(unionIdList);
    }
}
