package com.sh.game.map.route;

import com.sh.game.map.scene.point.Point;
import lombok.Data;

/**
 * 开启节点的二叉堆
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:53:42
 */
@Data
@Deprecated
public class OpenPointBinaryHeap {

	private int reservedSize = 0;

	private int capacity = 0;

	private int size = 0;

	private Point[] array;
	private int[] F;

	public void enlargeSize(int newCapacity) {
		if (newCapacity > reservedSize) {
			reservedSize = newCapacity;
			this.array = new Point[this.reservedSize];
			this.F = new int[this.reservedSize];
		}
		this.capacity = newCapacity;
	}

	private void swapPoint(int i, int j) {
		Point temp = array[i];
		array[i] = array[j];
		array[j] = temp;

		int tF = F[i];
		F[i] = F[j];
		F[j] = tF;
	}

	public void push(Point e, int F) {
		if (size == capacity) {
			throw new RuntimeException("The heap has not enough space!");
		}
		array[size] = e;
		this.F[size] = F;
		this.size++;
		int fatherIndex = (size >> 1) - 1; // 由于数组索引下表是从0开始的， 所以，这里
		// 左孩子：2i+1, 右孩子：2i+2
		int currentIndex = size - 1;
		siftUp(fatherIndex, currentIndex);
	}

	private void siftUp(int fatherIndex, int currentIndex) {
		while (fatherIndex >= 0) {
			if (F[fatherIndex] >= F[currentIndex]) {
				swapPoint(fatherIndex, currentIndex);
				currentIndex = fatherIndex;
				fatherIndex = ((currentIndex + 1) >> 1) - 1;
			} else {
				break;
			}
		}
	}

	public Point pop() {
		if (size <= 0) {
			throw new RuntimeException("The heap is empty!");
		}
		Point ret = array[0];
		swapPoint(0, size - 1);
		size--;

		if (size == 0) {
			return ret;
		}

		int currentIndex = 0;
		int childIndex = (currentIndex << 1) + 1; // 由于数组索引下表是从0开始的， 所以，这里
		// 左孩子：2i+1, 右孩子：2i+2
		while (childIndex < size) {

			if (childIndex + 1 < size && F[childIndex] > F[childIndex + 1]) {
				childIndex++;
			}

			if (F[currentIndex] <= F[childIndex]) {
				break;
			}

			swapPoint(currentIndex, childIndex);

			currentIndex = childIndex;
			childIndex = (currentIndex << 1) + 1;
		}

		return ret;
	}

	public void update(Point Point, int newF) {

		int currentIndex = -1;
		for (int i = 0; i < size; i++) {
			Point pPoint = array[i];
			if (pPoint == Point) { // Point在整个地图中是唯一的，所以，这里为了速度快，直接用等号
				currentIndex = i;
				break;
			}
		}

		if (currentIndex == -1) {
			return;
		}
		this.F[currentIndex] = newF;

		// f值只会变小，不会变大，所以只要和父节点比较就可以了。
		int fatherIndex = ((currentIndex + 1) >> 1) - 1;
		siftUp(fatherIndex, currentIndex);

	}

	public void clear() {
		for (int i = 0; i < size; i++) {
			array[i] = null;
			F[i] = 0;
		}
		this.size = 0;
	}

	public boolean isEmpty() {
		return this.size == 0;
	}

	@Override
	public String toString() {
		return "";
	}

//    public static void main(String[] args) {
//        OpenPointBinaryHeap heap = new OpenPointBinaryHeap(20);
//        Point Point1 = new Point(1, 2);
//        Point1.setF(90);
//        heap.push(Point1);
//        Point Point2 = new Point(4, 5);
//        Point2.setF(30);
//        heap.push(Point2);
//        Point Point3 = new Point(1, 3);
//        Point3.setF(100);
//        heap.push(Point3);
//        Point Point4 = new Point(4, 2);
//        Point4.setF(60);
//        heap.push(Point4);
//        System.out.println(Arrays.toString(heap.array));
//        Point3.setF(5);
//        heap.update(Point3);
//        System.out.println(Arrays.toString(heap.array));
//        heap.pop();
//        System.out.println(Arrays.toString(heap.array));
//
//    }

}
