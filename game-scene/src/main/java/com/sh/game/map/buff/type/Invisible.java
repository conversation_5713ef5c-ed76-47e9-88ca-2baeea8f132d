package com.sh.game.map.buff.type;

public class Invisible extends AbsBuffEffect{

//    @Override
//    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
//        buff.initParam(b -> {
//            int[][] extraParam = BuffParamEnhanceUtil.calExtraParam(caster, buff.config());
//            buff.getParams().add(extraParam[0][0]);
//        });
//        performer.getBuffState().increase(BuffState.State.INVISIBLE, stack);
//        performer.getBuffState().setInvisibleRange(buff.fetchParam(0));
//    }
//
//    @Override
//    public void onRemove(Performer performer, Buff buff, int stack) {
//        performer.getBuffState().decrease(BuffState.State.INVISIBLE, stack);
//        if (!performer.getBuffState().inStateOf(BuffState.State.INVISIBLE)) {
//            performer.getBuffState().setInvisibleRange(0);
//        }
//    }
}
