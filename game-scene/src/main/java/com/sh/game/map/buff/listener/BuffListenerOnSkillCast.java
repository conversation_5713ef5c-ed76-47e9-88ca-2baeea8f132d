package com.sh.game.map.buff.listener;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.event.EventType;
import com.sh.game.event.IListener;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.listener.SkillCastEvent;

public class BuffListenerOnSkillCast implements IListener<SkillCastEvent> {

    @Override
    public void update(EventType type, SkillCastEvent param) {
        SkillConditionConfig config = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, param.getSkill().getSkillId() + Symbol.JINHAO + param.getSkill().getLevel());
        if (config == null) {
            return;
        }
        if (config.getBuffercondition() > 0 && param.getTarget().getLevel() - param.getCaster().getLevel() > config.getBuffercondition()) {
            return;
        }

        int[] buffs = ParamAddUtil.calSkillConditionBuff(param.getCaster().getSkillSpecEffectMap(), config);
        for (int buffId : buffs) {
            BuffManager.getInstance().append(param.getTarget(), buffId, param.getCaster(), param.getEffect(), param.getTime());
        }
    }

}
