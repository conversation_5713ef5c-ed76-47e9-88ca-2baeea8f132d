package com.sh.game.map.fight.effect.impl;

import com.sh.game.common.config.model.SkillEffectConfig;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.fight.effect.AbstractEffect;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;

/**
 * mp恢复效果
 */
public class MpRecoveryEffect extends AbstractEffect {
    @Override
    public void apply(GameMap map, EffectContext context) {
        Performer target = context.target;
        SkillEffectConfig effectConfig = context.effectConfig;
        if (target.isDead()) {
            return;
        }
        if (target.getBuffState().inStateOf(BuffState.State.AVOID)) {
            return;
        }
        if (target.addMp(effectConfig.getParameter().get(0)[0], false)) {
            context.hurt = effectConfig.getParameter().get(0)[0];
            context.replaceFloaterType(FightConst.FloaterType.RECOVER_MP);
        }
    }
}
