package com.sh.game.map.daily.controller.type;

import com.sh.game.common.constant.EmailConst;
import com.sh.game.map.daily.controller.Controller;
import com.sh.game.map.duplicate.instance.GoldPigDuplicate;
import com.sh.game.map.mail.SceneMailManager;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

import java.util.Iterator;
import java.util.Map;

/**
 * 金猪副本
 */
public class GoldPigController extends Controller {

    /**
     * 清除金猪
     *
     * @param map
     */
    @Override
    public void onMapFinish(GameMap map) {
        Iterator<Map.Entry<Long, MonsterActor>> iterator = map.getMonsterMap().entrySet().iterator();
        while (iterator.hasNext()) {
            MonsterActor value = iterator.next().getValue();
            iterator.remove();
            map.removeMonster(value);
        }
        //发邮件
        GoldPigDuplicate duplicate = (GoldPigDuplicate) map;
        if (!duplicate.getEnterPlayer().isEmpty()) {
            duplicate.getEnterPlayer().forEach(rid -> SceneMailManager.getInstance().sendMail(rid, EmailConst.MailId.GOLD_PIG_REWARD, null));
        }
    }
}
