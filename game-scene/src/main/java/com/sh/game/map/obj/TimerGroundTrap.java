package com.sh.game.map.obj;

import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.config.model.SkillEffectConfig;
import com.sh.game.common.constant.CdConst;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.gemo.shap.Shape;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.fight.FightManager;
import com.sh.game.map.fight.effect.AbstractEffect;
import com.sh.game.map.fight.effect.SkillEffectFactory;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.scene.GameMap;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Getter
@Setter
public class TimerGroundTrap extends GroundTrap {
    private List<SkillEffectConfig> effects;

    /**
     * 触发CD
     */
    private int cd;

    public TimerGroundTrap(SkillEffectConfig config, Performer master, Shape area, Vec3 pos, int duration) {
        super(config, master, area, pos, duration);
    }


    @Override
    public int trapType() {
        return 1;
    }

    @Override
    public boolean isEnemy(IMapObject obj, boolean ignoreTargetOnly) {
        return master.isEnemy(obj, ignoreTargetOnly);
    }

    @Override
    public boolean isFriend(IMapObject obj, boolean ignoreTargetOnly) {
        return master.isFriend(obj, ignoreTargetOnly);
    }

    @Override
    public void trigger(GameMap map, int dt) {
        if (effects == null || effects.isEmpty()) {
            return;
        }
        if (!CDUtil.isCool(this, CdConst.CdType.GROUND_TRAP, CdConst.CdKey.COMMON_KEY)) {
            return;
        }
        List<Performer> enemy = new ArrayList<>();
        List<Performer> friend = new ArrayList<>();
        map.visObject(area, (obj) -> {
            if (!area.contains(obj.getPosition().x, obj.getPosition().y)) {
                return;
            }
            if (isEnemy(obj, false)) {
                enemy.add(obj);
            }
            if (isFriend(obj, false)) {
                friend.add(obj);
            }
        });
        List<EffectContext> effectContexts = new ArrayList<>(effects.size());
        for (SkillEffectConfig effectConfig : effects) {
            List<Performer> targets;
            if (effectConfig.getTrapTarget() == 1) {
                targets = enemy;
            } else if (effectConfig.getTrapTarget() == 2) {
                targets = friend;
            } else {
                targets = new ArrayList<>(enemy);
                targets.addAll(friend);
            }
            for (Performer target : targets) {
                EffectContext context = new EffectContext(getMaster(), target, effectConfig);
                AbstractEffect skillEffect = SkillEffectFactory.getSkillEffect(effectConfig.getType());
                skillEffect.apply(map, context);
                context.hp = target.getHp();
                effectContexts.add(context);
            }
        }
        CDUtil.addCd(this, CdConst.CdType.GROUND_TRAP, CdConst.CdKey.COMMON_KEY, TimeUtil.getNowOfMills() + cd);
        FightManager.getInstance().sendSkillEffectMsg(getMaster(), null, map, effectContexts);

    }
}
