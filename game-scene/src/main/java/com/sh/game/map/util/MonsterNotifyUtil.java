package com.sh.game.map.util;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.ResMonsterNotifyMessage;
import com.sh.game.common.communication.msg.map.bean.PointBean;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.map.Module;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;

/**
 * 怪物预告工具类
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/9/23.
 */
@Slf4j
public class MonsterNotifyUtil {


    public static int birth = 1;
    public static int die = 2;


    public static void playerEnterNotify(PlayerActor playerActor, GameMap gameMap) {

        Collection<MonsterActor> monsterActors = gameMap.getMonsterMap().values();
        /**
         * 进入场景玩家广播一下,需要预告的怪物
         */
        for (MonsterActor monsterActor : monsterActors) {
            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterActor.getConfigId());
            if (monsterConfig == null) {
                continue;
            }
            if (monsterConfig.getNotify() == 0) {
                continue;
            }
            if (monsterActor.isDead()) {
                continue;
            }

            ResMonsterNotifyMessage notifyMessage = new ResMonsterNotifyMessage();
            notifyMessage.setAction(1);
            notifyMessage.setUniqueId(monsterActor.getId());
            PointBean pointBean = new PointBean();
            pointBean.setX(monsterActor.getPoint().x);
            pointBean.setY(monsterActor.getPoint().y);
            notifyMessage.setPoint(pointBean);
            notifyMessage.setCfgId(monsterConfig.getId());
            //发给我自己
            Module.MSG_TRANSFORMER.sendMsg(notifyMessage, playerActor.getId());
            log.error("发送出生的时候消息:{}", notifyMessage);
        }

    }


    /**
     * 更新需要预告怪物信息
     *
     * @param monsterActor
     * @param map
     * @param action  操作
     */
    public static void monterUpdateNotify(MonsterActor monsterActor, GameMap map, int action) {
        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterActor.getConfigId());
        if (monsterConfig == null) {
            return;
        }
        //不通知
        if (monsterConfig.getNotify() == 0) {
            return;
        }


        ResMonsterNotifyMessage notifyMessage = new ResMonsterNotifyMessage();
        notifyMessage.setAction(action);
        notifyMessage.setUniqueId(monsterActor.getId());
        PointBean pointBean = new PointBean();
        pointBean.setX(monsterActor.getPoint().x);
        pointBean.setY(monsterActor.getPoint().y);
        notifyMessage.setPoint(pointBean);
        notifyMessage.setCfgId(monsterConfig.getId());

        //要发送全场景玩家
        Module.MSG_TRANSFORMER.sendMsgToRids(notifyMessage, map.getPlayerMap().keySet());
//        log.error("更新的时候发送消息:{}", notifyMessage);

    }


}
