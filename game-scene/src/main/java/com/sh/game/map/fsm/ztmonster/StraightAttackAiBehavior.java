package com.sh.game.map.fsm.ztmonster;

import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.gemo.shap.Circle;
import com.sh.game.common.gemo.shap.Shape;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.fight.skill.PerformerSkillReleaser;
import com.sh.game.map.fsm.ai.AIData;
import com.sh.game.map.move.NewMoveManager;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.util.GeomUtil;


import java.util.List;

import static com.sh.game.map.fsm.FSMState.*;

public class StraightAttackAiBehavior extends NormalAttackAiBehavior {
    private void goStraight(GameMap map,MonsterActor performer,Vec3 target) {
        double distance = GeomUtil.distance(performer.getPosition(), target);
        float stepDistance = (float) performer.getMoveSpeed() / TimeUtil.ONE_MILLS * performer.getMoveInterval();
        if(map.isWalkable(target)){
            NewMoveManager.getInstance().performerMove(map, performer, target, true);
        }
        double rate = distance / stepDistance;
        int sleepTime = rate > 1 ? (int) (performer.getMoveInterval() * rate) : performer.getMoveInterval();
        performer.getMachine().getAiData().incClockSleep(sleepTime);
    }

    @Override
    public void alertUpdate(GameMap map, MonsterActor performer, int dt) {
        AIData aiData = performer.getMachine().getAiData();
        if (aiData.getClockSleep() > 100) {
            return;
        }
        // 目标点只初始化一次
        Vec3 targetPos = performer.getMachine().getAiData().getTargetPos();
        if (targetPos == null) {
            Shape shape = new Circle(performer.getPosition().x, performer.getPosition().y, performer.getMinAttackRange());
            Performer target = null;
            List<Performer> filtered = map.filterObject(shape, false, obj -> {
                if (!obj.isMonster()) {
                    return false;
                }
                MonsterActor monster = (MonsterActor) obj;
                return monster.getMonsterType() != 9 && obj.getHitBox().intersect(shape);
            });
            if (!filtered.isEmpty()) {
                target = filtered.get(0);
            }
            if(target != null) {
                targetPos = target.getPoint().toVec3();
                performer.getMachine().getAiData().setTargetPos(targetPos);
            }
        }
        PerformerSkillReleaser skillReleaser = PerformerSkillReleaser.findSkillReleaser(map, performer);
        // 找技能
        if (skillReleaser != null) {
            skillReleaser.skillCost();
            aiData.setSkillReleaser(skillReleaser);
            performer.getMachine().updateMachine(map,0);
        }else{
            if(targetPos != null) {
                goStraight(map,performer,targetPos);
            }
        }
    }

    @Override
    public int alertTransition(GameMap map, MonsterActor performer, int dt) {
        if (performer.isDead()) {
            return DIE;
        }
        if (map.isSafe()) {
            return ACTIVE;
        }
        AIData aiData = performer.getMachine().getAiData();
        PerformerSkillReleaser skillReleaser = aiData.getSkillReleaser();
        if (skillReleaser != null) {
            SkillBattleConfig config = skillReleaser.getConfig();
            if (config.getQianyao() > 0) {
                return SPELL;
            }
            // 瞬发技能直接释放
            return CAST;
        }
        return ALERT;
    }
}
