package com.sh.game.map.groundevent.executor;

import com.sh.game.map.duplicate.instance.XiuLuoDuplicate;
import com.sh.game.map.groundevent.Event;
import com.sh.game.map.groundevent.EventExecutor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/8/12 19:16
 */
@Slf4j
public class XiuLuoReliveExecutor extends EventExecutor {

    private int addCount;

    public XiuLuoReliveExecutor(int addCount) {
        super(XIU_LUO_RELIVE_COUNT_ADD);
        this.addCount = addCount;
    }

    @Override
    public void execute(GameMap map, Event event, PlayerActor player) {
        if (map instanceof XiuLuoDuplicate) {
            XiuLuoDuplicate duplicate = (XiuLuoDuplicate) map;
            // duplicate.addReliveCount(player, addCount);
        }
    }
}
