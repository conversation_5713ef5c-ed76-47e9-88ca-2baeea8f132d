package com.sh.game.map.duplicate.condition;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/9/24
 */
public class DuplicateTimeEndCondition extends DuplicateEndCondition{
    public DuplicateTimeEndCondition(int type, int[] value) {
        super(type, value);
        progress = new int[2];
    }

    @Override
    public boolean check() {
        return progress[1] >= progress[0];
    }

    @Override
    public boolean update(Object obj) {
        int[] array = (int[])obj;
        progress = array;
        return check();
    }

    @Override
    public void setCount(int count) {
        progress[1] = count;
    }
}
