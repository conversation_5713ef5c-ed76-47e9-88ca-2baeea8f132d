package com.sh.game.map.duplicate.instance;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.pvp.bean.BattleGroupBean;
import com.sh.game.common.communication.msg.pvp.bean.BattlePlayerBean;
import com.sh.game.common.communication.msg.system.xiuluo.ResXiuLuoDuoQiInfoMessage;
import com.sh.game.common.communication.msg.system.xiuluo.ResXiuLuoDuoQiViewUpdateMessage;
import com.sh.game.common.communication.msg.system.xiuluo.ResXiuLuoDupInfoMessage;
import com.sh.game.common.communication.msg.system.xiuluo.ResXiuLuoEventMessage;
import com.sh.game.common.communication.msg.system.xiuluo.bean.XiuLuoDuplicateInfoBean;
import com.sh.game.common.communication.msg.system.xiuluo.bean.XiuLuoEventBean;
import com.sh.game.common.communication.notice.SwitchFightModuleNotice;
import com.sh.game.common.communication.notice.XiuLuoEndNotice;
import com.sh.game.common.communication.notice.entity.XiuLuoInfo;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.config.model.MapEventConfig;
import com.sh.game.common.config.model.XiuLuoKillConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.announce.AnnounceManager;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.groundevent.Event;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.topography.Topography;
import com.sh.game.map.util.TipUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/11 17:41
 */
@Slf4j
public class XiuLuoDuplicate extends Duplicate {

    private boolean jieSuan = false;

    /**
     * 存放本次匹配的玩家
     */
    private Map<Long, PlayerActor> joinPlayerMap = new HashMap<>(6);
    /**
     * 玩家可复活次数表
     */
    // private Map<Long, Integer> playerCanReliveCountMap = new HashMap<>(6);
    /**
     * 玩家已复活次数表
     */
    // private Map<Long, Integer> playerDoReliveCountMap = new HashMap<>(6);
    /**
     * 玩家击杀表
     */
    private Map<Long, Integer> playerKillCountMap = new HashMap<>(6);
    /**
     * 玩家夺旗表
     */
    private Map<Long, Integer> playerQiZiMap = new HashMap<>(6);
    /**
     * 队伍表
     */
    private Map<Long, Set<Long>> teamMap = new HashMap<>(2);
    /**
     * 获胜队伍id
     */
    private long winTeamId;
    /**
     * 出生点下标表
     */
    private Map<Long, Integer> pointIndexMap = new HashMap<>(2);
    /**
     * 出生点数据
     */
    private List<int[]> birthPoint;
    /**
     * 玩家击杀时间表
     */
    private Map<Long, TwoTuple<Integer, Integer>> playerKillTimeMap = new HashMap<>(6);
    /**
     * 玩家最大连杀表
     */
    private Map<Long, Integer> playerMaxLianShaMap = new HashMap<>(6);
    /**
     * 玩家积分表用于mvp计算
     */
    private Map<Long, TwoTuple<Integer, Long>> jiFenMap = new HashMap<>(6);

    private Map<Long, Integer> playerTeamMap = new HashMap<>(6);

    private boolean firstKill;

    private Map<Long, List<TwoTuple<Integer, Integer>>> playerEventMap = new HashMap<>(6);

    private List<MapEventConfig> eventConfigs;

    /**
     * 心跳方法检查间隔
     */
    private int heartInterval = 1000;

    /**
     * 玩家夺旗数量
     */
    private Map<Long, Integer> duoQiCount = new HashMap<>(6);

    private Map<Long, Integer> playerDieCountMap = new HashMap<>(6);

    // /**
    //  * 结束方法检查间隔
    //  */
    // private int checkEndInterval = 1000;

    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        super.init(key, host, config, topography, param);
        if (!(param instanceof List)) {
            return;
        }
        List<Object> list = (List<Object>) param;
        for (Object o : list) {
            if (!(o instanceof BattleGroupBean)) {
                return;
            }
            BattleGroupBean bean = (BattleGroupBean) o;
            for (BattlePlayerBean playerBean : bean.getPlayerList()) {
                playerTeamMap.put(playerBean.getRid(), bean.getTeam());
            }
        }
    }

    @Override
    public void enterPlayer(PlayerActor player, Vec3 point) {
//        player.setTempTitle(0);
//        player.setDuoQi(0);
//        player.setQiZiJieSuanTime(0);
//        player.getAppearanceWears().put(4, 0);
//        sendQiZiUpdateMsg(player);
//
//        Integer team = playerTeamMap.getOrDefault(player.getRid(), 0);
//        // 只在战斗服覆盖一下队伍id，毕竟玩家在战斗服玩3v3只能在这一个地图，回本服会被覆盖回去
//        player.setTeamID(team);
//        int[] birthPoint = getBirthPoint(player);
//        if (birthPoint == null || birthPoint.length < 3) {
//            super.enterPlayer(player, point);
//            return;
//        }
//        Point bp = getPoint(birthPoint[0], birthPoint[1], birthPoint[2]);
//        if (bp == null) {
//            super.enterPlayer(player, point);
//        } else {
//            player.setX(bp.x);
//            player.setY(bp.y);
//            player.setPoint(bp);
//            super.enterPlayer(player, bp);
//        }
    }

    private void sendDupInfo() {
        // 玩家杀人数，夺旗数，复活次数，可复活次数
        ResXiuLuoDupInfoMessage msg = new ResXiuLuoDupInfoMessage();
        for (PlayerActor actor : joinPlayerMap.values()) {
            XiuLuoDuplicateInfoBean bean = new XiuLuoDuplicateInfoBean();
            bean.setRid(actor.getId());
            bean.setName(actor.getName());
            bean.setTeamId(actor.getTeamID());
            bean.setKillCount(playerKillCountMap.getOrDefault(actor.getId(), 0));
            bean.setQiZiCount(jiFenMap.getOrDefault(actor.getId(), new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst());
            // bean.setCanRelive(playerCanReliveCountMap.getOrDefault(actor.getId(), 0));
            // bean.setReliveCount(playerDoReliveCountMap.getOrDefault(actor.getId(), 0));
            List<TwoTuple<Integer, Integer>> list = playerEventMap.getOrDefault(actor.getId(), new ArrayList<>(0));
            for (TwoTuple<Integer, Integer> event : list) {
                XiuLuoEventBean xiuLuoEventBean = new XiuLuoEventBean();
                xiuLuoEventBean.setEventId(event.getFirst());
                xiuLuoEventBean.setTriggerTime(event.getSecond());
                bean.getEventInfo().add(xiuLuoEventBean);
            }
            msg.getInfo().add(bean);
        }
        Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
    }

    @Override
    public void afterEnterMap(PlayerActor player) {
        super.afterEnterMap(player);
        // 进图回满血
        player.setHp(AttributeEnum.ZTXY_HP.getAttrValue(player.getFinalAttribute()));
        player.sendHpMpChangeMessage();

        joinPlayerMap.put(player.getId(), player);
        teamMap.computeIfAbsent(player.getTeamID(), k -> new HashSet<>()).add(player.getId());
        // int reliveCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_RELIVE_COUNT);
        // playerCanReliveCountMap.put(player.getId(), reliveCount);

        log.info("3v3,玩家进入地图，队伍id {} 玩家id {} name {} 地图id {}", player.getTeamID(), player.getId(), player.getName(), this.getId());

        if (eventConfigs != null) {
            ResXiuLuoEventMessage msg = new ResXiuLuoEventMessage();
            List<Integer> list = eventConfigs.stream().map(MapEventConfig::getId).collect(Collectors.toList());
            msg.getEventId().addAll(list);
            Module.MSG_TRANSFORMER.sendMsg(msg, player.getId());
        }
        sendDupInfo();
        sendJiFenInfo();
    }

    private int[] getBirthPoint(PlayerActor actor) {
        if (birthPoint == null) {
            List<int[]> list = new JinHaoAndYuHaoListConverter().convert(GlobalUtil.getGlobalValue(GameConst.GlobalId.XIU_LUO_BIRTH_POINT));
            if (list.size() != 2 || list.get(0).length < 2) {
                log.error("3v3队伍配置出生点错误，配置id {} 地图id {}", GameConst.GlobalId.XIU_LUO_BIRTH_POINT, this.getId());
                return null;
            }
            birthPoint = list;
        }

        if (pointIndexMap.isEmpty()) {
            pointIndexMap.put(actor.getTeamID(), 0);
        } else if (!pointIndexMap.containsKey(actor.getTeamID())) {
            pointIndexMap.put(actor.getTeamID(), 1);
        }

        Integer index = pointIndexMap.get(actor.getTeamID());
        if (index == null) {
            return null;
        }
        return birthPoint.get(index);
    }

    // @Override
    // public int[] commonRelivePoint(Player player) {
    //     long teamId = 0;
    //     for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
    //         if (entry.getValue().contains(player.getId())) {
    //             teamId = entry.getKey();
    //             break;
    //         }
    //     }
    //
    //     if (teamId == 0) {
    //         return super.commonRelivePoint(player);
    //     }
    //
    //
    //     Integer index = pointIndexMap.get(teamId);
    //     if (index == null) {
    //         return super.commonRelivePoint(player);
    //     }
    //     int[] xy = birthPoint.get(index);
    //     return new int[]{this.cfgId, xy[0], xy[1]};
    // }

    @Override
    public void onPlayerDie(PlayerActor playerActor) {

        playerDieCountMap.compute(playerActor.getId(), (k, v) -> {
            if (v == null) {
                return 1;
            }
            v++;
            return v;
        });

        long killerId = playerActor.getKillerId();
        PlayerActor killer = playerMap.get(killerId);
        if (killer == null) {
            return;
        }

        if (killer.getTeamID() == playerActor.getTeamID()) {
            return;
        }

        Integer count = playerKillCountMap.compute(killer.getId(), (k, v) -> {
            if (v == null) {
                return 1;
            }
            return v + 1;
        });
        if (playerActor.getDuoQi() > 0) {
            playerActor.setTempTitle(0);
            playerActor.setDuoQi(0);
            playerActor.setQiZiJieSuanTime(0);
            playerActor.getAppearanceWears().put(4, 0);
            sendQiZiUpdateMsg(playerActor);

            killer.setTempTitle(GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
            killer.setDuoQi(1);
            killer.getAppearanceWears().put(4, GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
            TipUtil.show(killer.getId(), CommonTips.抢夺旗子成功);
            int time = GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_EXIST_TIME);
            killer.setQiZiJieSuanTime(TimeUtil.getNowOfSeconds() + time);
            sendQiZiUpdateMsg(killer);
            log.info("3v3，玩家抢夺旗子成功，玩家id {} name {} 地图id {}", killer.getId(), killer.getName(), this.getId());

            duoQiCount.compute(killer.getId(), (k, v) -> {
                if (v == null) {
                    v = 0;
                }
                v++;
                return v;
            });
        }
        int now = TimeUtil.getNowOfSeconds();
        playerKillTimeMap.compute(killerId, (k, v) -> {
            if (v == null) {
                v = new TwoTuple<>(now, 0);
            }
            if (now - v.getFirst() > 60) {
                v = new TwoTuple<>(now, 1);
            } else {
                Integer lianSha = v.getSecond();
                int maxLian = lianSha + 1;
                v.setSecond(maxLian);
                playerMaxLianShaMap.compute(killerId, (key, value) -> {
                    if (value == null) {
                        return maxLian;
                    }
                    return Math.max(value, maxLian);
                });
                sendLianShaAnnounce(killer, maxLian);
            }
            return v;
        });
        int killJiFen = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_KILL_JI_FEN);
        Integer jiFen = jiFenMap.compute(killer.getId(), (k, v) -> {
            if (v == null) {
                return new TwoTuple<>(killJiFen, TimeUtil.getNowOfMills());
            }
            return new TwoTuple<>(killJiFen + v.getFirst(), TimeUtil.getNowOfMills());
        }).getFirst();
        log.info("3v3玩家击杀，队伍id {} 玩家id {} name {} 玩家击杀次数 {} 玩家总积分 {} 被击杀者id {} name {} 地图id {}", killer.getTeamID(), killer.getId(), killer.getName(), count, jiFen, playerActor.getId(), playerActor.getName(), this.getId());
        // checkReliveCount();
        // checkKillEnd();
        checkJiFenEnd();
        sendJiFenInfo();
        sendDupInfo();
    }

    private void sendLianShaAnnounce(PlayerActor actor, int lianSha) {
        if (!firstKill) {
            AnnounceManager.getInstance().post(ChatConst.AnnounceId.XIU_LUO_FIRST_KILL, this.getId(), actor);
            firstKill = true;
        } else {
            XiuLuoKillConfig config = ConfigDataManager.getInstance().getById(XiuLuoKillConfig.class, lianSha);
            if (config != null) {
                AnnounceManager.getInstance().post(config.getAnnounce(), this.getId(), actor);
            }
        }
    }

    // @Override
    // public void relive(PlayerActor playerActor) {
    //     super.relive(playerActor);
    //     playerDoReliveCountMap.compute(playerActor.getId(), (k, v) -> {
    //         if (v == null) {
    //             v = 0;
    //         }
    //         return v + 1;
    //     });
    // }

    // public void addReliveCount(PlayerActor actor, int count) {
    //     Integer relive = playerCanReliveCountMap.compute(actor.getId(), (k, v) -> {
    //         if (v == null) {
    //             v = 0;
    //         }
    //         return v + count;
    //     });
    //     log.info("3v3，玩家触发修罗战场增加复活次数事件，队伍id {} 玩家id {} name {} 增加次数 {} 可复活次数 {}", actor.getTeamID(), actor.getId(), actor.getName(), count, relive);
    // }

    // @Override
    // public boolean moveCheck(PlayerActor actor, Point point) {
    //     Integer canReliveCount = playerCanReliveCountMap.getOrDefault(actor.getId(), 0);
    //     Integer doReliveCount = playerDoReliveCountMap.getOrDefault(actor.getId(), 0);
    //     if (canReliveCount > doReliveCount) {
    //         return true;
    //     }
    //     PointState state = this.getPointState(point);
    //     if (state == null) {
    //         return false;
    //     }
    //     return state.isSafe();
    // }

    @Override
    public void afterPlayerExit(PlayerActor playerActor) {
        super.afterPlayerExit(playerActor);
        playerActor.setOffline(true);
        // playerActor.setDuoQi(0);
        // playerActor.setQiZiJieSuanTime(0);
        // playerActor.setTempTitle(0);
        // playerActor.getAppearanceWears().put(4, 0);
        Player player = PlayerManager.getInstance().getPlayer(playerActor.getId());
        if (player == null) {
            return;
        }
        playerActor.setFightModel(FightConst.FightModel.UNION);
        SwitchFightModuleNotice notice = new SwitchFightModuleNotice(playerActor.getId(), playerActor.getFightModel());
        notice.addHost(player.getRemoteHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, playerActor.getId());

        checkTeamExit(playerActor);
    }

    private void checkTeamExit(PlayerActor playerActor) {
        long otherTeamId = 0;
        boolean allOffLine = true;
        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            if (entry.getKey() != playerActor.getTeamID()) {
                otherTeamId = entry.getKey();
            } else {
                Set<Long> rids = entry.getValue();
                for (Long rid : rids) {
                    PlayerActor actor = joinPlayerMap.get(rid);
                    if (actor != null && !actor.isOffline()) {
                        allOffLine = false;
                        break;
                    }
                }
            }
        }

        if (allOffLine) {
            winTeamId = otherTeamId;
        }
    }

    private void sendJiFenInfo() {
        Map<Long, Integer> jiFenMap = new HashMap<>(2);
        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            Long teamId = entry.getKey();
            int jiFenCount = 0;
            for (Long rid : entry.getValue()) {
                jiFenCount += this.jiFenMap.getOrDefault(rid, new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst();
            }
            jiFenMap.put(teamId, jiFenCount);
        }
        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            Long teamId = entry.getKey();
            Set<Long> rid = entry.getValue();
            ResXiuLuoDuoQiInfoMessage msg = new ResXiuLuoDuoQiInfoMessage();
            for (Map.Entry<Long, Integer> e : jiFenMap.entrySet()) {
                if (e.getKey().longValue() == teamId.longValue()) {
                    msg.setTeamQiZi(e.getValue());
                } else {
                    msg.setEnemyTeamQiZi(e.getValue());
                }
            }
            Module.MSG_TRANSFORMER.sendMsgToRids(msg, rid);
        }
    }

    @Override
    public void heart(int delta) {
        super.heart(delta);

        heartInterval -= delta;
        if (heartInterval > 0) {
            return;
        }

        heartInterval = 1000;

        boolean qiZiChange = false;

        for (PlayerActor actor : joinPlayerMap.values()) {
            if (actor.getDuoQi() <= 0) {
                continue;
            }
            int time = GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_EXIST_TIME);
            int now = TimeUtil.getNowOfSeconds();
            if (actor.isOffline()) {
                actor.setDuoQi(0);
                actor.setQiZiJieSuanTime(0);
                actor.setTempTitle(0);
                actor.getAppearanceWears().put(4, 0);
                sendQiZiUpdateMsg(actor);

                // 随机交给一个队友
                Set<Long> rids = teamMap.get(actor.getTeamID());
                List<Long> idList = new ArrayList<>(rids);
                idList.removeIf(id -> id == actor.getId());
                // 如果没有人可以给说明这这边没人了，对方直接获胜
                if (idList.isEmpty()) {
                    winTeamId = teamMap.keySet().stream().filter(k -> k != actor.getTeamID()).findFirst().orElse(0L);
                    log.error("3v3，玩家掉线 ，玩家id {} name {} 没有队友可以继承旗子 地图id {}", actor.getId(), actor.getName(), this.getId());
                    return;
                }

                Long teamMember = RandomUtil.randomElement(idList);
                PlayerActor member = playerMap.get(teamMember);
                if (member == null) {
                    winTeamId = teamMap.keySet().stream().filter(k -> k != actor.getTeamID()).findFirst().orElse(0L);
                    log.error("3v3，玩家掉线 ，玩家id {} name {} 没有队友可以继承旗子 地图id {}", actor.getId(), actor.getName(), this.getId());
                    return;
                }
                member.setDuoQi(1);
                member.setTempTitle(GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
                member.getAppearanceWears().put(4, GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
                member.setQiZiJieSuanTime(now + time);
                log.info("3v3，玩家掉线 ，玩家id {} name {} 旗子转移给队友 id {} name {} 地图id {}", actor.getId(), actor.getName(), member.getId(), member.getName(), this.getId());
                sendQiZiUpdateMsg(member);
                return;
            }
            int expireTime = actor.getQiZiJieSuanTime();
            if (expireTime > now) {
                continue;
            }

            actor.setQiZiJieSuanTime(now + time);

            int qiZiJiFen = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_QI_ZI_JI_FEN);
            Integer count = playerQiZiMap.compute(actor.getId(), (k, v) -> {
                if (v == null) {
                    v = 0;
                }
                return v + qiZiJiFen;
            });
            jiFenMap.compute(actor.getId(), (k, v) -> {
                if (v == null) {
                    return new TwoTuple<>(qiZiJiFen, TimeUtil.getNowOfMills());
                }
                return new TwoTuple<>(qiZiJiFen + v.getFirst(), TimeUtil.getNowOfMills());
            });
            log.info("3v3玩家获取积分，队伍id {} 玩家id {} name {} 玩家个人获取积分 {} 地图id {}", actor.getTeamID(), actor.getId(), actor.getName(), count, this.getId());
            // checkQiZiEnd();
            checkJiFenEnd();
            qiZiChange = true;
        }

        if (qiZiChange) {
            sendJiFenInfo();
            sendDupInfo();
        }
    }

    private void sendQiZiUpdateMsg(PlayerActor actor) {
        ResXiuLuoDuoQiViewUpdateMessage msg = new ResXiuLuoDuoQiViewUpdateMessage();
        msg.setRid(actor.getId());
        msg.setQiZi(actor.getTempTitle());
        msg.setExpireTime(actor.getQiZiJieSuanTime());
        Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
    }

    // private void checkQiZiEnd() {
    //     int winQiZiCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.WIN_QI_ZI_COUNT);
    //     for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
    //         int qiZiCount = 0;
    //         Long teamId = entry.getKey();
    //         Set<Long> ridList = entry.getValue();
    //         for (Long rid : ridList) {
    //             qiZiCount += playerQiZiMap.getOrDefault(rid, 0);
    //         }
    //         log.info("3v3检查队伍旗帜数，队伍id {} 旗帜数 {} 获胜旗帜数 {}", teamId, qiZiCount, winQiZiCount);
    //         if (qiZiCount >= winQiZiCount) {
    //             this.winTeamId = teamId;
    //             return;
    //         }
    //     }
    // }

    private void checkJiFenEnd() {
        int winJiFen = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_WIN_JI_FEN_COUNT);
        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            int jiFenCount = 0;
            Long teamId = entry.getKey();
            Set<Long> ridList = entry.getValue();
            for (Long rid : ridList) {
                jiFenCount += jiFenMap.getOrDefault(rid, new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst();
            }
            log.info("3v3检查队积分数，队伍id {} 积分数 {} 获胜积分数 {} 地图id {}", teamId, jiFenCount, winJiFen, this.getId());
            if (jiFenCount >= winJiFen) {
                this.winTeamId = teamId;
                return;
            }
        }
    }

    // private void checkKillEnd() {
    //     int winKillCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_WIN_KILL_COUNT);
    //     for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
    //         int killCount = 0;
    //         Long teamId = entry.getKey();
    //         Set<Long> ridList = entry.getValue();
    //         for (Long rid : ridList) {
    //             killCount += playerKillCountMap.getOrDefault(rid, 0);
    //         }
    //         log.info("3v3检查队伍杀人数，队伍id {} 杀人数 {} 获胜杀人数 {}", teamId, killCount, winKillCount);
    //         if (killCount >= winKillCount) {
    //             this.winTeamId = teamId;
    //             return;
    //         }
    //     }
    // }

    // private void checkReliveCount() {
    //     List<Long> teamIdList = new ArrayList<>(teamMap.keySet());
    //     if (teamIdList.size() != 2) {
    //         return;
    //     }
    //
    //     Long teamA = teamIdList.get(0);
    //     Long teamB = teamIdList.get(1);
    //
    //     for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
    //         int canReliveCount = 0;
    //         int doReliveCount = 0;
    //         Long teamId = entry.getKey();
    //         Set<Long> ridList = entry.getValue();
    //         for (Long rid : ridList) {
    //             canReliveCount += playerCanReliveCountMap.getOrDefault(rid, 0);
    //             doReliveCount += playerDoReliveCountMap.getOrDefault(rid, 0);
    //         }
    //         log.info("3v3检查队伍复活次数，队伍id {} 可复活次数 {} 已复活次数 {}", teamId, canReliveCount, doReliveCount);
    //         if (doReliveCount >= canReliveCount) {
    //             if (teamId.longValue() == teamA.longValue()) {
    //                 winTeamId = teamB;
    //             } else {
    //                 winTeamId = teamA;
    //             }
    //
    //             return;
    //         }
    //     }
    // }

    @Override
    public boolean checkEnd(int delta) {
        boolean end = super.checkEnd(delta);

        if (winTeamId > 0) {
            return true;
        }

        if (end) {
            List<Long> teamId = new ArrayList<>(teamMap.keySet());
            if (teamId.size() != 2) {
                return false;
            }

            Long teamA = teamId.get(0);
            Long teamB = teamId.get(1);
            Set<Long> aPlayers = teamMap.get(teamA);
            Set<Long> bPlayers = teamMap.get(teamB);

            // 等待玩家进入
            if (aPlayers.isEmpty() && bPlayers.isEmpty()) {
                return false;
            }

            if (aPlayers.isEmpty()) {
                winTeamId = teamB;
            }
            if (bPlayers.isEmpty()) {
                winTeamId = teamA;
            }

            int jiFenCountA = 0;
            int jiFenCountB = 0;

            for (Long rid : aPlayers) {
                jiFenCountA += jiFenMap.getOrDefault(rid, new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst();
            }

            for (Long rid : bPlayers) {
                jiFenCountB += jiFenMap.getOrDefault(rid, new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst();
            }
            // 时间到积分多的那一队赢
            if (jiFenCountA > jiFenCountB) {
                winTeamId = teamA;
            } else if (jiFenCountA < jiFenCountB) {
                winTeamId = teamB;
            }
            if (winTeamId > 0) {
                log.info("3v3副本时间到，检查队伍积分数量，A队伍id {} ACount {} B队伍id {} BCount {} 地图id {}", teamA, jiFenCountA, teamB, jiFenCountB, this.getId());
            }
        }

        return end;
    }

    @Override
    public boolean close(int delta) {

        if (jieSuan) {
            return super.close(delta);
        }

        jieSuan = true;

        log.info("3v3副本结束，获胜 队伍id {} 地图id {}", winTeamId, this.getId());

        XiuLuoEndNotice notice = new XiuLuoEndNotice();

        notice.setWinTeam(winTeamId);
        notice.setTime(TimeUtil.getNowOfSeconds());
        notice.getRids().addAll(joinPlayerMap.keySet());
        Map<Long, Long> teamMvp = findTeamMvp();

        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            Long teamId = entry.getKey();
            Set<Long> ridList = entry.getValue();
            for (Long rid : ridList) {
                PlayerActor actor = joinPlayerMap.get(rid);
                if (actor == null) {
                    continue;
                }

                actor.setTempTitle(0);
                actor.setDuoQi(0);
                actor.setQiZiJieSuanTime(0);
                actor.getAppearanceWears().put(4, 0);
                sendQiZiUpdateMsg(actor);

                XiuLuoInfo info = createInfo(actor);
                PlayerActor playerActor = playerMap.get(actor.getId());
                if (playerActor == null || playerActor.isOffline()) {
                    notice.getOffLine().add(info.getRid());
                }
                Long mvpId = teamMvp.getOrDefault(teamId, 0L);
                if (mvpId == actor.getId()) {
                    info.setMvp(1);
                }
                notice.getTeamInfo().compute(teamId, (k, v) -> {
                    if (v == null) {
                        v = new ArrayList<>(3);
                    }
                    v.add(info);
                    return v;
                });
                notice.addHost(actor.getHostId());
            }

        }

        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);

        return super.close(delta);
    }

    private XiuLuoInfo createInfo(PlayerActor actor) {
        XiuLuoInfo info = new XiuLuoInfo(actor.getId(), actor.getName());
        info.setCareer(actor.getCareer());
        info.setKillCount(playerKillCountMap.getOrDefault(actor.getId(), 0));
        info.setGameJiFen(jiFenMap.getOrDefault(actor.getId(), new TwoTuple<>(0, TimeUtil.getNowOfMills())).getFirst());
        info.setLianSha(Math.min(5, playerMaxLianShaMap.getOrDefault(actor.getId(), 0)));
        info.setSex(actor.getSex());
        info.setDuoQiCount(duoQiCount.getOrDefault(actor.getId(), 0));
        info.setDieCount(playerDieCountMap.getOrDefault(actor.getId(), 0));
        return info;
    }

    private Map<Long, Long> findTeamMvp() {
        Map<Long, Long> mvpMap = new HashMap<>(2);
        for (Map.Entry<Long, Set<Long>> entry : teamMap.entrySet()) {
            Long teamId = entry.getKey();
            if (teamId != winTeamId) {
                continue;
            }
            Set<Long> idList = entry.getValue();
            List<ThreeTuple<Long, Integer, Long>> jiFenList = new ArrayList<>(3);
            for (Long rid : idList) {
                TwoTuple<Integer, Long> tuple = jiFenMap.getOrDefault(rid, new TwoTuple<>(0, TimeUtil.getNowOfMills()));
                ThreeTuple<Long, Integer, Long> threeTuple = new ThreeTuple<>(rid, tuple.first, tuple.second);
                jiFenList.add(threeTuple);
            }
            jiFenList.sort((a, b) -> {
                int i = b.getSecond() - a.getSecond();
                if (i == 0) {
                    return (int) (a.getThird() - b.getThird());
                }
                return i;
            });
            if (jiFenList.isEmpty()) {
                mvpMap.put(teamId, 0L);
            } else {
                ThreeTuple<Long, Integer, Long> tuple = jiFenList.get(0);
                Long first = tuple.getFirst();
                if (first > 0) {
                    mvpMap.put(teamId, first);
                } else {
                    mvpMap.put(teamId, 0L);
                }
            }
        }
        return mvpMap;
    }

    @Override
    public void afterEventTrigger(PlayerActor actor, Event event) {
        playerEventMap.compute(actor.getId(), (k, v) -> {
            if (v == null) {
                v = new ArrayList<>();
            }
            TwoTuple<Integer, Integer> tuple = new TwoTuple<>(event.getId(), TimeUtil.getNowOfSeconds());
            v.add(tuple);
            return v;
        });
        sendDupInfo();
    }

    @Override
    public void afterInitEvent(List<MapEventConfig> configs) {
        eventConfigs = configs;
    }

    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        int configId = monster.getConfigId();
        if (configId != GlobalUtil.getGlobalInt(GameConst.GlobalId.XIU_LUO_QI_ZI_MONSTER)) {
            return;
        }
        long id = monster.getOwner();
        PlayerActor actor = playerMap.get(id);
        if (actor == null) {
            log.error("3v3，旗子怪物死亡，没有找到击杀者，地图id {}", this.getId());
            return;
        }
        log.info("3v3，旗子怪物死亡 击杀者 id {} name {} 地图id {}", actor.getId(), actor.getName(), this.getId());
        actor.setTempTitle(GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
        actor.getAppearanceWears().put(4, GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_TE_XIAO));
        actor.setDuoQi(1);
        int time = GlobalUtil.getGlobalInt(GameConst.GlobalId.QI_ZI_EXIST_TIME);
        actor.setQiZiJieSuanTime(TimeUtil.getNowOfSeconds() + time);
        sendQiZiUpdateMsg(actor);
        TipUtil.show(actor.getId(), CommonTips.获得旗子);

        duoQiCount.compute(actor.getId(), (k, v) -> {
            if (v == null) {
                v = 0;
            }
            v++;
            return v;
        });
    }
}
