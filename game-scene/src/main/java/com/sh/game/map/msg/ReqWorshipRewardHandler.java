package com.sh.game.map.msg;

import com.sh.game.common.communication.msg.system.worship.ReqWorshipRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.map.SceneManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求膜拜雕像奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.MAP_PLAYER)
public class ReqWorshipRewardHandler extends AbstractHandler<ReqWorshipRewardMessage> {

    @Override
    public void doAction(ReqWorshipRewardMessage msg) {
        SceneManager.getInstance().reqWorshipReward(msg.getSession().getId());
    }

}