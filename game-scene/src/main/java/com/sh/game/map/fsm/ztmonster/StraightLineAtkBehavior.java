package com.sh.game.map.fsm.ztmonster;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.fight.skill.PerformerSkillReleaser;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.ai.AIData;
import com.sh.game.map.move.NewMoveManager;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.util.GeomUtil;


import static com.sh.game.map.fsm.FSMState.*;


public class StraightLineAtkBehavior extends NormalAttackAiBehavior {

    private void goStraight(GameMap map,MonsterActor performer,Vec3 target) {
        double distance = GeomUtil.distance(performer.getPosition(), target);
        float stepDistance = (float) performer.getMoveSpeed() / TimeUtil.ONE_MILLS * performer.getMoveInterval();
        if(map.isWalkable(target)){
            NewMoveManager.getInstance().performerMove(map, performer, target, true);
        }
        double rate = distance / stepDistance;
        int sleepTime = Math.max(100,(int) (performer.getMoveInterval() * rate));
        performer.getMachine().getAiData().incClockSleep(sleepTime);
    }

    @Override
    public void alertUpdate(GameMap map, MonsterActor performer, int dt) {
        AIData aiData = performer.getMachine().getAiData();
        if (aiData.getClockSleep() > 100) {
            return;
        }
        TwoTuple<Float,Float> dirVec = performer.getMachine().getAiData().getDirVec();
        PerformerSkillReleaser skillReleaser = PerformerSkillReleaser.findSkillReleaser(map, performer);
        // 找技能
        if (skillReleaser != null) {
            skillReleaser.skillCost();
            aiData.setSkillReleaser(skillReleaser);
            performer.getMachine().updateMachine(map,0);
            return;
        }
        GeomUtil.vectorScalar2Length(dirVec,(float) performer.getMoveSpeed() / TimeUtil.ONE_MILLS * performer.getMoveInterval());
        Vec3 targetPos = new Vec3(performer.getPosition().x + dirVec.first,performer.getPosition().y + dirVec.second,0);
        if(map.isWalkable(targetPos)){
            goStraight(map,performer,targetPos);
        }
    }

    @Override
    public int alertTransition(GameMap map, MonsterActor performer, int dt) {
        if (performer.isDead()) {
            return DIE;
        }
        AIData aiData = performer.getMachine().getAiData();
        PerformerSkillReleaser skillReleaser = aiData.getSkillReleaser();
        if (skillReleaser != null) {
            SkillBattleConfig config = skillReleaser.getConfig();
            if (config.getQianyao() > 0) {
                return SPELL;
            }
            // 瞬发技能直接释放
            return CAST;
        }
        return ALERT;
    }

    @Override
    public int activeTransition(GameMap map, MonsterActor performer, int dt) {
        if (performer.isDead()) {
            return FSMState.DIE;
        }
        return FSMState.ALERT;
    }
}
