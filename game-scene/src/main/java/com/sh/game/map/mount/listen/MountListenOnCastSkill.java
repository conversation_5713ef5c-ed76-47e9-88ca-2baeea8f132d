package com.sh.game.map.mount.listen;

import com.sh.game.common.communication.notice.MountFightNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.event.EventType;
import com.sh.game.event.IListener;
import com.sh.game.map.Module;
import com.sh.game.map.obj.PlayerActor;

public class MountListenOnCastSkill implements IListener<PlayerActor> {

    @Override
    public void update(EventType type, PlayerActor param) {
        if (param.getMount() == 0) {
            return;
        }
        if (param.getId() != param.getRid()) {
            return;
        }
        MountFightNotice notice = new MountFightNotice();
        notice.setRoleId(param.getRid());
        notice.addHost(param.getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, param.getRid());
    }
}
