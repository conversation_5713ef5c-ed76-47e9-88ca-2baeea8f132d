package com.sh.game.map.fight;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueLongIntBean;
import com.sh.game.common.communication.msg.map.fight.ResBatchObjectDieMessage;
import com.sh.game.common.communication.msg.map.fight.ResEffectApplyMessage;
import com.sh.game.common.communication.msg.map.fight.ResHpMpChangeMessage;
import com.sh.game.common.communication.msg.map.fight.bean.ElementHurtBean;
import com.sh.game.common.communication.msg.map.fight.bean.HpMpChangeBean;
import com.sh.game.common.communication.msg.map.fight.bean.HurtTarget;
import com.sh.game.common.communication.msg.map.fight.bean.HurtTypeInfo;
import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.map.Module;
import com.sh.game.map.buff.BuffStateUtil;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.fight.skill.ClientHitReleaser;
import com.sh.game.map.fight.skill.PerformerSkillReleaser;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

import java.util.*;


/**
 * 战斗模块
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:34:24
 */
@Slf4j
public class FightManager {

    private static final FightManager INSTANCE = new FightManager();

    private FightManager() {

    }

    public static FightManager getInstance() {
        return INSTANCE;
    }

    public void releaseSkill(GameMap map, SkillBattleConfig config, long casterId, long targetId) {
        Performer caster = map.queryPerformer(map, casterId);
        Performer forceTarget = map.queryPerformer(map, targetId);
        if (caster == null || forceTarget == null) {
            return;
        }
        if (!BuffStateUtil.canDoing(caster)) {
            log.warn("战斗-请求施法#玩家：{}，昵称：{}，buff限制不可行动", casterId, caster.getName());
            return;
        }
        PerformerSkillReleaser releaser = new PerformerSkillReleaser();
        releaser.setMap(map);
        releaser.setCaster(caster);
        releaser.setFocusTarget(forceTarget);
        releaser.setPos(forceTarget.getPosition());
        releaser.setConfig(config);
        releaser.setDir(caster.getDir());
        if (!releaser.canReleaseSkill()) {
            return;
        }
        releaser.prepareRelease();
    }


    public void clientReleaseSkill(GameMap map, long casterId, SkillBattleConfig skillConfig, List<CommonKeyValueLongIntBean> rates) {
        Map<Long, Integer> clientTargets = new HashMap<>();
        for (CommonKeyValueLongIntBean bean : rates) {
            if (bean.getValue() <= 0) {
                continue;
            }
            clientTargets.put(bean.getKey(), bean.getValue());
        }
        Performer caster = map.queryPerformer(map, casterId);
        if (caster == null) {
            return;
        }
        if (rates.isEmpty()) {
            return;
        }
        Performer focusTarget = null;
        for (Long id : clientTargets.keySet()) {
            Performer find = map.queryPerformer(map, id);
            if (find != null) {
                focusTarget = find;
                break;
            }
        }
        if (focusTarget == null) {
            return;
        }
        ClientHitReleaser releaser = new ClientHitReleaser(clientTargets);
        releaser.setMap(map);
        releaser.setCaster(caster);
        releaser.setConfig(skillConfig);
        releaser.setDir(caster.getDir());
        releaser.setFocusTarget(focusTarget);
        releaser.setPos(focusTarget.getPosition());
        releaser.prepareRelease();
    }

    public void sendSkillEffectMsg(Performer caster, SkillBattleConfig skillConfig, GameMap map, List<EffectContext> contexts) {
        Set<Long> diedList = new HashSet<>();
        ResEffectApplyMessage msg = new ResEffectApplyMessage();
        msg.setCasterId(caster.getId());
        msg.setSkillId(Optional.ofNullable(skillConfig).map(SkillBattleConfig::getId).orElse(0));
        List<HurtTarget> hurtList = msg.getHurtList();
        contexts.sort((a, b) -> Math.toIntExact(b.hp - a.hp));
        // 血量变化通知消息
        ResHpMpChangeMessage hpMpChangeMessage = new ResHpMpChangeMessage();
        for (int i = 0; i < contexts.size(); i++) {
            EffectContext curr = contexts.get(i);
            HurtTarget hurtTarget = new HurtTarget();
            hurtTarget.setTargetId(curr.target.getId());
            hurtTarget.setHp(curr.hp);
            HurtTypeInfo hurtTypeInfo = new HurtTypeInfo();
            hurtTypeInfo.setEffectCfgId(curr.effectConfig.getId());
            hurtTypeInfo.setHurt(curr.hurt);
            hurtTypeInfo.setRateCount(1);
            hurtTarget.setHp(curr.hp);
            hurtTypeInfo.setRateCount(1);
            if (curr.hurt > 0) {
                HpMpChangeBean hpMpBean = new HpMpChangeBean();
                hpMpBean.setHp(curr.hp);
                hpMpBean.setRateCount(1);
                hpMpBean.setMaxShield(curr.target.getMaxShield());
                hpMpBean.setShield(curr.target.getShield());
                hpMpBean.setLid(curr.target.getId());
                hpMpBean.setType(curr.floaterType.type());
                hpMpBean.setHp(curr.hp);
                hpMpBean.setRateCount(1);
                hpMpBean.setMp(caster.getMp());
                hpMpChangeMessage.getBeans().add(hpMpBean);
                // 合并伤害相同的
                if (i + 1 < contexts.size()) {
                    int mergeCount = 0;
                    int rateCount = 1;
                    for (int j = i + 1; j < contexts.size(); j++) {
                        EffectContext next = contexts.get(j);
                        if (!curr.canHurtMerge(next)) {
                            break;
                        }
                        hpMpBean.setHp(next.hp);
                        hpMpBean.setRateCount(rateCount + 1);
                        hurtTarget.setHp(next.hp);
                        hurtTypeInfo.setRateCount(rateCount + 1);
                        if (next.target.isDead()) {
                            diedList.add(next.target.getId());
                        }
                        rateCount++;
                        mergeCount++;
                    }
                    i += mergeCount;
                }
            }
            if (curr.target.isDead()) {
                diedList.add(curr.target.getId());
            }
            // 元素印记信息
            if (curr.elementMarking != null) {
                ElementHurtBean elementHurtBean = new ElementHurtBean();
                elementHurtBean.setCfgId(curr.elementMarking.config.getId());
                elementHurtBean.setEndTime(curr.elementMarking.endTime);
                hurtTarget.getElementHurtList().add(elementHurtBean);
            }
            hurtList.add(hurtTarget);
            hurtTarget.getHurtTypeList().add(hurtTypeInfo);
            if (curr.target.isDead()) {
                diedList.add(curr.target.getId());
            }
        }
        // 技能释放结果
        Module.MSG_TRANSFORMER.sendMapMessage(msg, map);

        // 血量改变与飘字
        if (!hpMpChangeMessage.getBeans().isEmpty()) {
            Module.MSG_TRANSFORMER.sendMapMessage(hpMpChangeMessage, map);
        }
        if (!diedList.isEmpty()) {
            ResBatchObjectDieMessage dieMessage = new ResBatchObjectDieMessage();
            for (Long id : diedList) {
                dieMessage.getIdList().add(id);
            }
            Module.MSG_TRANSFORMER.sendMapMessage(dieMessage, map);
        }
    }
}
