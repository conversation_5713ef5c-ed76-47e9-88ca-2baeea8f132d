package com.sh.game.map.fight.effect;

import com.sh.game.map.fight.effect.impl.*;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

public class SkillEffectFactory {
    private final static Map<Integer, AbstractEffect> SKILL_EFFECT_MAP = new HashMap<>();
    /**
     * 技能效果类型
     */
    public enum SKILL_EFFECT {

        /**
         * 伤害效果
         */
        HURT(1, new HurtSkillEffect()),
        /**
         * 触发buff
         */
        ADD_BUFF(3, new BuffEffect()),
        /**
         * 发射子弹
         */
        FIRE_BULLET(4,new BulletEffect()),
        /**
         * 该效果只是前置条件，用于声明指定时间范围内施法者可对满足条件的敌人释放未拥有的技能
         */
        PRE_SKILL(5,new PreUnSkillEffect()),
        /**
         * 召唤效果
         */
        ZHAO_HUAN(6,new ZhaoHuanSkillEffect()),
        /**
         * 强制位移效果
         */
        FORCE_MOVE(7,new ForceMoveEffect()),
        /**
         * 处决效果
         */
        CHU_JUE(8,new ChuJueSkillEffect()),
        /**
         * 毒元素感染效果
         */
        INFECT(9,new InfectEffect()),
        /**
         * 最大生命值万分比伤害效果
         */
        MAX_HP_HURT(10,new MaxHpHurtSkillEffect()),
        /**
         * 向地面添加陷阱的效果
         */
        APPEND_GROUND_TRAP(11,new AppendGroundTrapEffect()),
        /**
         * 立即回血效果
         */
        HP_RECOVERY(12,new HpRecoveryEffect()),
        /**
         * 向地面添加客户端自定义陷阱效果
         */
        CLIENT_GROUND_TRAP(13, new ClientGroundTrapEffect()),
        /**
         * 恢复蓝条
         */
        MP_RECOVERY(18, new MpRecoveryEffect()),
        ;
        private final AbstractEffect effect;
        @Getter
        private final int type;

        SKILL_EFFECT(int type, AbstractEffect effect){
            this.effect = effect;
            this.type = type;
        }

    }

    static{
        for (SKILL_EFFECT value : SKILL_EFFECT.values()) {
            SKILL_EFFECT_MAP.put(value.type,value.effect);
        }
    }

    public static AbstractEffect getSkillEffect(int type) {
        return SKILL_EFFECT_MAP.get(type);
    }


}
