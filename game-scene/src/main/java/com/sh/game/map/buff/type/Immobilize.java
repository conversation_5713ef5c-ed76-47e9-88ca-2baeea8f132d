package com.sh.game.map.buff.type;

import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;

public class Immobilize extends AbsBuffEffect{

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        performer.getBuffState().increase(BuffState.State.IMMOBILIZE, stack);
    }

    @Override
    public void onRemove(Performer performer, Buff buff, int stack) {
        performer.getBuffState().decrease(BuffState.State.IMMOBILIZE, stack);
    }
}
