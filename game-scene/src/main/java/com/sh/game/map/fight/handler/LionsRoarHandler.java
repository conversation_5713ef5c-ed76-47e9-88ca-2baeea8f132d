package com.sh.game.map.fight.handler;

import com.sh.game.common.entity.skill.Skill;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.EmptyEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 狮子吼
 */
@Slf4j
public class LionsRoarHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new EmptyEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        super.cast(ret, skill, map, caster, targetList, targetPoint);
        if (ret.buffEffect.newBuffs != null) {
            ret.castSkillSuc = true;
        }
    }
}
