package com.sh.game.map.fight.effect.impl;

import com.sh.game.common.config.model.SkillEffectConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.fight.effect.AbstractEffect;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;


public class HpRecoveryEffect extends AbstractEffect {
    @Override
    public void apply(GameMap map, EffectContext context) {
        Performer target = context.target;
        SkillEffectConfig effectConfig = context.effectConfig;
        if (target.isDead()) {
            return;
        }
        if (target.getBuffState().inStateOf(BuffState.State.AVOID)) {
            return;
        }
        double attackPercentValue = effectConfig.getParameter().get(0)[0] / 10000.0;
        double maxHpPercentValue = effectConfig.getParameter().get(0)[1] / 10000.0;
        if (target.getFinalAttribute() != null) {
            double value = target.getFinalAttribute().findMaxHp() * maxHpPercentValue +
                    AttributeEnum.ZTXY_ATTACK.getAttrValue(target.getFinalAttribute()) * attackPercentValue;
            if (target.addHp((int) value, false)) {
                context.hurt = (long) value;
                context.replaceFloaterType(FightConst.FloaterType.CURE);
            }
        }
    }
}
