package com.sh.game.map.startup.boots.maintain;

import com.sh.client.DefaultMessagePool;
import com.sh.game.common.communication.msg.system.back.ReqBackLoginMessage;
import com.sh.game.common.communication.msg.system.back.ReqCloseServerMessage;
import com.sh.game.common.communication.msg.system.back.ReqReloadCfgMessage;
import com.sh.game.map.startup.boots.maintain.msg.ReqBackLoginHandler;
import com.sh.game.map.startup.boots.maintain.msg.ReqCloseServerHandler;
import com.sh.game.map.startup.boots.maintain.msg.ReqReloadCfgHandler;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.net.*;
import com.sh.server.AbstractMessage;
import com.sh.server.MessageHandler;
import com.sh.server.Session;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleState;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@Slf4j
public class MaintainNet {


	private NetworkService service;

	private DefaultMessagePool msgPool;

	private NetworkServiceBuilder builder;

	public MaintainNet() {

		int bossLoopGroupCount = 2;
		int workerLoopGroupCount = 2;
		builder = new NetworkServiceBuilder();

		msgPool = new DefaultMessagePool();
		msgPool.register(new ReqCloseServerMessage(), ReqCloseServerHandler.class);
		msgPool.register(new ReqBackLoginMessage(), ReqBackLoginHandler.class);
		msgPool.register(new ReqReloadCfgMessage(), ReqReloadCfgHandler.class);

		builder.setMsgPool(msgPool);

		builder.setBossLoopGroupCount(bossLoopGroupCount);
		builder.setWorkerLoopGroupCount(workerLoopGroupCount);
		builder.setNetworkEventlistener(new BackEventListener());
		builder.setConsumer(new BackServerConsumer());


	}

	public void start(int port) {
		builder.setPort(port);
		service = builder.createService();

		service.start();

	}

	public void stop(){
		service.stop();
	}


	class BackServerConsumer implements NetworkConsumer {

		@Override
		public void consume(Channel channel, Message msg) {
			Session session = new Session();
			session.setChannel(channel);
			AbstractMessage absMsg = (AbstractMessage) msg;
			absMsg.setSession(session);
			MessageHandler<? extends Message> handler = msgPool.getHandler(absMsg.getId());
			handler.setMsg(absMsg);

			Boolean isLogin = AttributeUtil.get(channel, ChannelAttrKey.BACK_LOGIN);
			if (isLogin == null) {
				isLogin = false;
			}

			// 如果没登录且消息不是登录请求则不让通过
			if (!isLogin && !(absMsg instanceof ReqBackLoginMessage)) {
				return;

			}

			try {
				handler.run();
			} catch (Exception e) {
				log.error("back server 执行消息发生错误,{}", e);
			}
		}
	}

	public class BackEventListener implements NetworkEventlistener {

		private final Logger LOGGER = LoggerFactory.getLogger(BackEventListener.class);

		@Override
		public void onConnected(ChannelHandlerContext ctx) {
			LOGGER.info("back server 收到一个连接：" + ctx);
		}

		@Override
		public void onDisconnected(ChannelHandlerContext ctx) {
			LOGGER.info("back server 断开一个连接：" + ctx);
		}

		@Override
		public void onExceptionOccur(ChannelHandlerContext ctx, Throwable cause) {
			LOGGER.error("back server 发生错误：" + ctx, cause);
		}

		@Override
		public void idle(ChannelHandlerContext channelHandlerContext, IdleState idleState) {

		}
	}
}
