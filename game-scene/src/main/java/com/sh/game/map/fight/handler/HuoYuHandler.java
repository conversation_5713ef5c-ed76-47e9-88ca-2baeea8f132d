package com.sh.game.map.fight.handler;

import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.map.fight.FightUtil;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.HurtEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;

import java.util.List;

/**
 * 流星火雨类型。
 * <p>
 * 主要是修改伤害类型
 */
public class HuoYuHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new HurtEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        super.cast(ret, skill, map, caster, targetList, targetPoint);
        for (int i = 0; i < ret.getEffectList().size(); i++) {
            FightResult.FightEffectRet effectRet = ret.getEffectList().get(i);
//            FightUtil.setHurtType(effectRet, FightConst.HurtType.HUOYU);
            ret.castSkillSuc = true;
        }
    }
}
