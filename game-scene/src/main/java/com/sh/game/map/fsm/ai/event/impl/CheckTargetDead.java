package com.sh.game.map.fsm.ai.event.impl;

import com.sh.game.map.fsm.ai.event.AbstractEventAI;
import com.sh.game.map.fsm.ai.event.Event;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

//检测目标死亡
public class CheckTargetDead extends AbstractEventAI {

    @Override
    public void check(Event event, GameMap map, MonsterActor monster, int dt) {
        if (monster.isDead()) {
            return;
        }

        if (event.getCalls() == null || event.getCalls().isEmpty()) {
            return;
        }

        boolean allDead = true;
        for (MonsterActor target : event.getCalls()) {
            if (target != null && !target.isDead()) {
                allDead = false;
                break;
            }
        }

        if (!allDead) {
            event.setDelay(0);
            return;
        }

        if (canTrigger(event, map, monster, dt)) {
            trigger(event, map, monster, dt);
        }
    }
}
