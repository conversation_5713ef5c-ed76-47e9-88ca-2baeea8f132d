package com.sh.game.map.heart;

import com.sh.common.config.ConfigDataManager;
import com.sh.concurrent.ScheduledEvent;
import com.sh.game.common.communication.notice.SyncPointNotice;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.groundevent.GroundEventManager;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.teleport.TeleportManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 玩家心跳事件
 *
 * <AUTHOR> 2017年5月18日 下午8:37:04
 */
@Slf4j
public class PlayerHeartEvent extends ScheduledEvent {

    private long lastHurtTime = TimeUtil.getNowOfMills();

    private long nextSyncPointTime = TimeUtil.getNowOfSeconds() + 1;

    GameMap map;

    public PlayerHeartEvent(GameMap map) {
        super(-1, 100);
        this.map = map;
    }

    public PlayerHeartEvent(long end) {
        super(end);
    }

    @Override
    public void doAction() {

        if (!Module.isReady()) {
            return;
        }

        if (!map.isReady()) {
            return;
        }

        long curTime = TimeUtil.getNowOfMills();

        int delta = (int) (curTime - lastHurtTime);

        lastHurtTime = curTime;

        Map<Long, PlayerActor> playerMap = map.getPlayerMap();
        PlayerActor[] playerArray = playerMap.values().toArray(new PlayerActor[0]);


        for (PlayerActor player : playerArray) {

            try {
                if (player.isOffline()) {
                    log.info("玩家[{}]开始下线", player.getName());
                    //删除player
                    PlayerManager.getInstance().offline(map, player);
                    //PlayerManager.getInstance().removePlayer(player.getRid());
                    continue;
                }
                player.recoverHpAndMp();
                player.getMachine().updateMachine(map, delta);

                // 检查buffer
                BuffManager.getInstance().onUpdateByHeart(map, player, curTime, delta);

                GroundEventManager.getInstance().updateGroundEvent(map, player);

                // 呆在地图中的消耗
                if (map.isNeedCostPerSec()) {
                    if (PlayerManager.getInstance().isNeedKickOut(player.getId())) {
                        MapConfig config = ConfigDataManager.getInstance().getById(MapConfig.class, map.getCfgId());
                        int[] homeMap = config.getHomeMap();
                        TeleportManager.getInstance().teleport(player, homeMap[0], homeMap[1], homeMap[2]);
                    }
                }
            } catch (Throwable e) {
                log.error("玩家心跳执行错误:" + player.getRid() + "-" + player.getName(), e);
            }
        }

        // 同步坐标(按照SilenceSu的要求1秒1次)
        if (nextSyncPointTime < TimeUtil.getNowOfSeconds()) {
            nextSyncPointTime = TimeUtil.getNowOfSeconds() + 1;
            for (PlayerActor player : playerArray) {
                SyncPointNotice notice = new SyncPointNotice();
                notice.setRid(player.getRid());
                notice.setMapId(map.getId());
                notice.setX(player.getPoint().x);
                notice.setY(player.getPoint().y);
                notice.addHost(player.getHostId());
                Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, player.getRid());
            }
        }
    }

}
