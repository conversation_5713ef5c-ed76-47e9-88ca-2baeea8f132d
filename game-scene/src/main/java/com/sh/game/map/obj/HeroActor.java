package com.sh.game.map.obj;

import com.sh.game.common.communication.msg.map.fight.ResHuoLongValueMessage;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.avatar.AvatarUnion;
import com.sh.game.common.sync.Sync;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.scene.point.Point;
import lombok.Getter;
import lombok.Setter;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2020/11/24 11:28
 * desc: 英雄
 */
@Getter
@Setter
public class HeroActor extends PlayerActor {
    /**
     * 战斗状态
     *
     * @see com.sh.game.common.constant.HeroConst
     */
    private int fightState;

    /**
     * 攻击状态
     *
     * @see com.sh.game.common.constant.HeroConst
     */
    private int attackState;

    /**
     * 下次可召唤时间
     * 时间戳（秒）
     */
    @Sync(launcher = true, dataType = DataType.ROLE)
    private int callTime;

    /**
     * 忠诚度
     */
    @Sync(launcher = true, dataType = DataType.ROLE, lazy = true)
    private int loyalty;

    /**
     * 火龙之心能量
     */
    @Sync(launcher = true, dataType = DataType.ROLE, lazy = true)
    private int huoLongValue;

    /**
     * 合击技能能量
     */
    @Sync(launcher = true, dataType = DataType.ROLE, lazy = true)
    private int combSkillValue;

    /**
     * 上次合体技能能量恢复时间
     */
    private long lastRecoverCombSkillValueTime;

    /**
     * 躲避时的技能
     */
    private int skillEscapeAfter;

    /**
     * 超过攻击范围跟随
     */
    private boolean outpaceAttackArea;

    /**
     * 待释放合体技能
     */
    private int combSkillId;

    /**
     * 合体技能释放状态
     */
    private int combSkillState;

    /**
     * 合体待释放目标
     */
    private Performer combSkillTarget;

    /**
     * 合体技能是否学习
     */
    private boolean combSkillLearn;

    /**
     * 客户端坐标
     */
    private Point clientPoint;

    /**
     * 合体技能槽恢复
     *
     * @param attribute
     */
    public void combSkillValueRecover(Attribute attribute) {
        if (!combSkillLearn) {
            return;
        }
        //主角跟英雄
        long reply = AttributeEnum.COMBAT_SKILL_VALUE_REPLY.getAttrValue(getFinalAttribute()) + AttributeEnum.COMBAT_SKILL_VALUE_REPLY.getAttrValue(attribute);
        reply = Math.min(reply, huoLongValue);
        if (reply <= 0) {
            return;
        }
        long oldValue = this.getCombSkillValue();
        long maxValue = GlobalUtil.getGlobalInt(GameConst.GlobalId.COMB_SKILL_VALUE_MAX);
        if (oldValue >= maxValue) {
            return;
        }
        long nowOfMills = TimeUtil.getNowOfMills();
        if (nowOfMills - lastRecoverCombSkillValueTime >= RoleConst.COMB_SKILL_VALUE_RECOVER_TIME_INTERVAL * 1000) {
            int newValue = (int) Math.min(maxValue, oldValue + reply);
            if (oldValue != newValue) {
                huoLongValue -= newValue - oldValue;
                this.setCombSkillValue(newValue);
                lastRecoverCombSkillValueTime = nowOfMills;
                sendHuoLongValueMessage();
            }
        }
    }

    public void sendHuoLongValueMessage() {
        ResHuoLongValueMessage message = new ResHuoLongValueMessage();
        message.setValue(getHuoLongValue());
        message.setSkillValue(getCombSkillValue());
        Module.MSG_TRANSFORMER.sendMsg(message, getRid());
    }

    @Override
    public void resetAttackTarget() {
        super.resetAttackTarget();
        setSkillEscapeAfter(0);
    }

    public void resetCombSkillTarget() {
        setCombSkillId(0);
        setCombSkillState(HeroConst.CombSkillState.NORMAL);
        setCombSkillTarget(null);
    }

    public boolean isCombSkillLearn() {
        return combSkillLearn;
    }

    public void setCombSkillLearn(boolean combSkillLearn) {
        this.combSkillLearn = combSkillLearn;
    }

    public int getHuoLongValue() {
        return huoLongValue;
    }

    public void setHuoLongValue(int huoLongValue) {
        this.huoLongValue = huoLongValue;
    }

    public int getCombSkillValue() {
        return combSkillValue;
    }

    public void setCombSkillValue(int combSkillValue) {
        this.combSkillValue = combSkillValue;
    }

    public boolean isOutpaceAttackArea() {
        return outpaceAttackArea;
    }

    public void setOutpaceAttackArea(boolean outpaceAttackArea) {
        this.outpaceAttackArea = outpaceAttackArea;
    }

    public int getSkillEscapeAfter() {
        return skillEscapeAfter;
    }

    public void setSkillEscapeAfter(int skillEscapeAfter) {
        this.skillEscapeAfter = skillEscapeAfter;
    }

    public int getFightState() {
        return fightState;
    }

    public void setFightState(int fightState) {
        this.fightState = fightState;
    }

    public int getCallTime() {
        return callTime;
    }

    public void setCallTime(int callTime) {
        this.callTime = callTime;
    }

    public Point getClientPoint() {
        return clientPoint;
    }

    public void setClientPoint(Point clientPoint) {
        this.clientPoint = clientPoint;
    }

    /**
     * 是否能进入地图
     *
     * @return
     */
    public boolean canCall() {
        return getCallTime() <= TimeUtil.getNowOfSeconds();
    }

    /**
     * 是否休息中
     *
     * @return
     */
    public boolean isSleep() {
        return fightState == HeroConst.FightState.SLEEP;
    }

    /**
     * 是否等待中
     *
     * @return
     */
    public boolean isWait() {
        return fightState == HeroConst.FightState.WAIT;
    }

    /**
     * 是否战斗中
     *
     * @return
     */
    public boolean isFight() {
        return fightState == HeroConst.FightState.ATTACK || fightState == HeroConst.FightState.LOCK || combSkillState != HeroConst.CombSkillState.NORMAL;
    }

    public int getAttackState() {
        return attackState;
    }

    public void setAttackState(int attackState) {
        this.attackState = attackState;
    }

    @Override
    public void setPoint(Point point) {
        this.point = point;
    }

    @Override
    public void setMaster(Performer master) {
        this.master = master;
    }

    @Override
    public Performer getMaster() {
        return master;
    }

    @Override
    public int getLoyalty() {
        return loyalty;
    }

    public void setLoyalty(int loyalty) {
        this.loyalty = loyalty;
    }

    @Override
    public int getPkValue() {
        return ((PlayerActor) master).getPkValue();
    }

    @Override
    public boolean isGrayShow() {
        return ((PlayerActor) master).isGrayShow();
    }

    @Override
    public long getUnionId() {
        return ((PlayerActor) master).getUnionId();
    }

    @Override
    public AvatarUnion getUnion() {
        return ((PlayerActor) master).getUnion();
    }

    @Override
    public int getType() {
        return MapObjectType.HERO;
    }

    @Override
    public long getRoleId() {
        return getRid();
    }

    @Override
    public long getOwnRId() {
        return getRoleId();
    }

    @Override
    public boolean penetrate(IMapObject stand, int penetrable) {
        return true;
    }

    @Override
    public int getGroup() {
        return ((PlayerActor) master).getGroup();
    }
}
