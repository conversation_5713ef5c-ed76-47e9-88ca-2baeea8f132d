package com.sh.game.map.monster.msg;

import com.sh.game.common.communication.msg.system.monster.ReqMonsterCountMenuMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.map.monster.MonsterInfoManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求怪物数量列表信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@HandlerProcessor(ProcessorId.MAP_COMMON)
public class ReqMonsterCountMenuHandler extends AbstractHandler<ReqMonsterCountMenuMessage> {

    @Override
    public void doAction(ReqMonsterCountMenuMessage msg) {
        MonsterInfoManager.getInstance().reqCountMenu(msg.getSession().getId(), msg.getMonsterType());
    }

}
