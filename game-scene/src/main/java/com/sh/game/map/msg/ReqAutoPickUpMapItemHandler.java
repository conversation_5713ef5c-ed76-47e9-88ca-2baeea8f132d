package com.sh.game.map.msg;

import com.sh.game.common.communication.msg.map.ReqAutoPickUpMapItemMessage;
import com.sh.game.map.player.PlayerManager;
import com.sh.server.AbstractHandler;

/**
 * <p>拾取地图道具</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqAutoPickUpMapItemHandler extends AbstractHandler<ReqAutoPickUpMapItemMessage> {

    @Override
    public void doAction(ReqAutoPickUpMapItemMessage msg) {
        PlayerManager.getInstance().pickUp(msg.getSession().getId(), msg.getObjIds());
    }

}
