package com.sh.game.map;

import com.sh.game.common.communication.msg.map.ResAttrChangeMessage;
import com.sh.game.common.communication.msg.map.ResHpMpMaxChangeMessage;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.util.AttrUtil;

import java.util.Map;

public class AttributeManager {
    private static final AttributeManager INSTANCE = new AttributeManager();

    private AttributeManager() {

    }

    public static AttributeManager getInstance() {
        return INSTANCE;
    }


    /**
     * 先有普通属性变化，后有buff影响的属性变化
     *
     * @param performer
     */
    public void attributeCount(Performer performer) {
        Attribute newAttribute = new Attribute();
        Attribute base = performer.getAttributeMap().getOrDefault(AttributeConst.AttributeType.Cate.BASE, new Attribute());
        newAttribute.fixAdd(base);
        newAttribute.fixAdd(performer.getAttributeMap().getOrDefault(AttributeConst.AttributeType.Cate.BUFF, new Attribute()));
        Attribute ratioAttr = performer.getAttributeMap().getOrDefault(AttributeConst.AttributeType.Cate.BUFF_RATIO, new Attribute());
        ratioAttr.getAttributeMap().forEach((k, v) -> {
            double percent = v / 10000.0;
            long v1 = (long) (base.get(AttributeEnum.valueOf(k)) * percent);
            newAttribute.fixAdd(k, v1);
        });
        Attribute oldAttribute = performer.getFinalAttribute();

        long chp = performer.getHp(), nhp = chp;
        double currentHpPercent = (double) chp / (double) AttributeEnum.ZTXY_HP.getAttrValue(oldAttribute);

        if (!performer.isDead()) {
            // 属性变更后，玩家当前血量 =  血量上限 * 当前血量百分比
            nhp = Math.max(0, (long) (currentHpPercent * AttributeEnum.ZTXY_HP.getAttrValue(newAttribute)));
        }
        performer.setFinalAttribute(newAttribute);
        long cmp = performer.getMp();
        long nmp = Math.max(0, cmp + AttributeEnum.ZTXY_ENERGY.getAttrValue(newAttribute) - AttributeEnum.ZTXY_ENERGY.getAttrValue(oldAttribute));
        if (chp != nhp) {
            performer.setHp(nhp);
            performer.setMp((int) nmp);
            performer.sendHpMpChangeMessage();
            ResHpMpMaxChangeMessage msg = new ResHpMpMaxChangeMessage();
            msg.setLid(performer.getId());
            msg.setHpMax(AttributeEnum.ZTXY_HP.getAttrValue(newAttribute));
            msg.setMpMax(AttributeEnum.ZTXY_ENERGY.getAttrValue(newAttribute));
            Module.MSG_TRANSFORMER.sendRoundMessage(msg, performer);
        }

        Player player = PlayerManager.getInstance().getPlayer(performer.getRid());
        if (player == null) {
            return;
        }
        // 比较变化
        Map<Integer, Long> changes = Attribute.compare(newAttribute, oldAttribute);
        if (!changes.isEmpty()) {
            //公开属性广播
            AttributeBean attributeBean = AttrUtil.findBroadcastAttr(changes);
            ResAttrChangeMessage message = new ResAttrChangeMessage();
            message.setLid(performer.getId());
            message.setAttr(attributeBean);
            Module.MSG_TRANSFORMER.sendRoundMessage(message, performer);
        }
    }
}
