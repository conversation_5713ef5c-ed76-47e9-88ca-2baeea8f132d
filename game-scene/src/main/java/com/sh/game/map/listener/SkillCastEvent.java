package com.sh.game.map.listener;


import com.sh.game.common.entity.skill.Skill;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import lombok.Getter;

@Getter
public class SkillCastEvent {

    private Performer caster;

    private Performer target;

    private Skill skill;

    private FightResult.BuffImmediateEffect effect;

    private long time;

    public SkillCastEvent(Performer caster, Performer target, Skill skill, FightResult.BuffImmediateEffect effect, long time) {
        this.caster = caster;
        this.target = target;
        this.skill = skill;
        this.effect = effect;
        this.time = time;
    }
}
