package com.sh.game.map.fsm.monster;

import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.ai.AIFactory;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

public class MonsterAlertState extends FSMState<MonsterActor> {

    public MonsterAlertState(int type, MonsterActor performer) {
        super(type, performer);
    }

    @Override
    public void enter(GameMap map) {
        AIFactory.getAI(performer.getAiType()).battleEnter(map, performer);
    }

    @Override
    public void exit(GameMap map) {
        AIFactory.getAI(performer.getAiType()).battleExit(map, performer);
    }

    @Override
    public void update(GameMap map, int delta) {
        AIFactory.getAI(performer.getAiType()).battleUpdate(map, performer, delta);
    }

    @Override
    public int checkTransition(GameMap map, int dt) {
        return AIFactory.getAI(performer.getAiType()).checkStateTransitionOnFight(map, performer);
    }

}
