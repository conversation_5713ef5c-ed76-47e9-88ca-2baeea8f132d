package com.sh.game.map.buff.type;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HotWheel extends AbsBuffEffect {

//    @Override
//    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
//        buff.initParam(b -> {
//            int[][] extraParam = BuffParamEnhanceUtil.calExtraParam(caster, buff.config());
//            int range = extraParam[0][0];
//            int damage = FightUtil.calBufferHurt(caster, effect, extraParam[0][1], 0);
//            buff.getParams().add(range);
//            buff.getParams().add(damage);
//        });
//    }
//
//    @Override
//    public void onUpdate(GameMap map, Performer performer, Buff buff, long time, int delta) {
//        if (buff.getParams().size() < 2) {
//            buff.setInvalid(true);
//            return;
//        }
//
//        int range = buff.fetchParam(0);
//        int damage = buff.fetchParam(1);
//        Performer boomTarget = (Performer) map.getObject(performer.getPoint().x, performer.getPoint().y, range,
//                iMapObject -> {
//                    if (iMapObject == null || !iMapObject.isPerformer() || iMapObject.isDead()) {
//                        return false;
//                    }
//                    if (!performer.isEnemy(iMapObject, true)) {
//                        return false;
//                    }
//                    return !((Performer) iMapObject).getBuffState().inStateOf(BuffState.State.AVOID);
//                });
//
//        if (boomTarget != null) {
//            FightResult.FightEffectRet effectRet = new FightResult.FightEffectRet();
//            effectRet.hurt = damage;
//            effectRet.showHurt = damage;
//
//            FightResult ret = new FightResult();
//            FightUtil.hurt(FightUtil.HurtFrom.BUFF, ret, effectRet, performer, boomTarget, TimeUtil.getNowOfMills(), false);
//
//            //发送消息
//            boomTarget.sendHpMpChangeMessage();
//            ResPiaoZiEffectMessage piaoMsg = new ResPiaoZiEffectMessage();
//            MapProtos.ResPiaoZiEffect.Builder piaoZiEffect = MapProtos.ResPiaoZiEffect.newBuilder();
//            piaoZiEffect.setTargetId(boomTarget.getId());
//            piaoZiEffect.setParam(damage);
//            piaoZiEffect.setPiaozi(FightConst.HurtType.NORMAL_HURT.type());
//            piaoMsg.setProto(piaoZiEffect.build());
//            Module.MSG_TRANSFORMER.sendRoundMessage(piaoMsg, boomTarget);
//            //buff特效展现
//            ResDisplayBufferEffectMessage bufferEffectMessage = new ResDisplayBufferEffectMessage();
//            BufferProtos.ResDisplayBufferEffect.Builder displayBufferEffect = BufferProtos.ResDisplayBufferEffect.newBuilder();
//            displayBufferEffect.setBuffId(buff.getId());
//            displayBufferEffect.setTargetId(boomTarget.getId());
//            bufferEffectMessage.setProto(displayBufferEffect.build());
//            Module.MSG_TRANSFORMER.sendRoundMessage(bufferEffectMessage, boomTarget);
//            // 如果死了需要发死亡消息
//            log.info("对象[{}]->[{}]受到风火轮伤害,当前血量{}/{}", boomTarget.getId(), boomTarget.getName(), boomTarget.getHp(), boomTarget.getFinalAttribute().findMaxHp());
//            buff.setInvalid(true);
//        }
//    }
}
