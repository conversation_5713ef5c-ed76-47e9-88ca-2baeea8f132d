package com.sh.game.map.fsm.ztmonster.states;

import com.sh.game.common.constant.MapConst;
import com.sh.game.map.duplicate.instance.ztxy.ZtxyBarrierDuplicate;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.ztmonster.MonsterAiFactory;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;


public class MonsterActiveState extends FSMState<MonsterActor> {
    public MonsterActiveState(int type, MonsterActor performer) {
        super(type, performer);
    }

    @Override
    protected void enter(GameMap map) {
        MonsterAiFactory.getByType(performer.getAiType()).activeEnter(map, performer);
    }

    @Override
    protected void exit(GameMap map) {
        MonsterAiFactory.getByType(performer.getAiType()).activeExist(map, performer);
    }

    @Override
    protected void update(GameMap map, int delta) {
        MonsterAiFactory.getByType(performer.getAiType()).activeUpdate(map, performer, delta);
    }

    @Override
    protected int checkTransition(GameMap map, int dt) {
        return MonsterAiFactory.getByType(performer.getAiType()).activeTransition(map, performer, dt);
    }
}
