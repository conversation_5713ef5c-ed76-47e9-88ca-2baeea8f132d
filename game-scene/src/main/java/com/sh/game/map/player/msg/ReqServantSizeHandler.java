package com.sh.game.map.player.msg;

import com.sh.game.common.communication.msg.system.monster.ReqServantSizeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.HandlerProcessor;
import com.sh.game.map.player.PlayerManager;
import com.sh.server.AbstractHandler;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-10-13
 **/
@HandlerProcessor(ProcessorId.MAP_PLAYER)
public class ReqServantSizeHandler extends AbstractHandler<ReqServantSizeMessage> {
    @Override
    public void doAction(ReqServantSizeMessage msg) {
        PlayerManager.getInstance().sendServantSize(msg.getSession().getId());
    }
}
