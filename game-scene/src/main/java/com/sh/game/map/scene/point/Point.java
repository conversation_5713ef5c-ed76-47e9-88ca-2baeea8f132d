package com.sh.game.map.scene.point;

import com.sh.game.common.gemo.Vec2;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.map.SceneManager;
import com.sh.game.map.obj.IMapObject;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.util.GeomUtil;
import lombok.extern.log4j.Log4j2;

import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.Map;

/**
 * 格子(更具体的点)，这个点绝对不允许自己new，只能从地图中获取，如果一定要传递x,y这样的结构，请使用 TwoTuple<Integer,
 * Integer>替代
 *
 * <AUTHOR>
 */
@Log4j2
public class Point {

    /**
     * 坐标
     */
    public int x;

    /**
     * 坐标
     */
    public int y;

    /**
     * 类型
     */
    public short type;

    public Point() {

    }

    public Vec2 toVec2(){
        return new Vec2(this.x, this.y);
    }

    public Point(String xy) {
        int _index = xy.indexOf('_');
        this.x = Short.parseShort(xy.substring(0, _index));
        this.y = Short.parseShort(xy.substring(_index + 1));
    }

    public Point(int x, int y) {
        this.x = x;
        this.y = y;
    }

    public Point(int x, int y,int type) {
        this.x = x;
        this.y = y;
        this.type = (short) type;
    }

    public Point(int id) {
        x = (short) (id >>> 16);
        y = (short) (id & 0xFFFF);
    }

    /**
     * 是否可以站立在该格子里（包含重叠规则）
     *
     * @param obj
     * @return
     */
    public boolean canStand(IMapObject obj) {
        if (obj == null) {
            return false;
        }
        return canStand(SceneManager.getInstance().getMap(obj), obj);
    }

    public boolean canStand(GameMap map, IMapObject object) {
        if (map == null) {
            return false;
        }
        if(!PointType.canMove(type)){
            return false;
        }
        PointState state = map.getPointState(this);
        if (state != null && state.isBlock()) {
            return false;
        }
        if (object != null) {
            Map<Long, IMapObject> objectMapInGrid = map.getObjectMapInGrid(x, y);
            if (objectMapInGrid == null) {
                return true;
            }
            int penetrable = state == null ? 0 : state.getPenetrable();
            int pen = penetrable | map.getPenetrable();
            Iterator<Map.Entry<Long, IMapObject>> iterator = objectMapInGrid.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, IMapObject> entry = iterator.next();
                IMapObject stand = entry.getValue();
                if (stand == null) {
                    continue;
                }
                if (stand.getId() == object.getId()) {
                    continue;
                }
                if (map.getObject(stand.getId()) == null) {
                    log.error("坐标点存在未清除的对象: {}.{} {}", this, stand.getId(), stand.getName());
                    iterator.remove();
                    continue;
                }
                // 怪物可在同一点
//                if (!object.overlying(stand, pen)) {
//                    return false;
//                }
            }
        }

        return true;
    }


    /**
     * 是否可以走到该格子里（包含重叠规则）
     * @param map
     * @param object
     * @param force
     * @return
     */
    public boolean canWalk(GameMap map, IMapObject object, boolean force) {
        if (!PointType.canMove(type)) {
            log.error("canWalk check failed mapId:{} id:{} name:{} type :{} point:{}", map.getCfgId(), object.getId(), object.getName(), type, this);
            return false;
        }
        if (!force && object != null) {
            PointState state = map.getPointState(this);
            if (object.getType() == MapObjectType.MONSTER && state != null && state.isSafe()) {
                return false;
            }
            Map<Long, IMapObject> objectMapInGrid = map.getObjectMapInGrid(x, y);
            if (objectMapInGrid == null) {
                return true;
            }
            // TODO: 2023/11/20 去掉阻挡
//            int penetrable = state == null ? 0 : state.getPenetrable();
//            int pen = penetrable | map.getPenetrable();
//            for (Map.Entry<Long, IMapObject> entry : objectMapInGrid.entrySet()) {
//                IMapObject stand = entry.getValue();
//                if (stand != null && !stand.isDead() && !object.penetrate(stand, pen)) {
//                    return false;
//                }
//            }
        }

        return true;
    }


    public Point getNearPointByDir(GameMap map, int dir) {
        int tx = x + GeomUtil.DIRECTION_VECTOR[dir][0];
        int ty = y + GeomUtil.DIRECTION_VECTOR[dir][1];
        return map.getPoint(tx, ty);
    }


    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public short getType() {
        return type;
    }

    public int getId() {
        return (x << 16) | y;
    }

    public Collection<IMapObject> getObjectList(GameMap map) {
        if (map == null) {
            return Collections.EMPTY_LIST;
        }
        Map<Long, IMapObject> objectMapInGrid = map.getObjectMapInGrid(x, y);
        if (objectMapInGrid == null) {
            return Collections.EMPTY_LIST;
        }
        return objectMapInGrid.values();
    }

    public static int id2X(int id) {
        return id >>> 16;
    }

    public static int id2Y(int id) {
        return id & 0xFFFF;
    }

    public static int xy2Id(int x, int y) {
        return (x << 16) | y;
    }

    /**
     * 获取x坐标对应的像素坐标
     * @return
     */
    public int getPixelX() {
        return x * 3200;
    }

    /**
     * 获取y坐标对应的像素坐标
     * @return
     */
    public int getPixelY() {
        return y * 1600;
    }

    /**
     * 获取像素坐标
     * @return
     */
    public PixelPoint getPixelPoint() {
        return new PixelPoint(getPixelX(), getPixelY(), this);
    }

    /**
     * 获取向量坐标
     * @return
     */
    public Vec3 toVec3(){
        return new Vec3(this.x, this.y, 0);
    }

    @Override
    public String toString() {
        return "Point[" + this.x + "," + this.y + "]";
    }
}
