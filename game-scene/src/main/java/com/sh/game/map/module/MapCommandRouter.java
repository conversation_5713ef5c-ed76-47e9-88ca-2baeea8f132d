package com.sh.game.map.module;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.processor.MapCommonProcessor;
import com.sh.game.map.processor.MapPlayerProcessor;
import com.sh.game.map.processor.MapProcessor;
import com.sh.game.server.CommandRouter;

/**
 * 地图服务器命令路由
 * <AUTHOR>
 * @date 2018/8/30 10:31
 */
public class MapCommandRouter extends CommandRouter {


    public MapCommandRouter() {
    }

    @Override
    public void register() {
        //地图玩家
        this.registerProcessor(ProcessorId.MAP_PLAYER, new MapPlayerProcessor());

        // 地图
        this.registerProcessor(ProcessorId.MAP_SCENE, new MapProcessor());

        //其他逻辑
        this.registerProcessor(ProcessorId.MAP_COMMON, new MapCommonProcessor());
    }
}
