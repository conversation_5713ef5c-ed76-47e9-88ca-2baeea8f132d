package com.sh.game.map.util;


import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.bean.*;
import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.config.model.XiaLvConfig;
import com.sh.game.common.constant.DailyConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.common.entity.map.ZTPetDTO;
import com.sh.game.common.util.PositionUtil;
import com.sh.game.map.SceneManager;
import com.sh.game.map.daily.DailyManager;
import com.sh.game.map.daily.controller.type.ShaBaKeController;
import com.sh.game.map.daily.controller.type.ShaBaKeCrossController;
import com.sh.game.map.obj.*;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.PointState;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class ObjUtil {

    public static RoundPlayerBean packRoundPlayer(PlayerActor actor) {
        RoundPlayerBean roundPlayer = new RoundPlayerBean();

        if (actor.getAvatarBackState().isInvisible()) {
            return roundPlayer;
        }

        roundPlayer.setRid(actor.getRid());
        roundPlayer.setLid(actor.getId());
        roundPlayer.setX(actor.getPoint().x);
        roundPlayer.setY(actor.getPoint().y);
        GameMap map = SceneManager.getInstance().getMap(actor);
        PointState pointState = map.getPointState(actor.getPoint());
        if (pointState != null) {
            roundPlayer.setInSafeArea(pointState.isSafe());
            roundPlayer.setPenetrable(pointState.getPenetrable());
        }
        roundPlayer.setDir(actor.getDir());
        roundPlayer.setName(actor.getName());
        roundPlayer.setOwnerName(actor.getOwnerName() == null ? "" : actor.getOwnerName());
        roundPlayer.setCareer(actor.getCareer());
        roundPlayer.setSex(actor.getSex());
        roundPlayer.setLevel(actor.getLevel());
        roundPlayer.setHair(actor.getHair());
        roundPlayer.setHp(actor.getHp());
        roundPlayer.setMaxhp(actor.getFinalAttribute().findMaxHp());
        roundPlayer.setState(0);
        // 行会
        roundPlayer.setUnionId(actor.getUnion().getId());
        roundPlayer.setUnionName(actor.getUnion().getName() == null ? "" : actor.getUnion().getName());
        roundPlayer.setUnionPosition(actor.getUnion().getPosition());
        // 队伍
        roundPlayer.setTeamId(actor.getTeamID());
        //阵营
        roundPlayer.setGroup(actor.getGroup());
        //高级颜色显示
        roundPlayer.setSuperColor(actor.isSuperColor() ? 1 : 0);
        roundPlayer.setEquips(new ArrayList<>());
        roundPlayer.setWears(new ArrayList<>());
        // 装备
        actor.getEquipMap().forEach((k, v) -> {
            PerformerEquipBean equipBean = new PerformerEquipBean();
            equipBean.setEquipIndex(k);
            equipBean.setItemId(v.getItemId());
            roundPlayer.getEquips().add(equipBean);
        });

        //设置进入场景属性
        AttributeBean attrBean = AttrUtil.findBroadcastAttr(actor.getFinalAttribute());
        roundPlayer.setAttr(attrBean);

        // appearance
        actor.getAppearanceWears().forEach((k, v) -> {
            AppearanceWearBean bean = new AppearanceWearBean();
            bean.setType(k);
            bean.setId(v);
            roundPlayer.getWears().add(bean);
        });
        roundPlayer.setBuffers(new ArrayList<>());
        // buff
        roundPlayer.getBuffers().addAll(packPerformerBuffer(actor.getBuffs().getBuffMap().values()));
        // pk值
        roundPlayer.setPkValue(actor.getPkValue());
        roundPlayer.setGray(actor.isGrayShow() ? 1 : 0);
        roundPlayer.setCollecting(actor.getCollecting());
        roundPlayer.setTeleport(actor.isTeleport());
        roundPlayer.setMount(actor.getMount());
        if (actor.getTempTitle() != 0) {
            roundPlayer.setTitle(actor.getTempTitle());
        } else {
            roundPlayer.setTitle(actor.getTitle());
        }

        roundPlayer.setDigMiner(actor.isDig());
        ShaBaKeController controller = (ShaBaKeController) DailyManager.getInstance().getController(DailyConst.DailyType.SHABAKE);
        ShaBaKeCrossController crossController = (ShaBaKeCrossController) DailyManager.getInstance().getController(DailyConst.DailyType.CROSS_SHABAKE);
        if (controller != null) {
            long startChairId = controller.getStartChairId();
            if (startChairId > 0 && startChairId == actor.getUnionId()) {
                roundPlayer.setShabake(1);
            }
        }
        if (crossController != null) {
            long startChairId = crossController.getStartChairId();
            if (startChairId > 0 && startChairId == actor.getUnionId()) {
                roundPlayer.setShabake(2);
            }
        }
        roundPlayer.setWorldFirstKillNum(actor.getWorldFirstKillNum());
        roundPlayer.setType(actor.getType());
        roundPlayer.setMingWang(actor.getMingWang());
        // roundPlayer.setSuperMan(actor.isSuperMan());
        roundPlayer.setMapID(actor.getMapCfgId());
        roundPlayer.setShield(actor.getShield());
        roundPlayer.setMaxShield(actor.getFinalAttribute().findMaxShield());
        roundPlayer.setShenHuangId(actor.getShenHuangId());
        roundPlayer.setCampType(actor.getCampType());
        roundPlayer.setMilitaryRankCfgId(actor.getMilitaryRankCfgId());
        roundPlayer.setRealmConfigId(actor.getRealmId());
        roundPlayer.setZtPet(packPerformerZTPetBean(actor.getZtPet()));

        roundPlayer.setMp(actor.getMp());
        roundPlayer.setMaxMp(actor.getFinalAttribute().findMaxMp());
        return roundPlayer;
    }

    public static List<PerformerBufferBean> packPerformerBuffer(Collection<Buff> buffList) {
        List<PerformerBufferBean> beanList = new ArrayList<>();
        buffList.forEach(buff -> {
            if (!buff.isShow()) {
                return;
            }
            PerformerBufferBean bufferBean = new PerformerBufferBean();
            bufferBean.setBid(buff.getId());
            bufferBean.setCfgId(buff.getId());
            bufferBean.setOwnerId(buff.getCaster());
            bufferBean.setEndTime((int) (buff.getExpire() / 1000));
            bufferBean.setOverlying(buff.getStack());
            beanList.add(bufferBean);
        });
        return beanList;
    }

    public static RoundMonsterBean packRoundMonster(MonsterActor actor, boolean newBorn) {
        RoundMonsterBean roundMonster = new RoundMonsterBean();
        roundMonster.setLid(actor.getId());
        roundMonster.setMid(actor.getConfigId());
        roundMonster.setModel(actor.getModel());
        roundMonster.setName(actor.getName());
        roundMonster.setLevel(actor.getLevel());
        roundMonster.setX(actor.getPoint().x);
        roundMonster.setY(actor.getPoint().y);
        roundMonster.setDir(actor.getDir());
        roundMonster.setHp(actor.getHp());
        roundMonster.setMaxHp(actor.getFinalAttribute().findMaxHp());
        roundMonster.setTeleport(actor.isTeleport());
        roundMonster.setMapID(actor.getMapCfgId());
        roundMonster.setBuffers(new ArrayList<>());
        roundMonster.setGlobalSkills(actor.getGlobalSkills());
        if (actor.getMaster() != null) {
            roundMonster.setMasterId(actor.getMaster().getId());
            roundMonster.setMasterName(actor.getMaster().getName());
        }
        // creator
        if (actor.getOwner() > 0) {
            GameMap map = SceneManager.getInstance().getMap(actor);
            if (map != null) {
                PlayerActor playerActor = map.getPlayerMap().get(actor.getOwner());
                if (playerActor != null) {
                    roundMonster.setOwnerLid(playerActor.getId());
                    roundMonster.setOwnerName(playerActor.getName());
                }
            }
        }
        // buff
        roundMonster.getBuffers().addAll(packPerformerBuffer(actor.getBuffs().getBuffMap().values()));
        if (newBorn && actor.isNewBorn()) {
            if (actor.getRebornPerHp() > 0) {
                roundMonster.setNewBorn(2);
            } else {
                roundMonster.setNewBorn(1);
            }
        }
        if (actor.getType() == MapObjectType.XIALV) {
            XiaLvActor xiaLvActor = (XiaLvActor) actor;
            XiaLvConfig xiaLvConfig = ConfigDataManager.getInstance().getById(XiaLvConfig.class, xiaLvActor.getXialvId());
//            if (xiaLvConfig.getXialvtype() == 1) { // 只有侠侣显示光环
                PlayerActor playerActor = (PlayerActor) xiaLvActor.getMaster();
                roundMonster.setXialvGuanghuan(playerActor.getXialvGuanghuan());
                roundMonster.setXialvUpTeXiao(playerActor.getXialvUpTeXiao());
//            }
            // 徐森说侠侣只配一个麻痹buff
            List<Integer> xiaLvBuff = xiaLvActor.findXiaLvBuff();
            if (xiaLvBuff != null && !xiaLvBuff.isEmpty()) {
                Integer xiaLvMaBiBuff = xiaLvBuff.get(0);
                BuffConfig config = ConfigDataManager.getInstance().getById(BuffConfig.class, xiaLvMaBiBuff);
                if (config != null) {
                    roundMonster.setXiaLvMaBiBuff(config.getProbability());
                }
            }
        }
        roundMonster.setShield(actor.getShield());
        roundMonster.setMaxShield(actor.getMaxShield());
        return roundMonster;
    }

    public static RoundTombBean packRoundTomb(MonsterActor actor) {
        RoundTombBean tomb = new RoundTombBean();
        tomb.setLid(actor.getId());
        tomb.setMid(actor.getConfigId());
        tomb.setX(actor.getPoint().x);
        tomb.setY(actor.getPoint().y);
        tomb.setNextReliveTime((int) (actor.getMachine().getAiData().getNextReliveTime() / 1000));
        tomb.setMapID(actor.getMapCfgId());
        return tomb;
    }

    public static RoundNpcBean packRoundNpc(NPCActor actor) {
        RoundNpcBean roundNpc = new RoundNpcBean();

        roundNpc.setLid(actor.getId());
        roundNpc.setNid(actor.getConfigId());
        roundNpc.setMapNpcId(actor.getConfigId());
        roundNpc.setNpcName(actor.getName());
        roundNpc.setX(actor.getPoint().x);
        roundNpc.setBelongRid(actor.getBelongRid());
        roundNpc.setY(actor.getPoint().y);
        roundNpc.setDir(actor.getDir());
        if (actor.getUnionName() != null) {
            roundNpc.setUnionName(actor.getUnionName());
        }
        roundNpc.setMapID(actor.getMapCfgId());

        return roundNpc;
    }

    public static RoundItemBean packRoundItem(GroundItem actor) {
        RoundItemBean roundItem = new RoundItemBean();

        roundItem.setLid(actor.getId());
        if (actor.getItem() != null) {
            roundItem.setItemId(actor.getItem().getCfgId());
            roundItem.setCount(actor.getItem().findCount());
            if (actor.getItem().getEquipData() != null) {
                roundItem.setTjId(actor.getItem().eData().getTjId());
                roundItem.setDemonEquipId(actor.getItem().eData().getDemonEquipId());
            }
        }
        roundItem.setX(actor.getPoint().x);
        roundItem.setY(actor.getPoint().y);
        //归属时间
        roundItem.setDropTime(actor.getOwnerProtectedExpire());
        //最后设置个人 先设置行会队伍归属
        if (actor.getOwnerTeam() > 0) {
            roundItem.setOwnerId(actor.getOwnerTeam());
        } else if (actor.getOwnerUnion() > 0) {
            roundItem.setOwnerId(actor.getOwnerUnion());
        } else {
            roundItem.setOwnerId(actor.getOwner());
        }
        roundItem.setSection(actor.getSection());
        if (actor.getBuff() != null) {
            roundItem.setBuffId(actor.getBuff().getId());
        }
        roundItem.setDiscarder(actor.getDiscarder());
        return roundItem;
    }

    public static RoundEventBean packRoundEvent(GroundEvent actor) {
        RoundEventBean roundEvent = new RoundEventBean();

        roundEvent.setLid(actor.getId());
        roundEvent.setEventId(actor.getConfigId());
        roundEvent.setX(actor.getPoint().x);
        roundEvent.setY(actor.getPoint().y);

        return roundEvent;
    }

    public static RoundBufferBean packRoundBuffer(GroundBuff actor) {
        RoundBufferBean roundBuffer = new RoundBufferBean();
        roundBuffer.setBuffers(new ArrayList<>());
        // buff
        roundBuffer.getBuffers().addAll(packPerformerBuffer(actor.getBuffList()));
        roundBuffer.setLid(actor.getId());
        roundBuffer.setX(actor.getPoint().x);
        roundBuffer.setY(actor.getPoint().y);
        roundBuffer.setBlock(actor.getBuffState().inStateOf(BuffState.State.BLOCK));

        return roundBuffer;
    }

    public static RoundRobotBean packRoundRobot(RobotActor actor) {
        RoundRobotBean roundRobot = new RoundRobotBean();
        roundRobot.setLid(actor.getId());
        roundRobot.setX(actor.getPoint().x);
        roundRobot.setY(actor.getPoint().y);
        roundRobot.setDir(actor.getDir());
        roundRobot.setName(actor.getName());
        roundRobot.setCareer(actor.getCareer());
        roundRobot.setSex(actor.getSex());
        roundRobot.setHair(0);
        roundRobot.setLevel(actor.getLevel());
        roundRobot.setTitle(actor.getTitle());
        roundRobot.setHp(actor.getHp());
        roundRobot.setMaxhp(actor.getFinalAttribute().findMaxHp());
        actor.getEquips().forEach((k, v) -> {
            PerformerEquipBean bean = new PerformerEquipBean();
            bean.setEquipIndex(k);
            bean.setItemId(v);
            roundRobot.getEquips().add(bean);
        });
        AppearanceWearBean wearBean = new AppearanceWearBean();
        wearBean.setType(4);
        wearBean.setId(actor.getTitle());
        roundRobot.getWears().add(wearBean);
        return roundRobot;
    }


    public static RoundBulletBean packRoundBullet(BulletActor actor) {
        RoundBulletBean roundBullet = new RoundBulletBean();
        roundBullet.setLid(actor.getId());
        roundBullet.setX(PositionUtil.getClientX(actor.getPosition()));
        roundBullet.setY(PositionUtil.getClientY(actor.getPosition()));
        roundBullet.setDir(actor.getDir());
        roundBullet.setCid(actor.getConfigId());
        roundBullet.setUnitVx((int) actor.getUnitVx());
        roundBullet.setUnitVy((int) actor.getUnitVy());
        Performer master = actor.getMaster();
        if (master != null) {
            roundBullet.setMasterId(master.getId());
        }
        return roundBullet;
    }

    public static GroundTrapBean packGroundTrap(GroundTrap actor) {
        GroundTrapBean bean = new GroundTrapBean();
        bean.setLid(actor.getId());
        bean.setX((int)actor.getPosition().x);
        bean.setY((int)actor.getPosition().y);
        bean.setEffectId(actor.getConfig().getId());
        bean.setType(actor.trapType());
        return bean;
    }

    public static PerformerZTPetBean packPerformerZTPetBean(ZTPetDTO dto) {
        PerformerZTPetBean bean = new PerformerZTPetBean();
        bean.setId(dto.getId());
        bean.setLevel(dto.getLevel());
        bean.setRank(dto.getRank());
        return bean;
    }
}
