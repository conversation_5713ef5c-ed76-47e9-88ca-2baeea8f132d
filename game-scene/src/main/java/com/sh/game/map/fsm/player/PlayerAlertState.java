package com.sh.game.map.fsm.player;

import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.map.fight.skill.AbsSkillReleaser;
import com.sh.game.map.fight.skill.PerformerSkillReleaser;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.ai.AIData;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;


/**
 * 警戒状态，进入战斗副本时维持该状态
 * 在该状态下寻找攻击目标，找到目标后立即切换一次技能状态
 */
public class PlayerAlertState extends FSMState<PlayerActor> {

    public PlayerAlertState(int type, PlayerActor performer) {
        super(type, performer);
    }

    @Override
    public void enter(GameMap map) {
    }

    @Override
    public void exit(GameMap map) {
    }

    @Override
    public void update(GameMap map, int delta) {
        if (this.performer.isDead()) {
            return;
        }
        AIData aiData = performer.getMachine().getAiData();
        aiData.updateMyTargetOrAttackedMe(performer, delta);
    }

    @Override
    public int checkTransition(GameMap map, int dt) {
        if (performer.isDead()) {
            return DIE;
        }
        if (map.isSafe()) {
            return ACTIVE;
        }
        AIData aiData = performer.getMachine().getAiData();

        AbsSkillReleaser skillReleaser = aiData.getSkillReleaser();
        if (skillReleaser != null) {
            return CAST;
        }
        return ALERT;
    }

}
