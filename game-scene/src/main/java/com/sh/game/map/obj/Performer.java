package com.sh.game.map.obj;

import com.google.common.collect.Maps;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.cd.CD;
import com.sh.game.common.cd.CDObject;
import com.sh.game.common.communication.msg.map.buffer.ResDisplayBufferEffectMessage;
import com.sh.game.common.communication.msg.map.fight.ResHpMpChangeMessage;
import com.sh.game.common.communication.msg.map.fight.ResObjectDieMessage;
import com.sh.game.common.communication.msg.map.fight.ResShieldChangeMessage;
import com.sh.game.common.communication.msg.map.fight.bean.HpMpChangeBean;
import com.sh.game.common.config.cache.ElementEffectConfigCache;
import com.sh.game.common.config.entity.ModelOffset;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.common.entity.buff.Buffs;
import com.sh.game.common.entity.map.SkillDTO;
import com.sh.game.common.entity.map.ZTPetDTO;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.gemo.Vec2;
import com.sh.game.common.gemo.shap.Rectangle;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.AttributeManager;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.buff.BuffTriggerUtil;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.SkillEffectFactory;
import com.sh.game.map.fight.entity.ElementMarking;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.fsm.FSMMachine;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 有行为的对象
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
public abstract class Performer extends MapObject implements CDObject {

    private Buffs buffs = new Buffs();

    private BuffState buffState = new BuffState();

    private Map<AttributeConst.AttributeType, Attribute> attributeMap = new HashMap<>();

    /**
     * 最终属性（包含buff）
     */
    private Attribute finalAttribute;

    private int level;

    private int sex;

    private int career;

    /**
     * HP
     */
    protected long hp;

    /**
     * MP
     */
    protected int mp;

    /**
     * 护盾
     */
    protected long shield;
    /**
     * 护盾最大值
     */
    protected long maxShield;

    /**
     * 是否死亡
     */
    protected boolean dead;

    /**
     * CD 系统 包含技能和 Buff 之类的
     */
    protected Map<Long, CD> cdMap = new HashMap<>();

    /**
     * 仇恨列表
     * key: 仇恨对象 id
     * value：仇恨值
     */
    private Map<Long, Long> threatMap = new HashMap<>();

    /**
     * 战斗对象
     */
    private long fightTarget;

    /**
     * 攻击我的对象
     */
    private long whoAttackMe;
    private long whoAttackMeTime;

    /**
     * 我的攻击目标
     */
    private long whoMyTarget;
    private long whoMyTargetTime;

    /**
     * 领导
     */
    protected Performer master;


    /**
     * 移动速度 （单位：像素/秒）
     */
    private int moveSpeed;

    /**
     * 移动时间间隔 (ms)
     */
    private int moveInterval = 300;

    /**
     * 当前路径
     */
    private List<Point> pathList = new ArrayList<>();

    /**
     * 上一次从pathList中取出的坐标点，通过和玩家当前坐标进行比较来判断对象的pathList是否是有效的。
     * 主要在对象被推走或者进行了其他方式的移动后，pathList可能不再适用寻路的情况
     */
    private Point pathPreviousPoint;

    /**
     * 路径目标点
     */
    private Point pathTargetPoint;

    /**
     * 主动技能
     */
    private Map<Integer, Skill> skillMap = new HashMap<>();

    /**
     * 被动技能
     */
    private Map<Integer, Skill> passiveSkillMap = new HashMap<>();


    @Deprecated
    private FSMMachine<? extends Performer> skillFSMMachine;


    public abstract FSMMachine<? extends Performer> getMachine();

    // 受击盒
    private ModelOffset modelOffset = new ModelOffset();

    /**
     * 短时间内可拥有的暂时可使用的技能
     * key: 技能id
     * value: 截止时间（ms）
     */
    private Map<Integer, Long> skillTemp = new HashMap<>();


    /**
     * 元素印记层数
     * key: 元素id
     * value: [层数，到期时间(s)]
     */
    private Map<AttributeEnum, TwoTuple<Integer, Long>> elementLayers = new HashMap<>();

    /**
     * ----------------------------------------------------
     *        TODO   以下内容尚未整理
     * ----------------------------------------------------
     */


    /**
     * 杀手ID
     */
    private long killerId;


    /**
     * 死亡时间
     */
    private long deadTime;

    /**
     * 技能特殊效果
     */
    private Map<Integer, Set<Integer>> skillSpecEffectMap = Maps.newHashMap();

    /**
     * buffTriggerId  triggerType -> triggerId set
     */
    private Map<Integer, Set<Integer>> buffTriggerMap = new HashMap<>();

    /**
     * buff参数效果增强 buffId -> cfg_buff_special id
     */
    private Map<Integer, Set<Integer>> buffParamEnhanceMap = Maps.newHashMap();


    private int bornTime;

    /**
     * 转移仇恨目标id
     */
    private long threatTransferId;

    /**
     * 技能释放目标的类型（用于判断善恶模式敌友双方）
     */
    private int skillTargetType;

    /**
     * 技能释放目标的id（用于判断善恶模式敌友双方）
     */
    private long skillTargetId;


    // ======移动====================

    private Point lastPoint;

    private long moveAvailableAt;

    private boolean moved = false;

    protected boolean teleport = false;

    protected int mount;

    protected ZTPetDTO ztPet;

    //=====================系统数据生成需要即时更新===================


    private boolean afterMove;

    /**
     * 上一次同步血量和蓝量sum
     */
    private long syncHpMapSum = 0;

    /**
     * 上次移动时间
     */
    private long lastMoveTime;

    /**
     * 收到伤害时记录攻击对象id
     */
    private Set<Long> hurtMap = new HashSet<>();

    private Rectangle hitBox;

    @Override
    public boolean isPerformer() {
        return true;
    }

    public void addThreat(Performer performer, long value) {
        this.threatMap.merge(performer.getId(), value, Long::sum);
    }

    public void setWhoAttackMe(long whoAttackMe) {
        this.whoAttackMe = whoAttackMe;
        this.whoAttackMeTime = 4000;
    }

    public void setWhoMyTarget(long whoMyTarget) {
        this.whoMyTarget = whoMyTarget;
        this.whoMyTargetTime = 4000;
    }

    public void resetAttackTarget() {
        setWhoMyTarget(0);
        setWhoAttackMe(0);
        setFightTarget(0);
    }

    public boolean isMoved() {
        return moved;
    }

    public void setMoved(boolean moved) {
        this.moved = moved;
    }

    public Performer getMaster() {
        return master;
    }

    public void setMaster(Performer master) {
        this.master = master;
    }

    public boolean isPatrolHoming() {
        return false;
    }

    public boolean isPlayer() {
        return false;
    }

    public boolean isMonster() {
        return this.getType() == MapObjectType.MONSTER || this.getType() == MapObjectType.ESCORT || this.getType() == MapObjectType.SERVANT;
    }

    public final void calAttackHurt(SkillEffect effect, Skill skill, Performer target, FightResult.FightEffectRet curEffectRet, FightResult ret) {
        if (target.isPlayer()) {
            calAttackPlayerHurt(effect, skill, target, curEffectRet, ret);
        } else {
            calAttackMonsterHurt(effect, skill, target, curEffectRet, ret);
        }
    }

    protected void calAttackPlayerHurt(SkillEffect effect, Skill skill, Performer target, FightResult.FightEffectRet effectRet, FightResult ret) {
    }

    protected void calAttackMonsterHurt(SkillEffect effect, Skill skill, Performer target, FightResult.FightEffectRet effectRet, FightResult ret) {
    }

    public void playBuff(Integer... buffIds) {
        if (buffIds.length <= 0) {
            return;
        }

        for (int buffId : buffIds) {
            ResDisplayBufferEffectMessage msg = new ResDisplayBufferEffectMessage();
            msg.setTargetId(getId());
            msg.setBuffId(buffId);
            Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
        }
    }

    public void attributeChange() {
        AttributeManager.getInstance().attributeCount(this);
    }

    public void sendHpMpChangeMessage() {
        sendHpMpChangeMessage(0);
    }

    public void sendHpMpChangeMessage(long rid) {
        sendHpMpChangeMessage(rid, 0, 1);
    }

    public void sendHpMpChangeMessage(int type) {
        sendHpMpChangeMessage(0, type, 1);
    }

    public void sendHpMpChangeMessage(int type, int rateCount) {
        sendHpMpChangeMessage(0, type, rateCount);
    }

    public void sendHpMpChangeMessage(long rid, int type, int rateCount) {
        ResHpMpChangeMessage msg = new ResHpMpChangeMessage();
        HpMpChangeBean bean = new HpMpChangeBean();
        bean.setLid(getId());
        bean.setHp(getHp());
        bean.setMp(getMp());
        bean.setType(type);
        bean.setMaxShield(getMaxShield());
        bean.setShield(getShield());
        bean.setRateCount(rateCount);
        msg.getBeans().add(bean);
        if (rid != 0) {
            Module.MSG_TRANSFORMER.sendMsg(msg, rid);
            return;
        }
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
    }

    public Set<Long> findWatcherIds() {
        GameMap map = SceneManager.getInstance().getMap(this);
        if (map == null) {
            return Collections.emptySet();
        }
        Map<Long, IMapObject> watchers = map.getAoi().getWatchers(this.getPoint());
        if (watchers.isEmpty()) {
            return Collections.emptySet();
        }
        return watchers.keySet();
    }

    /**
     * 是否处于副本
     */
    public boolean inDuplicate() {
        DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, getMapCfgId());
        return config != null;
    }

    public void sendDieMessage() {
        if (!isDead()) {
            return;
        }
        ResObjectDieMessage dieMessage = new ResObjectDieMessage();
        dieMessage.setLid(getId());
        dieMessage.setKillerId(getKillerId());
        dieMessage.setDir(getDir());
        Point point = getPoint();
        if (point != null) {
            dieMessage.setX(point.getX());
            dieMessage.setY(point.getY());
        }
        Module.MSG_TRANSFORMER.sendRoundMessage(dieMessage, this);
    }

    public void sendShieldChangeMessage() {
        ResShieldChangeMessage msg = new ResShieldChangeMessage();
        msg.setTargetId(getId());
        msg.setShield(getShield());
        msg.setMaxShield(getFinalAttribute().findMaxShield());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
    }

    public void sendShieldChangeMessage(int type) {
        ResShieldChangeMessage msg = new ResShieldChangeMessage();
        msg.setTargetId(getId());
        msg.setShield(getShield());
        msg.setType(type);
        msg.setMaxShield(getFinalAttribute().findMaxShield());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
    }

    public boolean isServant() {
        return false;
    }


    public Map<Integer, Long> findSkillTemp() {
        long nowOfMills = TimeUtil.getNowOfMills();
        Set<Integer> rmKey = new HashSet<>();
        for (Map.Entry<Integer, Long> entry : skillTemp.entrySet()) {
            if (nowOfMills > entry.getValue()) {
                rmKey.add(entry.getKey());
            }
        }
        for (Integer k : rmKey) {
            skillTemp.remove(k);
        }
        return skillTemp;
    }

    /**
     * 累加元素印记层数
     *
     * @param type 元素类型
     * @param inc  需要累加层数
     */
    public ElementMarking incElementEffectLevel(AttributeEnum type, int inc) {
        ElementEffectConfigCache configCache = ConfigCacheManager.getInstance().getCache(ElementEffectConfigCache.class);
        int maxLevel = configCache.findElementEffectMaxLevel(type);
        TwoTuple<Integer, Long> tuple = elementLayers.getOrDefault(type, new TwoTuple<>(0, -1L));
        int curLayer = tuple.getFirst();
        // 过期重置
        long endTime = tuple.getSecond();
        if (endTime > 0 && TimeUtil.getNowOfMills() > endTime) {
            curLayer = 0;
        }
        curLayer = Math.min(curLayer + inc, maxLevel);
        ElementEffectConfigCache cache = ConfigCacheManager.getInstance().getCache(ElementEffectConfigCache.class);
        SkillElementConfig elementEffectConfig = cache.findElementEffectConfig(type, curLayer);
        endTime = TimeUtil.getNowOfMills() + elementEffectConfig.getLastTime();
        tuple.setFirst(curLayer);
        tuple.setSecond(endTime);
        elementLayers.put(type, tuple);
        return new ElementMarking(type,elementEffectConfig,endTime);
    }

    /**
     * 累加元素印记
     *
     * @param type 元素类型
     */
    public ElementMarking incElementEffectLevel(AttributeEnum type) {
        return incElementEffectLevel(type, 1);
    }

    /**
     * 获取元素印记层数
     *
     * @param type 元素类型
     * @return 层数
     */
    public int findElementEffectLayer(AttributeEnum type) {
        TwoTuple<Integer, Long> tuple = elementLayers.getOrDefault(type, new TwoTuple<>(0, -1L));
        Long endTime = tuple.getSecond();
        if(endTime > 0 && TimeUtil.getNowOfMills() > endTime) {
            elementLayers.remove(type);
            return 0;
        }
        return tuple.getFirst();
    }

    public boolean canAttack(){
        return true;
    }

    public Rectangle getHitBox(){
        if (hitBox == null) {
            return null;
        }
        hitBox.x = position.x + modelOffset.getXOffset() - modelOffset.getHalfW();
        hitBox.y = position.y + modelOffset.getYOffset() - modelOffset.getHalfH();
        return hitBox;
    }

    /**
     * 到目标受击盒的距离
     */
    public int disToHitBox(Performer target) {
        if (target == null) {
            return 0;
        }
        Vec2 center = getHitBox().center();
        Rectangle targetHitBox = target.getHitBox();
        if (targetHitBox.contains(center.x, center.y)) {
            return 0;
        }
        float dx = center.x < targetHitBox.x ? targetHitBox.x - center.x : (center.x > targetHitBox.x + targetHitBox.w ? center.x - (targetHitBox.x + targetHitBox.w) : 0);
        float dy = center.y < targetHitBox.y ? targetHitBox.y - center.y : (center.y > targetHitBox.y + targetHitBox.h ? center.y - (targetHitBox.y + targetHitBox.h) : 0);
        return (int)Math.floor(Math.sqrt(dx * dx + dy * dy));
    }

    public boolean addHp(long hp, boolean sendMsg) {
        if (isDead()) {
            return false;
        }
        long bounds = getFinalAttribute().get(AttributeEnum.ZTXY_TREATMENT_STRONG);
        hp *= (long) (1 + bounds / 10000.0);
        long maxHp = getFinalAttribute().findMaxHp();
        long before = this.hp;
        long after = Math.min(this.hp + hp,maxHp);
        if(before != after){
            this.hp = after;
            if (sendMsg) {
                sendHpMpChangeMessage(FightConst.FloaterType.CURE.type());
            }
            return true;
        }
        return false;
    }

    public boolean addMp(long mp, boolean sendMsg) {
        if (isDead()) {
            return false;
        }
        long maxMp = getFinalAttribute().findMaxMp();
        long after = Math.min(this.mp + mp, maxMp);
        if (after != this.mp) {
            this.mp = Math.toIntExact(after);
            if (sendMsg) {
                sendHpMpChangeMessage(FightConst.FloaterType.RECOVER_MP.type());
            }
            return true;
        }
        return false;
    }

    public void hurt(Performer caster, long hurt){
        BuffTriggerUtil.triggerBuff(caster, this, BuffConst.TriggerType.AFTER_BE_HURT, null, TimeUtil.getNowOfMills());
        // 护盾
        long shield = Math.max(0,getShield());
        long afterShield = Math.max(0, shield - hurt);
        hurt = Math.max(0, hurt - shield);
        setShield(afterShield);
        if (afterShield <= 0) {
            EventUtil.fireEvent(MapEventType.BROKE_SHIELD, this);
        }
        setHp(Math.max(hp - hurt, 0));
        if (getHp() <= 0) {
            setDead(true);
            setKillerId(caster.getId());
        }
        addThreat(caster, 1);
    }

    private List<BuffConfig> getPassiveBuff(SkillBattleConfig skillConfig) {
        if (skillConfig.getReleaseType() != 1) {
            return Collections.emptyList();
        }
        List<BuffConfig> buffs = new ArrayList<>();
        for (Integer effectId : skillConfig.getSkillEffect()) {
            SkillEffectConfig effectConfig = ConfigDataManager.getInstance().getById(SkillEffectConfig.class, effectId);
            if (effectConfig.getType() != SkillEffectFactory.SKILL_EFFECT.ADD_BUFF.getType()) {
                continue;
            }
            List<int[]> parameter = effectConfig.getParameter();
            if (CollectionUtils.isEmpty(parameter)) {
                continue;
            }
            for (int buffId : parameter.get(0)) {
                BuffConfig buffConfig = ConfigDataManager.getInstance().getById(BuffConfig.class, buffId);
                buffs.add(buffConfig);
            }
        }
        return buffs;
    }

    public void addSkill(List<Skill> skills) {
        for (Skill skill : skills) {
            SkillBattleConfig skillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skill.getSkillId());
            // 如果新增被动技能，添加相应的被动 buff 到 buffTriggerMap 中用于后续触发
            List<BuffConfig> passiveBuff = getPassiveBuff(skillConfig);
            for (BuffConfig buffConfig : passiveBuff) {
                this.getBuffTriggerMap()
                        .computeIfAbsent(buffConfig.getBufferType(), k -> new HashSet<>())
                        .add(buffConfig.getId());
            }
            this.getSkillMap().put(skill.getSkillId(), skill);
        }
        if (!skills.isEmpty()) {
            // 如果当前地图是战斗地图，则触发一下入场被动
            MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, this.getMapCfgId());
            if (mapConfig != null && mapConfig.getHome() != 1) {
                BuffTriggerUtil.triggerBuff(this, this, BuffConst.TriggerType.PASSIVE_SKILL, null, TimeUtil.getNowOfMills());
            }
        }
    }

    public void removeSkill(List<Skill> skills) {
        for (Skill skill : skills) {
            SkillBattleConfig skillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skill.getSkillId());
            // 如果移除被动技能，移除技能的同时移除掉对应的buff
            List<BuffConfig> passiveBuff = getPassiveBuff(skillConfig);
            for (BuffConfig buffConfig : passiveBuff) {
                this.getBuffTriggerMap()
                        .computeIfAbsent(buffConfig.getBufferType(), k -> new HashSet<>())
                        .remove(buffConfig.getId());
                BuffManager.getInstance().remove(this, buffConfig.getId());
            }
            this.getSkillMap().remove(skill.getSkillId());
        }
    }

    public void updateSkill(List<SkillDTO> newSkillList) {
        List<SkillDTO> oldSkillList = this.getSkillMap().values().stream().map(skill -> {
            SkillDTO dto = new SkillDTO();
            dto.setSkillId(skill.getSkillId());
            dto.setLevel(skill.getLevel());
            return dto;
        }).collect(Collectors.toList());
        // 新增的技能
        Set<SkillDTO> addedSkill = new HashSet<>(newSkillList);
        for (SkillDTO oldSkill : oldSkillList) {
            addedSkill.removeIf(that -> that.getSkillId() == oldSkill.getSkillId() && that.getLevel() == oldSkill.getLevel());
        }
        // 移除的技能
        Set<SkillDTO> removedSkill = new HashSet<>(oldSkillList);
        for (SkillDTO newSkill : newSkillList) {
            removedSkill.removeIf(that -> that.getSkillId() == newSkill.getSkillId() && that.getLevel() == newSkill.getLevel());
        }
        this.addSkill(addedSkill.stream().map(SkillDTO::toSkill).collect(Collectors.toList()));
        this.removeSkill(removedSkill.stream().map(SkillDTO::toSkill).collect(Collectors.toList()));
    }
}
