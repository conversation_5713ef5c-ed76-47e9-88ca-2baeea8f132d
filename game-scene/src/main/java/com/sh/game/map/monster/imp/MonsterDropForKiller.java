package com.sh.game.map.monster.imp;

import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;

import java.util.ArrayList;
import java.util.List;

public class MonsterDropForKiller extends MonsterDropForInjurers {

    @Override
    protected List<PlayerActor> getReceivers(GameMap map, MonsterActor monster) {
        List<PlayerActor> receivers = new ArrayList<>();
        PlayerActor killer = map.getPlayerMap().get(monster.getKillerId());
        if (killer != null) {
            receivers.add(killer);
        }

        return receivers;
    }
}
