package com.sh.game.map.fight.skill;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.communication.msg.map.fight.ResFightMessage;
import com.sh.game.common.config.cache.ElementEffectConfigCache;
import com.sh.game.common.config.model.SkillBattleConfig;
import com.sh.game.common.config.model.SkillEffectConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.CdConst;
import com.sh.game.common.gemo.Vec2;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.fight.FightManager;
import com.sh.game.map.fight.effect.AbstractEffect;
import com.sh.game.map.fight.effect.SkillEffectFactory;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.obj.IMapObject;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;


@Slf4j
@Getter
@Setter
public abstract class AbsSkillReleaser {
    protected GameMap map;
    protected Performer caster;
    protected SkillBattleConfig config;
    protected Performer focusTarget;
    protected Vec2 pos;
    protected int dir;

    /**
     * 技能周期内触发的元素伤害列表
     * key：目标id
     * value：元素属性列表
     */
    private Map<Long, Set<AttributeEnum>> elementEffects = new HashMap<>();

    /**
     * 技能释放过程中全部的影响结果
     */
    protected List<EffectContext> effectContexts = new ArrayList<>();

    public void putElementEffect(Long id, AttributeEnum elementType) {
        elementEffects.putIfAbsent(id, new HashSet<>());
        elementEffects.get(id).add(elementType);
    }


    /**
     * 是否在公共cd中,子弹不统计公共cd
     *
     * @return
     */
    protected boolean skillCommonCdCool() {
        return true;
    }

    public void prepareRelease() {
        skillCost();
        tryCast();
    }

    public boolean canReleaseSkill() {
        if (config == null) {
            return false;
        }
        // 蓝量消耗不足
        if (config.getConsumeMP() > 0 && caster.getMp() < config.getConsumeMP()) {
            return false;
        }
        long cd = CDUtil.getCd(caster, CdConst.CdType.SKILL, config.getId());
        long value = AttributeEnum.ZTXY_COOLDOWN_REDUCTION.getAttrValue(caster.getFinalAttribute());
        if (value > 0) {
            cd = (long) (cd * (10000 - value) / 10000.0);
        }
        return TimeUtil.getNowOfMills() >= cd;
    }


    protected void addSkillCommonCd() {

    }

    /**
     * 技能消耗行为，增加 CD
     */
    public void skillCost() {
        // 增加 CD
        long nowOfMills = TimeUtil.getNowOfMills();
        long endTime = nowOfMills + config.getSkillCd();
        CDUtil.addCd(caster, CdConst.CdType.SKILL, config.getId(), endTime);
        if (!config.isAvoidPublicCd()) {
            addSkillCommonCd();
        }
        sendReleaseSkillMsg();
    }


    /**
     * 广播释放结果（先广播技能特效后续再计算伤害，最后再广播伤害结果，使客户端表现更加流畅）
     */
    public void sendReleaseSkillMsg() {
        ResFightMessage msg = new ResFightMessage();
        msg.setSkillId(config.getId());
        msg.setSkillLevel(config.getLevel());

        long cdEndTime = CDUtil.getCd(caster, CdConst.CdType.SKILL, config.getId());
        msg.setCdEndTime(cdEndTime);
        long commonCdEndTime = CDUtil.getCd(caster, CdConst.CdType.SKILL_COMMON, CdConst.CdKey.COMMON_KEY);
        msg.setCommonCdEndTime(commonCdEndTime);

        msg.setAttackerId(caster.getId());
        msg.setAttackerDir(caster.getDir());
        msg.setTargetIdList(Collections.singletonList(focusTarget.getId()));
        msg.setX((int) pos.x);
        msg.setY((int) pos.y);
        // 获取所有观察者对象
        Map<Long, IMapObject> watchers = map.getAoi().getWatchers((int) pos.x, (int) pos.y);
        Set<Long> sendIds = new HashSet<>(watchers.keySet());
        Module.MSG_TRANSFORMER.sendMsgToRids(msg, sendIds);
    }

    protected abstract Performer findEffectPerformer();

    protected abstract Function<IMapObject, Boolean> findTargetFilter();

    public abstract void tryCast();

    public void cast() {
        // 一个技能有多个目标，有多种效果，各种效果间的作用相互独立（不同效果目标也可能不同）
        // 效果触发顺序由配置顺序决定
        Performer effectPerformer = findEffectPerformer();
        for (int cid : config.getSkillEffect()) {
            SkillEffectConfig effectConfig = ConfigDataManager.getInstance().getById(SkillEffectConfig.class, cid);
            AbstractEffect skillEffect = SkillEffectFactory.getSkillEffect(effectConfig.getType());
            if (skillEffect == null) {
                continue;
            }
            List<Performer> targetList = skillEffect.selectTarget(map, caster, effectConfig, focusTarget.getId(), findTargetFilter());
            if (targetList.isEmpty()) {
                log.debug("技能-释放器#玩家{},effect没有目标:{},{}", caster.simpleName(), effectConfig.getId(), effectConfig.getName());
            }
            for (Performer target : targetList) {
                doEffect(target, skillEffect, effectPerformer, effectConfig);
            }
        }
        // 扣除技能消耗mp
        if (config.getConsumeMP() > 0) {
            caster.setMp(caster.getMp() - config.getConsumeMP());
        }
        // 结算元素印记爆发
        settlementElementMarking();
        castAfter();
        // 发送技能释放成功消息
        FightManager.getInstance().sendSkillEffectMsg(caster, config, map, effectContexts);
        // 清空本次作用信息
        clearMsg();
    }

    protected void doEffect(Performer target, AbstractEffect skillEffect,
                                     Performer effectPerformer, SkillEffectConfig effectConfig) {
        EffectContext context = new EffectContext(caster, target, effectConfig);
        skillEffect.apply(map, context);
        context.hp = target.getHp();
        effectContexts.add(context);
    }


    protected void clearMsg() {
        elementEffects.clear();
        effectContexts.clear();
    }


    protected void castAfter() {

    }

    /**
     * 结算元素印记爆发
     */
    protected void settlementElementMarking() {
        Map<Long, Set<AttributeEnum>> copyMap = new HashMap<>(elementEffects);
        ElementEffectConfigCache cache = ConfigCacheManager.getInstance().getCache(ElementEffectConfigCache.class);
        for (Map.Entry<Long, Set<AttributeEnum>> entry : copyMap.entrySet()) {
            long eleTargetId = entry.getKey();
            Performer eleTarget = (Performer) map.getObject(eleTargetId);
            for (AttributeEnum attributeEnum : entry.getValue()) {
                int level = eleTarget.getElementLayers().get(attributeEnum).first;
                List<SkillEffectConfig> elementSkillEffects = cache.findElementEffectConfigs(attributeEnum, level);
                for (SkillEffectConfig effectConfig : elementSkillEffects) {
                    AbstractEffect elementEffect = SkillEffectFactory.getSkillEffect(effectConfig.getType());
                    List<Performer> targets = elementEffect.selectTarget(map, findEffectPerformer(), effectConfig, eleTargetId, null);
                    for (Performer target : targets) {
                        doEffect(target, elementEffect, findEffectPerformer(), effectConfig);
                    }
                }
            }
        }
    }
}
