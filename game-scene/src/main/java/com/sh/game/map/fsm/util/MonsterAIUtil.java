package com.sh.game.map.fsm.util;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.config.entity.ModelOffset;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.config.model.SkillConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.CdConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.common.entity.skill.MonsterSkill;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.gemo.shap.Circle;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.SceneManager;
import com.sh.game.map.constant.Dir;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.area.AreaDetectUtil;
import com.sh.game.map.fsm.ai.AIData;
import com.sh.game.map.listener.MonsterOwnerChangeEvent;
import com.sh.game.map.move.MoveManager;
import com.sh.game.map.move.NewMoveManager;
import com.sh.game.map.obj.*;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;
import com.sh.game.map.scene.point.PointType;
import com.sh.game.map.util.GeomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


@Slf4j
public class MonsterAIUtil {

    /**
     * 查找指定范围内的可攻击对象
     */
    public static Performer findNearestFightAblePerformer(GameMap map, MonsterActor monster, ModelOffset range) {
        if (map == null || monster == null || monster.getPoint() == null) {
            return null;
        }
        return AreaDetectUtil.findNearestFightAblePerformer(map, monster, range);
    }

    /**
     * 追击玩家
     *
     * @param map
     * @param monster
     * @param target
     */
    public static void chaseWalk(GameMap map, MonsterActor monster, Performer target) {
        if (target == null) {
            return;
        }
        // TODO 寻路待完善 A*
        Vec3 sPosition = monster.getPosition();
        Vec3 tPosition = Vec3.of(target.getPosition().x, target.getPosition().y);
        // 目标坐标增加一点随机，防止重叠
        tPosition.x += ThreadLocalRandom.current().nextInt(10, 100);
        tPosition.y += ThreadLocalRandom.current().nextInt(10, 100);
        // 追击时不紧贴目标
        double hitDistance = monster.disToHitBox(target);
        int minAttackDistance = monster.getMinAttackRange();
        if (hitDistance < minAttackDistance) {
            return;
        }
        // 如果移动距离大于单次移动距离需要调整一下终点坐标，保证每次怪物移动都按照最小的单次距离进行移动
        float stepDistance = (float) monster.getMoveSpeed() / TimeUtil.ONE_MILLS * monster.getMoveInterval();
        if (GeomUtil.distance(tPosition, sPosition) > stepDistance) {
            TwoTuple<Float, Float> dirVector = new TwoTuple<>(tPosition.x - sPosition.x, tPosition.y - sPosition.y);
            GeomUtil.vectorScalar2Length(dirVector, stepDistance);
            tPosition.x = sPosition.x + dirVector.getFirst();
            tPosition.y = sPosition.y + dirVector.getSecond();
        }
        move(map, monster, tPosition);
    }

    /**
     * 移动到到目标坐标，同时更新所需移动的时间（状态机等待）
     */
    public static void move(GameMap map, MonsterActor monster, Vec3 pos) {
        Vec3 sPosition = monster.getPosition();
        //  不可直达目标 (尝试按八方向移动)
        if (!map.isWalkable(pos)) {
            int direction = GeomUtil.getDirection(sPosition, pos);
            // 周围方向
            int[] surroundingDirections = GeomUtil.getSurroundingDirections(direction);
            for (int surroundingDirection : surroundingDirections) {
                Dir d = Dir.getByIndex(surroundingDirection);
                if (d == null) {
                    continue;
                }
                byte[] vector = GeomUtil.getVector(d.getIndex());
                TwoTuple<Float, Float> dirVector = new TwoTuple<>((float) vector[0], (float) vector[1]);
                float stepDistance = (float) monster.getMoveSpeed() / TimeUtil.ONE_MILLS * monster.getMoveInterval();
                GeomUtil.vectorScalar2Length(dirVector, stepDistance);
                pos.x = monster.getPosition().x + dirVector.getFirst();
                pos.y = monster.getPosition().y + dirVector.getSecond();
            }
        }
        if (!map.isWalkable(pos)) {
            return;
        }
        long speed = monster.getMoveSpeed();
//        log.info("怪物 {}  移速 {} 移动距离 {} {}-->{} ", monster.simpleName(), monster.getMoveSpeed(),GeomUtil.distance(monster.getPosition(), pos),monster.getPosition(),pos);
        double time = GeomUtil.distance(pos, monster.getPosition()) / speed * 1000.0;
        monster.getMachine().getAiData().incClockSleep(Math.max(100, (int) time));
        NewMoveManager.getInstance().performerMove(map, monster, pos, true);
    }

    /**
     * 走直线追击，遇到障碍物停止
     * @param map 怪物所在的地图对象
     * @param monster 需要进行移动的怪物
     * @param targetPos 需要追击的目标
     * @return 返回此次移动需要多少移动周期
     */
    public static float straightWalk(GameMap map, MonsterActor monster, Vec3 targetPos){
        if (targetPos == null) {
            return 0;
        }
        Point p1 = monster.getPoint();
        double distance = GeomUtil.distance(p1.toVec3(), targetPos);
        float stepDistance = (float) monster.getMoveSpeed() / TimeUtil.ONE_MILLS * monster.getMoveInterval();
        // 需要贴近的距离
        float moveDistance = (float) Math.min(stepDistance, distance - monster.getMinAttackRange());
        // 如果目标坐标不可到达放弃追击
        if (!map.isWalkable(targetPos.x, targetPos.y)) {
            return 0;
        }
        NewMoveManager.getInstance().performerMove(map, monster, targetPos, true);
        return moveDistance / stepDistance;
    }

    /**
     * 找技能
     */
    public static MonsterSkill findMonsterSkill(GameMap map, Performer monster, Performer target) {
        // 和目标的距离
        int tDis = GeomUtil.distance(monster.getPoint(), target.getPoint());
        // 当前血量百分比
        int hpPercent = (int) (monster.getHp() / (float) monster.getFinalAttribute().findMaxHp() * 100);

        long curTime = TimeUtil.getNowOfMills();
        List<MonsterSkill> skills = new ArrayList<>();
        for (Skill skill : monster.getSkillMap().values()) {
            MonsterSkill monsterSkill = (MonsterSkill) skill;
            if (hpPercent <= monsterSkill.getHpPercentMax()
                    && hpPercent >= monsterSkill.getHpPercentMin()
                    && tDis <= monsterSkill.getAttackDistance()
                    && CDUtil.isCool(monster, CdConst.CdType.SKILL, monsterSkill.getSkillId(), curTime)) {
                SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skill.getSkillId());
                if (config.getCommonCd() == CdConst.CdKey.COMMON_KEY
                        && CDUtil.getCd(monster, CdConst.CdType.SKILL_COMMON, 1) > curTime) {
                    continue;
                }
                skills.add(monsterSkill);
            }
        }
        return skills.stream().min(Comparator.comparingInt(MonsterSkill::getPriority)).orElse(null);
    }

    public static void monsterCastSkill(MonsterActor monster, Performer target, int skillId) {
//        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        int dir = GeomUtil.getDirection(monster.getPosition(), target.getPosition());

        int x = target.getPoint().getX();
        int y = target.getPoint().getY();
        long targetId = target.getId();
        // 由下层的技能逻辑去决定释放坐标和对象
//        if (skillConfig.getReleaseType() == ReleaseType.SELF_POINT) {
//            x = monster.getPoint().getX();
//            y = monster.getPoint().getY();
//            targetId = monster.getId();
//        }
//        FightManager.getInstance().castSkill(monster, skillId, dir, x, y, targetId);
    }


    /**
     * 寻找追踪的目标
     */
    public static List<Performer> findChaseTargets(GameMap map, MonsterActor monster) {
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, monster.getConfigId());
        if (config.getHatredRange() == null || config.getHatredRange().length != 2) {
            return Collections.emptyList();
        }
        // 在警戒范围内查找追击目标
        int alertRange = config.getHatredRange()[0];
        Circle alertArea = new Circle(monster.getPosition().x, monster.getPosition().y, alertRange);
        return map.filterObject(alertArea, true, performer -> monster.isEnemy(performer, true));
    }

    /**
     * 回到怪的出生区域
     */
    public static void homeWalk(GameMap map, MonsterActor monster) {
        if (monster.getBirthPoint() == null) {
            return;
        }
        Vec3 birthPoint = monster.getBirthPoint().toVec3();
        if (GeomUtil.distance(monster.getPosition(), birthPoint) == 0) {
            return;
        }
        move(map, monster, birthPoint);
    }

    /**
     * 随机走动
     */
    public static void randomWalk(GameMap map, MonsterActor monster) {
        Point birthPoint = monster.getBirthPoint();
        Point mPoint = monster.getPoint();
        int dirIndex = ThreadLocalRandom.current().nextInt(8);
        float distance = (float) monster.getMoveSpeed() / TimeUtil.ONE_MILLS * monster.getMoveInterval();
        if (distance <= 0) {
            return;
        }
        Point nextPoint = GeomUtil.nextDirPoint(map, mPoint, dirIndex, distance);
        if (nextPoint != null) {
            NewMoveManager.getInstance().performerMove(map, monster, new Vec3(nextPoint.x, nextPoint.y, 0), true);
        }

    }


    /**
     * 寻找最高仇恨的目标
     *
     * @param map
     * @param monster
     * @return
     */
    public static Performer findHighestThreatPerformer(GameMap map, MonsterActor monster) {
        long maxValue = -1;
        Performer ret = null;
        Map<Long, Long> threatMap = monster.getThreatMap();

        List<Long> removeList = new ArrayList<>();

        for (long key : threatMap.keySet()) {

            long value = threatMap.get(key);
            Performer performer = (Performer) map.getObject(key);
            if (performer == null) {
                continue;
            }

            if (MonsterAIUtil.isInvisible(monster, performer)) {
                removeList.add(key);
                continue;
            }

            if (performer.isDead()) {
                // 移除仇恨
                removeList.add(key);
                continue;
            }
//            PointState pointState = map.getPointState(performer.getPoint());
//            if (performer.getPoint() != null && (pointState != null && pointState.isSafe())
//                    && monster.getAiType() != AIType.GUARD_ARCHER && monster.getAiType() != AIType.GUARD_SWORD) {
//                // 移除仇恨
//                removeList.add(key);
//                continue;
//            }

            if (value > maxValue) {
                ret = performer;
                maxValue = value;
            }
        }
        for (Long removeId : removeList) {
            threatMap.remove(removeId);
        }
        return ret;
    }


    /**
     * 更新怪物仇恨（随着时间推移更新）
     *
     * @param monster
     */
    public static void updateThreat(GameMap map, MonsterActor monster) {
        threatTransfer(map, monster);

        //检查仇恨列表中已经死亡的id，以及超过追击距离的id
        Map<Long, Long> threatMap = monster.getThreatMap();
        Iterator<Long> it = threatMap.keySet().iterator();
        while (it.hasNext()) {
            long key = it.next();
            Performer performer = (Performer) map.getObject(key);
            if (performer == null) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 100);
                continue;
            }
            if (!monster.isEnemy(performer, true)) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 100);
                continue;
            }
            if (MonsterAIUtil.isInvisible(monster, performer)) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 100);
                continue;
            }
            if (performer.isDead()) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 200);
                continue;
            }
            Point performerPoint = performer.getPoint();
            if (performerPoint == null) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 300);
            } else {
                int dis = GeomUtil.distance(performerPoint, monster.getPoint());
                if (dis > monster.getChaseDis()) {
                    it.remove();
                    clearOwnerAfterThreadRemove(map, monster, key, 400);
                    continue;
                }
            }
            int clearThreadTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.MONSTER_CLEAR_THREAD_TIME);
            Integer attackMeTime = monster.getAttackMeTimeMap().getOrDefault(performer.getId(), 0);
            if (attackMeTime + clearThreadTime < TimeUtil.getNowOfSeconds()) {
                it.remove();
                clearOwnerAfterThreadRemove(map, monster, key, 400);
            }
        }

        //检查归属是否在仇恨列表中,不在就要移除
        if (monster.getOwner() > 0) {
            if (!threatMap.containsKey(monster.getOwner())) {
                IMapObject obj = map.getObject(monster.getOwner());
                if (obj != null) {
                    clearOwnerAfterThreadRemove(map, monster, monster.getOwner(), 500);
                }
            }
        }

        if (monster.getFightTarget() > 0) {
            if (!threatMap.containsKey(monster.getFightTarget())) {
                monster.setFightTarget(0);
            }
        }
    }

    private static void threatTransfer(GameMap map, MonsterActor monster) {
        Map<Long, Long> transferMap = new HashMap<>();

        Map<Long, Long> threatMap = monster.getThreatMap();
        Iterator<Long> it = threatMap.keySet().iterator();
        while (it.hasNext()) {
            long key = it.next();
            IMapObject iMapObject = map.getObject(key);
            if (iMapObject == null || !iMapObject.isPerformer()) {
                continue;
            }
            Performer performer = (Performer) iMapObject;
            //替身术
            if (performer.getThreatTransferId() > 0) {
                Performer transfer = (Performer) map.getObject(performer.getThreatTransferId());
                if (transfer != null && !transfer.isDead()) {
                    transferMap.put(transfer.getId(), threatMap.get(key));
                    if (monster.getWhoMyTarget() == performer.getId()) {
                        monster.setWhoMyTarget(transfer.getId());
                    }
                    if (monster.getWhoAttackMe() == performer.getId()) {
                        monster.setWhoAttackMe(transfer.getId());
                    }
                    it.remove();
                }
            }
        }

        if (!transferMap.isEmpty()) {
            threatMap.putAll(transferMap);
        }
    }

    public static void clearOwnerAfterThreadRemove(GameMap map, MonsterActor monster, long key, int index) {
        if (monster.getWhoMyTarget() == key) {
            monster.setWhoMyTarget(0);
        }
        if (monster.getWhoAttackMe() == key) {
            monster.setWhoAttackMe(0);
        }
        if (monster.getOwner() <= 0 || key != monster.getOwner()) {
            return;
        }
        long oldOwner = monster.getOwner();

        IMapObject object = map.getObject(oldOwner);
        if (object == null) {
            monster.ownerChange(null);
            EventUtil.fireEvent(MapEventType.AFTER_MONSTER_OWNER_CHANGE, new MonsterOwnerChangeEvent(monster, oldOwner, index + 4));
            map.onMonsterOwnerChange(monster, oldOwner, 0);
            return;
        }

        if (object.getType() != MapObjectType.PLAYER) {
            return;
        }

//        PlayerActor ownerPlayer = (PlayerActor) object;
//        HeroActor heroActor = ownerPlayer.getHeroActor();
//        if (heroActor != null && monster.getThreatMap().containsKey(heroActor.getId())) {
//            return;
//        }

        monster.ownerChange(null);
        EventUtil.fireEvent(MapEventType.AFTER_MONSTER_OWNER_CHANGE, new MonsterOwnerChangeEvent(monster, oldOwner, index + 5));
        map.onMonsterOwnerChange(monster, oldOwner, 0);
    }

    /**
     * 回血
     */
    public static void recoverHp(MonsterActor monster, int dt) {
        if (monster.isDead()) {
            return;
        }

        long oldHp = monster.getHp();
        long maxHp = monster.getFinalAttribute().findMaxHp();
        if (oldHp >= maxHp) {
            return;
        }

        long fixAdd = 0;
        long rateAdd = 0;

        AIData aiData = monster.getMachine().getAiData();

        if (aiData.getClockRecoverHp() <= 0) {
            aiData.setClockRecoverHp(RoleConst.HP_RECOVER_TIME_INTERVAL * 1000);
            fixAdd = AttributeEnum.ZTXY_HP_RECOVERY.getAttrValue(monster.getFinalAttribute());
        }

        if (aiData.getClockRecoverHpRateAdd() <= 0) {
            aiData.setClockRecoverHpRateAdd(RoleConst.HP_RECOVER_RATE_ADD_TIME_INTERVAL * 1000);
            long replyRateAddAttrValue = AttributeEnum.ZTXY_REPLY_RATE_ADD.getAttrValue(monster.getFinalAttribute());
            rateAdd = maxHp * replyRateAddAttrValue / 10000;
        }


        long reply = fixAdd + rateAdd;

        long percent = AttributeEnum.REPLY_REDUCE.getAttrValue(monster.getFinalAttribute());
        reply = (long) (reply * (1 - percent / 10000.0));
        if (reply <= 0) {
            return;
        }

        long newHp = Math.min(maxHp, oldHp + reply);
        if (newHp == oldHp) {
            return;
        }

        monster.setHp(newHp);

        /**
         * 怪物已经定时同步，不需要每次改变就发消息
         * @see  GameMap syncMapObjectHpMp
         */
//        monster.sendHpMpChangeMessage();
    }


    /**
     * 脱战后回复内力值
     */
    public static void shieldRecover(MonsterActor monster, int dt) {
        if (monster.isDead() || monster.getWhoMyTarget() > 0) {
            return;
        }
        AIData aiData = monster.getMachine().getAiData();
        if (aiData.getClockRecoverShield() > 0) {
            return;
        }
        int cd = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHIELD_REPLY_CD) * 1000;
        if (cd <= 0) {
            return;
        }
        aiData.setClockRecoverShield(cd);
        long attrValue = AttributeEnum.SHIELD_REPLY.getAttrValue(monster.getFinalAttribute());
        if (attrValue <= 0) {
            return;
        }
        long oldShield = monster.getShield();
        long maxShield = monster.getFinalAttribute().findMaxShield();
        if (oldShield >= maxShield) {
            return;
        }
        long reply = AttributeEnum.SHIELD_REPLY.getAttrValue(monster.getFinalAttribute()) * monster.getFinalAttribute().findMaxShield() / 10000;
        if (reply <= 0) {
            return;
        }
        long newShield = Math.min(maxShield, oldShield + reply);
        if (oldShield != newShield) {
            monster.setShield(newShield);
            monster.sendShieldChangeMessage();
        }
    }

    public static void updateOwnerTime(MonsterActor monster, int dt) {
        if (monster.isDead()) {
            return;
        }

        long curTime = monster.getOwnerTime();
        if (curTime > 0) {
            monster.setOwnerTime(Math.max(0, curTime - dt));
            if (monster.getOwnerTime() <= 0) {
                monster.ownerChange(null);
            }
        }
    }


    /**
     * 跟着走
     *
     * @param map
     * @param monster
     * @param target
     * @param step
     * @param action
     * @return
     */
    public static final boolean followWalk(GameMap map, Performer monster, Point target, int step, int action) {
        return followWalk(map, monster, target, step, action, 1);
    }

    public static final boolean followWalk(GameMap map, Performer monster, Point target, int step, int action,
                                           int way) {
        if (monster == null) {
            return false;
        }
        // 寻找下一个移动位置
        Point point = WalkUtil.getPathNext(map, monster, target, step, action);

        if (point != null && point.canStand(map, monster)) {
            MoveManager.getInstance().performerMove(map, monster, point, way);
            return true;
        } else {
            return false;
        }
    }

    public static Performer findPlayerInRange(GameMap map, MonsterActor monster, int range, int... types) {
        if (types.length == 0) {
            return null;
        }

        return (Performer) map.filterObject(monster.getPosition(), range,
                iMapObject -> {
                    if (iMapObject.isDead()) {
                        return false;
                    }
                    if (!ArrayUtils.contains(types, iMapObject.getType())) {
                        return false;
                    }
                    if (isInvisible(monster, (Performer) iMapObject)) {
                        return false;
                    }
                    return true;
                }).stream().findFirst().orElse(null);
    }

    /**
     * 躲避玩家
     */
    public static Performer escapeWalk(GameMap map, MonsterActor monster, int range) {
        Point curPoint = monster.getPoint();

        Performer performer = (Performer) map.filterObject(monster.getPosition(), range, iMapObject -> !iMapObject.isDead() && (iMapObject.getType() == MapObjectType.PLAYER || iMapObject.getType() == MapObjectType.HERO) && !isInvisible(monster, (Performer) iMapObject)).stream().findFirst().orElse(null);

        if (performer == null) {
            return null;
        }
        WalkUtil.moveByDir(map, monster, GeomUtil.getDirection(performer.getPoint(), curPoint));
        return performer;
    }

    /**
     * 躲避目标
     *
     * @param map
     * @param monster
     * @param target
     */
    public static void escapeWalk(GameMap map, MonsterActor monster, Performer target) {
        if (monster == null || target == null) {
            return;
        }
        WalkUtil.moveByDir(map, monster, GeomUtil.getDirection(target.getPoint(), monster.getPoint()));
    }

    public static void clearTargetTime(MonsterActor monster, int dt) {
        AIData aiData = monster.getMachine().getAiData();
        if (aiData.getClockClearAttackAndTarget() > 0) {
            return;
        }
        aiData.setClockClearAttackAndTarget(10000);

        monster.setWhoAttackMe(0);
        monster.setWhoMyTarget(0);
    }

    public static int calSleepTime(MonsterActor monster) {
        return (int) (monster.getSleepTime() * (10000d / (10000 + AttributeEnum.MON_SLEEP_TIME.getAttrValue(monster.getFinalAttribute()))));
    }

    public static boolean isInvisible(Performer checker, Performer beChecker) {
        if (!beChecker.getBuffState().inStateOf(BuffState.State.INVISIBLE)) {
            return false;
        }
        if (beChecker instanceof PlayerActor) {
            PlayerActor beCheckerPlayer = (PlayerActor) beChecker;
            if (beCheckerPlayer.getAvatarBackState().isInvisible()) {
                return false;
            }
        }
        if (checker.getWhoAttackMe() == beChecker.getId()) {
            return false;
        }
        if (checker.getType() == MapObjectType.MONSTER) {
            MonsterActor monsterActor = (MonsterActor) checker;
            if (monsterActor.getOwner() == beChecker.getId()) {
                return false;
            }
            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterActor.getConfigId());
            if (monsterConfig.getIgnorevanish() == 1) {
                return false;
            }
        }
        return GeomUtil.distance(checker.getPoint(), beChecker.getPoint()) > beChecker.getBuffState().getInvisibleRange();
    }

    public static void changeEscortMap(EscortActor actor, int mapId, int x, int y) {
        GameMap newMap = SceneManager.getInstance().getMap(mapId);
        if (newMap == null) {
            return;
        }

        GameMap oldMap = SceneManager.getInstance().getMap(actor);
        if (oldMap == null) {
            return;
        }

        //oldMap.removeMonster(actor, true);
        actor.setRemoved(true);
        oldMap.removeView(actor);


        Point point = newMap.getPoint(x, y);
        if (point == null || !point.canStand(newMap, actor)) {
            point = newMap.getRoundRandomStandAblePoint(actor, x, y);
        }


        Point birth = point;

        newMap.addCommand(new AbstractCommand() {
            @Override
            public void doAction() {
                newMap.enterMonster(actor, birth, true);
            }
        });
    }
}
