package com.sh.game.map.duplicate.instance;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.notice.HangHuiBaoDiEndNotice;
import com.sh.game.common.communication.notice.HangHuiBaoDiMonsterStateNotice;
import com.sh.game.common.communication.notice.HangHuiBaoDiNpcAppearNotice;
import com.sh.game.common.communication.notice.HangHuiBaoDiPosChangeNotice;
import com.sh.game.common.config.model.HangHuiBaoDiConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.config.model.MapNpcConfig;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.util.IDUtil;
import com.sh.game.map.Module;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.mail.SceneMailManager;
import com.sh.game.map.obj.*;
import com.sh.game.map.scene.point.Point;
import com.sh.game.map.scene.topography.Topography;
import com.sh.game.map.teleport.TeleportManager;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/22 16:51
 */
@Slf4j
public class HangHuiBaoDiDuplicate extends Duplicate {

    private long unionId;

    // private long teleportTime;

    private int x;

    private int y;

    private int range;

    private boolean end;

    private boolean canTeleport = false;

    private int teleportDelay = 1000;

    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        super.init(key, host, config, topography, param);
        if (param instanceof Long) {
            unionId = ((Long) param);
        }
    }


    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        int monsterConfigId = monster.getConfigId();
        HangHuiBaoDiConfig config = ConfigDataManager.getInstance().getById(HangHuiBaoDiConfig.class, monsterConfigId);
        if (config == null) {
            return;
        }

        log.info("行会宝地，副本怪物死亡，当前怪物id {} 行会id {}", monsterConfigId, unionId);

        HangHuiBaoDiMonsterStateNotice stateNotice = new HangHuiBaoDiMonsterStateNotice();
        stateNotice.setUnionId(unionId);
        stateNotice.setMonsterCfgId(monsterConfigId);
        stateNotice.setState(0);
        stateNotice.addHost(Module.NET.getAllClientId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, stateNotice, 0);

        // 发送参与邮件
        Set<Long> playerMap = new HashSet<>();
        for (long id : monster.getHurtMap()) {
            IMapObject mapObject = this.getObjectMap().get(id);
            if (mapObject == null || mapObject.getRid() == 0) {
                continue;
            }
            PlayerActor actor = this.getPlayerMap().get(mapObject.getRid());
            if (actor == null) {
                continue;
            }
            if (!playerMap.add(actor.getId())) {
                continue;
            }
            SceneMailManager.getInstance().sendMail(actor.getId(), config.getMail(), null);
            log.info("行会宝地，副本怪物死亡，发送玩家击杀邮件，当前怪物id {} 邮件id {} 行会id {} 玩家id {} name {}", monsterConfigId, config.getMail(), unionId, actor.getId(), actor.getName());
        }


        int[] param = config.getParam();
        switch (config.getType()) {
            case 1:
                // 刷怪
                int[] nextMonster = config.getNextMonster();
                if (nextMonster.length < 3) {
                    log.error("行会宝地，副本怪物死亡，下一只刷新怪物的配置错误，当前怪物配置id {}", monsterConfigId);
                    return;
                }

                int nextMonsterCfgId = nextMonster[0];
                int mx = nextMonster[1];
                int my = nextMonster[2];
                Point point = getPoint(mx, my);
                MonsterActor next = MapObjectFactory.createMonster(nextMonsterCfgId, true);
                if (point == null) {
                    log.error("行会宝地，副本怪物死亡，下一只刷新怪物的点位找不到或者点位不可用，当前怪物配置id {} 点位信息 {}", monsterConfigId, JSON.toJSONString(point));
                    return;
                }
                next.setBirthPoint(point);
                this.enterMonster(next, point, true);
                log.info("行会宝地，副本怪物死亡，下一只怪物刷新成功，当前怪物id {} 刷新怪物id {} x {} y {} 行会id {}", monsterConfigId, next.getConfigId(), point.getX(), point.getY(), unionId);

                // 准备传送信息，传送逻辑再heart方法中
                if (param.length < 4) {
                    log.error("行会宝地，副本怪物死亡，怪物死亡后传送参数配置错误，配置id {}", monsterConfigId);
                    return;
                }
                x = param[0];
                y = param[1];
                range = param[2];
                canTeleport = true;
                teleportDelay = 1000;

                log.info("行会宝地，副本怪物死亡，准备传送地图内玩家，传送信息 x {} y {} time {} 行会id {}", x, y, range, unionId);
                break;
            case 2:
                // 召唤npc
                if (param.length < 3) {
                    log.error("行会宝地，副本怪物死亡，怪物死亡后添加npc参数配置错误，当前怪物配置id {}", monsterConfigId);
                    return;
                }

                int npcId = param[0];
                MapNpcConfig npcConfig = ConfigDataManager.getInstance().getById(MapNpcConfig.class, npcId);
                if (npcConfig == null) {
                    log.error("行会宝地，副本怪物死亡，怪物死亡后添加npc配置找不到，当前怪物配置id {} npc配置id {}", monsterConfigId, npcId);
                    return;
                }
                NPCActor actor = new NPCActor();
                actor.setId(IDUtil.getId(IDConst.TRANSIENT));
                actor.setConfigId(npcId);
                actor.setName(npcConfig.getName());

                int nx = param[1];
                int ny = param[2];
                Point npcPoint = this.getPoint(nx, ny);
                if (npcPoint == null) {
                    log.error("行会宝地，副本怪物死亡，npc点位找不到或者点位不可用，当前怪物配置id {} 点位信息 {}", monsterConfigId, JSON.toJSONString(npcPoint));
                    return;
                }
                this.enterNpc(actor, npcPoint);
                log.info("行会宝地，副本怪物死亡，npc刷新成功，当前怪物id {} npc配置id {} x {} y {} 行会id {}", monsterConfigId, actor.getConfigId(), npcPoint.getX(), npcPoint.getY(), unionId);

                // 通知逻辑服行会npc已刷新，启动转盘逻辑
                HangHuiBaoDiNpcAppearNotice npcAppearNotice = new HangHuiBaoDiNpcAppearNotice();
                npcAppearNotice.setUnionId(unionId);
                npcAppearNotice.addHost(Module.NET.getAllClientId());
                Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, npcAppearNotice, 0);
                break;
            default:
        }
    }

    @Override
    public boolean close(int delta) {
        if (end) {
            return super.close(delta);
        }
        end = true;
        // 通知逻辑服行会副本结束，关闭进入副本和转盘逻辑
        HangHuiBaoDiEndNotice notice = new HangHuiBaoDiEndNotice();
        notice.setUnionId(unionId);
        notice.addHost(Module.NET.getAllClientId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);
        log.info("行会宝地，副本结束，行会id {}", unionId);
        return super.close(delta);
    }

    @Override
    public void heart(int delta) {
        super.heart(delta);

        teleportDelay -= delta;

        if (teleportDelay > 0) {
            return;
        }

        teleportDelay = 1000;

        if (!canTeleport || !itemMap.isEmpty()) {
            return;
        }

        canTeleport = false;

        // 发送传送信息到逻辑服行会保存，保证后面进入的玩家进入接下来的点
        HangHuiBaoDiPosChangeNotice notice = new HangHuiBaoDiPosChangeNotice();
        notice.setUnionId(unionId);
        notice.setX(x);
        notice.setY(y);
        notice.setRange(range);
        notice.addHost(Module.NET.getAllClientId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);

        for (PlayerActor actor : playerMap.values()) {
            TeleportManager.getInstance().teleport(actor, 0, this.cfgId, x, y, range);
            log.info("行会宝地，传送玩家，行会id {} x {} y {} range {} 玩家id {} name {}", unionId, x, y, range, actor.getId(), actor.getName());
        }

    }

    @Override
    public void onMonsterEnter(MonsterActor monsterActor) {
        super.onMonsterEnter(monsterActor);
        HangHuiBaoDiConfig config = ConfigDataManager.getInstance().getById(HangHuiBaoDiConfig.class, monsterActor.getConfigId());
        if (config == null) {
            return;
        }
        HangHuiBaoDiMonsterStateNotice notice = new HangHuiBaoDiMonsterStateNotice();
        notice.setUnionId(unionId);
        notice.setMonsterCfgId(monsterActor.getConfigId());
        notice.setState(1);
        notice.addHost(Module.NET.getAllClientId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0);
    }
}
