package com.sh.game.map.buff.type;

import com.sh.game.common.communication.msg.map.ResNoticeViewTypeInfoMessage;
import com.sh.game.common.communication.msg.map.bean.NoticeBean;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.GroundBuff;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;
import com.sh.game.map.scene.point.PointState;

import java.util.HashMap;
import java.util.Map;

public class Paul<PERSON>lock extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        if (performer.getType() == MapObjectType.BUFF) {
            GroundBuff gb = (GroundBuff) performer;
            gb.getBuffState().increase(BuffState.State.BLOCK);
            GameMap map = SceneManager.getInstance().getMap(gb);
            Map<Point, PointState> pointStateMap = map.getPointStateMap();
            if (pointStateMap == null) {
                pointStateMap = new HashMap<>();
                map.setPointStateMap(pointStateMap);
            }
            PointState pointState = pointStateMap.computeIfAbsent(gb.getPoint(), k -> new PointState());
            pointState.setBlock(true);

            ResNoticeViewTypeInfoMessage message = new ResNoticeViewTypeInfoMessage();
            message.getLidList().add(performer.getId());
            NoticeBean noticeBean = new NoticeBean();
            noticeBean.setUpdateType(MapConst.NOTICE_TYPE.BLOCK);
            noticeBean.setValue(1);
            message.getUpdateList().add(noticeBean);
            Module.MSG_TRANSFORMER.sendRoundMessage(message, gb);
        }
    }

    @Override
    public void onRemove(Performer performer, Buff buff, int stack) {
        if (performer.getType() == MapObjectType.BUFF) {
            GroundBuff gb = (GroundBuff) performer;
            gb.getBuffState().clear(BuffState.State.BLOCK);
            GameMap map = SceneManager.getInstance().getMap(gb);
            Map<Point, PointState> pointStateMap = map.getPointStateMap();
            if (pointStateMap != null) {
                PointState pointState = pointStateMap.computeIfAbsent(gb.getPoint(), k -> new PointState());
                pointState.setBlock(false);
            }
            ResNoticeViewTypeInfoMessage message = new ResNoticeViewTypeInfoMessage();
            message.getLidList().add(performer.getId());
            NoticeBean noticeBean = new NoticeBean();
            noticeBean.setUpdateType(MapConst.NOTICE_TYPE.BLOCK);
            noticeBean.setValue(0);
            message.getUpdateList().add(noticeBean);
            Module.MSG_TRANSFORMER.sendRoundMessage(message, gb);
        }
    }
}

