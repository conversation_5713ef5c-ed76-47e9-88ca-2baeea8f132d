package com.sh.game.map.obj;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.commons.util.StringUtil;
import com.sh.game.common.communication.msg.system.xialv.ResXiaLvDieTimeMessage;
import com.sh.game.common.communication.notice.logic.player.MapPlayerEnterToSceneNotice;
import com.sh.game.common.config.cache.MonsterEnticeConfigCache;
import com.sh.game.common.config.cache.MonsterSkillCache;
import com.sh.game.common.config.entity.ModelOffset;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.entity.map.HeroDTO;
import com.sh.game.common.entity.map.RoleDTO;
import com.sh.game.common.entity.skill.MonsterSkill;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.gemo.shap.Circle;
import com.sh.game.common.gemo.shap.Rectangle;
import com.sh.game.common.gemo.shap.Shape;
import com.sh.game.common.sync.SyncDataCreator;
import com.sh.game.common.util.IDUtil;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.buff.BuffTriggerUtil;
import com.sh.game.map.constant.Dir;
import com.sh.game.map.duplicate.instance.ztxy.ZtxyBarrierDuplicate;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fsm.FSMMachine;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.bullet.BulletActiveState;
import com.sh.game.map.fsm.bullet.BulletDieState;
import com.sh.game.map.fsm.player.*;
import com.sh.game.map.fsm.ztmonster.states.*;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.servant.ServantActorManager;
import com.sh.game.map.startup.boots.MapContext;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/27 10:37
 */
@Slf4j
public class MapObjectFactory {

    private static final String SERVER_KEY = "区.";

    public static String serverName(int sid, String name) {
        String pattern = "^[sS]\\d+\\.";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(name);
        if (m.find()) {
            return name;
        } else {
            return "S" + sid + "." + name;
        }
    }

    public static Player createPlayer(MapPlayerEnterToSceneNotice notice) {
        //
        Player player = new Player();
        player.setRemoteHostId(notice.getHost());
        //
        PlayerActor actor = SyncDataCreator.create(PlayerActor.class);
        long roleId = notice.getRoleId();
        RoleDTO dto = notice.getRoleDTO();
        player.setId(roleId);
        player.setActor(actor);

        actor.setId(roleId);
        actor.setRid(roleId);
        actor.setCareer(dto.getCareer());
        actor.setSex(dto.getSex());
        actor.setCreateTime(dto.getCreateTime());
        actor.setFightModel(dto.getFightModel());

        actor.setMoveSpeed(1);
        actor.setMoveInterval(500);
        actor.setDir(Dir.LEFT.getIndex());
        actor.updateSkill(new ArrayList<>(dto.getSkillMap().values()));


        //pk值
        actor.setPkValue(dto.getPkValue());
        actor.setPkValueTotalTime(dto.getPkValueTotalTime());
        AttributeEnum.PK_VALUE.setAttribute(actor.getAttributeMap().computeIfAbsent(AttributeConst.AttributeType.Cate.PK, k -> new Attribute()), actor.getPkValue());

        //神龙之魂强化等级
        actor.setDragonWingLevel(dto.getDragonWingStrengthLevel());

        //装备
        actor.getEquipMap().putAll(dto.getEquipMap());

        List<FSMState<PlayerActor>> states = new ArrayList<>();
        states.add(new PlayerActiveState(FSMState.ACTIVE, actor));
        states.add(new PlayerDieState(FSMState.DIE, actor));
        states.add(new PlayerAlertState(FSMState.ALERT, actor));
        states.add(new PlayerCastSkillState(FSMState.CAST, actor));
        FSMMachine<PlayerActor> machine = new FSMMachine<>(actor, states, states.get(0));
        actor.setMachine(machine);
        actor.setMount(dto.getMount());
        actor.setZtPet(dto.getZtPet());

        //=========
        //复制属性
        Attribute attribute = new Attribute();
        attribute.fixAdd(dto.getAttribute());
        actor.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        EventUtil.fireEvent(MapEventType.ON_ATTRIBUTE_COUNT, actor);

        actor.attributeChange();
        BuffTriggerUtil.triggerBuff(actor, actor, BuffConst.TriggerType.INIT, null, TimeUtil.getNowOfMills());

        actor.setHp(dto.getHp());
        actor.setMp((int) dto.getMp());


        //召唤宠物
        actor.callServant(dto.getServantDTOS());


        actor.updateSync(dto.getSyncBeans());
        if (MapContext.getServerType() == MapConst.SERVE_TYPE.CROSS.getCode() || MapContext.getServerType() == MapConst.SERVE_TYPE.BATTLE.getCode()) {
            actor.setName(actor.getSid() + SERVER_KEY + actor.getName());
        }
        player.setName(actor.getName());
        actor.setHostId(notice.getHost());
        player.getActorMap().put(actor.getId(), actor);
        //创建英雄
        HeroDTO heroDTO = dto.getHeroDTO();
        if (heroDTO != null) {
            HeroActor hero = createHero(player, heroDTO);
            hero.setMaster(actor);
            actor.setHeroActor(hero);
        }

        // 放入数据库保存的buff，如果已加载则不覆盖
        Map<Integer, Buff> buffMap = dto.getBuffs().getBuffMap();
        for (Map.Entry<Integer, Buff> entry : buffMap.entrySet()) {
            actor.getBuffs().getBuffMap().putIfAbsent(entry.getKey(), entry.getValue());
        }

        actor.getBuffs().setLogin(true);
        actor.cleanExpireBuff();

        BuffManager.getInstance().initBuff(player.getActor(), actor.getBuffs().getBuffMap());


        //创建宠物
        if (dto.getPetCfgId() > 0) {
            ChongWuActor petActor = createChongWu(player.getActor(), dto.getPetCfgId());
            if (petActor != null) {
                petActor.setMaster(actor);
                actor.setChongWuActor(petActor);
            }
        }
        actor.setFaQiLevel(dto.getFaQiLevel());

        actor.setYuPei(dto.getYuPeiId());

        actor.setAvatarBackState(dto.getAvatarBackState());

        CharacterConfig characterConfig = ConfigDataManager.getInstance().getById(CharacterConfig.class,
                dto.getCareer() + Symbol.JINHAO + dto.getSex());
        // 增加模型范围
        ModelOffset modelOffset = characterConfig.getModelOffset();
        actor.setModelOffset(modelOffset);
        actor.setHitBox(new Rectangle(0,0,modelOffset.getHalfW()*2f,modelOffset.getHalfH()*2));
        actor.setEquipSuits(dto.getEquipSuits());
        PlayerManager.getInstance().addPlayer(player);
        return player;
    }

    public static PlayerCloneActor createPlayerClone(PlayerActor actor, int attrRate, int validPeriod) {
        PlayerCloneActor cloneActor = new PlayerCloneActor();
        actor.setCloneActor(cloneActor);
        cloneActor.setId(IDUtil.getId(IDConst.PERSISTENT));
        cloneActor.setRid(actor.getRid());
        cloneActor.setMaster(actor);
        cloneActor.setCareer(actor.getCareer());
        cloneActor.setMoveSpeed(2);
        cloneActor.setMoveInterval(500);
        cloneActor.setOwnerName(actor.getName());
        cloneActor.setHair(actor.getSex());
        cloneActor.setSex(actor.getSex());
        cloneActor.setName(actor.getName() + "的分身");
        cloneActor.setValidPeriod(validPeriod);
        cloneActor.setLevel(actor.getLevel());
        cloneActor.setAppearanceWears(actor.getAppearanceWears());
        //复制技能
        actor.getSkillMap().forEach((skillId, skill) -> {
            SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
            if (config == null) {
                return;
            }
            if (config.getCls() != SkillConst.SkillCls.FS_BASIC.getCls()) {
                return;
            }
            if (config.getHandlerType() == SkillConst.HandlerType.PLAYER_CLONE) {
                return;
            }
            if (config.getClone() != 1) {
                return;
            }
            cloneActor.getSkillMap().put(skillId, skill);
        });
        Attribute attribute = new Attribute();
        //复制属性万分比
        for (AttributeEnum attributeEnum : AttributeEnum.values()) {
            int value = (int) (actor.getFinalAttribute().get(attributeEnum) * attrRate / 10000.0D);
            attribute.set(attributeEnum, value);
        }
        cloneActor.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        cloneActor.attributeChange();
        //复制装备
        actor.getEquipMap().forEach((index, equipDTO) -> {
            cloneActor.getEquipMap().put(index, equipDTO);
        });
        cloneActor.setCampType(actor.getCampType());
        List<FSMState<PlayerCloneActor>> states = new ArrayList<>();
        FSMMachine<PlayerCloneActor> machine = new FSMMachine<>(cloneActor, states, states.get(0));
        cloneActor.setMachine(machine);
        cloneActor.setShield(actor.getShield());
        return cloneActor;
    }



    public static PlayerFakeActor createPlayerFake(PlayerActor actor) {

        PlayerFakeActor cloneActor = new PlayerFakeActor();
        actor.setCloneActor(cloneActor);
        cloneActor.setId(IDUtil.getId(IDConst.PERSISTENT));
        cloneActor.setRid(actor.getRid());
        cloneActor.setMaster(actor);
        cloneActor.setCareer(actor.getCareer());
        cloneActor.setMoveSpeed(2);
        cloneActor.setMoveInterval(500);
        cloneActor.setOwnerName(actor.getName());
        cloneActor.setHp(actor.getHp());
        cloneActor.setHair(actor.getSex());
        cloneActor.setSex(actor.getSex());
        cloneActor.setName(actor.getName());
        cloneActor.setLevel(actor.getLevel());

         //复制技能
        actor.getSkillMap().forEach((skillId, skill) -> {
            SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
            if (config == null) {
                return;
            }
            if (config.getCls() != SkillConst.SkillCls.FS_BASIC.getCls()) {
                return;
            }
            if (config.getHandlerType() == SkillConst.HandlerType.PLAYER_CLONE) {
                return;
            }
            cloneActor.getSkillMap().put(skillId, skill);
        });

        cloneActor.setAttributeMap(actor.getAttributeMap());
        cloneActor.setFinalAttribute(actor.getFinalAttribute());
        ;
        //复制装备
        actor.getEquipMap().forEach((index, equipDTO) -> {
            cloneActor.getEquipMap().put(index, equipDTO);
        });
        List<FSMState<PlayerCloneActor>> states = new ArrayList<>();
//        FSMMachine<PlayerCloneActor> machine = new FSMMachine<>(cloneActor, states, states.get(0));
//        cloneActor.setMachine(machine);
//        cloneActor.setShield(actor.getShield());
        return cloneActor;
    }


    public static HeroActor createHero(Player player, HeroDTO dto) {
        HeroActor hero = SyncDataCreator.create(HeroActor.class);
        PlayerActor actor = player.getActor();
        hero.setMaster(actor);
        hero.setHostId(player.getRemoteHostId());
        hero.setRid(dto.getOwnerId());
        hero.setId(dto.getId());
        hero.setMoveSpeed(2);
        hero.setMoveInterval(500);
        hero.setCallTime(dto.getCallTime());
        hero.setLoyalty(dto.getLoyalty());
        hero.updateSync(dto.getSyncBeans());
        hero.setOwnerName(actor.getName());
        hero.setHair(hero.getSex());
        hero.setLastRecoverCombSkillValueTime(TimeUtil.getNowOfMills());
        //复制技能列表
        dto.getSkillMap().forEach((k, v) -> {
            SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, k);
            if (skillConfig == null) {
                return;
            }
            //是否学习了合体技
            if (skillConfig.getCls() == SkillConst.SkillCls.COMB_SKILL.getCls()) {
                hero.setCombSkillLearn(true);
            }
            if (skillConfig.getPassive() == 0) {
                hero.getSkillMap().put(k, v.toSkill());
            } else {
                hero.getPassiveSkillMap().put(k, v.toSkill());
            }
        });

        //装备
        hero.getEquipMap().putAll(dto.getEquipMap());
        //复制系统buff和特殊属性
        hero.getBuffs().setBuffMap(dto.getBuffs().getBuffMap());
        hero.getBuffs().setLogin(true);
        //设置魔血石
        hero.setMagicBloodStoneHpSet(actor.getMagicBloodStoneHpSet());
        hero.setMagicBloodStoneMpSet(actor.getMagicBloodStoneMpSet());

        //复制属性
        Attribute attribute = new Attribute();
        attribute.fixAdd(dto.getAttribute());
        hero.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        hero.attributeChange();
        //设置血量
        hero.setHp(dto.getHp());
        hero.setMp((int) dto.getMp());
        hero.cleanExpireBuff();
        //召唤宠物
        hero.callServant(dto.getServantDTOS());

        hero.setFightState(hero.canCall() ? HeroConst.FightState.ATTACK : HeroConst.FightState.SLEEP);
        hero.setAttackState(HeroConst.AttackState.STORM);

        List<FSMState<HeroActor>> states = new ArrayList<>();
        FSMMachine<HeroActor> machine = new FSMMachine<>(hero, states, states.get(0));
        hero.setMachine(machine);
        player.getActorMap().put(hero.getId(), hero);
        return hero;
    }

    public static GroundBuff createGroundBuff() {
        GroundBuff gb = new GroundBuff();
        gb.setId(IDUtil.getId(IDConst.PERSISTENT));
        return gb;
    }

    public static GroundEvent createEvent(int configId) {
        GroundEvent event = new GroundEvent();
        event.setId(IDUtil.getId(IDConst.PERSISTENT));
        event.setConfigId(configId);
        return event;
    }

    public static GroundItem createItem(int itemCfgId, int count, int hungSecond, ThreeTuple<Long, Long, Long> owner, int monsterId, int dropTime) {
        Item item = ItemUtil.create(itemCfgId, count, LogAction.UNSURE);
        if (item == null) {
            return null;
        }

        return createItem(item, hungSecond, owner, monsterId, dropTime);
    }

    public static GroundItem createItem(Item item, int hungSecond, ThreeTuple<Long, Long, Long> owner, int monsterId, int dropTime) {
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getCfgId());
        if (config == null) {
            return null;
        }

        GroundItem groundItem = new GroundItem();
        groundItem.setItem(item);
        groundItem.setId(IDUtil.getId(IDConst.PERSISTENT));
        groundItem.setName(config.getName());

        int now = TimeUtil.getNowOfSeconds();
        groundItem.setDropTime(now);
        if (dropTime > 0) {
            groundItem.setTotalTime(dropTime);
        } else {
            groundItem.setTotalTime(config.getDropTime() * 1000);
        }
        groundItem.setOwnerProtectedExpire(now + hungSecond);
        groundItem.setMonsterId(monsterId);

        if (owner != null) {
            groundItem.setOwner(owner.first);
            groundItem.setOwnerTeam(owner.second);
            groundItem.setOwnerUnion(owner.third);
        }
        return groundItem;
    }

    public static MonsterActor createMonster(MonsterConfig monsterConfig, MapSpawnConfig spawnConfig, boolean removeAfterDie){
        return createMonster(monsterConfig,spawnConfig,removeAfterDie,null);
    }

    public static MonsterActor createMonster(MonsterConfig monsterConfig, MapSpawnConfig spawnConfig, boolean removeAfterDie,Attribute initAttribute) {
        MonsterActor monster = new MonsterActor();

        // 初始化标记
        monster.setInit(true);
        // 分配唯一id
        monster.setId(IDUtil.getId(IDConst.PERSISTENT));
        // 初始化时间
        monster.setBornTime(TimeUtil.getNowOfSeconds());
        // 死亡是否移除
        monster.setRemoveAfterDie(removeAfterDie);

        // 配置id
        monster.setConfigId(monsterConfig.getId());
        // 模型id
        monster.setModel(monsterConfig.getModel());
        // 名字
        monster.setName(monsterConfig.getName());
        // 等级
        monster.setLevel(monsterConfig.getLevel());
        // AI类型
        monster.setAiType(monsterConfig.getAitype());
        // 怪物类型
        monster.setMonsterType(monsterConfig.getType());
        // 是否需要范围广播怪物归属改变
        monster.setBroadcastOwner(monsterConfig.getType() == 3
                || monsterConfig.getType() == 4
                || monsterConfig.getType() == 5
                || monsterConfig.getType() == 15
                || monsterConfig.getType() == 16
        );
        // 怪物心跳间隔
        monster.setHeart(monsterConfig.getHeart());
        // 移动间隔
        monster.setMoveInterval(Math.max(monster.getMoveInterval(), monsterConfig.getMoveInterval()));
        // 放技能后的休眠时间
        monster.setSleepTime(Math.max(monster.getSleepTime(), monsterConfig.getSleeptime()));
        monster.setDelayTime(monsterConfig.getDelayTime() * 1000);
        // 躺尸体时间
        monster.setDieDelay(monsterConfig.getDieDelay());
        // 攻击间隔
        monster.setAttackReady(monsterConfig.getAttackready());
        // 巡逻范围
        monster.setToAttackArea(monsterConfig.getToattackarea());
        // 巡逻强制回家距离限制
        monster.setPatrol(monsterConfig.getPatrol());
        // 追击距离
        monster.setChaseDis(monsterConfig.getChaseDis());
        // 能否被冲撞
        monster.setCanRepel(monsterConfig.getCanRepel() == 1);
        // 是否显示墓碑
        monster.setTombMonster(monsterConfig.getTomb() > 0);
        //复活是否随机坐标
        monster.setRelivePointRandom(monsterConfig.getRandom() == 1);
        //fixHurt固定1点血
        monster.setFixHurtType(FixHurtType.valueOf(monsterConfig.getFixHurt()));
        //单次最大伤害
        monster.setMaxdamage(monsterConfig.getMaxdamage());
        //受伤害上限类型
        monster.setHurtLimitType(FixHurtType.valueOf(monsterConfig.getWordLimit()));
        // 对应的计数怪
        monster.setBindCfgId(monsterConfig.getBondid());
        //复活血量参数
        Map<Integer, Integer> increase = monsterConfig.getIncrease();
        if (increase != null && increase.size() > 0) {
            TreeMap<Integer, Integer> reliveParamMap = new TreeMap<>();
            increase.forEach(reliveParamMap::put);
            monster.setReliveParamMap(reliveParamMap);
        }
        int[] adjustInfo = monsterConfig.getAutodistance();
        if (adjustInfo != null && adjustInfo.length >= 2) {
            // 攻击距离调整
            monster.setAdjustDistance(adjustInfo[0]);
            // 攻击距离调整时最大步子数
            monster.setAdjustStep(adjustInfo[1]);
        }

        if (spawnConfig != null) {
            if (spawnConfig.getRemove() > 0) {
                monster.setRemoveAt(TimeUtil.getNowOfMills() + spawnConfig.getRemove() * 1000L);
            }
            monster.setDir(spawnConfig.getDir());
            // 刷新复活机制
            monster.setReliveType(spawnConfig.getReviveType());
            monster.setReliveParam(spawnConfig.getReviveParam());
            monster.setDailyAttached(spawnConfig.getActivityType());
        } else {
            // 复活类型
            monster.setReliveType(monsterConfig.getReliveType());
            monster.setReliveParam(monsterConfig.getReviveParam());
            monster.setRemoveAfterDie(monsterConfig.getReliveType() == 0 && removeAfterDie);
        }

        if(initAttribute == null){
            // 没有显示指定的初始化属性则根据配置文件进行初始化
            initAttribute = new Attribute();
            initAttribute.fixAdd(monsterConfig.getAttribute());
            monster.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, initAttribute);
        }
        monster.attributeChange();
        int moveSpeed = (int) initAttribute.get(AttributeEnum.ZTXY_MOVEMENT_SPEED);
        // 移动速度
        monster.setMoveSpeed(moveSpeed);
        monster.setHp(initAttribute.findMaxHp());
        monster.setShield(initAttribute.findMaxShield());
        //初始化技能
        initMonsterSkill(monster);
        // 初始化buff
        initMonsterBuff(monster);

        // 设置状态机
        List<FSMState<MonsterActor>> states = new ArrayList<>();
        states.add(new MonsterSleepState(FSMState.SLEEP, monster));
        states.add(new MonsterActiveState(FSMState.ACTIVE, monster));
        states.add(new MonsterAlertState(FSMState.ALERT, monster));
        states.add(new MonsterSpellState(FSMState.SPELL, monster));
        states.add(new MonsterCastState(FSMState.CAST, monster));
        states.add(new MonsterDieState(FSMState.DIE, monster));
        FSMMachine<MonsterActor> machine = new FSMMachine<>(monster, states, states.get(0));
        monster.setMachine(machine);

        // 增加模型范围
        monster.setModelOffset(monsterConfig.getModelOffset());
        monster.setHitBox(new Rectangle(0,0,monster.getModelOffset().getHalfW()*2,monster.getModelOffset().getHalfH()*2));
//        dealSynStatus(monster, monsterConfig, spawnConfig);

        return monster;
    }

    private static void dealSynStatus(MonsterActor monster, MonsterConfig monsterConfig, MapSpawnConfig spawnConfig) {
        if (spawnConfig == null) {
            return;
        }
        MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, spawnConfig.getMapid());
        if (mapConfig == null) {
            return;
        }

        MonsterCountConfig config = ConfigDataManager.getInstance().getById(MonsterCountConfig.class, monsterConfig.getBossType());
        if (config == null || config.getMapcls() == null) {
            return;
        }

        if (config.getState() > 0) {
            monster.setSyncState(true);
        }

        if (config.getNumcount() > 0) {
            monster.setSyncCount(true);
        }

    }

    public  static MonsterActor createMonster(int monsterCfgId, boolean removeAfterDie, Attribute attribute) {
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterCfgId);
        if (config == null) {
            throw new RuntimeException("monster config not found: " + monsterCfgId);
        }
        return createMonster(config, null, removeAfterDie,attribute);
    }

    public static MonsterActor createMonster(int monsterCfgId, boolean removeAfterDie) {
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterCfgId);
        if (config == null) {
            throw new RuntimeException("monster config not found: " + monsterCfgId);
        }
        return createMonster(config, null, removeAfterDie,null);
    }

    public static MonsterActor createMonster(int id, MapSpawnConfig spawnConfig, boolean removeAfterDie) {
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, id);
        if (config == null) {
            log.error(" monster config not found: monsterId:[{}],mapId:[{}]", id,spawnConfig.getMapid());
            return null;
        }

        return createMonster(config, spawnConfig, removeAfterDie,null);
    }

    public static ServantActor createServant(Performer performer, int monsterId, int initLevel, int maxLevel, int from, MonsterActor monsterActor, Attribute baseAttribute, int skillId) {
        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterId);
        MonsterEnticeConfig monsterEnticeConfig = ConfigCacheManager.getInstance().getCache(MonsterEnticeConfigCache.class)
                .getConfigByLevel(monsterId, initLevel);
        if (monsterEnticeConfig == null) {
            return null;
        }

        ServantActor servantActor = new ServantActor();

        servantActor.setSkillId(skillId);
        servantActor.setId(IDUtil.getId(IDConst.PERSISTENT));
        servantActor.setRid(performer.getRid());
        servantActor.setConfigId(monsterId);
        servantActor.setMonsterType(from == ServantConst.From.ARREST ? MonsterType.SKILL_CALL : monsterConfig.getType());
        servantActor.setModel(monsterConfig.getModel());
        servantActor.setMaster(performer);
        servantActor.setLevel(initLevel);
        servantActor.setMaxLevel(maxLevel);
        servantActor.setFrom(from);
        servantActor.setReplace(monsterEnticeConfig.getReplace());
        servantActor.setName(monsterEnticeConfig.getNewname());
        servantActor.setFixHurtType(FixHurtType.valueOf(monsterConfig.getFixHurt()));
        servantActor.setHurtLimitType(FixHurtType.valueOf(monsterConfig.getWordLimit()));
        Attribute attribute = new Attribute();
        if (baseAttribute != null) {
            attribute.fixAdd(baseAttribute);
        } else {
            if (monsterEnticeConfig.getHtype() == 1) {
                if (monsterActor != null) {
                    attribute.fixAdd(monsterActor.getFinalAttribute());
                }
            } else if (monsterEnticeConfig.getHtype() == 2) {
                attribute.fixAdd(performer.getFinalAttribute());
            }
        }
        servantActor.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        ServantActorManager.getInstance().createServantFinalAttribute(servantActor, monsterEnticeConfig);
        servantActor.attributeChange();

        // 战斗状态
        if (performer.isPlayer()) {
            servantActor.setFightState(((PlayerActor) performer).getServantFightState());
        }
        // 移动速度
        servantActor.setMoveSpeed(monsterConfig.getMoveSpeed());
        // 移动间隔
        servantActor.setMoveInterval(monsterConfig.getMoveInterval());
        servantActor.setSleepTime(monsterConfig.getSleeptime());
        servantActor.setAttackReady(monsterConfig.getAttackready());
        // 巡逻范围
        servantActor.setToAttackArea(monsterConfig.getToattackarea());

        // 怪物心跳
        servantActor.setHeart(monsterConfig.getHeart());

        // 躺尸体时间
        servantActor.setDieDelay(monsterConfig.getDieDelay());

        // 复活类型
        servantActor.setReliveType(monsterConfig.getReliveType());

        //初始化技能
        initMonsterSkill(servantActor);

        initMonsterBuff(servantActor);

        if (from == ServantConst.From.ARREST) {
            BuffManager.getInstance().append(servantActor, BuffConst.BuffId.ARREST_BLUE_SHOW, servantActor, null, TimeUtil.getNowOfMills());
        }

        // 设置状态机
        List<FSMState<ServantActor>> states = new ArrayList<>();

        FSMMachine<ServantActor> machine = new FSMMachine<>(servantActor, states, states.get(0));
        servantActor.setMachine(machine);
        servantActor.setInit(true);
        return servantActor;
    }

    public static RobotActor createRobotActor(int id, String name, int action, int level, Map<Integer, Integer> equips, List<int[]> skills) {
        MapRobotSpawnConfig config = ConfigDataManager.getInstance().getById(MapRobotSpawnConfig.class, id);
        if (config == null) {
            log.error("map robot spawn config not found: {}", id);
            return null;
        }

        RobotActor actor = new RobotActor();
        actor.setId(IDUtil.getId(IDConst.PERSISTENT));
        actor.setConfigId(config.getId());
        actor.setCareer(config.getCareer());
        actor.setSex(config.getSex());
        actor.setTitle(config.getTitle());
        actor.setMoveSpeed(2);
        actor.setToAttackArea(new ModelOffset(10,10,0,0));
        actor.setChaseDis(10);
        actor.setSleepTime(1000);

        if (StringUtil.isEmpty(name)) {
            name = config.getName() + ThreadLocalRandom.current().nextInt(1000);
        }
        actor.setName(name);

        if (action == 0) {
            action = config.getAction();
        }
        actor.setAction(action);

        if (level <= 0) {
            level = config.getLevel();
        }
        actor.setLevel(level);

        if (equips != null) {
            actor.setEquips(equips);
        } else {
            actor.appendEquip(config.getEquip(), false);
        }

        if (skills == null) {
            skills = config.getSkill();
        }
        actor.appendSkill(skills);

        Attribute attribute = new Attribute();
        attribute.fixAdd(config.getAttribute());
        MapRobotLevelConfig levelConfig = ConfigDataManager.getInstance().getById(MapRobotLevelConfig.class, level);
        if (levelConfig != null) {
            attribute.fixAdd(levelConfig.getAttribute());
        }
        actor.getEquips().forEach((k, v) -> {
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, v);
            if (itemConfig == null) {
                return;
            }
            attribute.fixAdd(itemConfig.getAttribute());
        });
        actor.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        actor.attributeChange();

        // 设置状态机
        List<FSMState<MonsterActor>> states = new ArrayList<>();
        states.add(new MonsterActiveState(FSMState.ACTIVE, actor));
        states.add(new MonsterDieState(FSMState.DIE, actor));
        states.add(new MonsterAlertState(FSMState.ALERT, actor));
        FSMMachine<MonsterActor> machine = new FSMMachine<>(actor, states, states.get(0));
        actor.setMachine(machine);

        return actor;
    }

    public static void initMonsterSkill(MonsterActor monster) {
        int minAttackRange = 100;
        int mid = monster.getConfigId();
        MonsterSkillCache cache = ConfigCacheManager.getInstance().getCache(MonsterSkillCache.class);
        if (cache.getMonsterSkillMap().containsKey(mid)) {
            List<MonsterSkillConfig> monsterSkills = cache.getMonsterSkillMap().get(mid);
            for (MonsterSkillConfig config : monsterSkills) {
                int[] skillIdArray = config.getSkillId();
                for (int i = 0; i < skillIdArray.length; i++) {
                    MonsterSkill skill = new MonsterSkill();
                    skill.setHpPercentMax(config.getHpPercentMax()[i]);
                    skill.setHpPercentMin(config.getHpPercentMin()[i]);
                    skill.setPriority(config.getPriority()[i]);
                    skill.setSkillId(config.getSkillId()[i]);
                    skill.setLevel(config.getSkillLevel()[i]);
                    SkillBattleConfig skillConfig = ConfigDataManager.getInstance().getById(SkillBattleConfig.class, skill.getSkillId());
                    if (skillConfig == null) {
                        continue;
                    }
                    skill.setAttackDistance(skillConfig.getRange());
                    Map<Integer, Skill> skillMap = monster.getSkillMap();
                    skillMap.put(skill.getSkillId(), skill);
                    minAttackRange = Math.min(minAttackRange, skill.getAttackDistance());
                }
            }
        }
        monster.setMinAttackRange(minAttackRange);
    }

    public static void initMonsterBuff(MonsterActor monster) {
        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monster.getConfigId());
        int[] buffArray = monsterConfig.getBuff();
        if (buffArray != null && buffArray.length > 0) {
            Map<Integer, Set<Integer>> buffTriggerMap = monster.getBuffTriggerMap();
            for (int buffId : buffArray) {
                BuffConfig bufferConfig = ConfigDataManager.getInstance().getById(BuffConfig.class, buffId);
                Set<Integer> buffIdSet = buffTriggerMap.computeIfAbsent(bufferConfig.getBufferType(), k -> new HashSet<>());
                buffIdSet.add(buffId);
            }
        }
    }

    public static XiaLvActor createXiaLv(Player player, int xialvId, int xiaLvGuardianLevel, List<Integer> xiaLvEquip, long lastDieTime, long hp) {
        XiaLvActor monster = new XiaLvActor();
        monster.setMaster(player.getActor());
        monster.setRid(player.getId());
        monster.setXialvId(xialvId);
        monster.setGuardianLevel(xiaLvGuardianLevel);
        XiaLvConfig xiaLvConfig = ConfigDataManager.getInstance().getById(XiaLvConfig.class, xialvId);

        // 分配唯一id
        monster.setId(IDUtil.getId(IDConst.PERSISTENT));

        // 使用默认怪物模型，如果有穿戴皮肤则使用皮肤怪模型
        int monsterCid = 0;
        PlayerActor actor = player.getActor();
        Map<Integer, Integer> appearanceWears = actor.getAppearanceWears();
        int fashionCid = appearanceWears.getOrDefault(RoleConst.AppearanceType.XIALV, 0);
        AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionCid);
        if (appearanceConfig != null) {
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, appearanceConfig.getItemId());
            if (itemConfig != null) {
                monsterCid = itemConfig.getModel();
            }
        }
        int configId = monsterCid > 0 ? monsterCid : xiaLvConfig.getMonsterid();

        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, configId);

        if (monsterConfig == null) {
            log.error("monster config not found: {}", configId);
            return null;
        }

        // 初始化标记
        monster.setInit(true);
        // 分配唯一id
        monster.setId(IDUtil.getId(IDConst.PERSISTENT));
        // 初始化时间
        monster.setBornTime(TimeUtil.getNowOfSeconds());

//        monster.setOwner(player.getId());
//        monster.setOwnerName(player.getName());

        if (player.getActor() != null) {
            monster.setFightState(player.getActor().getServantFightState());
        }

        // 配置id
        monster.setConfigId(monsterConfig.getId());
        // 模型id
        monster.setModel(monsterConfig.getModel());
        // 名字
        monster.setName(monsterConfig.getName());
        // 等级
        monster.setLevel(monsterConfig.getLevel());
        // AI类型
        monster.setAiType(monsterConfig.getAitype());
        // 怪物类型
        monster.setMonsterType(monsterConfig.getType());
        // 怪物心跳间隔
        monster.setHeart(monsterConfig.getHeart());
        // 移动速度
        monster.setMoveSpeed(monsterConfig.getMoveSpeed());
        // 移动间隔
        monster.setMoveInterval(monsterConfig.getMoveInterval());
        // 休眠时间
        monster.setSleepTime(monsterConfig.getSleeptime());
        // 躺尸体时间
        monster.setDieDelay(monsterConfig.getDieDelay());
        // 攻击间隔
        monster.setAttackReady(monsterConfig.getAttackready());
        // 巡逻范围
        monster.setToAttackArea(monsterConfig.getToattackarea());
        // 巡逻强制回家距离限制
        monster.setPatrol(monsterConfig.getPatrol());
        // 追击距离
        monster.setChaseDis(monsterConfig.getChaseDis());
        // 能否被冲撞
        monster.setCanRepel(monsterConfig.getCanRepel() == 1);
        // 是否显示墓碑
        monster.setTombMonster(monsterConfig.getTomb() > 0);
        //复活是否随机坐标
        monster.setRelivePointRandom(monsterConfig.getRandom() == 1);
        //fixHurt固定1点血
        monster.setFixHurtType(FixHurtType.valueOf(monsterConfig.getFixHurt()));
        //受伤害上限类型
        monster.setHurtLimitType(FixHurtType.valueOf(monsterConfig.getWordLimit()));
        // 设置侠侣装备
        monster.setEquip(xiaLvEquip);
        monster.attributeChange();

        // 设置血量
        if (hp > 0){
            monster.setHp(Math.min(monster.getFinalAttribute().findMaxHp(), hp));
        }

//        if (monsterConfig.getPerHp() > 0) {
//            monster.setHp((long) (attribute.getMaxHp() * (monsterConfig.getPerHp() / 10000.0)));
//        } else {
//            monster.setHp(attribute.getMaxHp());
//        }
        //初始化技能
        initMonsterSkill(monster);
        // 初始化buff
        initMonsterBuff(monster);
        // 侠侣拥有特殊buff
        monster.initXiaLvBuff();

        //是否需要同步怪物状态和数量 上面是通用 下面是各地图处理 防止同步太多
        monster.setSyncState(monsterConfig.getType() == MonsterType.COMMON_BOSS || monsterConfig.getType() == MonsterType.WORLD_BOSS);
        monster.setSyncCount(monsterConfig.getType() == MonsterType.WORLD_BOSS);

        monster.setDeadTime(lastDieTime);
        if (TimeUtil.getNowOfMills() < lastDieTime + xiaLvConfig.getRelivetime() * TimeUtil.ONE_MILLS) {
            monster.setHp(0);
            monster.setDead(true);

            //  通知客户端侠侣死亡时间
            ResXiaLvDieTimeMessage msg = new ResXiaLvDieTimeMessage();
            msg.setDieTimeStamp(lastDieTime);
            Module.MSG_TRANSFORMER.sendMsg(msg, actor.getRid());

        }

        // 创建侠侣时设置状态机
        List<FSMState<XiaLvActor>> states = new ArrayList<>();
        FSMMachine<XiaLvActor> machine = new FSMMachine<>(monster, states, states.get(0));
        monster.setMachine(machine);

        return monster;
    }

    public static EscortActor createEscort(int mid) {
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, mid);
        if (config == null) {
            return null;
        }
        EscortActor monster = new EscortActor();

        monster.setId(IDUtil.getId(IDConst.PERSISTENT));
        monster.setConfigId(mid);
        monster.setModel(config.getModel());
        monster.setLevel(config.getLevel());
        monster.setMonsterType(config.getType());
        monster.setName(config.getName());
        monster.setRemoveAfterDie(true);

        Attribute attribute = new Attribute();
        attribute.fixAdd(config.getAttribute());
        monster.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        monster.attributeChange();

        // AI类型
        monster.setAiType(config.getAitype());
        // 移动速度
        monster.setMoveSpeed(config.getMoveSpeed());

        // 移动间隔
        monster.setMoveInterval(config.getMoveInterval());

        // 巡逻范围
        monster.setToAttackArea(config.getToattackarea());

        monster.setChaseDis(config.getChaseDis());

        // 怪物心跳
        monster.setHeart(config.getHeart());

        // 躺尸体时间
        monster.setDieDelay(config.getDieDelay());

        //fixHurt固定1点血
        monster.setFixHurtType(FixHurtType.valueOf(config.getFixHurt()));

        //受伤害上限类型
        monster.setHurtLimitType(FixHurtType.valueOf(config.getWordLimit()));

        //单次最大伤害
        monster.setMaxdamage(config.getMaxdamage());



        // 设置状态机
        List<FSMState<MonsterActor>> states = new ArrayList<>();
        states.add(new MonsterActiveState(FSMState.ACTIVE, monster));
        states.add(new MonsterDieState(FSMState.DIE, monster));
        states.add(new MonsterSleepState(FSMState.SLEEP, monster));
        FSMMachine<MonsterActor> machine = new FSMMachine<>(monster, states, states.get(0));
        monster.setMachine(machine);

        return monster;
    }

    public static ChongWuActor createChongWu(PlayerActor player, int cfgId) {
        ChongWuActor monster = new ChongWuActor();
        monster.setMaster(player);
        monster.setRid(player.getRid());
        // 分配唯一id
        monster.setId(IDUtil.getId(IDConst.PERSISTENT));

        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, cfgId);

        if (monsterConfig == null) {
            log.error("monster config not found: {}", cfgId);
            return null;
        }

        // 初始化标记
        monster.setInit(true);
        // 分配唯一id
        monster.setId(IDUtil.getId(IDConst.PERSISTENT));
        // 初始化时间
        monster.setBornTime(TimeUtil.getNowOfSeconds());

        monster.setOwner(player.getId());
        monster.setOwnerName(player.getName());

        // 配置id
        monster.setConfigId(monsterConfig.getId());

        // 模型id
        monster.setModel(monsterConfig.getModel());

        // 名字
        monster.setName(monsterConfig.getName());
        // 等级
        monster.setLevel(monsterConfig.getLevel());
        // AI类型
        monster.setAiType(monsterConfig.getAitype());
        // 怪物类型
        monster.setMonsterType(monsterConfig.getType());
        // 怪物心跳间隔
        monster.setHeart(monsterConfig.getHeart());
        // 移动速度
        monster.setMoveSpeed(monsterConfig.getMoveSpeed());
        // 移动间隔
        monster.setMoveInterval(1000);
        // 休眠时间
        monster.setSleepTime(monsterConfig.getSleeptime());
        // 躺尸体时间
        monster.setDieDelay(monsterConfig.getDieDelay());
        // 攻击间隔
        monster.setAttackReady(monsterConfig.getAttackready());
        // 巡逻范围
        monster.setToAttackArea(monsterConfig.getToattackarea());
        // 巡逻强制回家距离限制
        monster.setPatrol(monsterConfig.getPatrol());
        // 追击距离
        monster.setChaseDis(monsterConfig.getChaseDis());
        // 能否被冲撞
        monster.setCanRepel(monsterConfig.getCanRepel() == 1);
        // 是否显示墓碑
        monster.setTombMonster(monsterConfig.getTomb() > 0);
        //复活是否随机坐标
        monster.setRelivePointRandom(monsterConfig.getRandom() == 1);
        //fixHurt固定1点血
        monster.setFixHurtType(FixHurtType.valueOf(monsterConfig.getFixHurt()));
        //受伤害上限类型
        monster.setHurtLimitType(FixHurtType.valueOf(monsterConfig.getWordLimit()));
        // 初始化属性
        Attribute attribute = new Attribute();
        attribute.fixAdd(monsterConfig.getAttribute());
        monster.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, attribute);
        monster.attributeChange();
        monster.setHp(attribute.findMaxHp());
        //初始化技能
        initMonsterSkill(monster);
        // 初始化buff
        initMonsterBuff(monster);

        //是否需要同步怪物状态和数量 上面是通用 下面是各地图处理 防止同步太多
        monster.setSyncState(monsterConfig.getType() == MonsterType.COMMON_BOSS || monsterConfig.getType() == MonsterType.WORLD_BOSS);
        monster.setSyncCount(monsterConfig.getType() == MonsterType.WORLD_BOSS);

        monster.setDelayTime(monsterConfig.getDelayTime() * 1000);
        List<FSMState<MonsterActor>> states = new ArrayList<>();
        states.add(new MonsterSleepState(FSMState.SLEEP, monster));
        states.add(new MonsterActiveState(FSMState.ACTIVE, monster));
        states.add(new MonsterAlertState(FSMState.ALERT, monster));
        states.add(new MonsterSpellState(FSMState.SPELL, monster));
        states.add(new MonsterCastState(FSMState.CAST, monster));
        states.add(new MonsterDieState(FSMState.DIE, monster));
        FSMMachine<MonsterActor> machine = new FSMMachine<>(monster, states, states.get(0));
        monster.setMachine(machine);

        // 增加模型范围
        monster.setModelOffset(monsterConfig.getModelOffset());
        monster.setHitBox(new Rectangle(0, 0, monster.getModelOffset().getHalfW() * 2, monster.getModelOffset().getHalfH() * 2));
        return monster;
    }


    public static BulletActor createBullet(Performer master, BulletConfig bulletConfig) {
        BulletActor bullet = new BulletActor();
        bullet.setConfigId(bulletConfig.getId());
        bullet.setId(IDUtil.getId(IDConst.PERSISTENT));
        bullet.setMaster(master);
        bullet.setName(bulletConfig.getName());
        bullet.setRemoveTime(TimeUtil.getNowOfMills() + bulletConfig.getLifeTime());

        // 移动速度
        bullet.setMoveSpeed(bulletConfig.getSpeed());
        // 移动间隔
        bullet.setMoveInterval(Math.max(bullet.getMoveInterval(), bulletConfig.getMoveInterval()));
        bullet.setModelOffset(bulletConfig.getModelOffset());

        // 设置状态机
        List<FSMState<BulletActor>> states = new ArrayList<>();
        states.add(new BulletActiveState(FSMState.ACTIVE, bullet));
        states.add(new BulletDieState(FSMState.DIE, bullet));

        FSMMachine<BulletActor> machine = new FSMMachine<>(bullet, states, states.get(0));
        bullet.setMachine(machine);
        return bullet;
    }

    public static GroundTrap createTimerTrap(Performer caster, Vec3 pos, SkillEffectConfig config) {
        // 陷阱触发时应用的效果
        int[] param1 = config.getParameter().get(0);
        // 半径
        int r = config.getParameter().get(1)[0];
        // 触发间隔(CD时间）
        int interval = config.getParameter().get(2)[0];
        // 持续时间（毫秒）
        int duration = config.getDuration();
        // 陷阱区域（目前只有圆形）
        Shape shape = new Circle(caster.getPosition().x, caster.getPosition().y, r);
        List<SkillEffectConfig> effects = new ArrayList<>(param1.length);
        for (int id : param1) {
            if (id > 0) {
                effects.add(ConfigDataManager.getInstance().getById(SkillEffectConfig.class, id));
            }
        }
        TimerGroundTrap trap = new TimerGroundTrap(config, caster, shape, pos, duration);
        trap.setEffects(effects);
        trap.setCd(interval);
        trap.setConfig(config);
        return trap;
    }

    public static GroundTrap createHitTrap(GameMap map, Performer caster, Vec3 pos, SkillEffectConfig config) {
        // 持续时间（毫秒）
        int duration = config.getDuration();
        // 半径
        int r = config.getParameter().get(1)[0];
        // 陷阱影响的对象类型(只有符合的type才会受到陷阱的效果）
        Set<Integer> filterTypes = new HashSet<>();
        for (int type : config.getParameter().get(2)) {
            filterTypes.add(type);
        }
        // 陷阱区域（目前只有圆形）
        Shape shape = new Circle(caster.getPosition().x, caster.getPosition().y, r);
        // 陷阱可以被客户端触发的碰撞技能列表
        Map<Integer, Long> hitSkillMap = new HashMap<>();
        for (int skillId : config.getParameter().get(3)) {
            hitSkillMap.put(skillId, TimeUtil.getNowOfMills() + duration);
        }
        // 陷阱的属性来自boss配置（只取攻击力）
        int bossConfigId = ((ZtxyBarrierDuplicate) map).getBossConfigId();
        MonsterConfig bossConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, bossConfigId);
        Attribute temp = new Attribute().fixAdd(bossConfig.getAttribute());
        Attribute attribute = new Attribute();
        attribute.fixAdd(AttributeEnum.ZTXY_ATTACK.getType(), temp.get(AttributeEnum.ZTXY_ATTACK));
        HitGroundTrap trap = new HitGroundTrap(config, caster, shape, pos, duration);
        trap.setSkillTemp(hitSkillMap);
        trap.setFinalAttribute(attribute);
        return trap;
    }

}
