package com.sh.game.map.obj;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.map.ResNoticeViewTypeInfoMessage;
import com.sh.game.common.communication.msg.map.ResPlayerLevelUpdateMessage;
import com.sh.game.common.communication.msg.map.ResUpdateWearMessage;
import com.sh.game.common.communication.msg.map.bean.NoticeBean;
import com.sh.game.common.communication.msg.map.fight.ResAddServantPanelMessage;
import com.sh.game.common.communication.msg.map.fight.ResRemoveServantPanelMessage;
import com.sh.game.common.communication.msg.map.fight.ResServantReliveTimeMessage;
import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.communication.notice.*;
import com.sh.game.common.communication.notice.entity.RoleSimpleData;
import com.sh.game.common.communication.notice.entity.ShaBaKeCrossData;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.avatar.AvatarBackState;
import com.sh.game.common.entity.avatar.AvatarUnion;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.entity.buff.Buffs;
import com.sh.game.common.entity.map.EquipDTO;
import com.sh.game.common.entity.map.ServantDTO;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.gemo.Vec3;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.sync.Sync;
import com.sh.game.common.sync.SyncData;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.duplicate.instance.ztxy.ZtxyBarrierDuplicate;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.fsm.FSMMachine;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.persistence.CrossDataPersistenceUtil;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.point.Point;
import com.sh.game.map.scene.point.PointState;
import com.sh.game.map.startup.boots.MapContext;
import com.sh.game.map.util.GeomUtil;
import com.sh.game.map.util.TipUtil;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.notice.ProcessNotice;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 玩家
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:55:04
 */
@Setter
@Getter
@Slf4j
@EqualsAndHashCode(callSuper = false, of = "rid")
public class PlayerActor extends Performer implements IAvatar, SyncData {

    /**
     * 对应的服务器id
     */
    private int hostId;

    /**
     * sid
     */
    private int sid;

    /**
     * 归属者名字
     */
    protected String ownerName;

    /**
     * 角色创建时间
     */
    private int createTime;

    /**
     * 发型
     */
    private int hair;

    /**
     * 只在上线/切换地图的时候才会变化(实时坐标点取point的x)
     */
    private int x;

    /**
     * 只在上线/切换地图的时候才会变化(实时坐标点取point的y)
     */
    private int y;

    /**
     * 刚刚登陆
     */
    private boolean afterLogin;

    /**
     * 是否已下线
     */
    private boolean offline;

    /**
     * 队伍id
     */
    private long teamID;

    /**
     * 队伍消息同步时间
     */
    private int teamNoticeTime;

    /**
     * 死亡时间
     */
    private int dieTime;

    /**
     * 复活方式
     */
    private int reliveType;

    /**
     * 回城复活时间
     */
    private int reliveTime;
    /**
     * 禁止手动复活(强制躺)
     */
    private boolean manualBan;
    /**
     * 复活阶段 0 正常死亡阶段 1 进入复活阶段 2 已处理完复活逻辑
     */
    private int reliveStage;

    /**
     * 战斗模式
     */
    private int fightModel;


    //===============拷贝Role的数据，需要即时通知==================

    /**
     * vip等级
     */
    private int vipLevel;

    /**
     * roleId
     */
    private long rid;

    /**
     * 装备
     */
    private Map<Integer, EquipDTO> equipMap = new HashMap<>();

    /**
     * 行会
     */
    private AvatarUnion union = new AvatarUnion();

    /**
     * 阵营
     */
    private int group;


    /**
     * 称号
     */
    private int title;

    /**
     * 临时称号
     */
    private int tempTitle;

    /**
     * 装备套装
     */
    private Set<Integer> equipSuits;

    /**======================历史地图信息（世界地图）用于回城和登录=================================*/
    /**
     * 上个x
     */
    private int lastX;

    /**
     * 上个y
     */
    private int lastY;

    /**
     * 上个地图id
     */
    private int lastMapId;


    /**
     * 上次回复魔法时间
     */
    private long lastRecoverMpTime = TimeUtil.getNowOfMills();

    /**
     * 上次回复血量时间
     */
    private long lastRecoverHpTime = TimeUtil.getNowOfMills();

    /**
     * 上次万分比回复血量时间
     */
    private long lastRecoverHpRateAddTime = TimeUtil.getNowOfMills();

    /**
     * 上次回复护盾时间
     */
    private long lastRecoverShieldTime = TimeUtil.getNowOfMills();

    /**
     * 上次万分比回复血量时间
     */
    private long lastRecoverMpRateAddTime = TimeUtil.getNowOfMills();


    /**
     * 英雄信息
     */
    private HeroActor heroActor;

    /**
     * 侠侣
     */
    private XiaLvActor xiaLvActor;

    /**
     * 光环特效
     */
    private int xialvGuanghuan;

    /**
     * 光环表等级
     */
    private int xialvGuanghuanLevel;

    /**
     * 侠侣上层特效
     */
    private int xialvUpTeXiao;

    /**
     * 拥有的侠侣
     */
    private Map<Integer, Integer> xiaLvCidByLevelMap = new HashMap<>();

    /**
     * 侠侣守护等级
     */
    private int xiaLvGuardianLevel;

    /**
     * 分身信息
     */
    private PlayerCloneActor cloneActor;

    /**
     * 召唤宠物
     */
    private List<ServantActor> servantList = new ArrayList<>();

    /**
     * 宠物控制面板显示
     */
    private boolean servantPanelShow;

    /**
     * 天下第一击杀数
     */
    private int worldFirstKillNum;

    private FSMMachine<? extends PlayerActor> machine;


    private long collecting;

    //药水血蓝恢复
    //useType -> <lastRecoverTime, [hpTotal, mpTotal, hpPer, mpPer]>
    private Map<Integer, TwoTuple<Long, int[]>> liquidHpMpRecoverMap = new HashMap<>();


    /**
     * 最终属性
     */
    @Sync(launcher = true, dataType = DataType.ROLE)
    private Attribute finalAttribute;

    /**
     * pk值
     */
    @Sync(launcher = true, dataType = DataType.ROLE)
    private int pkValue = 0;

    /**
     * pk值减少累积时间
     */
    private int pkValueTotalTime = 0;

    /**
     * 是否显示灰色
     */
    private boolean grayShow;

    /**
     * 变灰累积时间
     */
    private int grayTotalTime;

    /**
     * 高级颜色显示
     */
    private boolean superColor;

    private int dragonWingLevel;

    /**
     * 已穿戴外观
     */
    private Map<Integer, Integer> appearanceWears = new HashMap<>();
    //已有的装扮id
    private Set<Integer> appearanceSets = new HashSet<>();


    /**
     * 祭坛奖励次数
     */
    @Deprecated
    private int jiTanBossCount;

    /**
     * 挖矿状态
     */
    @Deprecated
    private boolean isDig;

    /**
     * 魔血石最后更新时间
     */
    @Deprecated
    private int lastMagicBloodStoneUpdateTime;

    /**
     * 魔血石HP恢复生效设置
     */
    @Deprecated
    private int magicBloodStoneHpSet;

    /**
     * 魔血石MP恢复生效设置
     */
    @Deprecated
    private int magicBloodStoneMpSet;

    /**
     * 战意配置
     */
    @Deprecated
    private int zhanyiLv;

    /**
     * 神兵会员等级
     */
    @Deprecated
    private int shenBingVipLevel;

    /**
     * 已解锁神兵
     */
    @Deprecated
    private int maxGainExpShenBingId;

    /**
     * 神兵会员特权每日额外增加的经验值
     */
    @Deprecated
    private long dailyAddExtraExp;

    /**
     * 神兵每日免费复活次数
     */
    private int reliveFreeTime;

    /**
     * 最后一次击杀时间
     */
    private long lastKillTime;

    /**
     * 连杀
     */
    private int comboKills;


    /**
     * 被动技能通知
     */
    private boolean PassiveSkillNotice = false;

    /**
     * 江湖名望id,{@link MingWangConfig} 的id
     */
    @Deprecated
    private int mingWang;

    /**
     * 觉醒等级
     * key: 装备类型
     * value: 等级
     */
    @Deprecated
    private Map<Integer, Integer> juexingLevel = new HashMap<>();


    /**
     * 角色月卡
     * key: 月卡id
     * value: 到期时间(永久为-1)
     */
    @Deprecated
    private Map<Integer, Integer> monthCard = new HashMap<>();

    /**
     * 是否开启狂暴之力
     */
    @Deprecated
    private boolean isSuperMan;

    /**
     * 是否开启狂暴回城保护
     */
    @Deprecated
    private boolean isProtect;

    /**
     * 是否开启狂暴随机保护
     */
    @Deprecated
    private boolean isRandomProtect;

    /**
     * 神威突破属性，key:type,value:神威突破表cfgId
     */
    @Deprecated
    private Map<Integer, Integer> shenWei = new HashMap<>();

    /**
     * 神威boss每日掉落次数
     */
    @Deprecated
    private int shenWeiBossDailyDropCount;
    /**
     * 法器会员额外神威boss次数
     */
    @Deprecated
    private int faQiHuiYuanAddshenWeiBossCount;

    private ChongWuActor chongWuActor;

    /**
     * 法器等级
     */
    @Deprecated
    private int faQiLevel;

    /**
     * 足迹特效充能值
     */
    @Deprecated
    private long footprintRecharge;

    /**
     * 玉佩id
     */
    @Deprecated
    private int yuPei;

    /**
     * 是否拥有旗子
     */
    @Deprecated
    private int duoQi;

    /**
     * 旗子结算积分时间
     */
    @Deprecated
    private int qiZiJieSuanTime;

    /**
     * 每日刀刀元宝获取数
     */
    @Deprecated
    private long goldCount;

    /**
     * 神皇id
     */
    @Deprecated
    private int shenHuangId;

    /**
     * 玩家后台状态
     */
    private AvatarBackState avatarBackState = new AvatarBackState();

    /**
     * 阵营类型
     */
    @Deprecated
    private int campType;

    /**
     * 军衔配置表id
     */
    @Deprecated
    private int militaryRankCfgId;

    /**
     * 境界配置id
     */
    @Deprecated
    private int realmId;

    /**
     * 从巅峰联赛皇宫传送阵出来
     */
    @Deprecated
    private boolean outFromHuangGong;

    /**
     * 上阵的伙伴
     */
    private Set<Integer> huoBan;

    @Override
    public void sendSyncDataNotice(SyncDataNotice notice) {
        if (this.getHostId() == 0) {
            return;
        }
        notice.addHost(getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, getRid());
    }

    public void onSetAppearanceWears() {
        if (appearanceWears == null) {
            return;
        }

        appearanceWears.forEach((k, v) -> Module.MSG_TRANSFORMER.sendRoundMessage(() -> {
            ResUpdateWearMessage msg = new ResUpdateWearMessage();
            msg.setLid(this.getId());
            AppearanceWearBean bean = new AppearanceWearBean();
            bean.setType(k);
            bean.setId(v);
            msg.setWear(bean);
            return msg;
        }, this));

        ResUpdateWearMessage msg = new ResUpdateWearMessage();
        msg.setLid(this.getId());
        AppearanceWearBean bean = new AppearanceWearBean();
        bean.setType(4);
        if (this.getTempTitle() != 0) {
            bean.setId(this.getTempTitle());
        } else {
            bean.setId(appearanceWears.getOrDefault(4, 0));
        }
        msg.setWear(bean);
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
    }

    private int servantFightState;

    //monsterType -> boxId
    private Map<Integer, Integer> monsterTypeExtraDrop = new HashMap<>();

    public void onSetLevel() {
        if (this.isInView()) {
            setHp(getFinalAttribute().findMaxHp());
            setMp((int) getFinalAttribute().findMaxMp());
        }

        ResPlayerLevelUpdateMessage msg = new ResPlayerLevelUpdateMessage();
        msg.setLid(getId());
        msg.setLevel(getLevel());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, this);
    }

    @Override
    public FSMMachine<? extends PlayerActor> getMachine() {
        return machine;
    }

    public void setMachine(FSMMachine<? extends PlayerActor> machine) {
        this.machine = machine;
    }

    public PlayerActor() {
    }


    @Override
    public int getType() {
        return MapObjectType.PLAYER;
    }

    @Override
    public boolean isPlayer() {
        return true;
    }

    @Override
    public int getSkillLevel(int skillID) {
        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillID);
        if (skillConfig == null) {
            return 0;
        }
        Map<Integer, Skill> skillMap = skillConfig.getPassive() == 0 ? this.getSkillMap() : this.getPassiveSkillMap();
        Skill skill = skillMap.get(skillID);
        return skill != null ? skill.getLevel() : 0;
    }

    public int getLoyalty() {
        return heroActor == null ? 0 : heroActor.getLoyalty();
    }

    public int getMagicBloodStoneHpSet() {
        return magicBloodStoneHpSet;
    }

    public void setMagicBloodStoneHpSet(int magicBloodStoneHpSet) {
        this.magicBloodStoneHpSet = magicBloodStoneHpSet;
    }

    public int getMagicBloodStoneMpSet() {
        return magicBloodStoneMpSet;
    }

    public void setMagicBloodStoneMpSet(int magicBloodStoneMpSet) {
        this.magicBloodStoneMpSet = magicBloodStoneMpSet;
    }

    public int getLastMapId() {
        return this.lastMapId;
    }

    public void setLastMapId(int lastMapId) {
        this.lastMapId = lastMapId;
    }

    public int getLastX() {
        return this.lastX;
    }

    public void setLastX(int lastX) {
        this.lastX = lastX;
    }

    public int getLastY() {
        return this.lastY;
    }

    public void setLastY(int lastY) {
        this.lastY = lastY;
    }


    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    @Override
    public int getZhuanShengId() {
        return 0;
    }

    @Override
    public Map<Integer, Integer> getBossChallengeInfo() {
        return null;
    }

    public void recover() {
        // 1.恢复满血
        dead = false;
        hp = finalAttribute.findMaxHp();
        // 2.重置所有CD
        cdMap.forEach((key, value) -> value.setEndTime(0));
        // 3.移除所有buff
        setBuffs(new Buffs());
    }

    /**
     * 脱战后回复内力值
     */
    public void shieldRecover() {
        if (this.isDead() || this.getWhoMyTarget() > 0) {
            return;
        }
        long attrValue = AttributeEnum.SHIELD_REPLY.getAttrValue(this.getFinalAttribute());
        if (attrValue <= 0) {
            return;
        }
        long cd = GlobalUtil.getGlobalInt(GameConst.GlobalId.SHIELD_REPLY_CD) * TimeUtil.ONE_MILLS;
        if (cd <= 0) {
            return;
        }
        long nowOfMills = TimeUtil.getNowOfMills();
        if (nowOfMills - this.lastRecoverShieldTime < cd) {
            return;
        }
        long oldShield = this.getShield();
        long maxShield = this.getFinalAttribute().findMaxShield();
        if (oldShield >= maxShield) {
            return;
        }
        long reply = AttributeEnum.SHIELD_REPLY.getAttrValue(getFinalAttribute()) * getFinalAttribute().findMaxShield() / 10000;
        if (reply <= 0) {
            return;
        }
        long newShield = Math.min(maxShield, oldShield + reply);
        if (oldShield != newShield) {
            this.setShield(newShield);
            this.lastRecoverShieldTime = nowOfMills;
            sendShieldChangeMessage();
        }
    }

    //todo buff not show
    public void updateLiquidEffect() {
        long time = TimeUtil.getNowOfMills();
        //药水回血回蓝
        if (this.isDead()) {
            return;
        }

        boolean notify = false;
        Iterator<Map.Entry<Integer, TwoTuple<Long, int[]>>> recoverIterator = liquidHpMpRecoverMap.entrySet().iterator();
        while (recoverIterator.hasNext()) {
            Map.Entry<Integer, TwoTuple<Long, int[]>> entry = recoverIterator.next();
            TwoTuple<Long, int[]> twoTuple = entry.getValue();
            long lastRecoverTime = twoTuple.getFirst();

            if (time - lastRecoverTime < 1000) {
                continue;
            }

            int[] hpMpInfo = twoTuple.getSecond();
            int oldHpTotal = hpMpInfo[0], oldMpTotal = hpMpInfo[1], hpRecover = hpMpInfo[2], mpRecover = hpMpInfo[3];
            if (oldHpTotal > 0) {
                long oldHp = this.getHp(), maxHp = this.getFinalAttribute().findMaxHp();
                if (oldHp < maxHp) {
                    //药水回复效果
                    int realHpRecover;
                    if (entry.getKey() == 107) {
                        realHpRecover = hpRecover;
                    } else {
                        int potionEffect = (int) (AttributeEnum.POTION_ADD.getAttrValue(this.getFinalAttribute()) - AttributeEnum.POTION_REDUCE.getAttrValue(this.getFinalAttribute()));
                        realHpRecover = Math.max(0, hpRecover + (int) (hpRecover * (potionEffect / 10000D)));
                    }
                    if (realHpRecover > 0) {
                        long newHp = Math.min(maxHp, oldHp + realHpRecover);
                        this.setHp(newHp);
                        notify = true;
                    }
                    hpMpInfo[0] = Math.max(0, oldHpTotal - hpRecover);
                    twoTuple.setFirst(time);
                }
            }
            if (oldMpTotal > 0) {
                long oldMp = this.getMp(), maxMp = this.getFinalAttribute().findMaxMp();
                if (oldMp < maxMp) {
                    int realMpRecover;
                    if (entry.getKey() == 107) {
                        realMpRecover = mpRecover;
                    } else {
                        realMpRecover = Math.max(0, mpRecover);
                    }
                    if (realMpRecover > 0) {
                        long newMp = Math.min(maxMp, oldMp + realMpRecover);
                        this.setMp((int) newMp);
                        notify = true;
                    }
                    hpMpInfo[1] = Math.max(0, oldMpTotal - mpRecover);
                    twoTuple.setFirst(time);
                }
            }

            if (this.getHp() == this.getFinalAttribute().findMaxHp()) {
                hpMpInfo[0] = 0;
            }
            if (this.getMp() == this.getFinalAttribute().findMaxMp()) {
                hpMpInfo[1] = 0;
            }

            if (hpMpInfo[0] == 0 && hpMpInfo[1] == 0) {
                recoverIterator.remove();
            }
        }

        if (notify) {
            sendHpMpChangeMessage(FightConst.HurtType.RECOVER_HP.type());
        }
    }

    /**
     * 魔血石恢复效果
     */
    public void updateMagicBloodStoneEffect() {
        EquipDTO dto = equipMap.get(EquipConst.EquipIndex.POS_JEWEL.getCls());
        if (dto == null) {
            lastMagicBloodStoneUpdateTime = 0;
            return;
        }
        int magicBloodStoneValue = dto.getMagicBloodStoneValue();
        if (dto.getMagicBloodStoneValue() <= 0) {
            return;
        }
        long maxHp = finalAttribute.findMaxHp();
        long maxMp = finalAttribute.findMaxMp();
        int hpRate = (int) (getHp() * 1.0 / maxHp * 100);
        int mpRate = (int) (getMp() * 1.0 / maxMp * 100);
        if (hpRate >= magicBloodStoneHpSet && mpRate >= magicBloodStoneMpSet) {
            return;
        }
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, dto.getItemId());
        if (config == null) {
            return;
        }
        int[][] useParam = config.getUseParam();
        if (useParam == null || useParam[0] == null || useParam[0].length < 3) {
            return;
        }
        int cd = useParam[0][0];
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        if (lastMagicBloodStoneUpdateTime == 0) {
            lastMagicBloodStoneUpdateTime = nowOfSeconds + cd;
            return;
        }
        if (lastMagicBloodStoneUpdateTime > nowOfSeconds) {
            return;
        }
        lastMagicBloodStoneUpdateTime = nowOfSeconds + cd;
        int hpReply = useParam[0][1];
        int mpReply = useParam[0][2];
        if (hpRate < magicBloodStoneHpSet && getHp() < maxHp) {
            double cureReduce = Math.max(0, 1 - AttributeEnum.REPLY_REDUCE.getAttrValue(this.getFinalAttribute()) / 10000D);
            hpReply = (int) (hpReply * cureReduce);
            hpReply = Math.min(hpReply, magicBloodStoneValue);
            magicBloodStoneValue -= hpReply;
            long newHp = Math.min(maxHp, getHp() + hpReply);
            setHp(newHp);
        }
        if (mpRate < magicBloodStoneMpSet && magicBloodStoneValue > 0 && getMp() < maxMp) {
            mpReply = Math.min(mpReply, magicBloodStoneValue);
            magicBloodStoneValue -= mpReply;
            long newMp = Math.min(maxMp, getMp() + mpReply);
            setMp((int) newMp);
        }
        MagicBloodStoneNotice notice = new MagicBloodStoneNotice(getRid(), getId(), dto.getPos(), magicBloodStoneValue);
        notice.getHosts().add(getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, getRid());
        dto.setMagicBloodStoneValue(magicBloodStoneValue);
        sendHpMpChangeMessage(FightConst.HurtType.RECOVER_HP.type());
    }

    @Override
    public boolean penetrate(IMapObject stand, int penetrable) {
        if (stand.isDead()) {
            return true;
        }
        switch (stand.getType()) {
            case MapObjectType.PLAYER:
                if ((penetrable & MapConst.PENETRABLE.PENETRABLE_4_PLAYER) == 0) {
                    return false;
                }
                break;
            case MapObjectType.MONSTER:
                if ((penetrable & MapConst.PENETRABLE.PENETRABLE_4_MONSTER) == 0) {
                    return false;
                }
                break;
        }

        return true;
    }

    @Override
    public boolean overlying(IMapObject stand, int penetrable) {
        return penetrate(stand, penetrable);
    }


    /**
     * 是否是敌人
     *
     * @param obj obj
     * @return boolean
     */
    @Override
    public boolean isEnemy(IMapObject obj, boolean ignoreTargetOnly) {
        if (!(obj instanceof Performer)) {
            return false;
        }
        if (this == obj || getRid() == obj.getRid()) {
            return false;
        }

        PlayerActor self = this;
        if (getType() == MapObjectType.HERO || getType() == MapObjectType.PLAYER_CLONE) {
            self = (PlayerActor) getMaster();
        }
        Performer target = (Performer) obj;
        switch (target.getType()) {
            case MapObjectType.PLAYER:
            case MapObjectType.HERO:
            case MapObjectType.PLAYER_CLONE: {
                GameMap objMap = SceneManager.getInstance().getMap(obj);
                if (objMap.isSafe()) {
                    return false;
                }
                if (obj.getPoint() == null) {
                    return false;
                }
                PointState pointState = objMap.getPointState(obj.getPoint());
                if (pointState != null && pointState.isSafe()) {
                    return false;
                }

                PlayerActor targetPlayer;
                if (target.getType() == MapObjectType.HERO || target.getType() == MapObjectType.PLAYER_CLONE) {
                    targetPlayer = (PlayerActor) target.getMaster();
                } else {
                    targetPlayer = (PlayerActor) target;
                }
                if (this == targetPlayer) {
                    return false;
                }

                switch (self.getFightModel()) {
                    case FightConst.FightModel.PEACE: {
                        return false;
                    }
                    case FightConst.FightModel.ALL: {
                        return true;
                    }
                    case FightConst.FightModel.TARGET_ONLY: {
                        return targetPlayer.getPkValue() > FightConst.YELLOW_SHOW_PK_VALUE || targetPlayer.isGrayShow();
                    }
                    case FightConst.FightModel.TEAM: {
                        return self.getTeamID() == 0 || self.getTeamID() != targetPlayer.getTeamID();
                    }
                    case FightConst.FightModel.UNION: {
                        if (getUnion().ifSameUnion(targetPlayer.getUnion().getId())) {
                            return false;
                        }
                        return true;
                    }
                    case FightConst.FightModel.ALLIANCE:
                        if (getUnion().ifAlly(targetPlayer.getUnion().getId())
                                || getUnion().getId() == targetPlayer.getUnion().getId()) {
                            return false;
                        }
                        return true;
                    case FightConst.FightModel.GROUP:
                        if (self.getGroup() > 0) {
                            return targetPlayer.getGroup() != self.getGroup();
                        } else {
                            return true;
                        }
                    case FightConst.FightModel.UNION_CAMP: {
                        if (!UnionCampConst.ALL_CAMP_TYPE.contains(targetPlayer.getCampType())) {
                            return true;
                        }
                        return self.getCampType() != targetPlayer.getCampType();
                    }
                    default:
                        return true;
                }
            }

            case MapObjectType.PLAYER_FAKE: {
                //假人不可攻击
                return false;
            }

            case MapObjectType.MONSTER: {

                //采集怪不能打
                if (((MonsterActor) target).getMonsterType() == MonsterType.COLLECT_NEW) {
                    return false;
                }
                // 侠侣不能打
                if (((MonsterActor) target).getMonsterType() == MonsterType.XIALV) {
                    return false;
                }
                // 召唤怪不能打
                if (((MonsterActor) target).getMonsterType() == MonsterType.SKILL_CALL) {
                    return false;
                }
                if (((MonsterActor) target).getMonsterType() == MonsterType.DUP_BUFF) {
                    return false;
                }

                //基本是替身草人
                if (target.getMaster() == this) {
                    return false;
                }

                if (this.getPoint() == null) {
                    return false;
                }

                return true;
            }
            case MapObjectType.SERVANT: {
                GameMap objMap = SceneManager.getInstance().getMap(obj);
                PointState pointState = objMap.getPointState(obj.getPoint());
                if (pointState != null && pointState.isSafe()) {
                    return false;
                }
                return this.getFightModel() == FightConst.FightModel.ALL;
            }
            case MapObjectType.ROBOT: {
                GameMap objMap = SceneManager.getInstance().getMap(obj);
                PointState pointState = objMap.getPointState(obj.getPoint());
                if (pointState != null && pointState.isSafe()) {
                    return false;
                }

                if (this.getPoint() == null) {
                    return false;
                }
                pointState = objMap.getPointState(this.getPoint());
                if (pointState != null && pointState.isSafe()) {
                    return false;
                }

                switch (this.getFightModel()) {
                    case FightConst.FightModel.TARGET_ONLY:
                    case FightConst.FightModel.PEACE: {
                        return false;
                    }
                    default:
                        return true;
                }
            }
            case MapObjectType.ESCORT: {
                //工会镖车
//                if (((EscortActor) target).getUnionId() > 0) {
//                    return this.getUnionId() != ((EscortActor) target).getUnionId();
//                }
                // 镖车的主人不是人
                if (target.getMaster().getType() != MapObjectType.PLAYER) {
                    return true;
                }
                // 镖车主人
                PlayerActor master = (PlayerActor) target.getMaster();
                // 不可攻击自己的镖车
                if (this.getId() == master.getId()) {
                    return false;
                }

                switch (this.getFightModel()) {
                    // 组队模式下不可攻击队友的镖车
                    case FightConst.FightModel.TEAM:

                        if (this.getTeamID() != 0 && this.getTeamID() == master.getTeamID()) {
                            return false;
                        }
                        // 行会模式下不可攻击同行
                    case FightConst.FightModel.UNION: {
                        if (this.getUnionId() != 0 && this.getUnionId() == master.getUnionId()) {
                            return false;
                        }
                    }
                    default:
                        return true;
                }
            }
            default:
                //其他所有都不是敌人
                return false;
        }
    }

    @Override
    public boolean isFriend(IMapObject obj, boolean ignoreTargetOnly) {

        if (this == obj || getRid() == obj.getRid()) {
            return true;
        }
        PlayerActor self = this;
        if (getType() == MapObjectType.HERO || getType() == MapObjectType.PLAYER_CLONE) {
            self = (PlayerActor) getMaster();
        }
        Performer target = (Performer) obj;

        switch (target.getType()) {
            case MapObjectType.PLAYER:
            case MapObjectType.PLAYER_CLONE:
            case MapObjectType.HERO: {
                return true;
            }
            case MapObjectType.SERVANT:
                return true;
            case MapObjectType.XIALV:
                return true;
            default:
                //其他所有都不是朋友
                return false;
        }
    }

    public boolean isAfterLogin() {
        return afterLogin;
    }

    public void setAfterLogin(boolean afterLogin) {
        this.afterLogin = afterLogin;
    }

    public boolean isOffline() {
        return offline;
    }

    public void setOffline(boolean offline) {
        this.offline = offline;
    }

    public int getVipLevel() {
        return this.vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }


    public int getReliveTime() {
        return reliveTime;
    }

    public void setReliveTime(int reliveTime) {
        this.reliveTime = reliveTime;
    }

    @Override
    public long getRid() {
        return rid;
    }

    public void setRid(long rid) {
        this.rid = rid;
    }


    public Map<Integer, EquipDTO> getEquipMap() {
        return equipMap;
    }

    public void setEquipMap(Map<Integer, EquipDTO> equipMap) {
        this.equipMap = equipMap;
    }

    @Override
    public int getHostId() {
        return hostId;
    }

    public void setHostId(int hostId) {
        this.hostId = hostId;
    }

//    public int getLastFightWithPlayerSeconds() {
//        return lastFightWithPlayerSeconds;
//    }
//
//    public void setLastFightWithPlayerSeconds(int lastFightWithPlayerSeconds) {
//        this.lastFightWithPlayerSeconds = lastFightWithPlayerSeconds;
//    }

    public int getReliveStage() {
        return reliveStage;
    }

    public void setReliveStage(int reliveStage) {
        this.reliveStage = reliveStage;
    }

    public byte hpPercent() {
        if (hp <= 0) {
            return -1;
        }
        return (byte) Math.floor((float) hp / getFinalAttribute().findMaxHp() * 100);
    }

    public byte ipPercent() {
        Attribute attr = getFinalAttribute();
        return (byte) Math.floor((float) this.mp / attr.findMaxMp() * 100);
    }

    public void updateAllState(GameMap gameMap, int type, int dt) {
//        updateServant(gameMap, dt);
        updateServant(gameMap, dt);
        updateGrayShow(dt);
        updatePkValue(dt);
        updateXiaLv(gameMap);
    }

    public void updateServant(GameMap gameMap, int dt) {
        Iterator<ServantActor> iterator = servantList.iterator();

        boolean canShow = false;

        while (iterator.hasNext()) {
            ServantActor servantActor = iterator.next();
            if (servantActor.getMonsterType() == MonsterType.ITEM_CALL) {
                servantActor.setTotalTime(servantActor.getTotalTime() - dt);

                if (servantActor.isNeedInit()) {
                    PlayerManager.getInstance().addBufferShow(this, servantActor.getBuffId(), (int) ((TimeUtil.getNowOfMills() + servantActor.getTotalTime()) / 1000));
                }

                if (servantActor.isDead() && !servantActor.isCanRelive()) {
                    int reliveCountdown = GlobalConfig.reliveCountdown;
                    servantActor.setReliveCountdown(reliveCountdown);
                    servantActor.setCanRelive(true);

                    if (reliveCountdown < servantActor.getTotalTime()) {
                        //显示复活倒计时
                        ResServantReliveTimeMessage message = new ResServantReliveTimeMessage();
                        message.setTime(reliveCountdown / 1000);
                        Module.MSG_TRANSFORMER.sendMsg(message, this.getRid());
                    }
                }

                if (servantActor.getTotalTime() <= 0) {
                    servantActor.setDead(true);
                    iterator.remove();

                    PlayerManager.getInstance().removeBufferShow(this, servantActor.getBuffId());
                    continue;
                }

                if (servantActor.isDead() && servantActor.isCanRelive()) {
                    servantActor.setReliveCountdown(servantActor.getReliveCountdown() - dt);
                    if (servantActor.getReliveCountdown() <= 0) {
                        //复活
                        servantActor.setDead(false);
                        servantActor.setCanRelive(false);
                        servantActor.setHp(servantActor.getFinalAttribute().findMaxHp());

                        //这里要判断地图让不让进
                        MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, gameMap.getId());
                        if (!this.isDead() && mapConfig != null && ArrayUtils.contains(mapConfig.getMonsterType(), servantActor.getMonsterType())) {
                            gameMap.enterServant(servantActor, this.getPoint());
                        }

                        //关闭复活倒计时
                        ResServantReliveTimeMessage message = new ResServantReliveTimeMessage();
                        message.setTime(0);
                        Module.MSG_TRANSFORMER.sendMsg(message, this.getRid());
                    }

                    if (servantActor.isNeedInit() && servantActor.getReliveCountdown() > 0) {
                        //显示复活倒计时
                        ResServantReliveTimeMessage message = new ResServantReliveTimeMessage();
                        message.setTime(servantActor.getReliveCountdown() / 1000);
                        Module.MSG_TRANSFORMER.sendMsg(message, this.getRid());
                    }
                }

                if (servantActor.isNeedInit()) {
                    servantActor.setNeedInit(false);
                }
            } else if (servantActor.getMonsterType() == MonsterType.EVENT_CALL) {
                servantActor.setTotalTime(servantActor.getTotalTime() - dt);
                if (servantActor.getTotalTime() <= 0) {
                    servantActor.setDead(true);
                    iterator.remove();
                    continue;
                }
            } else {
                if (servantActor.isDead()) {
                    iterator.remove();
                    continue;
                }
            }

            canShow = true;
        }

        if (canShow) {
            if (!this.servantPanelShow) {
                ResAddServantPanelMessage msg = new ResAddServantPanelMessage();
                Module.MSG_TRANSFORMER.sendMsg(msg, this.getRid());
                this.servantPanelShow = true;
            }
        } else {
            if (this.servantPanelShow) {
                ResRemoveServantPanelMessage msg = new ResRemoveServantPanelMessage();
                Module.MSG_TRANSFORMER.sendMsg(msg, this.getRid());
                this.servantPanelShow = false;
            }
        }
    }

    public void updateXiaLv(GameMap gameMap) {
        //不要侠侣，暂时屏蔽侠侣控制面板
        // 侠侣存在并且无面板的时候增加面板
//        if (this.xiaLvActor != null && !this.servantPanelShow) {
//            ResAddServantPanelMessage msg = new ResAddServantPanelMessage();
//            Module.MSG_TRANSFORMER.sendMsg(msg, this.getRid());
//            this.servantPanelShow = true;
//        }

        // 侠侣复活
        if (xiaLvActor != null && xiaLvActor.isDead()) {
            XiaLvConfig config = ConfigDataManager.getInstance().getById(XiaLvConfig.class, xiaLvActor.getXialvId());
            if (config == null) {
                return;
            }

            if (TimeUtil.getNowOfMills() < xiaLvActor.getDeadTime() + config.getRelivetime() * TimeUtil.ONE_MILLS) {
                return;
            }
            xiaLvActor.setDead(false);
            xiaLvActor.setHp(xiaLvActor.getFinalAttribute().findMaxHp());

            gameMap.enterXiaLv(xiaLvActor, this.getPoint());
        }

        // 侠侣不存在且面板存在的时候移除面板
//        if (this.xiaLvActor == null && this.servantPanelShow) {
//            ResRemoveServantPanelMessage msg = new ResRemoveServantPanelMessage();
//            Module.MSG_TRANSFORMER.sendMsg(msg, this.getRid());
//            this.servantPanelShow = false;
//        }
    }

    private void updateGrayShow(int dt) {
        if (!this.isGrayShow()) {
            return;
        }

        grayTotalTime += dt;

        if (this.getGrayTotalTime() >= 60 * 1000) {
            this.setGrayShow(false);
            this.setGrayTotalTime(0);

            ResNoticeViewTypeInfoMessage message = new ResNoticeViewTypeInfoMessage();
            ArrayList<Long> lIdList = new ArrayList<>();
            message.setLidList(lIdList);
            lIdList.add(this.getId());
            HeroActor heroActor = getHeroActor();
            if (heroActor != null) {
                lIdList.add(heroActor.getId());
            }

            NoticeBean noticeBean = new NoticeBean();
            noticeBean.setUpdateType(MapConst.NOTICE_TYPE.GRAY);
            noticeBean.setValue(0);
            ArrayList<NoticeBean> updateList = new ArrayList<>();
            updateList.add(noticeBean);
            message.setUpdateList(updateList);
            Module.MSG_TRANSFORMER.sendRoundMessage(message, this);
        }
    }

    private void updatePkValue(int dt) {
        if (this.getPkValue() == 0) {
            return;
        }

        if (pkValueTotalTime == 0) {
            pkValueTotalTime = GlobalConfig.pkValueTotalTime * 1000;
        }

        pkValueTotalTime -= dt;

        if (pkValueTotalTime <= 0) {
            pkValueTotalTime = 0;

            int curValue = this.getPkValue();
            if (curValue > 0) {
                PlayerManager.getInstance().changePkValue(this, curValue - 1);
            }
        }
    }

    @Override
    protected void calAttackPlayerHurt(SkillEffect effect, Skill skill, Performer target, FightResult.FightEffectRet effectRet, FightResult ret) {
//        FightUtil.pvpAttack(effect, skill, this, target, effectRet, ret);
    }

    @Override
    protected void calAttackMonsterHurt(SkillEffect effect, Skill skill, Performer target, FightResult.FightEffectRet effectRet, FightResult ret) {
//        FightUtil.pveOrEvpAttack(effect, skill, this, target, effectRet, ret);
    }

    @Override
    public void setHp(long hp) {
        super.setHp(hp);
        syncHpMpToTeam();
    }

    @Override
    public void setMp(int mp) {
        super.setMp(mp);
        syncHpMpToTeam();
    }

    /**
     * 队伍hp、mp同步
     */
    private void syncHpMpToTeam() {
        if (teamID == 0 || rid != getId()) {
            return;
        }
        int minutes = TimeUtil.getNowOfMinutes();
        if (minutes == teamNoticeTime) {
            return;
        }
        teamNoticeTime = minutes;
        sendTeamHpMp();
    }

    /**
     * 发送队伍血量
     */
    public void sendTeamHpMp() {
        if (teamID == 0 || rid != getId()) {
            return;
        }
        TeamHpMpNotice notice = new TeamHpMpNotice();
        notice.setRid(rid);
        notice.setHp(getHp());
        notice.setMaxHp(getFinalAttribute().findMaxHp());
        notice.setMp(getMp());
        notice.setMaxMp(getFinalAttribute().findMaxMp());
        notice.addHost(getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_SOCIAL, notice, rid);
    }

    public void cleanExpireBuff() {
        //buff检测 如果先发前端提示时间会有问题 先移除过期的
        long time = TimeUtil.getNowOfMills();
        for (Iterator<Map.Entry<Integer, Buff>> it = getBuffs().getBuffMap().entrySet().iterator(); it.hasNext(); ) {
            Map.Entry<Integer, Buff> entry = it.next();
            Buff buff = entry.getValue();
            //fix  2030.01.01
            if (buff.getExpire() > 1893427200000L || buff.isInvalid()) {
                log.error("创建检测移除buff：{}", buff.getId());
                it.remove();
                continue;
            }
            if (!buff.isForever() && buff.getExpire() < time) {
                if (buff.config().getFormtype() == BuffConst.FormReplace.FROZEN) {
                    for (Buff b : getBuffs().getBuffMap().values()) {
                        if (b == buff || b.config().getFormtype() != BuffConst.FormReplace.FROZEN || b.config().getForm() != buff.config().getForm()) {
                            continue;
                        }
                        if (b.getFrozen() > 0) {
                            b.setExpire(time + (b.getExpire() - b.getFrozen()));
                            b.setFrozen(0);
                            log.info("创建检测 解冻buff:{} {}", b.getId(), b.config().getName());
                        }
                    }
                }
                log.info("创建检测 移除过期buff:{} {}", buff.getId(), buff.config().getName());
                it.remove();
            }
        }

    }

    @Override
    public long getUnionId() {
        return union.getId();
    }

    @Override
    public Object getSlotEquip(int slot) {
        return equipMap.get(slot);
    }

    @Override
    public void sendNotice(byte processorId, ProcessNotice notice) {
        notice.setSourceHost(MapContext.getHostId());
        notice.addHost(getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(processorId, notice, getRid());
    }

    /**
     * 获取所需替换的Servant
     *
     * @param config
     * @return
     */
    public ServantActor getReplaceServant(MonsterEnticeConfig config, MonsterConfig monsterConfig, int skillId) {
        ServantActor replaceTarget = null;
        List<ServantActor> originList = new ArrayList<>(getServantList());
        if (originList.stream().anyMatch(servantActor -> servantActor.getMonsterType() == monsterConfig.getType())) {
            for (ServantActor servantActor : originList) {
                if (servantActor.getFrom() == ServantConst.From.ARREST) {
                    continue;
                }
                if (servantActor.getMonsterType() != monsterConfig.getType()) {
                    continue;
                }
                if (skillId != 0 && skillId != servantActor.getSkillId()) {
                    continue;
                }
                replaceTarget = servantActor;
                break;
            }
        }
        return replaceTarget;
    }

    /**
     * 召唤宠物
     *
     * @param servantDTOS
     */
    public void callServant(List<ServantDTO> servantDTOS) {
        if (servantDTOS != null) {
            servantDTOS.forEach(servantDTO -> {
                Attribute baseAttribute = new Attribute();
                baseAttribute.fixAdd(servantDTO.getBaseAttribute());
                ServantActor servantActor = MapObjectFactory.createServant(this, servantDTO.getCfgId(), servantDTO.getLevel(), servantDTO.getMaxLevel(), servantDTO.getFrom(), null, baseAttribute, servantDTO.getSkillId());
                if (servantActor == null) {
                    return;
                }
                servantActor.setHp(servantDTO.getHp());
                if (servantActor.getMonsterType() == MonsterType.ITEM_CALL) {
                    servantActor.setTotalTime(servantDTO.getTotalTime());
                    servantActor.setReliveCountdown(servantDTO.getReliveCountdown());
                    servantActor.setCanRelive(servantDTO.isCanRelive());
                    servantActor.setBuffId(servantDTO.getBuffId());
                    servantActor.setDead(servantDTO.isDead());
                    if (servantActor.isDead()) {
                        servantActor.getMachine().setState(servantActor.getMachine().getStateByType(FSMState.DIE));
                    }
                    servantActor.setNeedInit(true);//上线重新设置标记
                }
                servantActor.setExp(servantDTO.getExp());
                this.getServantList().add(servantActor);
            });
        }
    }

    @Override
    public void sendErrorTips(String tips) {
        TipUtil.error(getRid(), tips);
    }

    @Override
    public void sendPost(int postId) {
        TipUtil.post(getRid(), postId);
    }

    public void addBuff(int buffId) {
        AddBuffNotice notice = new AddBuffNotice();
        notice.setRid(getRid());
        notice.setBuffId(buffId);
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    public void removeBuff(int buffId) {
        RemoveBuffNotice notice = new RemoveBuffNotice();
        notice.setRid(getRid());
        notice.setBuffId(buffId);
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    /**
     * 持久化保存buff
     *
     * @param buffMap 角色当前buff
     */
    public void saveBuff(Map<Integer, Buff> buffMap) {
        BuffSaveNotice notice = new BuffSaveNotice();
        notice.setRoleId(getRoleId());
        notice.setBuffMap(buffMap);
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    @Override
    public int getZhanYiLevel() {
        return zhanyiLv;
    }

    @Override
    public int findCurShenBingLevel() {
        return shenBingVipLevel;
    }

    @Override
    public int findMaxGainExpShenBingId() {
        return maxGainExpShenBingId;
    }

    public long findDailyAddExtraExp() {
        return dailyAddExtraExp;
    }

    @Override
    public Map<Integer, Integer> getXiaLvs() {
        return xiaLvCidByLevelMap;
    }

    @Override
    public long findHp() {
        return getHp();
    }

    @Override
    public long getItemCount(int id, BackpackConst.Place[] browse) {
        for (EquipDTO dto : equipMap.values()) {
            if (dto.getItemId() == id) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public List<Integer> findBackpackEquipCfgIdList(BackpackConst.Place[] browse) {
        return equipMap.values().stream().map(EquipDTO::getItemId).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Integer> findJueXingLevel() {
        return juexingLevel;
    }

    @Override
    public int findXiaLvGuardianLevel() {
        return xiaLvGuardianLevel;
    }

    @Override
    public int findXiaLvGuanghuanLevel() {
        return xialvGuanghuanLevel;
    }

    @Override
    public Map<Integer, Integer> findShenWei() {
        return getShenWei();
    }


    /**
     * 判断特权月卡是否可用
     *
     * @param id 月卡特权id
     * @return {@link boolean} true:可用  false:不可用
     **/
    @Override
    public boolean monthCardAvailable(int id) {
        if (!monthCard.containsKey(id)) {
            return false;
        }
        int secondTimes = monthCard.get(id);
        return secondTimes == -1 || secondTimes > TimeUtil.getNowOfSeconds();
    }

    /**
     * 是否穿戴隐身斗篷
     */
    public boolean findInvisibility() {
        EquipDTO equipDTO = equipMap.get(EquipConst.EquipIndex.INVISIBILITY_CLOAK.getCls());
        if (equipDTO == null) {
            return false;
        }
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, equipDTO.getItemId());
        return itemConfig != null;
    }


    /**
     * 同步属性到逻辑服
     */
    public void updateAttribute() {
        AttributeUpdateNotice notice = new AttributeUpdateNotice();
        notice.setAttribute(getFinalAttribute());
        notice.setRid(getRid());
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    /**
     * 同步更新神威boss每日掉落次数到逻辑服(在逻辑服更改次数加1)
     */
    public void updateSyncShenWeiBossDailyDropCount() {
        ShenWeiBossDailyDropCountNotice notice = new ShenWeiBossDailyDropCountNotice();
        notice.setRoleId(this.rid);
        notice.setWaitTime(30000);

        NoticeCallback<ShenWeiBossDailyDropCountRetNotice> callback = new NoticeCallback<ShenWeiBossDailyDropCountRetNotice>() {
            @Override
            public void callback(ShenWeiBossDailyDropCountRetNotice notice) {
                if (notice.isSuccess()) {
                    shenWeiBossDailyDropCount = notice.getCount();
                    faQiHuiYuanAddshenWeiBossCount = notice.getFaQiHuiYuanAddshenWeiBossCount();
                } else {
                    shenWeiBossDailyDropCount = Integer.MAX_VALUE;
                }
            }
        };
        notice.setHandler(handler -> {
            ShenWeiBossDailyDropCountRetNotice retNotice = new ShenWeiBossDailyDropCountRetNotice();
            retNotice.setRoleId(rid);
            retNotice.setSuccess(false);
            callback.setNotice(retNotice);
            callback.doAction();
        });
        notice.setCallback(callback);
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    public int getFaQiLevel() {
        return faQiLevel;
    }

    public void setFaQiLevel(int faQiLevel) {
        this.faQiLevel = faQiLevel;
    }

    @Override
    public int findPetLevel() {
        ChongWuActor actor = getChongWuActor();
        if (actor == null) {
            return 0;
        }
        ChongWuConfig config = ConfigDataManager.getInstance().getById(ChongWuConfig.class, actor.getChongWuCfgId());
        if (config == null) {
            return 0;
        }
        return config.getLevel();
    }

    /**
     * 获取玩家足迹充能
     *
     * @return long 角色当前足迹充能
     */
    @Override
    public long findFootprintRecharge() {
        return footprintRecharge;
    }

    /**
     * 重置足迹充能
     */
    public void resetFootprintRecharge() {
        this.footprintRecharge = 0;
    }

    /**
     * 增加足迹充能
     *
     * @param time 充能增加值(两次移动的间隔时间)
     */
    public void addFootprintRecharge(long time) {
        this.footprintRecharge += time;
    }

    @Override
    public boolean findShabakeUnionChairman() {
        ShaBaKeCrossData shaBaKeCrossData = CrossDataPersistenceUtil.findShaBaKeCrossData();
        if (shaBaKeCrossData == null) {
            return false;
        }
        RoleSimpleData chairman = shaBaKeCrossData.getChairman();
        return chairman.getRid() == this.getRoleId();
    }

    @Override
    public int findYuPei() {
        return yuPei;
    }


    /**
     * 判断玩家该时装是否解锁
     *
     * @return
     */
    public boolean findFashionUnlock(int fashionId) {
        return appearanceSets.contains(fashionId);
    }

    @Override
    public int findCampType() {
        return this.getCampType();
    }

    @Override
    public int findMilitaryRankCfgId() {
        return this.getMilitaryRankCfgId();
    }

    public void setYuPei(int yuPei) {
        this.yuPei = yuPei;
    }

    @Override
    public int findRoleRealm() {
        return this.realmId;
    }

    /**
     * 同步更新神威boss每日掉落次数到逻辑服(在逻辑服更改次数加1)
     */
    public void addGoldByFight(long count) {
        if (count <= 0) {
            return;
        }
        GoldDailyNotice notice = new GoldDailyNotice();
        notice.setRoleId(this.rid);
        notice.setAddCount(count);
        notice.setWaitTime(30000);

        NoticeCallback<GoldDailyRetNotice> callback = new NoticeCallback<GoldDailyRetNotice>() {
            @Override
            public void callback(GoldDailyRetNotice notice) {
                goldCount = notice.getCount();
            }
        };
        notice.setHandler(handler -> {
            GoldDailyRetNotice retNotice = new GoldDailyRetNotice();
            retNotice.setRoleId(rid);
            retNotice.setCount(Long.MAX_VALUE);
            callback.setNotice(retNotice);
            callback.doAction();
        });
        notice.setCallback(callback);
        sendNotice(ProcessorId.SERVER_PLAYER, notice);
    }

    /**
     * 恢复血量和蓝量
     */
    public void recoverHpAndMp() {
        if (this.isDead()) {
            return;
        }
        long nowOfMills = TimeUtil.getNowOfMills();

        long maxHp = getFinalAttribute().findMaxHp();
        if (hp >= maxHp) {
            return;
        }
        if (nowOfMills - lastRecoverHpTime >= 1000) {
            long replyRateAddAttrValue = AttributeEnum.ZTXY_REPLY_RATE_ADD.getAttrValue(getFinalAttribute());
            long recover = maxHp * replyRateAddAttrValue / 10000;
            if (recover > 0) {
                addHp(recover, true);
                lastRecoverHpTime = nowOfMills;
            }
        }

        long maxMp = getFinalAttribute().findMaxMp();
        if (mp >= maxMp) {
            return;
        }
        // 目前暂时加固定值不加万分比
        if (nowOfMills - lastRecoverMpTime >= 1000) {
            long attrValue = AttributeEnum.ZTXY_ENERGY_RECOVERY.getAttrValue(getFinalAttribute());
            if (attrValue > 0) {
                addMp(attrValue, true);
                lastRecoverMpTime = nowOfMills;
            }
        }
    }

    @Override
    public void hurt(Performer caster, long hurt) {
        super.hurt(caster, hurt);
        // 死亡时如果主角身上有变身buff，移除掉变身buff(玩家有且只会存在一个变身buff）
        if (hp <= 0) {
            Buff removeTransfromBuff = null;
            for (Buff buff : getBuffs().getBuffMap().values()) {
                BuffConfig buffConfig = buff.getConfig();
                if (buffConfig.getBufferEffectType() == BuffConst.EffectType.TRANSFORM) {
                    removeTransfromBuff = buff;
                    break;
                }
            }
            if (removeTransfromBuff != null) {
                BuffManager.getInstance().remove(this, removeTransfromBuff.getConfig().getId());
            }
            // 移除变身buff后，血量会有变化，这里需要重新判断下是否死亡
            if (hp > 0) {
                setDead(false);
                setKillerId(0);
            }
        }
    }

    public void huoBanChongWuUpdate(Set<Integer> added, Set<Integer> removed) {
        GameMap map = SceneManager.getInstance().getMap(getMapId());
        if (!(map instanceof ZtxyBarrierDuplicate)) {
            return;
        }
        if (removed != null) {
            for (Integer configId : removed) {
                HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, configId);
                MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, config.getCallMonster());
                if (monsterConfig == null) {
                    continue;
                }
                MonsterActor find = map.getMonsterMap().values().stream()
                        .filter(monsterActor -> monsterActor.getType() == MapObjectType.CHONG_WU && monsterActor.getConfigId() == monsterConfig.getId())
                        .findFirst().orElse(null);
                map.removeMonster(find);
            }
        }

        if (added != null) {
            for (Integer configId : added) {
                HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, configId);
                if (config.getCallMonster() == 0) {
                    continue;
                }
                MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, config.getCallMonster());
                if (monsterConfig == null) {
                    continue;
                }
                MonsterActor find = map.getMonsterMap().values().stream()
                        .filter(monsterActor -> monsterActor.getType() == MapObjectType.CHONG_WU && monsterActor.getConfigId() == monsterConfig.getId())
                        .findFirst().orElse(null);
                // 同一个伙伴宠物地图上只能有一个
                if (find != null) {
                    return;
                }
                long speed = this.getAttributeMap().get(AttributeConst.AttributeType.Cate.BASE)
                        .get(AttributeEnum.ZTXY_MOVEMENT_SPEED);
                ChongWuActor chongWu = MapObjectFactory.createChongWu(this, monsterConfig.getId());
                if (chongWu == null) {
                    return;
                }
                Attribute attribute = chongWu.getAttributeMap().get(AttributeConst.AttributeType.Cate.BASE);
                // 继承的属性
                List<int[]> inherit = monsterConfig.getInherit();
                for (int[] ints : inherit) {
                    AttributeEnum attributeEnum = AttributeEnum.valueOf(ints[0]);
                    if (attributeEnum != null) {
                        double rate = ints[1] / 10000.0;
                        long casterValue = this.getFinalAttribute().get(attributeEnum);
                        attribute.fixAdd(attributeEnum.getType(), (long) Math.floor(casterValue * rate));
                    }
                }
                chongWu.setMoveSpeed((int) speed);
                chongWu.setMaster(this);
                chongWu.setAiType(5);
                chongWu.attributeChange();
                Vec3 pos = getPosition().copy();
                TwoTuple<Float, Float> value = GeomUtil.vectorCreateFromDeg(180 + monsterConfig.getBirthAngle().get(0));
                GeomUtil.vectorScalar2Length(value, monsterConfig.getBirthPoint());
                pos.x += value.first;
                pos.y += value.second;
                chongWu.setPosition(pos);
                chongWu.setPoint(new Point((int) pos.x, (int) pos.y));
                map.enterMonster(chongWu, chongWu.getPoint(), false);
            }
        }
    }
}

