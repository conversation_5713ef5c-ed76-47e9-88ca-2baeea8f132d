package com.sh.game.map;

import com.sh.concurrent.AbstractCommand;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.player.PlayerManager;

/**
 * PlayerAliveCheckTask
 *
 * <AUTHOR>
 * @date 2020/9/8 11:21
 */
public class PlayerAliveCheckTask extends AbstractCommand {
    @Override
    public void doAction() {

        Module.COMMAND_ROUTER.process(ProcessorId.MAP_COMMON, new AbstractCommand() {
            @Override
            public void doAction() {
                PlayerManager.getInstance().clearUnavailableHostPlayer();
            }
        }, 0L);

    }
}
